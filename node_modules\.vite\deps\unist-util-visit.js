import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  CONTINUE,
  EXIT,
  SKIP,
  visit
} from "./chunk-IMHLD5XR.js";
import "./chunk-3TBAVN4U.js";
export {
  CONTINUE,
  EXIT,
  SKIP,
  visit
};
