import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  <PERSON><PERSON>,
  _<PERSON><PERSON>,
  _<PERSON><PERSON>,
  _<PERSON><PERSON><PERSON>,
  _<PERSON><PERSON><PERSON>,
  _Text<PERSON><PERSON>er,
  _Tokenizer,
  _defaults,
  _getDefaults,
  lexer,
  marked,
  options,
  parse,
  parseInline,
  parser,
  setOptions,
  use,
  walkTokens
} from "./chunk-XRJB3B2O.js";
import "./chunk-3TBAVN4U.js";
export {
  _Hooks as <PERSON><PERSON>,
  _<PERSON><PERSON> as <PERSON><PERSON>,
  <PERSON><PERSON>,
  _<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  _<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  _<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  _<PERSON>ken<PERSON> as Tokenizer,
  _defaults as defaults,
  _getDefaults as getDefaults,
  lexer,
  marked,
  options,
  parse,
  parseInline,
  parser,
  setOptions,
  use,
  walkTokens
};
