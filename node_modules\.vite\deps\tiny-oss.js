import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  require_base64_js
} from "./chunk-X2J74R54.js";
import {
  __commonJS,
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/object-assign/index.js
var require_object_assign = __commonJS({
  "node_modules/object-assign/index.js"(exports, module) {
    "use strict";
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    var getOwnPropertySymbols = Object.getOwnPropertySymbols;
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    var propIsEnumerable = Object.prototype.propertyIsEnumerable;
    function toObject(val) {
      if (val === null || val === void 0) {
        throw new TypeError("Object.assign cannot be called with null or undefined");
      }
      return Object(val);
    }
    function shouldUseNative() {
      try {
        if (!Object.assign) {
          return false;
        }
        var test1 = new String("abc");
        test1[5] = "de";
        if (Object.getOwnPropertyNames(test1)[0] === "5") {
          return false;
        }
        var test2 = {};
        for (var i = 0; i < 10; i++) {
          test2["_" + String.fromCharCode(i)] = i;
        }
        var order2 = Object.getOwnPropertyNames(test2).map(function(n) {
          return test2[n];
        });
        if (order2.join("") !== "0123456789") {
          return false;
        }
        var test3 = {};
        "abcdefghijklmnopqrst".split("").forEach(function(letter) {
          test3[letter] = letter;
        });
        if (Object.keys(Object.assign({}, test3)).join("") !== "abcdefghijklmnopqrst") {
          return false;
        }
        return true;
      } catch (err) {
        return false;
      }
    }
    module.exports = shouldUseNative() ? Object.assign : function(target, source) {
      var from;
      var to = toObject(target);
      var symbols;
      for (var s = 1; s < arguments.length; s++) {
        from = Object(arguments[s]);
        for (var key in from) {
          if (hasOwnProperty.call(from, key)) {
            to[key] = from[key];
          }
        }
        if (getOwnPropertySymbols) {
          symbols = getOwnPropertySymbols(from);
          for (var i = 0; i < symbols.length; i++) {
            if (propIsEnumerable.call(from, symbols[i])) {
              to[symbols[i]] = from[symbols[i]];
            }
          }
        }
      }
      return to;
    };
  }
});

// node_modules/tiny-oss/lib/utils/ajax.js
var require_ajax = __commonJS({
  "node_modules/tiny-oss/lib/utils/ajax.js"(exports) {
    "use strict";
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    exports.__esModule = true;
    exports["default"] = ajax;
    function ajax(url, options) {
      if (options === void 0) {
        options = {};
      }
      return new Promise(function(resolve, reject) {
        var _options = options, _options$async = _options.async, async = _options$async === void 0 ? true : _options$async, _options$data = _options.data, data = _options$data === void 0 ? null : _options$data, _options$headers = _options.headers, headers = _options$headers === void 0 ? {} : _options$headers, _options$method = _options.method, method = _options$method === void 0 ? "get" : _options$method, _options$timeout = _options.timeout, timeout = _options$timeout === void 0 ? 0 : _options$timeout, onprogress = _options.onprogress;
        var xhr = new XMLHttpRequest();
        var timerId;
        if (timeout) {
          timerId = setTimeout(function() {
            reject(new Error("the request timeout " + timeout + "ms"));
          }, timeout);
        }
        xhr.onerror = function() {
          reject(new Error("unknown error"));
        };
        if (xhr.upload) {
          xhr.upload.onprogress = onprogress;
        }
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            if (timeout) clearTimeout(timerId);
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve(xhr.response, xhr);
            } else {
              var err = new Error("the request is error");
              reject(err);
            }
          }
        };
        xhr.open(method, url, async);
        Object.keys(headers).forEach(function(key) {
          xhr.setRequestHeader(key, headers[key]);
        });
        try {
          xhr.send(data);
        } catch (err) {
          reject(err);
        }
      });
    }
  }
});

// node_modules/crypt/crypt.js
var require_crypt = __commonJS({
  "node_modules/crypt/crypt.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    (function() {
      var base64map = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", crypt = {
        // Bit-wise rotation left
        rotl: function(n, b) {
          return n << b | n >>> 32 - b;
        },
        // Bit-wise rotation right
        rotr: function(n, b) {
          return n << 32 - b | n >>> b;
        },
        // Swap big-endian to little-endian and vice versa
        endian: function(n) {
          if (n.constructor == Number) {
            return crypt.rotl(n, 8) & 16711935 | crypt.rotl(n, 24) & 4278255360;
          }
          for (var i = 0; i < n.length; i++)
            n[i] = crypt.endian(n[i]);
          return n;
        },
        // Generate an array of any length of random bytes
        randomBytes: function(n) {
          for (var bytes = []; n > 0; n--)
            bytes.push(Math.floor(Math.random() * 256));
          return bytes;
        },
        // Convert a byte array to big-endian 32-bit words
        bytesToWords: function(bytes) {
          for (var words = [], i = 0, b = 0; i < bytes.length; i++, b += 8)
            words[b >>> 5] |= bytes[i] << 24 - b % 32;
          return words;
        },
        // Convert big-endian 32-bit words to a byte array
        wordsToBytes: function(words) {
          for (var bytes = [], b = 0; b < words.length * 32; b += 8)
            bytes.push(words[b >>> 5] >>> 24 - b % 32 & 255);
          return bytes;
        },
        // Convert a byte array to a hex string
        bytesToHex: function(bytes) {
          for (var hex = [], i = 0; i < bytes.length; i++) {
            hex.push((bytes[i] >>> 4).toString(16));
            hex.push((bytes[i] & 15).toString(16));
          }
          return hex.join("");
        },
        // Convert a hex string to a byte array
        hexToBytes: function(hex) {
          for (var bytes = [], c = 0; c < hex.length; c += 2)
            bytes.push(parseInt(hex.substr(c, 2), 16));
          return bytes;
        },
        // Convert a byte array to a base-64 string
        bytesToBase64: function(bytes) {
          for (var base64 = [], i = 0; i < bytes.length; i += 3) {
            var triplet = bytes[i] << 16 | bytes[i + 1] << 8 | bytes[i + 2];
            for (var j = 0; j < 4; j++)
              if (i * 8 + j * 6 <= bytes.length * 8)
                base64.push(base64map.charAt(triplet >>> 6 * (3 - j) & 63));
              else
                base64.push("=");
          }
          return base64.join("");
        },
        // Convert a base-64 string to a byte array
        base64ToBytes: function(base64) {
          base64 = base64.replace(/[^A-Z0-9+\/]/ig, "");
          for (var bytes = [], i = 0, imod4 = 0; i < base64.length; imod4 = ++i % 4) {
            if (imod4 == 0) continue;
            bytes.push((base64map.indexOf(base64.charAt(i - 1)) & Math.pow(2, -2 * imod4 + 8) - 1) << imod4 * 2 | base64map.indexOf(base64.charAt(i)) >>> 6 - imod4 * 2);
          }
          return bytes;
        }
      };
      module.exports = crypt;
    })();
  }
});

// node_modules/charenc/charenc.js
var require_charenc = __commonJS({
  "node_modules/charenc/charenc.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    var charenc = {
      // UTF-8 encoding
      utf8: {
        // Convert a string to a byte array
        stringToBytes: function(str) {
          return charenc.bin.stringToBytes(unescape(encodeURIComponent(str)));
        },
        // Convert a byte array to a string
        bytesToString: function(bytes) {
          return decodeURIComponent(escape(charenc.bin.bytesToString(bytes)));
        }
      },
      // Binary encoding
      bin: {
        // Convert a string to a byte array
        stringToBytes: function(str) {
          for (var bytes = [], i = 0; i < str.length; i++)
            bytes.push(str.charCodeAt(i) & 255);
          return bytes;
        },
        // Convert a byte array to a string
        bytesToString: function(bytes) {
          for (var str = [], i = 0; i < bytes.length; i++)
            str.push(String.fromCharCode(bytes[i]));
          return str.join("");
        }
      }
    };
    module.exports = charenc;
  }
});

// node_modules/is-buffer/index.js
var require_is_buffer = __commonJS({
  "node_modules/is-buffer/index.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    module.exports = function(obj) {
      return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer);
    };
    function isBuffer(obj) {
      return !!obj.constructor && typeof obj.constructor.isBuffer === "function" && obj.constructor.isBuffer(obj);
    }
    function isSlowBuffer(obj) {
      return typeof obj.readFloatLE === "function" && typeof obj.slice === "function" && isBuffer(obj.slice(0, 0));
    }
  }
});

// node_modules/md5/md5.js
var require_md5 = __commonJS({
  "node_modules/md5/md5.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    (function() {
      var crypt = require_crypt(), utf8 = require_charenc().utf8, isBuffer = require_is_buffer(), bin = require_charenc().bin, md5 = function(message, options) {
        if (message.constructor == String)
          if (options && options.encoding === "binary")
            message = bin.stringToBytes(message);
          else
            message = utf8.stringToBytes(message);
        else if (isBuffer(message))
          message = Array.prototype.slice.call(message, 0);
        else if (!Array.isArray(message) && message.constructor !== Uint8Array)
          message = message.toString();
        var m = crypt.bytesToWords(message), l = message.length * 8, a = 1732584193, b = -271733879, c = -1732584194, d = 271733878;
        for (var i = 0; i < m.length; i++) {
          m[i] = (m[i] << 8 | m[i] >>> 24) & 16711935 | (m[i] << 24 | m[i] >>> 8) & 4278255360;
        }
        m[l >>> 5] |= 128 << l % 32;
        m[(l + 64 >>> 9 << 4) + 14] = l;
        var FF = md5._ff, GG = md5._gg, HH = md5._hh, II = md5._ii;
        for (var i = 0; i < m.length; i += 16) {
          var aa = a, bb = b, cc = c, dd = d;
          a = FF(a, b, c, d, m[i + 0], 7, -680876936);
          d = FF(d, a, b, c, m[i + 1], 12, -389564586);
          c = FF(c, d, a, b, m[i + 2], 17, 606105819);
          b = FF(b, c, d, a, m[i + 3], 22, -1044525330);
          a = FF(a, b, c, d, m[i + 4], 7, -176418897);
          d = FF(d, a, b, c, m[i + 5], 12, 1200080426);
          c = FF(c, d, a, b, m[i + 6], 17, -1473231341);
          b = FF(b, c, d, a, m[i + 7], 22, -45705983);
          a = FF(a, b, c, d, m[i + 8], 7, 1770035416);
          d = FF(d, a, b, c, m[i + 9], 12, -1958414417);
          c = FF(c, d, a, b, m[i + 10], 17, -42063);
          b = FF(b, c, d, a, m[i + 11], 22, -1990404162);
          a = FF(a, b, c, d, m[i + 12], 7, 1804603682);
          d = FF(d, a, b, c, m[i + 13], 12, -40341101);
          c = FF(c, d, a, b, m[i + 14], 17, -1502002290);
          b = FF(b, c, d, a, m[i + 15], 22, 1236535329);
          a = GG(a, b, c, d, m[i + 1], 5, -165796510);
          d = GG(d, a, b, c, m[i + 6], 9, -1069501632);
          c = GG(c, d, a, b, m[i + 11], 14, 643717713);
          b = GG(b, c, d, a, m[i + 0], 20, -373897302);
          a = GG(a, b, c, d, m[i + 5], 5, -701558691);
          d = GG(d, a, b, c, m[i + 10], 9, 38016083);
          c = GG(c, d, a, b, m[i + 15], 14, -660478335);
          b = GG(b, c, d, a, m[i + 4], 20, -405537848);
          a = GG(a, b, c, d, m[i + 9], 5, 568446438);
          d = GG(d, a, b, c, m[i + 14], 9, -1019803690);
          c = GG(c, d, a, b, m[i + 3], 14, -187363961);
          b = GG(b, c, d, a, m[i + 8], 20, 1163531501);
          a = GG(a, b, c, d, m[i + 13], 5, -1444681467);
          d = GG(d, a, b, c, m[i + 2], 9, -51403784);
          c = GG(c, d, a, b, m[i + 7], 14, 1735328473);
          b = GG(b, c, d, a, m[i + 12], 20, -1926607734);
          a = HH(a, b, c, d, m[i + 5], 4, -378558);
          d = HH(d, a, b, c, m[i + 8], 11, -2022574463);
          c = HH(c, d, a, b, m[i + 11], 16, 1839030562);
          b = HH(b, c, d, a, m[i + 14], 23, -35309556);
          a = HH(a, b, c, d, m[i + 1], 4, -1530992060);
          d = HH(d, a, b, c, m[i + 4], 11, 1272893353);
          c = HH(c, d, a, b, m[i + 7], 16, -155497632);
          b = HH(b, c, d, a, m[i + 10], 23, -1094730640);
          a = HH(a, b, c, d, m[i + 13], 4, 681279174);
          d = HH(d, a, b, c, m[i + 0], 11, -358537222);
          c = HH(c, d, a, b, m[i + 3], 16, -722521979);
          b = HH(b, c, d, a, m[i + 6], 23, 76029189);
          a = HH(a, b, c, d, m[i + 9], 4, -640364487);
          d = HH(d, a, b, c, m[i + 12], 11, -421815835);
          c = HH(c, d, a, b, m[i + 15], 16, 530742520);
          b = HH(b, c, d, a, m[i + 2], 23, -995338651);
          a = II(a, b, c, d, m[i + 0], 6, -198630844);
          d = II(d, a, b, c, m[i + 7], 10, 1126891415);
          c = II(c, d, a, b, m[i + 14], 15, -1416354905);
          b = II(b, c, d, a, m[i + 5], 21, -57434055);
          a = II(a, b, c, d, m[i + 12], 6, 1700485571);
          d = II(d, a, b, c, m[i + 3], 10, -1894986606);
          c = II(c, d, a, b, m[i + 10], 15, -1051523);
          b = II(b, c, d, a, m[i + 1], 21, -2054922799);
          a = II(a, b, c, d, m[i + 8], 6, 1873313359);
          d = II(d, a, b, c, m[i + 15], 10, -30611744);
          c = II(c, d, a, b, m[i + 6], 15, -1560198380);
          b = II(b, c, d, a, m[i + 13], 21, 1309151649);
          a = II(a, b, c, d, m[i + 4], 6, -145523070);
          d = II(d, a, b, c, m[i + 11], 10, -1120210379);
          c = II(c, d, a, b, m[i + 2], 15, 718787259);
          b = II(b, c, d, a, m[i + 9], 21, -343485551);
          a = a + aa >>> 0;
          b = b + bb >>> 0;
          c = c + cc >>> 0;
          d = d + dd >>> 0;
        }
        return crypt.endian([a, b, c, d]);
      };
      md5._ff = function(a, b, c, d, x, s, t) {
        var n = a + (b & c | ~b & d) + (x >>> 0) + t;
        return (n << s | n >>> 32 - s) + b;
      };
      md5._gg = function(a, b, c, d, x, s, t) {
        var n = a + (b & d | c & ~d) + (x >>> 0) + t;
        return (n << s | n >>> 32 - s) + b;
      };
      md5._hh = function(a, b, c, d, x, s, t) {
        var n = a + (b ^ c ^ d) + (x >>> 0) + t;
        return (n << s | n >>> 32 - s) + b;
      };
      md5._ii = function(a, b, c, d, x, s, t) {
        var n = a + (c ^ (b | ~d)) + (x >>> 0) + t;
        return (n << s | n >>> 32 - s) + b;
      };
      md5._blocksize = 16;
      md5._digestsize = 16;
      module.exports = function(message, options) {
        if (message === void 0 || message === null)
          throw new Error("Illegal argument " + message);
        var digestbytes = crypt.wordsToBytes(md5(message, options));
        return options && options.asBytes ? digestbytes : options && options.asString ? bin.bytesToString(digestbytes) : crypt.bytesToHex(digestbytes);
      };
    })();
  }
});

// node_modules/tiny-oss/vendor/digest.js
var require_digest = __commonJS({
  "node_modules/tiny-oss/vendor/digest.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    (function() {
      "use strict";
      if (!ArrayBuffer.prototype.slice) {
        ArrayBuffer.prototype.slice = function(start, end) {
          var i;
          var that = new Uint8Array(this);
          if (end === void 0) {
            end = that.length;
          }
          var result = new ArrayBuffer(end - start);
          var resultArray = new Uint8Array(result);
          for (i = 0; i < resultArray.length; i++) {
            resultArray[i] = that[i + start];
          }
          return result;
        };
      }
    })();
    (function(global) {
      "use strict";
      function sha1Engine() {
      }
      sha1Engine.prototype.processBlock = function(input) {
        var A = this.current[0];
        var B = this.current[1];
        var C = this.current[2];
        var D = this.current[3];
        var E = this.current[4];
        var W = [
          input[0] << 24 | input[1] << 16 | input[2] << 8 | input[3],
          input[4] << 24 | input[5] << 16 | input[6] << 8 | input[7],
          input[8] << 24 | input[9] << 16 | input[10] << 8 | input[11],
          input[12] << 24 | input[13] << 16 | input[14] << 8 | input[15],
          input[16] << 24 | input[17] << 16 | input[18] << 8 | input[19],
          input[20] << 24 | input[21] << 16 | input[22] << 8 | input[23],
          input[24] << 24 | input[25] << 16 | input[26] << 8 | input[27],
          input[28] << 24 | input[29] << 16 | input[30] << 8 | input[31],
          input[32] << 24 | input[33] << 16 | input[34] << 8 | input[35],
          input[36] << 24 | input[37] << 16 | input[38] << 8 | input[39],
          input[40] << 24 | input[41] << 16 | input[42] << 8 | input[43],
          input[44] << 24 | input[45] << 16 | input[46] << 8 | input[47],
          input[48] << 24 | input[49] << 16 | input[50] << 8 | input[51],
          input[52] << 24 | input[53] << 16 | input[54] << 8 | input[55],
          input[56] << 24 | input[57] << 16 | input[58] << 8 | input[59],
          input[60] << 24 | input[61] << 16 | input[62] << 8 | input[63]
        ];
        var T;
        var i;
        for (i = 16; i < 80; i++) {
          W.push((W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16]) << 1 | (W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16]) >>> 31);
        }
        for (i = 0; i < 80; i++) {
          T = (A << 5 | A >>> 27) + E + W[i];
          if (i < 20) {
            T += (B & C | ~B & D) + 1518500249 | 0;
          } else if (i < 40) {
            T += (B ^ C ^ D) + 1859775393 | 0;
          } else if (i < 60) {
            T += (B & C | B & D | C & D) + 2400959708 | 0;
          } else {
            T += (B ^ C ^ D) + 3395469782 | 0;
          }
          E = D;
          D = C;
          C = B << 30 | B >>> 2;
          B = A;
          A = T;
        }
        this.current[0] += A;
        this.current[1] += B;
        this.current[2] += C;
        this.current[3] += D;
        this.current[4] += E;
        this.currentLen += 64;
      };
      sha1Engine.prototype.doPadding = function() {
        var datalen = (this.inLen + this.currentLen) * 8;
        var msw = 0;
        var lsw = datalen & 4294967295;
        var zeros = this.inLen <= 55 ? 55 - this.inLen : 119 - this.inLen;
        var pad = new Uint8Array(new ArrayBuffer(zeros + 1 + 8));
        pad[0] = 128;
        pad[pad.length - 1] = lsw & 255;
        pad[pad.length - 2] = lsw >>> 8 & 255;
        pad[pad.length - 3] = lsw >>> 16 & 255;
        pad[pad.length - 4] = lsw >>> 24 & 255;
        pad[pad.length - 5] = msw & 255;
        pad[pad.length - 6] = msw >>> 8 & 255;
        pad[pad.length - 7] = msw >>> 16 & 255;
        pad[pad.length - 8] = msw >>> 24 & 255;
        return pad;
      };
      sha1Engine.prototype.getDigest = function() {
        var rv = new Uint8Array(new ArrayBuffer(20));
        rv[3] = this.current[0] & 255;
        rv[2] = this.current[0] >>> 8 & 255;
        rv[1] = this.current[0] >>> 16 & 255;
        rv[0] = this.current[0] >>> 24 & 255;
        rv[7] = this.current[1] & 255;
        rv[6] = this.current[1] >>> 8 & 255;
        rv[5] = this.current[1] >>> 16 & 255;
        rv[4] = this.current[1] >>> 24 & 255;
        rv[11] = this.current[2] & 255;
        rv[10] = this.current[2] >>> 8 & 255;
        rv[9] = this.current[2] >>> 16 & 255;
        rv[8] = this.current[2] >>> 24 & 255;
        rv[15] = this.current[3] & 255;
        rv[14] = this.current[3] >>> 8 & 255;
        rv[13] = this.current[3] >>> 16 & 255;
        rv[12] = this.current[3] >>> 24 & 255;
        rv[19] = this.current[4] & 255;
        rv[18] = this.current[4] >>> 8 & 255;
        rv[17] = this.current[4] >>> 16 & 255;
        rv[16] = this.current[4] >>> 24 & 255;
        return rv.buffer;
      };
      sha1Engine.prototype.reset = function() {
        this.currentLen = 0;
        this.inLen = 0;
        this.current = new Uint32Array(new ArrayBuffer(20));
        this.current[0] = 1732584193;
        this.current[1] = 4023233417;
        this.current[2] = 2562383102;
        this.current[3] = 271733878;
        this.current[4] = 3285377520;
      };
      sha1Engine.prototype.blockLen = 64;
      sha1Engine.prototype.digestLen = 20;
      var fromASCII = function(s) {
        var buffer = new ArrayBuffer(s.length);
        var b = new Uint8Array(buffer);
        var i;
        for (i = 0; i < s.length; i++) {
          b[i] = s.charCodeAt(i);
        }
        return b;
      };
      var fromInteger = function(v) {
        var buffer = new ArrayBuffer(1);
        var b = new Uint8Array(buffer);
        b[0] = v;
        return b;
      };
      var convertToUint8Array = function(input) {
        if (input.constructor === Uint8Array) {
          return input;
        } else if (input.constructor === ArrayBuffer) {
          return new Uint8Array(input);
        } else if (input.constructor === String) {
          return fromASCII(input);
        } else if (input.constructor === Number) {
          if (input > 255) {
            throw "For more than one byte, use an array buffer";
          } else if (input < 0) {
            throw "Input value must be positive";
          }
          return fromInteger(input);
        } else {
          throw "Unsupported type";
        }
      };
      var convertToUInt32 = function(i) {
        var tmp = new Uint8Array(new ArrayBuffer(4));
        tmp[0] = (i & 4278190080) >> 24;
        tmp[1] = (i & 16711680) >> 16;
        tmp[2] = (i & 65280) >> 8;
        tmp[3] = i & 255;
        return tmp;
      };
      var dg = function(Constructor) {
        var update = function(input) {
          var len = input.length;
          var offset = 0;
          while (len > 0) {
            var copyLen = this.blockLen - this.inLen;
            if (copyLen > len) {
              copyLen = len;
            }
            var tmpInput = input.subarray(offset, offset + copyLen);
            this.inbuf.set(tmpInput, this.inLen);
            offset += copyLen;
            len -= copyLen;
            this.inLen += copyLen;
            if (this.inLen === this.blockLen) {
              this.processBlock(this.inbuf);
              this.inLen = 0;
            }
          }
        };
        var finalize = function() {
          var padding = this.doPadding();
          this.update(padding);
          var result = this.getDigest();
          this.reset();
          return result;
        };
        var engine = function() {
          if (!Constructor) {
            throw "Unsupported algorithm: " + Constructor.toString();
          }
          Constructor.prototype.update = update;
          Constructor.prototype.finalize = finalize;
          var engine2 = new Constructor();
          engine2.inbuf = new Uint8Array(new ArrayBuffer(engine2.blockLen));
          engine2.reset();
          return engine2;
        }();
        return {
          update: function(input) {
            engine.update(convertToUint8Array(input));
          },
          finalize: function() {
            return engine.finalize();
          },
          digest: function(input) {
            engine.update(convertToUint8Array(input));
            return engine.finalize();
          },
          reset: function() {
            engine.reset();
          },
          digestLength: function() {
            return engine.digestLen;
          }
        };
      };
      var hmac = function(digest) {
        var initialized = false;
        var key, ipad, opad;
        var init = function() {
          var i, kbuf;
          if (initialized) {
            return;
          }
          if (key === void 0) {
            throw "MAC key is not defined";
          }
          if (key.byteLength > 64) {
            kbuf = new Uint8Array(digest.digest(key));
          } else {
            kbuf = new Uint8Array(key);
          }
          ipad = new Uint8Array(new ArrayBuffer(64));
          for (i = 0; i < kbuf.length; i++) {
            ipad[i] = 54 ^ kbuf[i];
          }
          for (i = kbuf.length; i < 64; i++) {
            ipad[i] = 54;
          }
          opad = new Uint8Array(new ArrayBuffer(64));
          for (i = 0; i < kbuf.length; i++) {
            opad[i] = 92 ^ kbuf[i];
          }
          for (i = kbuf.length; i < 64; i++) {
            opad[i] = 92;
          }
          initialized = true;
          digest.update(ipad.buffer);
        };
        var resetMac = function() {
          initialized = false;
          key = void 0;
          ipad = void 0;
          opad = void 0;
          digest.reset();
        };
        var finalizeMac = function() {
          var result = digest.finalize();
          digest.reset();
          digest.update(opad.buffer);
          digest.update(result);
          result = digest.finalize();
          resetMac();
          return result;
        };
        var setKeyMac = function(k) {
          key = k;
        };
        return {
          setKey: function(key2) {
            setKeyMac(convertToUint8Array(key2));
            init();
          },
          update: function(input) {
            digest.update(input);
          },
          finalize: function() {
            return finalizeMac();
          },
          mac: function(input) {
            this.update(input);
            return this.finalize();
          },
          reset: function() {
            resetMac();
          },
          hmacLength: function() {
            return digest.digestLength();
          }
        };
      };
      var Digest = {
        SHA1: function() {
          return dg(sha1Engine);
        },
        HMAC_SHA1: function() {
          return hmac(dg(sha1Engine));
        }
      };
      if ("undefined" !== typeof exports) {
        if ("undefined" !== typeof module && module.exports) {
          module.exports = exports = Digest;
        } else {
          exports = Digest;
        }
      } else {
        global.Digest = Digest;
      }
    })(exports);
  }
});

// node_modules/tiny-oss/lib/utils/index.js
var require_utils = __commonJS({
  "node_modules/tiny-oss/lib/utils/index.js"(exports) {
    "use strict";
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    var _interopRequireDefault = require_interopRequireDefault();
    exports.__esModule = true;
    exports.unix = unix;
    exports.blobToBuffer = blobToBuffer;
    exports.assertOptions = assertOptions;
    exports.getContentMd5 = getContentMd5;
    exports.getCanonicalizedOSSHeaders = getCanonicalizedOSSHeaders;
    exports.getCanonicalizedResource = getCanonicalizedResource;
    exports.getSignature = getSignature;
    var _md = _interopRequireDefault(require_md5());
    var _base64Js = _interopRequireDefault(require_base64_js());
    var _digest = _interopRequireDefault(require_digest());
    function isDate(obj) {
      return obj && Object.prototype.toString.call(obj) === "[object Date]" && obj.toString !== "Invalid Date";
    }
    function unix(date) {
      var d;
      if (date) {
        d = new Date(date);
      }
      if (!isDate(d)) {
        d = /* @__PURE__ */ new Date();
      }
      return Math.round(d.getTime() / 1e3);
    }
    function blobToBuffer(blob) {
      return new Promise(function(resolve, reject) {
        var fr = new FileReader();
        fr.onload = function() {
          var result = new Uint8Array(fr.result);
          resolve(result);
        };
        fr.onerror = function() {
          reject(fr.error);
        };
        fr.readAsArrayBuffer(blob);
      });
    }
    function assertOptions(options) {
      var accessKeyId = options.accessKeyId, accessKeySecret = options.accessKeySecret, bucket = options.bucket, endpoint = options.endpoint;
      if (!accessKeyId) {
        throw new Error("need accessKeyId");
      }
      if (!accessKeySecret) {
        throw new Error("need accessKeySecret");
      }
      if (!bucket && !endpoint) {
        throw new Error("need bucket or endpoint");
      }
    }
    function hexToBuffer(hex) {
      var arr = [];
      for (var i = 0; i < hex.length; i += 2) {
        arr.push(parseInt(hex[i] + hex[i + 1], 16));
      }
      return Uint8Array.from(arr);
    }
    function getContentMd5(buf) {
      var bytes = Array.prototype.slice.call(buf, 0);
      var md5Buf = hexToBuffer((0, _md["default"])(bytes));
      return _base64Js["default"].fromByteArray(md5Buf);
    }
    function getCanonicalizedOSSHeaders(headers) {
      var result = "";
      var headerNames = Object.keys(headers);
      headerNames = headerNames.map(function(name) {
        return name.toLowerCase();
      });
      headerNames.sort();
      headerNames.forEach(function(name) {
        if (name.indexOf("x-oss-") === 0) {
          result += name + ":" + headers[name] + "\n";
        }
      });
      return result;
    }
    function getCanonicalizedResource(bucket, objectName, parameters) {
      if (bucket === void 0) {
        bucket = "";
      }
      if (objectName === void 0) {
        objectName = "";
      }
      var resourcePath = "";
      if (bucket) {
        resourcePath += "/" + bucket;
      }
      if (objectName) {
        if (objectName.charAt(0) !== "/") {
          resourcePath += "/";
        }
        resourcePath += objectName;
      }
      var canonicalizedResource = "" + resourcePath;
      var separatorString = "?";
      if (parameters) {
        var compareFunc = function compareFunc2(entry1, entry2) {
          if (entry1[0] > entry2[0]) {
            return 1;
          }
          if (entry1[0] < entry2[0]) {
            return -1;
          }
          return 0;
        };
        var processFunc = function processFunc2(key) {
          canonicalizedResource += separatorString + key;
          if (parameters[key]) {
            canonicalizedResource += "=" + parameters[key];
          }
          separatorString = "&";
        };
        Object.keys(parameters).sort(compareFunc).forEach(processFunc);
      }
      return canonicalizedResource;
    }
    function getSignature(options) {
      if (options === void 0) {
        options = {};
      }
      var _options = options, _options$type = _options.type, type = _options$type === void 0 ? "header" : _options$type, _options$verb = _options.verb, verb = _options$verb === void 0 ? "" : _options$verb, _options$contentMd = _options.contentMd5, contentMd5 = _options$contentMd === void 0 ? "" : _options$contentMd, _options$expires = _options.expires, expires = _options$expires === void 0 ? unix() + 3600 : _options$expires, bucket = _options.bucket, objectName = _options.objectName, accessKeySecret = _options.accessKeySecret, _options$headers = _options.headers, headers = _options$headers === void 0 ? {} : _options$headers, subResource = _options.subResource;
      var date = headers["x-oss-date"] || "";
      var contentType = headers["Content-Type"] || "";
      var data = [verb, contentMd5, contentType];
      if (type === "header") {
        data.push(date);
      } else {
        data.push(expires);
      }
      var canonicalizedOSSHeaders = getCanonicalizedOSSHeaders(headers);
      var canonicalizedResource = getCanonicalizedResource(bucket, objectName, subResource);
      data.push("" + canonicalizedOSSHeaders + canonicalizedResource);
      var text = data.join("\n");
      var hmac = new _digest["default"].HMAC_SHA1();
      hmac.setKey(accessKeySecret);
      hmac.update(text);
      var hashBuf = new Uint8Array(hmac.finalize());
      var signature = _base64Js["default"].fromByteArray(hashBuf);
      return signature;
    }
  }
});

// node_modules/tiny-oss/lib/TinyOSS.js
var require_TinyOSS = __commonJS({
  "node_modules/tiny-oss/lib/TinyOSS.js"(exports) {
    "use strict";
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    var _interopRequireDefault = require_interopRequireDefault();
    exports.__esModule = true;
    exports["default"] = void 0;
    var _objectAssign = _interopRequireDefault(require_object_assign());
    var _ajax = _interopRequireDefault(require_ajax());
    var _utils = require_utils();
    var TinyOSS = function() {
      function TinyOSS2(options) {
        if (options === void 0) {
          options = {};
        }
        (0, _utils.assertOptions)(options);
        this.opts = (0, _objectAssign["default"])({
          region: "oss-cn-hangzhou",
          internal: false,
          cname: false,
          secure: false,
          timeout: 6e4
        }, options);
        var _this$opts = this.opts, bucket = _this$opts.bucket, region = _this$opts.region, endpoint = _this$opts.endpoint, internal = _this$opts.internal;
        this.host = "";
        if (endpoint) {
          this.host = endpoint;
        } else {
          var host = bucket;
          if (internal) {
            host += "-internal";
          }
          host += "." + region + ".aliyuncs.com";
          this.host = host;
        }
      }
      var _proto = TinyOSS2.prototype;
      _proto.put = function put(objectName, blob, options) {
        var _this = this;
        if (options === void 0) {
          options = {};
        }
        return new Promise(function(resolve, reject) {
          (0, _utils.blobToBuffer)(blob).then(function(buf) {
            var _this$opts2 = _this.opts, accessKeyId = _this$opts2.accessKeyId, accessKeySecret = _this$opts2.accessKeySecret, stsToken = _this$opts2.stsToken, bucket = _this$opts2.bucket;
            var verb = "PUT";
            var contentMd5 = (0, _utils.getContentMd5)(buf);
            var contentType = blob.type;
            var headers = {
              "Content-Md5": contentMd5,
              "Content-Type": contentType,
              "x-oss-date": (/* @__PURE__ */ new Date()).toGMTString()
            };
            if (stsToken) {
              headers["x-oss-security-token"] = stsToken;
            }
            var signature = (0, _utils.getSignature)({
              verb,
              contentMd5,
              headers,
              bucket,
              objectName,
              accessKeyId,
              accessKeySecret
            });
            headers.Authorization = "OSS " + accessKeyId + ":" + signature;
            var protocol = _this.opts.secure ? "https" : "http";
            var url = protocol + "://" + _this.host + "/" + objectName;
            return (0, _ajax["default"])(url, {
              method: verb,
              headers,
              data: blob,
              timeout: _this.opts.timeout,
              onprogress: options.onprogress
            });
          }).then(resolve)["catch"](reject);
        });
      };
      _proto.putSymlink = function putSymlink(objectName, targetObjectName) {
        var _this$opts3 = this.opts, accessKeyId = _this$opts3.accessKeyId, accessKeySecret = _this$opts3.accessKeySecret, stsToken = _this$opts3.stsToken, bucket = _this$opts3.bucket;
        var verb = "PUT";
        var headers = {
          "x-oss-date": (/* @__PURE__ */ new Date()).toGMTString(),
          "x-oss-symlink-target": encodeURI(targetObjectName)
        };
        if (stsToken) {
          headers["x-oss-security-token"] = stsToken;
        }
        var signature = (0, _utils.getSignature)({
          verb,
          headers,
          bucket,
          objectName,
          accessKeyId,
          accessKeySecret,
          subResource: {
            symlink: ""
          }
        });
        headers.Authorization = "OSS " + accessKeyId + ":" + signature;
        var protocol = this.opts.secure ? "https" : "http";
        var url = protocol + "://" + this.host + "/" + objectName + "?symlink";
        return (0, _ajax["default"])(url, {
          method: verb,
          headers,
          timeout: this.opts.timeout
        });
      };
      _proto.signatureUrl = function signatureUrl(objectName, options) {
        if (options === void 0) {
          options = {};
        }
        var _options = options, _options$expires = _options.expires, expires = _options$expires === void 0 ? 1800 : _options$expires, method = _options.method, process = _options.process, response = _options.response;
        var _this$opts4 = this.opts, accessKeyId = _this$opts4.accessKeyId, accessKeySecret = _this$opts4.accessKeySecret, stsToken = _this$opts4.stsToken, bucket = _this$opts4.bucket;
        var headers = {};
        var subResource = {};
        if (process) {
          var processKeyword = "x-oss-process";
          subResource[processKeyword] = process;
        }
        if (response) {
          Object.keys(response).forEach(function(k) {
            var key = "response-" + k.toLowerCase();
            subResource[key] = response[k];
          });
        }
        Object.keys(options).forEach(function(key) {
          var lowerKey = key.toLowerCase();
          var value = options[key];
          if (lowerKey.indexOf("x-oss-") === 0) {
            headers[lowerKey] = value;
          } else if (lowerKey.indexOf("content-md5") === 0) {
            headers[key] = value;
          } else if (lowerKey.indexOf("content-type") === 0) {
            headers[key] = value;
          } else if (lowerKey !== "expires" && lowerKey !== "response" && lowerKey !== "process" && lowerKey !== "method") {
            subResource[lowerKey] = value;
          }
        });
        var securityToken = options["security-token"] || stsToken;
        if (securityToken) {
          subResource["security-token"] = securityToken;
        }
        var expireUnix = (0, _utils.unix)() + expires;
        var signature = (0, _utils.getSignature)({
          type: "url",
          verb: method || "GET",
          accessKeyId,
          accessKeySecret,
          bucket,
          objectName,
          headers,
          subResource,
          expires: expireUnix
        });
        var protocol = this.opts.secure ? "https" : "http";
        var url = protocol + "://" + this.host + "/" + objectName;
        url += "?OSSAccessKeyId=" + accessKeyId;
        url += "&Expires=" + expireUnix;
        url += "&Signature=" + encodeURIComponent(signature);
        Object.keys(subResource).forEach(function(k) {
          url += "&" + k + "=" + encodeURIComponent(subResource[k]);
        });
        return url;
      };
      return TinyOSS2;
    }();
    exports["default"] = TinyOSS;
  }
});

// node_modules/tiny-oss/lib/index.js
var require_lib = __commonJS({
  "node_modules/tiny-oss/lib/index.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    module.exports = require_TinyOSS()["default"];
  }
});
export default require_lib();
/*! Bundled license information:

object-assign/index.js:
  (*
  object-assign
  (c) Sindre Sorhus
  @license MIT
  *)

is-buffer/index.js:
  (*!
   * Determine if an object is a Buffer
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)

tiny-oss/vendor/digest.js:
  (*! ***** BEGIN LICENSE BLOCK *****
   *!
   *! Copyright 2011-2012, 2014 Jean-Christophe Sirot <<EMAIL>>
   *!
   *! This file is part of digest.js
   *!
   *! digest.js is free software: you can redistribute it and/or modify it under
   *! the terms of the GNU General Public License as published by the Free Software
   *! Foundation, either version 3 of the License, or (at your option) any later
   *! version.
   *!
   *! digest.js is distributed in the hope that it will be useful, but
   *! WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
   *! or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
   *! more details.
   *!
   *! You should have received a copy of the GNU General Public License along with
   *! digest.js. If not, see http://www.gnu.org/licenses/.
   *!
   *! ***** END LICENSE BLOCK *****  *)
*/
//# sourceMappingURL=tiny-oss.js.map
