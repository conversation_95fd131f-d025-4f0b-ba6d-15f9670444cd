{"version": 3, "sources": ["../../@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-V4Q32G6S.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  MermaidGeneratedSharedModule,\n  PacketGeneratedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/packet/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/packet/tokenBuilder.ts\nvar PacketTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PacketTokenBuilder\");\n  }\n  constructor() {\n    super([\"packet-beta\"]);\n  }\n};\n\n// src/language/packet/module.ts\nvar PacketModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new PacketTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPacketServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Packet = inject(\n    createDefaultCoreModule({ shared }),\n    PacketGeneratedModule,\n    PacketModule\n  );\n  shared.ServiceRegistry.register(Packet);\n  return { shared, Packet };\n}\n__name(createPacketServices, \"createPacketServices\");\n\nexport {\n  PacketModule,\n  createPacketServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA;AAiBA,IAAI,sBAAqB,mBAAc,4BAA4B;AAAA,EAIjE,cAAc;AACZ,UAAM,CAAC,aAAa,CAAC;AAAA,EACvB;AACF,GALI,OAAO,IAAM,oBAAoB,GAFZ;AAUzB,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,IACN,cAA8B,OAAO,MAAM,IAAI,mBAAmB,GAAG,cAAc;AAAA,IACnF,gBAAgC,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,qBAAqB,UAAU,iBAAiB;AACvD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,MAAM;AACtC,SAAO,EAAE,QAAQ,OAAO;AAC1B;AACA,OAAO,sBAAsB,sBAAsB;", "names": ["import_dist"]}