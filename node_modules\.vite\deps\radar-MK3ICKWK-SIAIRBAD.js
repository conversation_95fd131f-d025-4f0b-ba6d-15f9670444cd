import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  RadarModule,
  createRadarServices
} from "./chunk-NZKR3EU3.js";
import "./chunk-H4LMIB3O.js";
import "./chunk-IKTTXOWS.js";
import "./chunk-OSPUQ53P.js";
import "./chunk-XNDO3ZN6.js";
import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/@mermaid-js/parser/dist/chunks/mermaid-parser.core/radar-MK3ICKWK.mjs
var import_dist = __toESM(require_dist(), 1);
var import_dist2 = __toESM(require_dist2(), 1);
var import_dist3 = __toESM(require_dist3(), 1);
export {
  RadarModule,
  createRadarServices
};
//# sourceMappingURL=radar-MK3ICKWK-SIAIRBAD.js.map
