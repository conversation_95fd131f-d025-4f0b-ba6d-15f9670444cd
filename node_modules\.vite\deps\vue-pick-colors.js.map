{"version": 3, "sources": ["../../vue-pick-colors/dist/index.esm.js", "../../@popperjs/core/lib/index.js", "../../@popperjs/core/lib/enums.js", "../../@popperjs/core/lib/modifiers/index.js", "../../@popperjs/core/lib/modifiers/applyStyles.js", "../../@popperjs/core/lib/dom-utils/getNodeName.js", "../../@popperjs/core/lib/dom-utils/instanceOf.js", "../../@popperjs/core/lib/dom-utils/getWindow.js", "../../@popperjs/core/lib/modifiers/arrow.js", "../../@popperjs/core/lib/utils/getBasePlacement.js", "../../@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../@popperjs/core/lib/utils/math.js", "../../@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../@popperjs/core/lib/utils/userAgent.js", "../../@popperjs/core/lib/dom-utils/contains.js", "../../@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../@popperjs/core/lib/dom-utils/isTableElement.js", "../../@popperjs/core/lib/dom-utils/getParentNode.js", "../../@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../@popperjs/core/lib/utils/within.js", "../../@popperjs/core/lib/utils/mergePaddingObject.js", "../../@popperjs/core/lib/utils/getFreshSideObject.js", "../../@popperjs/core/lib/utils/expandToHashMap.js", "../../@popperjs/core/lib/modifiers/computeStyles.js", "../../@popperjs/core/lib/utils/getVariation.js", "../../@popperjs/core/lib/modifiers/eventListeners.js", "../../@popperjs/core/lib/modifiers/flip.js", "../../@popperjs/core/lib/utils/getOppositePlacement.js", "../../@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../@popperjs/core/lib/utils/detectOverflow.js", "../../@popperjs/core/lib/dom-utils/getClippingRect.js", "../../@popperjs/core/lib/dom-utils/getViewportRect.js", "../../@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../@popperjs/core/lib/dom-utils/listScrollParents.js", "../../@popperjs/core/lib/dom-utils/getScrollParent.js", "../../@popperjs/core/lib/dom-utils/isScrollParent.js", "../../@popperjs/core/lib/utils/rectToClientRect.js", "../../@popperjs/core/lib/utils/computeOffsets.js", "../../@popperjs/core/lib/utils/computeAutoPlacement.js", "../../@popperjs/core/lib/modifiers/hide.js", "../../@popperjs/core/lib/modifiers/offset.js", "../../@popperjs/core/lib/modifiers/popperOffsets.js", "../../@popperjs/core/lib/modifiers/preventOverflow.js", "../../@popperjs/core/lib/utils/getAltAxis.js", "../../@popperjs/core/lib/createPopper.js", "../../@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../@popperjs/core/lib/utils/orderModifiers.js", "../../@popperjs/core/lib/utils/debounce.js", "../../@popperjs/core/lib/utils/mergeByName.js", "../../@popperjs/core/lib/popper.js", "../../@popperjs/core/lib/popper-lite.js"], "sourcesContent": ["import{defineComponent as e,computed as t,openBlock as r,createElementBlock as n,normalizeStyle as o,withModifiers as a,createElementVNode as i,ref as c,onMounted as l,watch as u,unref as s,nextTick as f,onBeforeUnmount as d,onUnmounted as p,toDisplayString as v,createCommentVNode as h,createVNode as m,Transition as g,withCtx as y,Fragment as b,renderList as x,resolveComponent as w,withKeys as S,inject as k,createBlock as C,provide as O,toRaw as E,Teleport as A}from\"vue\";import{createPopper as N}from\"@popperjs/core\";function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function F(e,t){if(e){if(\"string\"==typeof e)return I(e,t);var r={}.toString.call(e).slice(8,-1);return\"Object\"===r&&e.constructor&&(r=e.constructor.name),\"Map\"===r||\"Set\"===r?Array.from(e):\"Arguments\"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?I(e,t):void 0}}function _(e){return function(e){if(Array.isArray(e))return I(e)}(e)||function(e){if(\"undefined\"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e[\"@@iterator\"])return Array.from(e)}(e)||F(e)||function(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}()}function L(e){return L=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},L(e)}function P(e,t,r,n,o,a,i){try{var c=e[a](i),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,o)}function j(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){P(a,n,o,i,c,\"next\",e)}function c(e){P(a,n,o,i,c,\"throw\",e)}i(void 0)}))}}var M={exports:{}},B={exports:{}};!function(e){function t(r){return e.exports=t=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(B),function(e){var t=B.exports.default;function r(){e.exports=r=function(){return o},e.exports.__esModule=!0,e.exports.default=e.exports;var n,o={},a=Object.prototype,i=a.hasOwnProperty,c=Object.defineProperty||function(e,t,r){e[t]=r.value},l=\"function\"==typeof Symbol?Symbol:{},u=l.iterator||\"@@iterator\",s=l.asyncIterator||\"@@asyncIterator\",f=l.toStringTag||\"@@toStringTag\";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},\"\")}catch(n){d=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof x?t:x,a=Object.create(o.prototype),i=new P(n||[]);return c(a,\"_invoke\",{value:I(e,r,i)}),a}function v(e,t,r){try{return{type:\"normal\",arg:e.call(t,r)}}catch(e){return{type:\"throw\",arg:e}}}o.wrap=p;var h=\"suspendedStart\",m=\"suspendedYield\",g=\"executing\",y=\"completed\",b={};function x(){}function w(){}function S(){}var k={};d(k,u,(function(){return this}));var C=Object.getPrototypeOf,O=C&&C(C(j([])));O&&O!==a&&i.call(O,u)&&(k=O);var E=S.prototype=x.prototype=Object.create(k);function A(e){[\"next\",\"throw\",\"return\"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,r){function n(o,a,c,l){var u=v(e[o],e,a);if(\"throw\"!==u.type){var s=u.arg,f=s.value;return f&&\"object\"==t(f)&&i.call(f,\"__await\")?r.resolve(f.__await).then((function(e){n(\"next\",e,c,l)}),(function(e){n(\"throw\",e,c,l)})):r.resolve(f).then((function(e){s.value=e,c(s)}),(function(e){return n(\"throw\",e,c,l)}))}l(u.arg)}var o;c(this,\"_invoke\",{value:function(e,t){function a(){return new r((function(r,o){n(e,t,r,o)}))}return o=o?o.then(a,a):a()}})}function I(e,t,r){var o=h;return function(a,i){if(o===g)throw Error(\"Generator is already running\");if(o===y){if(\"throw\"===a)throw i;return{value:n,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=F(c,r);if(l){if(l===b)continue;return l}}if(\"next\"===r.method)r.sent=r._sent=r.arg;else if(\"throw\"===r.method){if(o===h)throw o=y,r.arg;r.dispatchException(r.arg)}else\"return\"===r.method&&r.abrupt(\"return\",r.arg);o=g;var u=v(e,t,r);if(\"normal\"===u.type){if(o=r.done?y:m,u.arg===b)continue;return{value:u.arg,done:r.done}}\"throw\"===u.type&&(o=y,r.method=\"throw\",r.arg=u.arg)}}}function F(e,t){var r=t.method,o=e.iterator[r];if(o===n)return t.delegate=null,\"throw\"===r&&e.iterator.return&&(t.method=\"return\",t.arg=n,F(e,t),\"throw\"===t.method)||\"return\"!==r&&(t.method=\"throw\",t.arg=new TypeError(\"The iterator does not provide a '\"+r+\"' method\")),b;var a=v(o,e.iterator,t.arg);if(\"throw\"===a.type)return t.method=\"throw\",t.arg=a.arg,t.delegate=null,b;var i=a.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,\"return\"!==t.method&&(t.method=\"next\",t.arg=n),t.delegate=null,b):i:(t.method=\"throw\",t.arg=new TypeError(\"iterator result is not an object\"),t.delegate=null,b)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type=\"normal\",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:\"root\"}],e.forEach(_,this),this.reset(!0)}function j(e){if(e||\"\"===e){var r=e[u];if(r)return r.call(e);if(\"function\"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function t(){for(;++o<e.length;)if(i.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=n,t.done=!0,t};return a.next=a}}throw new TypeError(t(e)+\" is not iterable\")}return w.prototype=S,c(E,\"constructor\",{value:S,configurable:!0}),c(S,\"constructor\",{value:w,configurable:!0}),w.displayName=d(S,f,\"GeneratorFunction\"),o.isGeneratorFunction=function(e){var t=\"function\"==typeof e&&e.constructor;return!!t&&(t===w||\"GeneratorFunction\"===(t.displayName||t.name))},o.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,d(e,f,\"GeneratorFunction\")),e.prototype=Object.create(E),e},o.awrap=function(e){return{__await:e}},A(N.prototype),d(N.prototype,s,(function(){return this})),o.AsyncIterator=N,o.async=function(e,t,r,n,a){void 0===a&&(a=Promise);var i=new N(p(e,t,r,n),a);return o.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},A(E),d(E,f,\"Generator\"),d(E,u,(function(){return this})),d(E,\"toString\",(function(){return\"[object Generator]\"})),o.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},o.values=j,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method=\"next\",this.arg=n,this.tryEntries.forEach(L),!e)for(var t in this)\"t\"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if(\"throw\"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,o){return c.type=\"throw\",c.arg=e,t.next=r,o&&(t.method=\"next\",t.arg=n),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if(\"root\"===a.tryLoc)return r(\"end\");if(a.tryLoc<=this.prev){var l=i.call(a,\"catchLoc\"),u=i.call(a,\"finallyLoc\");if(l&&u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!u)throw Error(\"try statement without catch or finally\");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,\"finallyLoc\")&&this.prev<n.finallyLoc){var o=n;break}}o&&(\"break\"===e||\"continue\"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method=\"next\",this.next=o.finallyLoc,b):this.complete(a)},complete:function(e,t){if(\"throw\"===e.type)throw e.arg;return\"break\"===e.type||\"continue\"===e.type?this.next=e.arg:\"return\"===e.type?(this.rval=this.arg=e.arg,this.method=\"return\",this.next=\"end\"):\"normal\"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if(\"throw\"===n.type){var o=n.arg;L(r)}return o}}throw Error(\"illegal catch attempt\")},delegateYield:function(e,t,r){return this.delegate={iterator:j(e),resultName:t,nextLoc:r},\"next\"===this.method&&(this.arg=n),b}},o}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(M);var R=M.exports(),z=R;try{regeneratorRuntime=R}catch(e){\"object\"==typeof globalThis?globalThis.regeneratorRuntime=R:Function(\"r\",\"regeneratorRuntime = r\")(R)}function T(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:\"undefined\"!=typeof Symbol&&e[Symbol.iterator]||e[\"@@iterator\"];if(null!=r){var n,o,a,i,c=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}(e,t)||F(e,t)||function(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}()}function D(e){var t=function(e,t){if(\"object\"!=L(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||\"default\");if(\"object\"!=L(n))return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return(\"string\"===t?String:Number)(e)}(e,\"string\");return\"symbol\"==L(t)?t:t+\"\"}function H(e,t,r){return(t=D(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var G=e({name:\"Saturation\",props:{size:{type:Number,default:160},hue:{type:Number,default:0},saturation:{type:Number,default:0},value:{type:Number,default:0}},emits:[\"change\"],setup:function(e,r){var n=r.emit,o=t((function(){return{width:\"\".concat(e.size,\"px\"),height:\"\".concat(e.size,\"px\"),background:\"hsl(\".concat(e.hue,\", 100%, 50%)\")}}));return{saturationStyle:o,sliderStyle:t((function(){return{top:\"\".concat((100-e.value)/100*e.size-5,\"px\"),left:\"\".concat(e.saturation*e.size/100-5,\"px\"),width:\"\".concat(10,\"px\"),height:\"\".concat(10,\"px\")}})),onSelect:function(t){var r=t.target.getBoundingClientRect(),o=r.left,a=r.top,i=function(t){var r,i;t instanceof MouseEvent?(r=t.clientX,i=t.clientY):t instanceof TouchEvent&&(r=t.touches[0].clientX,i=t.touches[0].clientY);var c=r-o,l=i-a;c<0&&(c=0),l<0&&(l=0),c>e.size&&(c=e.size),l>e.size&&(l=e.size);var u=c/e.size*100,s=100-l/e.size*100;n(\"change\",u,s)};i(t);var c=function(){document.removeEventListener(\"mousemove\",i),document.removeEventListener(\"mouseup\",c),document.removeEventListener(\"touchmove\",i),document.removeEventListener(\"touchend\",c)};i(t),t instanceof MouseEvent&&(document.addEventListener(\"mousemove\",i),document.addEventListener(\"mouseup\",c)),t instanceof TouchEvent&&(t.preventDefault(),document.addEventListener(\"touchmove\",i,{passive:!1}),document.addEventListener(\"touchend\",c))}}}});function V(e,t){void 0===t&&(t={});var r=t.insertAt;if(e&&\"undefined\"!=typeof document){var n=document.head||document.getElementsByTagName(\"head\")[0],o=document.createElement(\"style\");o.type=\"text/css\",\"top\"===r&&n.firstChild?n.insertBefore(o,n.firstChild):n.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}V(\".saturation[data-v-24517fec]{position:relative}.saturation-black[data-v-24517fec],.saturation-white[data-v-24517fec]{cursor:pointer;height:100%;left:0;position:absolute;top:0;width:100%}.saturation-white[data-v-24517fec]{background:linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.saturation-black[data-v-24517fec]{background:linear-gradient(0deg,#000,transparent)}.slider[data-v-24517fec]{border:1px solid #fff;border-radius:50%;box-shadow:0 0 1px 1px rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;top:0;z-index:1}\"),G.render=function(e,t,c,l,u,s){return r(),n(\"div\",{class:\"saturation\",style:o(e.saturationStyle),onMousedown:t[0]||(t[0]=a((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),[\"prevent\",\"stop\"])),onTouchstart:t[1]||(t[1]=a((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),[\"prevent\",\"stop\"]))},[t[2]||(t[2]=i(\"div\",{class:\"saturation-white\"},null,-1)),t[3]||(t[3]=i(\"div\",{class:\"saturation-black\"},null,-1)),i(\"div\",{class:\"slider\",style:o(e.sliderStyle)},null,4)],36)},G.__scopeId=\"data-v-24517fec\",G.__file=\"src/picker/Saturation.vue\";var Y=e({name:\"Hue\",props:{width:{type:Number,default:15},height:{type:Number,default:160},hue:{type:Number,default:0}},emits:[\"change\"],setup:function(e,r){var n=r.emit,o=t((function(){return{top:\"\".concat((1-e.hue/360)*e.height-2,\"px\"),height:\"\".concat(4,\"px\")}})),a=c();l((function(){!function(){if(a.value){a.value.width=e.width,a.value.height=e.height;var t=a.value.getContext(\"2d\");if(t){var r=t.createLinearGradient(0,0,0,e.height);r.addColorStop(0,\"#FF0000\"),r.addColorStop(.17,\"#FF00FF\"),r.addColorStop(.34,\"#0000FF\"),r.addColorStop(.51,\"#00FFFF\"),r.addColorStop(.68,\"#00FF00\"),r.addColorStop(.17*5,\"#FFFF00\"),r.addColorStop(1,\"#FF0000\"),t.fillStyle=r,t.fillRect(0,0,e.width,e.height)}}}()}));return{canvas:a,sliderStyle:o,onSelect:function(t){var r=t.target.getBoundingClientRect().top,o=function(t){var o;t instanceof MouseEvent?o=t.clientY:t instanceof TouchEvent&&(o=t.touches[0].clientY);var a=o-r;a<0&&(a=0),a>e.height&&(a=e.height);var i=-100*a/e.height+100;n(\"change\",360*i/100)},a=function(){document.removeEventListener(\"mousemove\",o),document.removeEventListener(\"mouseup\",a),document.removeEventListener(\"touchmove\",o),document.removeEventListener(\"touchend\",a)};o(t),t instanceof MouseEvent&&(document.addEventListener(\"mousemove\",o),document.addEventListener(\"mouseup\",a)),t instanceof TouchEvent&&(t.preventDefault(),document.addEventListener(\"touchmove\",o,{passive:!1}),document.addEventListener(\"touchend\",a))}}}}),$={ref:\"canvas\"};V(\".hue[data-v-78b9f4f0]{position:relative;touch-action:none}.slider[data-v-78b9f4f0]{background:#fff;box-shadow:0 0 1px 0 rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;width:100%;z-index:1}\"),Y.render=function(e,t,c,l,u,s){return r(),n(\"div\",{class:\"hue\",onMousedown:t[0]||(t[0]=a((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),[\"prevent\",\"stop\"])),onTouchstart:t[1]||(t[1]=a((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),[\"prevent\",\"stop\"]))},[i(\"canvas\",$,null,512),i(\"div\",{class:\"slider\",style:o(e.sliderStyle)},null,4)],32)},Y.__scopeId=\"data-v-78b9f4f0\",Y.__file=\"src/picker/Hue.vue\";var X=e({name:\"Alpha\",props:{width:{type:Number,default:15},height:{type:Number,default:160},color:{type:String,default:\"#000000\"},alpha:{type:Number,default:1}},setup:function(e,r){var n=r.emit,o=t((function(){return{top:\"\".concat(e.alpha*e.height-2,\"px\"),height:\"\".concat(4,\"px\")}})),a=c(),i=function(){var t=a.value.getContext(\"2d\");a.value.width=e.width,a.value.height=e.height;var r=function(e){var t=document.createElement(\"canvas\"),r=t.getContext(\"2d\"),n=2*e;return t.width=n,t.height=n,r.fillStyle=\"#ffffff\",r.fillRect(0,0,n,n),r.fillStyle=\"#ccd5db\",r.fillRect(0,0,e,e),r.fillRect(e,e,e,e),t}(5);t.fillStyle=t.createPattern(r,\"repeat\"),t.fillRect(0,0,e.width,e.height);var n=t.createLinearGradient(0,0,0,e.height);n.addColorStop(.01,\"rgba(255,255,255,0)\"),n.addColorStop(.99,e.color),t.fillStyle=n,t.fillRect(0,0,e.width,e.height)};u((function(){return e.color}),(function(){i()})),l((function(){i()}));return{canvas:a,sliderStyle:o,onSelect:function(t){var r=t.target.getBoundingClientRect().top,o=function(t){var o;t instanceof MouseEvent?o=t.clientY:t instanceof TouchEvent&&(o=t.touches[0].clientY);var a=o-r;a<0&&(a=0),a>e.height&&(a=e.height);var i=parseFloat((a/e.height).toFixed(2));n(\"change\",i)},a=function(){document.removeEventListener(\"mousemove\",o),document.removeEventListener(\"mouseup\",a),document.removeEventListener(\"touchmove\",o),document.removeEventListener(\"touchend\",a)};o(t),t instanceof MouseEvent&&(document.addEventListener(\"mousemove\",o),document.addEventListener(\"mouseup\",a)),t instanceof TouchEvent&&(t.preventDefault(),document.addEventListener(\"touchmove\",o,{passive:!1}),document.addEventListener(\"touchend\",a))}}}}),J={ref:\"canvas\"};V(\".alpha[data-v-24dc9656]{position:relative;touch-action:none}.slider[data-v-24dc9656]{background:#fff;box-shadow:0 0 1px 0 rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;width:100%;z-index:1}\"),X.render=function(e,t,c,l,u,s){return r(),n(\"div\",{class:\"alpha\",onMousedown:t[0]||(t[0]=a((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),[\"prevent\",\"stop\"])),onTouchstart:t[1]||(t[1]=a((function(){return e.onSelect&&e.onSelect.apply(e,arguments)}),[\"prevent\",\"stop\"]))},[i(\"canvas\",J,null,512),i(\"div\",{class:\"slider\",style:o(e.sliderStyle)},null,4)],32)},X.__scopeId=\"data-v-24dc9656\",X.__file=\"src/picker/Alpha.vue\";var q={rgb:\"RGBA\",hex:\"HEX\",hsl:\"HSLA\",hsv:\"HSVA\"},U={rgb:\"RGB\",hex:\"HEX\",hsl:\"HSL\",hsv:\"HSV\"},W={RGB:\"rgb\",RGBA:\"rgb\",HEX:\"hex\",HSL:\"hsl\",HSLA:\"hsl\",HSV:\"hsv\",HSVA:\"hsv\"};function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach((function(t){H(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Z=null,ee=function(e,t,r){var n=c({}),o=r||{},a=o.placement,i=o.defaultStyle,l={strategy:o.strategy||\"absolute\",placement:a||\"auto\",onFirstUpdate:function(){Z.update()},modifiers:[{name:\"offset\",options:{offset:[0,5]}},{name:\"computeStyles\",options:{gpuAcceleration:!1,adaptive:!0}},{name:\"flip\",options:{allowedAutoPlacements:[\"top\",\"bottom\"]}},{name:\"applyStyles\",enabled:!1},{name:\"updateState\",enabled:!0,phase:\"write\",requires:[\"computeStyles\"],fn:function(e){var t=e.state,r=t.styles,o=t.placement,a=r.popper;n.value=Q(Q(Q({},a),i),{},{transformOrigin:\"top\"===o?\"center bottom\":\"center top\"})}}]};return u((function(){return[s(e),s(t)]}),(function(e,t){var r,n=T(e,2),o=n[0],a=n[1],i=T(t,2),c=i[0],u=i[1];if(o&&a&&(c!==o||u!==c)){null===(r=Z)||void 0===r||r.destroy();var s=o.$el||o,d=a.$el||a;f((function(){Z=N(s,d,l)}))}})),d((function(){var e;Z&&(null===(e=Z)||void 0===e||e.destroy(),Z=null)})),{instance:Z,style:n}},te=e({props:{value:{type:String,default:\"RGBA\"},showAlpha:{type:Boolean},options:{type:[Boolean,Array]}},emits:[\"change\"],setup:function(e,r){var n=r.emit,o=c(null),a=c(null),i=c(!1),u=ee(o,a,{strategy:\"fixed\",defaultStyle:{zIndex:2}}).style,f=t((function(){return Array.isArray(e.options)&&e.options.length>1})),d=t((function(){var t=e.options,r=e.showAlpha,n=e.value;return Array.isArray(t)?r?t.map((function(e){return q[e]})).filter((function(e){return!e.includes(n)})):t.map((function(e){return U[e]})).filter((function(e){return!e.includes(n)})):[]})),v=function(e){var t,r,n=e.target;!(null!==(t=s(o))&&void 0!==t&&t.isEqualNode(n))&&(null===(r=s(o))||void 0===r?void 0:r.contains(n))||(i.value=!1)};return l((function(){document.addEventListener(\"mouseup\",v,!1)})),p((function(){document.removeEventListener(\"mouseup\",v,!1)})),{targetRef:o,selectorRef:a,selectorStyle:u,isShowSelector:i,isNeedSelect:f,formatOptions:d,onShow:function(){f.value&&(i.value=!0)},onFormatChange:function(e){n(\"change\",W[e])}}}}),re={class:\"format\"},ne={key:0,class:\"arrow\"},oe=[\"onClick\"];V(\".format[data-v-5f6e8f5e]{position:relative}.label[data-v-5f6e8f5e]{align-items:center;background:#e7e8e9;color:#999;display:flex;float:left;font-weight:500;height:30px;justify-content:center;position:relative;width:60px}[pick-colors-theme=dark] .label[data-v-5f6e8f5e]{background:#252930;color:#999}.arrow[data-v-5f6e8f5e]{height:6px;margin-bottom:4px;margin-left:5px;transform:rotate(135deg);width:6px}.arrow[data-v-5f6e8f5e],[pick-colors-theme=dark] .arrow[data-v-5f6e8f5e]{border-right:1px solid #999;border-top:1px solid #999}.selector[data-v-5f6e8f5e]{align-items:center;background:#f7f8f9;border-radius:5px;box-shadow:0 0 16px 0 rgba(0,0,0,.16);cursor:pointer;display:flex;flex-direction:column;font-weight:400;justify-content:center;padding:4px}[pick-colors-theme=dark] .selector[data-v-5f6e8f5e]{background:#252930;color:#999}.selector-item[data-v-5f6e8f5e]{align-items:center;display:flex;height:30px;justify-content:center;width:60px}.selector-item[data-v-5f6e8f5e]:hover{background:#e1f2fe}[pick-colors-theme=dark] .selector-item[data-v-5f6e8f5e]{color:#fff}[pick-colors-theme=dark] .selector-item[data-v-5f6e8f5e]:hover{background:#0087fa}.active-selector-item[data-v-5f6e8f5e]{background:#e1f2fe}[pick-colors-theme=dark] .active-selector-item[data-v-5f6e8f5e]{background:#0087fa}.v-enter-active[data-v-5f6e8f5e],.v-leave-active[data-v-5f6e8f5e]{opacity:1;transform:scaleY(1);transform-origin:center top;transition:opacity .2s ease-in-out,transform .2s ease-in-out}.v-enter-from[data-v-5f6e8f5e],.v-leave-to[data-v-5f6e8f5e]{opacity:0;transform:scaleY(0)}\"),te.render=function(e,t,a,c,l,u){return r(),n(\"div\",re,[i(\"div\",{class:\"label\",ref:\"targetRef\",onClick:t[0]||(t[0]=function(){return e.onShow&&e.onShow.apply(e,arguments)})},[i(\"span\",null,v(e.value),1),e.isNeedSelect?(r(),n(\"div\",ne)):h(\"v-if\",!0)],512),m(g,null,{default:y((function(){return[e.isShowSelector?(r(),n(\"div\",{key:0,class:\"selector\",ref:\"selectorRef\",style:o(e.selectorStyle)},[(r(!0),n(b,null,x(e.formatOptions,(function(t){return r(),n(\"div\",{class:\"selector-item\",key:t,onClick:function(r){return e.onFormatChange(t)}},v(t),9,oe)})),128))],4)):h(\"v-if\",!0)]})),_:1})])},te.__scopeId=\"data-v-5f6e8f5e\",te.__file=\"src/picker/input-value/FormatValue.vue\";var ae=e({name:\"Input\",components:{FormatValue:te},props:{format:{type:String,default:\"RGBA\"},value:{type:String,default:\"\"},width:{type:Number},showAlpha:{type:Boolean},formatOptions:{type:[Boolean,Array]}},emits:[\"change\",\"focus\",\"blur\",\"enter\",\"formatChange\"],setup:function(e,r){var n=r.emit;return{onInput:function(e){var t;n(\"change\",null===(t=e.target)||void 0===t?void 0:t.value)},valueStyle:t((function(){return{minWidth:\"\".concat(e.width,\"px\"),maxWidth:\"\".concat(e.width,\"px\"),width:\"\".concat(e.width,\"px\")}})),onFocus:function(){n(\"focus\")},onBlur:function(){n(\"blur\")},onEnter:function(){n(\"enter\")},onFormatChange:function(e){n(\"formatChange\",e)}}}}),ie={class:\"input\"},ce=[\".value\"];function le(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ue(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?le(Object(r),!0).forEach((function(t){H(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):le(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}V(\".input[data-v-2c454c00]{display:flex;font-size:12px}.value[data-v-2c454c00]{background:#eceef0;border:1px solid #eceef0;box-sizing:border-box;color:#666;flex:1;height:30px;text-align:center}[pick-colors-theme=dark] .value[data-v-2c454c00]{background:#2e333a;border:1px solid #2e333a;color:#fff}.value[data-v-2c454c00]:focus{border:1px solid #1890ff;outline:none}\"),ae.render=function(e,t,a,c,l,u){var s=w(\"FormatValue\");return r(),n(\"div\",ie,[m(s,{value:e.format,showAlpha:e.showAlpha,options:e.formatOptions,onChange:e.onFormatChange},null,8,[\"value\",\"showAlpha\",\"options\",\"onChange\"]),i(\"input\",{class:\"value\",style:o(e.valueStyle),\".value\":e.value,onFocus:t[0]||(t[0]=function(){return e.onFocus&&e.onFocus.apply(e,arguments)}),onInput:t[1]||(t[1]=function(){return e.onInput&&e.onInput.apply(e,arguments)}),onBlur:t[2]||(t[2]=function(){return e.onBlur&&e.onBlur.apply(e,arguments)}),onKeydown:t[3]||(t[3]=S((function(){return e.onEnter&&e.onEnter.apply(e,arguments)}),[\"enter\"]))},null,44,ce)])},ae.__scopeId=\"data-v-2c454c00\",ae.__file=\"src/picker/input-value/InputValue.vue\";var se=function(e,t,r){return[e,t*r/((e=(2-t)*r)<1?e:2-e)||0,e/2]},fe={10:\"A\",11:\"B\",12:\"C\",13:\"D\",14:\"E\",15:\"F\"},de=function(e){e=Math.min(Math.round(e),255);var t=Math.floor(e/16),r=e%16;return\"\".concat(fe[t]||t).concat(fe[r]||r)},pe=function(e){var t=e.r,r=e.g,n=e.b;return isNaN(t)||isNaN(r)||isNaN(n)?\"\":\"#\".concat(de(t)).concat(de(r)).concat(de(n))},ve=function(e,t){var r;\"string\"==typeof(r=e)&&-1!==r.indexOf(\".\")&&1===parseFloat(r)&&(e=\"100%\");var n=function(e){return\"string\"==typeof e&&-1!==e.indexOf(\"%\")}(e);return e=Math.min(t,Math.max(0,parseFloat(\"\".concat(e)))),n&&(e=parseInt(\"\".concat(e*t),10)/100),Math.abs(e-t)<1e-6?1:e%t/parseFloat(t)},he=function(e,t,r){e=6*ve(e,360),t=ve(t,100),r=ve(r,100);var n=Math.floor(e),o=e-n,a=r*(1-t),i=r*(1-o*t),c=r*(1-(1-o)*t),l=n%6,u=[r,i,a,a,c,r][l],s=[c,r,r,i,a,a][l],f=[a,a,c,r,r,i][l];return{r:Math.round(255*u),g:Math.round(255*s),b:Math.round(255*f)}},me=function(e,t,r){var n,o,a=e.h,i=e.s,c=e.v,l=e.a;if(r)switch([\"hsl\",\"hsv\",\"rga\"].includes(t)&&(l=(o=(n=l).toString().match(/\\.(\\d{1,2})(\\d*)/))&&o[2].length>0?parseFloat(n.toFixed(2)):n),t){case\"hsl\":var u=se(a,i/100,c/100);return\"hsla(\".concat(a.toFixed(0),\", \").concat(Math.round(100*u[1]),\"%, \").concat(Math.round(100*u[2]),\"%, \").concat(l,\")\");case\"hsv\":return\"hsva(\".concat(a.toFixed(0),\", \").concat(Math.round(i),\"%, \").concat(Math.round(c),\"%, \").concat(l,\")\");case\"rgb\":var s=he(a,i,c),f=s.r,d=s.g,p=s.b;return\"rgba(\".concat(f,\", \").concat(d,\", \").concat(p,\", \").concat(l,\")\");default:return\"\".concat(pe(he(a,i,c))).concat(de(255*l))}else switch(t){case\"hsl\":var v=se(a,i/100,c/100);return\"hsl(\".concat(a.toFixed(0),\", \").concat(Math.round(100*v[1]),\"%, \").concat(Math.round(100*v[2]),\"%)\");case\"hsv\":return\"hsv(\".concat(a.toFixed(0),\", \").concat(Math.round(i),\"%, \").concat(Math.round(c),\"%)\");case\"rgb\":var h=he(a,i,c),m=h.r,g=h.g,y=h.b;return\"rgb(\".concat(m,\", \").concat(g,\", \").concat(y,\")\");default:return pe(he(a,i,c))}},ge=function(e){var t=e.r,r=e.g,n=e.b;t=ve(t,255),r=ve(r,255),n=ve(n,255);var o,a=Math.max(t,r,n),i=Math.min(t,r,n),c=a,l=a-i,u=0===a?0:l/a;if(a===i)o=0;else{switch(a){case t:o=(r-n)/l+(r<n?6:0);break;case r:o=(n-t)/l+2;break;case n:o=(t-r)/l+4}o/=6}return{h:360*o,s:100*u,v:100*c}},ye=function(e){var t=e.h,r=e.s,n=e.l;n/=100;var o=r/=100,a=Math.max(n,.01);return r*=(n*=2)<=1?n:2-n,o*=a<=1?a:2-a,{h:+t,s:100*(0===n?2*o/(a+o):2*r/(n+r)),v:100*((n+r)/2)}},be=function(e){var t=[];if(e.match(/^#([0-9a-fA-f]{3,4})$/g))for(var r=1;r<e.length;r++)t.push(parseInt(\"0x\"+e[r].repeat(2)));else if(e.match(/^#([0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/g))for(var n=1;n<e.length;n+=2)t.push(parseInt(\"0x\"+e.slice(n,n+2)));return{r:t[0],g:t[1],b:t[2],a:t[3]}},xe=function(e,t,r){if(\"string\"==typeof e&&\"\"!==e){var n=Ce(e,Oe(e),r),o=Ee(n);return null==o?\"\":me(o,t,r)}return\"\"},we=function(e){var t=T(e.match(/(\\d(\\.\\d+)?)+/g),4);return{r:t[0],g:t[1],b:t[2],a:t[3]}},Se=function(e){var t=T(e.match(/(\\d(\\.\\d+)?)+/g),4),r=t[0],n=t[1],o=t[2],a=t[3];return{h:r,s:parseFloat(n),l:parseFloat(o),a:a}},ke=function(e){var t=T(e.match(/(\\d(\\.\\d+)?)+/g),4),r=t[0],n=t[1],o=t[2],a=t[3];return{h:parseFloat(r),s:parseFloat(n),v:parseFloat(o),a:parseFloat(a)}},Ce=function(e,t){if(!(arguments.length>2&&void 0!==arguments[2])||arguments[2])switch(t){case\"rgb\":var r=we(e),n=r.r,o=r.g,a=r.b,i=r.a;return ue(ue({},ge({r:n,g:o,b:a})),{},{a:+i});case\"hsv\":var c=ke(e);return{h:c.h,s:c.s,v:c.v,a:c.a};case\"hsl\":var l=Se(e),u=l.h,s=l.s,f=l.l,d=l.a;return ue(ue({},ye({h:u,s:s,l:f})),{},{a:+d});default:var p=be(e),v=p.r,h=p.g,m=p.b,g=p.a;return ue(ue({},ge({r:v,g:h,b:m})),{},{a:g/255})}else{switch(t){case\"rgb\":return ue(ue({},ge(we(e))),{},{a:1});case\"hsv\":var y=ke(e);return{h:y.h,s:y.s,v:y.v,a:1};case\"hsl\":return ue(ue({},ye(Se(e))),{},{a:1});default:return ue(ue({},ge(be(e))),{},{a:1})}}},Oe=function(e){return e.match(/^#/)?\"hex\":e.match(/^rgb/)?\"rgb\":e.match(/^hsl/)?\"hsl\":e.match(/^hsv/)?\"hsv\":\"hex\"},Ee=function(e){var t=e.h,r=e.s,n=e.v,o=e.a;return isNaN(t)&&isNaN(r)&&isNaN(n)?null:(isNaN(t)&&(t=0),isNaN(r)&&(r=0),isNaN(n)&&(n=0),isNaN(o)&&(o=1),{h:t,s:r,v:n,a:o})},Ae=e({name:\"ColorItem\",props:{size:{type:[Number,String],default:20},width:{type:[Number,String]},height:{type:[Number,String]},value:{type:String,default:\"\"},border:{type:Boolean,default:!0},borderRadius:{type:Number,default:5},selected:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1}},emits:[\"select\"],setup:function(e){var r=c(),n=k(\"theme\",{theme:\"light\"}).theme,o=t((function(){return parseFloat((e.width||e.size)+\"\")})),a=t((function(){return parseFloat((e.height||e.size)+\"\")})),i=t((function(){return{width:\"\".concat(s(o),\"px\"),height:\"\".concat(s(a),\"px\"),border:e.border?\"1px solid \".concat(\"dark\"===s(n)?\"#434345\":\"#d9d9d9\"):\"\",borderRadius:\"\".concat(e.borderRadius,\"px\"),boxShadow:e.selected?\"0 0 3px 2px \".concat(\"dark\"===s(n)?\"#2681ff\":\"#1890ff\"):\"\"}})),f=function(){var t=r.value.getContext(\"2d\");r.value.width=s(o),r.value.height=s(a);var n=function(e){var t=document.createElement(\"canvas\"),r=t.getContext(\"2d\"),n=2*e;return t.width=n,t.height=n,r.fillStyle=\"#ffffff\",r.fillRect(0,0,n,n),r.fillStyle=\"#ccd5db\",r.fillRect(0,0,e,e),r.fillRect(e,e,e,e),t}(5);t.fillStyle=t.createPattern(n,\"repeat\"),t.fillRect(0,0,s(o),s(a)),t.fillStyle=e.value,t.fillRect(0,0,s(o),s(a))};return u((function(){return e.value}),(function(){f()})),l((function(){f()})),{canvas:r,colorItemStyle:i}}}),Ne=[\"draggable\"];V(\".color-item[data-v-02da71fd]{display:inline-block;vertical-align:top}\"),Ae.render=function(e,t,a,i,c,l){return r(),n(\"canvas\",{class:\"color-item\",style:o(e.colorItemStyle),ref:\"canvas\",draggable:e.draggable},null,12,Ne)},Ae.__scopeId=\"data-v-02da71fd\",Ae.__file=\"src/color-item/ColorItem.vue\";var Ie=e({name:\"Colors\",components:{ColorItem:Ae},props:{colors:{type:Array,default:function(){return[]}},selectedIndex:{type:Number,default:-1}},emits:[\"change\"],setup:function(e,r){var n=r.emit;return{onSelectColor:function(e,t){n(\"change\",e,t)},useColors:t((function(){return e.colors.map((function(e){return xe(e,\"hex\",!0)}))}))}}}),Fe={class:\"colors\"};function _e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Le(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(r),!0).forEach((function(t){H(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_e(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}V(\".colors[data-v-0f8b52a8]{align-items:center;display:flex;flex-wrap:wrap;margin-top:5px}.color-item[data-v-0f8b52a8]{margin:5px}\"),Ie.render=function(e,t,o,i,c,l){var u=w(\"color-item\");return r(),n(\"div\",Fe,[(r(!0),n(b,null,x(e.useColors,(function(t,n){return r(),C(u,{key:n,class:\"color-item\",size:16,value:t,border:!1,\"border-radius\":3,selected:e.selectedIndex===n,onClick:a((function(r){return e.onSelectColor(t,+n)}),[\"stop\",\"prevent\"])},null,8,[\"value\",\"selected\",\"onClick\"])})),128))])},Ie.__scopeId=\"data-v-0f8b52a8\",Ie.__file=\"src/picker/Colors.vue\";var Pe=e({name:\"Picker\",components:{Colors:Ie,Saturation:G,Hue:Y,Alpha:X,InputValue:ae},props:{format:{type:String,default:\"hex\"},showAlpha:{type:Boolean,default:!1},value:{type:String,default:\"\"},colors:{type:Array,default:function(){return[]}},style:{type:Object,default:function(){return{}}},formatOptions:{type:[Boolean,Array]}},emits:[\"change\",\"formatChange\"],setup:function(e,r){var n=r.emit,o=t((function(){return Le(Le({},e.style),{},{width:e.showAlpha?\"230px\":\"205px\"})})),a=c(),i=c(),l=c(),f=function(){var t,r=null===(t=e.value)||void 0===t?void 0:t.trim();if(null!=r&&\"\"!==r){var n=Oe(r);return Ee(Ce(r,n,e.showAlpha))}return null};u((function(){return e.value}),(function(){e.value!==s(l)&&(a.value=f())}),{immediate:!0}),u((function(){return[s(a),e.format]}),(function(t){var r=T(t,2),o=r[0],a=r[1],c=\"\";null!=o?(c=me(Le({},o),e.format,e.showAlpha),i.value=Le({},o)):i.value=null,l.value=c;var u=f();JSON.stringify(o)!==JSON.stringify(u)&&JSON.stringify(o)!==JSON.stringify(a)&&n(\"change\",c)}),{immediate:!0});var d=t((function(){var e;return(null===(e=s(a))||void 0===e?void 0:e.h)||0})),p=t((function(){var e;return(null===(e=s(a))||void 0===e?void 0:e.s)||0})),v=t((function(){var e;return(null===(e=s(a))||void 0===e?void 0:e.v)||0})),h=t((function(){var e,t;return null!=(null===(e=s(a))||void 0===e?void 0:e.a)?null===(t=s(a))||void 0===t?void 0:t.a:1})),m=t((function(){return he(s(d),s(p),s(v))})),g=t((function(){return\"rgb(\".concat(s(m).r,\", \").concat(s(m).g,\", \").concat(s(m).b,\")\")})),y=c(-1);return{h:d,s:p,v:v,a:h,rgbStr:g,onSelectSaturation:function(e,t){y.value=-1,null==s(a)?a.value={h:s(d),s:e,v:t,a:s(h)}:a.value=Le(Le({},s(a)),{},{s:e,v:t})},onSelectHue:function(e){y.value=-1,null==s(a)&&(a.value={h:e,s:s(p),v:s(v),a:s(h)}),a.value=Le(Le({},s(a)),{},{h:e})},onSelectAlpha:function(e){y.value=-1,null==s(a)?a.value={h:s(d),s:s(p),v:s(v),a:e}:a.value=Le(Le({},s(a)),{},{a:e})},handleInputFormat:function(t){return e.showAlpha?q[t]:U[t]},colorValue:l,pickerStyle:o,onInputChange:function(e){y.value=-1,l.value=e},handleChange:function(){var t,r=null===(t=s(l))||void 0===t?void 0:t.trim();if(\"\"!==r){var n=e.showAlpha,o=Oe(r),c=Ee(Ce(r,o,n)),u=function(e){if(!e)return!1;var t=e.h,r=e.s,n=e.v,o=e.a;return!(isNaN(t)||isNaN(r)||isNaN(n)||isNaN(o))}(c);u?a.value=c:null!=s(i)?a.value=s(i):l.value=\"\"}else a.value=null},selectColorIndex:y,onSelectColor:function(e,t){if(y.value=t,\"\"===(e=e.trim()))a.value=null;else{var r=Oe(e);r?function(e,t,r){if(0!==e.length){var n=Ce(e,t,r),o=n.h,i=n.s,c=n.v;if(!(isNaN(o)||isNaN(i)||isNaN(c))){var l=1,u=n.a;isNaN(u)||(l=u),a.value={h:o,s:i,v:c,a:l}}}}(e,r,!0):a.value=null}},onFormatChange:function(e){n(\"formatChange\",e)}}}}),je={class:\"picker-inner\"},Me={class:\"picker-header\"};V(\".picker[data-v-6ceadec6]{background:#f7f8f9;border-radius:4px;box-shadow:0 0 16px 0 rgba(0,0,0,.16)}.picker-inner[data-v-6ceadec6]{padding:10px}[pick-colors-theme=dark] .picker-inner[data-v-6ceadec6]{background:#1d2024;box-shadow:0 0 16px 0 rgba(0,0,0,.16)}.picker-header[data-v-6ceadec6]{display:flex;margin-bottom:5px}.alpha[data-v-6ceadec6],.hue[data-v-6ceadec6]{margin-left:10px}.colors[data-v-6ceadec6]{margin-top:5px}\"),Pe.render=function(e,t,a,c,l,u){var s=w(\"saturation\"),f=w(\"hue\"),d=w(\"alpha\"),p=w(\"input-value\"),v=w(\"Colors\");return r(),n(\"div\",{class:\"picker\",style:o(e.pickerStyle)},[i(\"div\",je,[i(\"div\",Me,[m(s,{class:\"saturation\",hue:e.h,saturation:e.s,value:e.v,onChange:e.onSelectSaturation},null,8,[\"hue\",\"saturation\",\"value\",\"onChange\"]),m(f,{class:\"hue\",hue:e.h,onChange:e.onSelectHue},null,8,[\"hue\",\"onChange\"]),e.showAlpha?(r(),C(d,{key:0,class:\"alpha\",alpha:e.a,color:e.rgbStr,onChange:e.onSelectAlpha},null,8,[\"alpha\",\"color\",\"onChange\"])):h(\"v-if\",!0)]),m(p,{format:e.handleInputFormat(e.format),value:e.colorValue,showAlpha:e.showAlpha,width:e.showAlpha?150:125,formatOptions:e.formatOptions,onChange:e.onInputChange,onBlur:e.handleChange,onEnter:e.handleChange,onFormatChange:e.onFormatChange},null,8,[\"format\",\"value\",\"showAlpha\",\"width\",\"formatOptions\",\"onChange\",\"onBlur\",\"onEnter\",\"onFormatChange\"]),e.colors.length>0?(r(),C(v,{key:0,class:\"colors\",colors:e.colors,\"selected-index\":e.selectColorIndex,onChange:e.onSelectColor},null,8,[\"colors\",\"selected-index\",\"onChange\"])):h(\"v-if\",!0)])],4)},Pe.__scopeId=\"data-v-6ceadec6\",Pe.__file=\"src/picker/Picker.vue\";var Be=e({name:\"AddColorItem\",props:{size:{type:Number,default:20},selected:{type:Boolean,default:!1}},setup:function(e){var r=k(\"theme\",{theme:\"light\"}).theme;return{addColorItemStyle:t((function(){return{width:\"\".concat(e.size,\"px\"),height:\"\".concat(e.size,\"px\"),lineHeight:\"\".concat(e.size,\"px\"),boxShadow:e.selected?\"0 0 3px 2px \".concat(\"dark\"===s(r)?\"#2681ff\":\"#1890ff\"):\"\"}}))}}});V(\".add-color-item[data-v-ceb1719c]{background:#fff;border:1px solid #d9d9d9;border-radius:5px;vertical-align:top}.container[data-v-ceb1719c]{pointer-events:none;transform:scale(.9);transform-origin:center}[pick-colors-theme=dark] .add-color-item[data-v-ceb1719c]{background:#141414;border:1px solid #434343}path[data-v-ceb1719c]{fill:#000}[pick-colors-theme=dark] path[data-v-ceb1719c]{fill:#fff}\"),Be.render=function(e,t,a,c,l,u){return r(),n(\"svg\",{class:\"add-color-item\",style:o(e.addColorItemStyle),viewBox:\"0 0 1024 1024\",xmlns:\"http://www.w3.org/2000/svg\"},t[0]||(t[0]=[i(\"g\",{class:\"container\"},[i(\"path\",{d:\"M544 464V160h-80v304H160v80h304v304h80V544h304v-80z\"})],-1)]),4)},Be.__scopeId=\"data-v-ceb1719c\",Be.__file=\"src/add-color-item/AddColorItem.vue\";var Re=e({name:\"ColorPicker\",components:{ColorItem:Ae,Picker:Pe,AddColorItem:Be},props:{value:{type:[String,Array]},theme:{type:String,default:\"light\"},size:{type:[Number,String],default:20},width:{type:[Number,String]},height:{type:[Number,String]},format:{type:String},showPicker:{type:Boolean,default:void 0},showAlpha:{type:Boolean,default:!1},addColor:{type:Boolean,default:!1},deleteColor:{type:Boolean,default:!0},max:{type:Number,default:13},popupContainer:{type:[String,Object,Boolean],default:\"body\"},zIndex:{type:Number,default:1e3},colors:{type:Array,default:function(){return[\"#ff4500\",\"#ff8c00\",\"#ffd700\",\"#90ee90\",\"#00ced1\",\"#1e90ff\",\"#c71585\",\"#ff4500\",\"#ff7d4d\",\"#00babd\",\"#1f93ff\",\"#fa64c3\"]}},position:{type:String},placement:{type:String},formatOptions:{type:[Boolean,Array],default:!1}},emits:[\"change\",\"update:value\",\"update:showPicker\",\"overflowMax\",\"closePicker\",\"formatChange\"],setup:function(e,r){var n=r.emit,o=c([]),a=t((function(){return s(o).map((function(t){return xe(t,\"hex\",e.showAlpha)}))})),i=c(\"hex\");u((function(){return e.format}),(function(){i.value=e.format}),{immediate:!0});u((function(){return e.value}),(function(){var t=e.value||\"\",r=Array.isArray(t)?t:[t];o.value=r.map((function(t){return xe(t,s(i),e.showAlpha)}))}),{immediate:!0});var d=c(void 0),v=t((function(){return s(o)[s(d)]})),h=c(!1);u((function(){return e.showPicker}),(function(){h.value=e.showPicker}),{immediate:!0});var m,g,y=c(null),b=c(null),x=ee(y,b,{defaultStyle:{zIndex:e.zIndex},strategy:e.position,placement:e.placement}).style,w=function(){null==s(y)&&(y.value=s(B)[0]),null==s(d)&&(d.value=0),void 0===e.showPicker?h.value=!0:n(\"update:showPicker\",!0)},S=function(){d.value=void 0,void 0===e.showPicker?h.value=!1:n(\"update:showPicker\",!1),n(\"closePicker\",E(Array.isArray(e.value)||e.addColor?s(o):s(o)[0]))},k=c(),C=function(){var e=j(z.mark((function e(t){var r,n,o,a;return z.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.target,null!=(o=null===(r=n.dataset)||void 0===r?void 0:r.index)&&\"\"!==o){e.next=4;break}return e.abrupt(\"return\");case 4:if(a=+o,s(d)!==a){e.next=7;break}return e.abrupt(\"return\");case 7:null!=s(d)&&s(d)!==a?(S(),m&&clearTimeout(m),m=setTimeout((function(){w(),clearTimeout(m)}),100)):w(),d.value=a,y.value=n;case 10:case\"end\":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),A=function(){var e=j(z.mark((function e(t){var r,n,o,a,i;return z.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t.target,!(!(null!==(r=s(k))&&void 0!==r&&r.isEqualNode(a))&&(null===(n=s(k))||void 0===n?void 0:n.contains(a)))){e.next=4;break}return e.abrupt(\"return\");case 4:if(i=null===(o=s(b))||void 0===o?void 0:o.$el,!((null==i?void 0:i.contains(a))||!1)){e.next=8;break}return e.abrupt(\"return\");case 8:g&&clearTimeout(g),g=setTimeout((function(){s(h)&&S()}),0);case 10:case\"end\":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();u(h,(function(){s(h)&&(w(),clearTimeout(g))}));var N,I=c(e.max>s(o).length),F=t((function(){return\"string\"==typeof e.popupContainer||\"object\"===L(e.popupContainer)&&null!=e.popupContainer?e.popupContainer:\"body\"})),P=t((function(){return\"boolean\"==typeof e.popupContainer&&!1===e.popupContainer})),M=t((function(){return e.theme}));u((function(){return[e.theme,s(b)]}),(function(){f((function(){var e,t;null===(e=s(k))||void 0===e||e.setAttribute(\"pick-colors-theme\",s(M)),null===(t=s(b))||void 0===t||null===(t=t.$el)||void 0===t||t.setAttribute(\"pick-colors-theme\",s(M))}))}),{immediate:!0}),O(\"theme\",{theme:M});var B=c([]);return l((function(){document.addEventListener(\"mouseup\",A,!1),e.showPicker&&w()})),p((function(){document.removeEventListener(\"mouseup\",A,!1),m&&(clearTimeout(m),m=null),g&&(clearTimeout(g),g=null)})),{valueList:o,colorItemSelected:function(t){return(e.addColor?s(o).length>0:s(o).length>1)&&s(d)===t},selectedColor:v,selectedIndex:d,isShowPicker:h,addColorItemShow:I,onPickerChange:function(t){var r=s(d),a=s(o).slice(),i=s(o).length;if(null!=r){r>=0?a[r]=t:(d.value=i,a.push(t));var c=\"\";c=Array.isArray(e.value)||e.addColor?a:t,o.value=Array.isArray(c)?c:[c],n(\"update:value\",c),n(\"change\",c,t,r),e.addColor&&i>=e.max&&(I.value=!1,n(\"overflowMax\"))}},colorPicker:k,onColorClick:C,pickerRef:b,onColorItemDragStart:function(e){e.dataTransfer.effectAllowed=\"move\";var t=e.target;N=+t.dataset.index},onColorItemDragOver:function(e){},onColorItemDrop:function(e){var t=+e.target.dataset.index,r=_(s(o)),a=r[N];r.splice(N,1);var i=r.slice(0,t),c=r.splice(t),l=i.concat([a]).concat(c);n(\"update:value\",l),n(\"change\",l,l[N],N)},colorItemsRef:B,pickerStyle:x,values:a,teleportDisabled:P,toPopupContainer:F,formatValue:i,onFormatChange:function(e){i.value=e,n(\"formatChange\",e)}}}});V(\".color-picker[data-v-3c43ade8]{display:inline-block}.color-item[data-v-3c43ade8]{margin:5px}.add-color-item[data-v-3c43ade8]{display:inline-block;margin:5px}.picker[data-v-3c43ade8]{overflow:hidden}.v-enter-active[data-v-3c43ade8],.v-leave-active[data-v-3c43ade8]{opacity:1;transform:scaleY(1);transform-origin:center top;transition:opacity .2s ease-in-out,transform .2s ease-in-out}.v-enter-from[data-v-3c43ade8],.v-leave-to[data-v-3c43ade8]{opacity:0;transform:scaleY(0)}\"),Re.render=function(e,t,i,c,l,u){var s=w(\"color-item\"),f=w(\"add-color-item\"),d=w(\"picker\");return r(),n(\"div\",{class:\"color-picker\",ref:\"colorPicker\",onDragstart:t[0]||(t[0]=a((function(){return e.onColorItemDragStart&&e.onColorItemDragStart.apply(e,arguments)}),[\"stop\"])),onDragover:t[1]||(t[1]=a((function(){return e.onColorItemDragOver&&e.onColorItemDragOver.apply(e,arguments)}),[\"prevent\",\"stop\"])),onDrop:t[2]||(t[2]=a((function(){return e.onColorItemDrop&&e.onColorItemDrop.apply(e,arguments)}),[\"prevent\",\"stop\"])),onClick:t[3]||(t[3]=a((function(){return e.onColorClick&&e.onColorClick.apply(e,arguments)}),[\"stop\"]))},[(r(!0),n(b,null,x(e.values,(function(t,n){return r(),C(s,{class:\"color-item\",key:n,ref_for:!0,ref:function(t){return e.colorItemsRef[n]=t},size:e.size,width:e.width,height:e.height,value:t,selected:e.colorItemSelected(n),\"data-index\":n,draggable:e.valueList.length>1,format:e.formatValue},null,8,[\"size\",\"width\",\"height\",\"value\",\"selected\",\"data-index\",\"draggable\",\"format\"])})),128)),e.addColor&&e.addColorItemShow?(r(),C(f,{key:0,class:\"add-color-item\",ref:\"addColorItem\",selected:e.colorItemSelected(-1),\"data-index\":-1},null,8,[\"selected\"])):h(\"v-if\",!0),(r(),C(A,{to:e.toPopupContainer,disabled:e.teleportDisabled},[m(g,null,{default:y((function(){return[e.isShowPicker?(r(),C(d,{key:0,class:\"picker\",style:o(e.pickerStyle),ref:\"pickerRef\",value:e.selectedColor,format:e.formatValue,\"show-alpha\":e.showAlpha,colors:e.colors,formatOptions:e.formatOptions,onChange:e.onPickerChange,onFormatChange:e.onFormatChange},null,8,[\"style\",\"value\",\"format\",\"show-alpha\",\"colors\",\"formatOptions\",\"onChange\",\"onFormatChange\"])):h(\"v-if\",!0)]})),_:1})],8,[\"to\",\"disabled\"]))],544)},Re.__scopeId=\"data-v-3c43ade8\",Re.__file=\"src/ColorPicker.vue\";export{q as ALPHA_FORMAT_MAP,U as FORMAT_MAP,W as FORMAT_VALUE_MAP,Re as default};\n", "export * from \"./enums.js\";\nexport * from \"./modifiers/index.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { popperGenerator, detectOverflow, createPopper as createPopperBase } from \"./createPopper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper } from \"./popper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\";", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export { default as applyStyles } from \"./applyStyles.js\";\nexport { default as arrow } from \"./arrow.js\";\nexport { default as computeStyles } from \"./computeStyles.js\";\nexport { default as eventListeners } from \"./eventListeners.js\";\nexport { default as flip } from \"./flip.js\";\nexport { default as hide } from \"./hide.js\";\nexport { default as offset } from \"./offset.js\";\nexport { default as popperOffsets } from \"./popperOffsets.js\";\nexport { default as preventOverflow } from \"./preventOverflow.js\";", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAO,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB,CAAC,KAAK,QAAQ,OAAO,IAAI;AAC9C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAmC,eAAe,OAAO,SAAU,KAAK,WAAW;AAC5F,SAAO,IAAI,OAAO,CAAC,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AACpE,GAAG,CAAC,CAAC;AACE,IAAI,aAA0B,CAAC,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,SAAU,KAAK,WAAW;AACtG,SAAO,IAAI,OAAO,CAAC,WAAW,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AAC/E,GAAG,CAAC,CAAC;AAEE,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB,CAAC,YAAY,MAAM,WAAW,YAAY,MAAM,WAAW,aAAa,OAAO,UAAU;;;AC9BrH,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAe,SAAR,YAA6B,SAAS;AAC3C,SAAO,WAAW,QAAQ,YAAY,IAAI,YAAY,IAAI;AAC5D;;;ACFA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAe,SAAR,UAA2B,MAAM;AACtC,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,MAAM,mBAAmB;AACzC,QAAI,gBAAgB,KAAK;AACzB,WAAO,gBAAgB,cAAc,eAAe,SAAS;AAAA,EAC/D;AAEA,SAAO;AACT;;;ADTA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,MAAM;AAE1B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;;;AFhBA,SAAS,YAAY,MAAM;AACzB,MAAI,QAAQ,KAAK;AACjB,SAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,QAAI,QAAQ,MAAM,OAAO,IAAI,KAAK,CAAC;AACnC,QAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,QAAI,UAAU,MAAM,SAAS,IAAI;AAEjC,QAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,IACF;AAKA,WAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAUC,OAAM;AAC9C,UAAI,QAAQ,WAAWA,KAAI;AAE3B,UAAI,UAAU,OAAO;AACnB,gBAAQ,gBAAgBA,KAAI;AAAA,MAC9B,OAAO;AACL,gBAAQ,aAAaA,OAAM,UAAU,OAAO,KAAK,KAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM;AAClB,MAAI,gBAAgB;AAAA,IAClB,QAAQ;AAAA,MACN,UAAU,MAAM,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,WAAW,CAAC;AAAA,EACd;AACA,SAAO,OAAO,MAAM,SAAS,OAAO,OAAO,cAAc,MAAM;AAC/D,QAAM,SAAS;AAEf,MAAI,MAAM,SAAS,OAAO;AACxB,WAAO,OAAO,MAAM,SAAS,MAAM,OAAO,cAAc,KAAK;AAAA,EAC/D;AAEA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,UAAI,UAAU,MAAM,SAAS,IAAI;AACjC,UAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,UAAI,kBAAkB,OAAO,KAAK,MAAM,OAAO,eAAe,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,cAAc,IAAI,CAAC;AAE9G,UAAI,QAAQ,gBAAgB,OAAO,SAAUC,QAAO,UAAU;AAC5D,QAAAA,OAAM,QAAQ,IAAI;AAClB,eAAOA;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,UAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,MACF;AAEA,aAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,WAAW;AACnD,gBAAQ,gBAAgB,SAAS;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,IAAO,sBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,eAAe;AAC5B;;;AInFA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,iBAAkC,WAAW;AAClD,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACHA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAO,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,QAAQ,KAAK;;;ACFxB,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,cAA+B;AACpC,MAAI,SAAS,UAAU;AAEvB,MAAI,UAAU,QAAQ,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM,GAAG;AACnE,WAAO,OAAO,OAAO,IAAI,SAAU,MAAM;AACvC,aAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,IACjC,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAEA,SAAO,UAAU;AACnB;;;ADTe,SAAR,mBAAoC;AACzC,SAAO,CAAC,iCAAiC,KAAK,YAAY,CAAC;AAC7D;;;AFCe,SAAR,sBAAuC,SAAS,cAAc,iBAAiB;AACpF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AAEA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AAEA,MAAI,aAAa,QAAQ,sBAAsB;AAC/C,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,gBAAgB,cAAc,OAAO,GAAG;AAC1C,aAAS,QAAQ,cAAc,IAAI,MAAM,WAAW,KAAK,IAAI,QAAQ,eAAe,IAAI;AACxF,aAAS,QAAQ,eAAe,IAAI,MAAM,WAAW,MAAM,IAAI,QAAQ,gBAAgB,IAAI;AAAA,EAC7F;AAEA,MAAI,OAAO,UAAU,OAAO,IAAI,UAAU,OAAO,IAAI,QACjD,iBAAiB,KAAK;AAE1B,MAAI,mBAAmB,CAAC,iBAAiB,KAAK;AAC9C,MAAI,KAAK,WAAW,QAAQ,oBAAoB,iBAAiB,eAAe,aAAa,MAAM;AACnG,MAAI,KAAK,WAAW,OAAO,oBAAoB,iBAAiB,eAAe,YAAY,MAAM;AACjG,MAAI,QAAQ,WAAW,QAAQ;AAC/B,MAAI,SAAS,WAAW,SAAS;AACjC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;;;ADrCe,SAAR,cAA+B,SAAS;AAC7C,MAAI,aAAa,sBAAsB,OAAO;AAG9C,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAErB,MAAI,KAAK,IAAI,WAAW,QAAQ,KAAK,KAAK,GAAG;AAC3C,YAAQ,WAAW;AAAA,EACrB;AAEA,MAAI,KAAK,IAAI,WAAW,SAAS,MAAM,KAAK,GAAG;AAC7C,aAAS,WAAW;AAAA,EACtB;AAEA,SAAO;AAAA,IACL,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;;;AKxBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,MAAI,WAAW,MAAM,eAAe,MAAM,YAAY;AAEtD,MAAI,OAAO,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT,WACS,YAAY,aAAa,QAAQ,GAAG;AACzC,QAAI,OAAO;AAEX,OAAG;AACD,UAAI,QAAQ,OAAO,WAAW,IAAI,GAAG;AACnC,eAAO;AAAA,MACT;AAGA,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC,SAAS;AAAA,EACX;AAGF,SAAO;AACT;;;ACtBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,iBAAkC,SAAS;AAChD,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;;;ACHA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,eAAgC,SAAS;AAC9C,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,QAAQ,YAAY,OAAO,CAAC,KAAK;AAChE;;;ACHA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,mBAAoC,SAAS;AAElD,WAAS,UAAU,OAAO,IAAI,QAAQ;AAAA;AAAA,IACtC,QAAQ;AAAA,QAAa,OAAO,UAAU;AACxC;;;ADFe,SAAR,cAA+B,SAAS;AAC7C,MAAI,YAAY,OAAO,MAAM,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA;AAAA;AAAA;AAAA;AAAA,IAGE,QAAQ;AAAA,IACR,QAAQ;AAAA,KACR,aAAa,OAAO,IAAI,QAAQ,OAAO;AAAA;AAAA,IAEvC,mBAAmB,OAAO;AAAA;AAG9B;;;AHVA,SAAS,oBAAoB,SAAS;AACpC,MAAI,CAAC,cAAc,OAAO;AAAA,EAC1B,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ;AACjB;AAIA,SAAS,mBAAmB,SAAS;AACnC,MAAI,YAAY,WAAW,KAAK,YAAY,CAAC;AAC7C,MAAI,OAAO,WAAW,KAAK,YAAY,CAAC;AAExC,MAAI,QAAQ,cAAc,OAAO,GAAG;AAElC,QAAI,aAAa,iBAAiB,OAAO;AAEzC,QAAI,WAAW,aAAa,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,cAAc,cAAc,OAAO;AAEvC,MAAI,aAAa,WAAW,GAAG;AAC7B,kBAAc,YAAY;AAAA,EAC5B;AAEA,SAAO,cAAc,WAAW,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,YAAY,WAAW,CAAC,IAAI,GAAG;AAC3F,QAAI,MAAM,iBAAiB,WAAW;AAItC,QAAI,IAAI,cAAc,UAAU,IAAI,gBAAgB,UAAU,IAAI,YAAY,WAAW,CAAC,aAAa,aAAa,EAAE,QAAQ,IAAI,UAAU,MAAM,MAAM,aAAa,IAAI,eAAe,YAAY,aAAa,IAAI,UAAU,IAAI,WAAW,QAAQ;AACpP,aAAO;AAAA,IACT,OAAO;AACL,oBAAc,YAAY;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO;AACT;AAIe,SAAR,gBAAiC,SAAS;AAC/C,MAAIC,UAAS,UAAU,OAAO;AAC9B,MAAI,eAAe,oBAAoB,OAAO;AAE9C,SAAO,gBAAgB,eAAe,YAAY,KAAK,iBAAiB,YAAY,EAAE,aAAa,UAAU;AAC3G,mBAAe,oBAAoB,YAAY;AAAA,EACjD;AAEA,MAAI,iBAAiB,YAAY,YAAY,MAAM,UAAU,YAAY,YAAY,MAAM,UAAU,iBAAiB,YAAY,EAAE,aAAa,WAAW;AAC1J,WAAOA;AAAA,EACT;AAEA,SAAO,gBAAgB,mBAAmB,OAAO,KAAKA;AACxD;;;AKpEA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,yBAA0C,WAAW;AAC1D,SAAO,CAAC,OAAO,QAAQ,EAAE,QAAQ,SAAS,KAAK,IAAI,MAAM;AAC3D;;;ACFA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACO,SAAS,OAAOC,MAAK,OAAOC,MAAK;AACtC,SAAO,IAAQD,MAAK,IAAQ,OAAOC,IAAG,CAAC;AACzC;AACO,SAAS,eAAeD,MAAK,OAAOC,MAAK;AAC9C,MAAI,IAAI,OAAOD,MAAK,OAAOC,IAAG;AAC9B,SAAO,IAAIA,OAAMA,OAAM;AACzB;;;ACPA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,qBAAsC;AAC3C,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;;;ADNe,SAAR,mBAAoC,eAAe;AACxD,SAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG,aAAa;AAC9D;;;AEHA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,gBAAiC,OAAO,MAAM;AACnD,SAAO,KAAK,OAAO,SAAU,SAAS,KAAK;AACzC,YAAQ,GAAG,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;AjBKA,IAAI,kBAAkB,SAASC,iBAAgB,SAAS,OAAO;AAC7D,YAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IAC/E,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,SAAO,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AAC5G;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI;AAEJ,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK,MACZ,UAAU,KAAK;AACnB,MAAI,eAAe,MAAM,SAAS;AAClC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,OAAO,yBAAyB,aAAa;AACjD,MAAI,aAAa,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK;AACzD,MAAI,MAAM,aAAa,WAAW;AAElC,MAAI,CAAC,gBAAgB,CAACA,gBAAe;AACnC;AAAA,EACF;AAEA,MAAI,gBAAgB,gBAAgB,QAAQ,SAAS,KAAK;AAC1D,MAAI,YAAY,cAAc,YAAY;AAC1C,MAAI,UAAU,SAAS,MAAM,MAAM;AACnC,MAAI,UAAU,SAAS,MAAM,SAAS;AACtC,MAAI,UAAU,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM,UAAU,IAAI,IAAIA,eAAc,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG;AACrH,MAAI,YAAYA,eAAc,IAAI,IAAI,MAAM,MAAM,UAAU,IAAI;AAChE,MAAI,oBAAoB,gBAAgB,YAAY;AACpD,MAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,gBAAgB,IAAI,kBAAkB,eAAe,IAAI;AAC/H,MAAI,oBAAoB,UAAU,IAAI,YAAY;AAGlD,MAAIC,OAAM,cAAc,OAAO;AAC/B,MAAIC,OAAM,aAAa,UAAU,GAAG,IAAI,cAAc,OAAO;AAC7D,MAAI,SAAS,aAAa,IAAI,UAAU,GAAG,IAAI,IAAI;AACnD,MAAIC,UAAS,OAAOF,MAAK,QAAQC,IAAG;AAEpC,MAAI,WAAW;AACf,QAAM,cAAc,IAAI,KAAK,wBAAwB,CAAC,GAAG,sBAAsB,QAAQ,IAAIC,SAAQ,sBAAsB,eAAeA,UAAS,QAAQ;AAC3J;AAEA,SAASC,QAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,wBAAwB;AAEzE,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AAGA,MAAI,OAAO,iBAAiB,UAAU;AACpC,mBAAe,MAAM,SAAS,OAAO,cAAc,YAAY;AAE/D,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM,SAAS,QAAQ,YAAY,GAAG;AAClD;AAAA,EACF;AAEA,QAAM,SAAS,QAAQ;AACzB;AAGA,IAAO,gBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,QAAQA;AAAA,EACR,UAAU,CAAC,eAAe;AAAA,EAC1B,kBAAkB,CAAC,iBAAiB;AACtC;;;AkBzFA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,aAA8B,WAAW;AAC9C,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ADOA,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAIA,SAAS,kBAAkB,MAAM,KAAK;AACpC,MAAI,IAAI,KAAK,GACT,IAAI,KAAK;AACb,MAAI,MAAM,IAAI,oBAAoB;AAClC,SAAO;AAAA,IACL,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,IAC3B,GAAG,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,OAAO;AACjC,MAAI;AAEJ,MAAIC,UAAS,MAAM,QACf,aAAa,MAAM,YACnB,YAAY,MAAM,WAClB,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,kBAAkB,MAAM,iBACxB,WAAW,MAAM,UACjB,eAAe,MAAM,cACrB,UAAU,MAAM;AACpB,MAAI,aAAa,QAAQ,GACrB,IAAI,eAAe,SAAS,IAAI,YAChC,aAAa,QAAQ,GACrB,IAAI,eAAe,SAAS,IAAI;AAEpC,MAAI,QAAQ,OAAO,iBAAiB,aAAa,aAAa;AAAA,IAC5D;AAAA,IACA;AAAA,EACF,CAAC,IAAI;AAAA,IACH;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,UAAU;AACZ,QAAI,eAAe,gBAAgBA,OAAM;AACzC,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,iBAAiB,UAAUA,OAAM,GAAG;AACtC,qBAAe,mBAAmBA,OAAM;AAExC,UAAI,iBAAiB,YAAY,EAAE,aAAa,YAAY,aAAa,YAAY;AACnF,qBAAa;AACb,oBAAY;AAAA,MACd;AAAA,IACF;AAGA,mBAAe;AAEf,QAAI,cAAc,QAAQ,cAAc,QAAQ,cAAc,UAAU,cAAc,KAAK;AACzF,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,UAAU;AAAA;AACvB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAEA,QAAI,cAAc,SAAS,cAAc,OAAO,cAAc,WAAW,cAAc,KAAK;AAC1F,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,SAAS;AAAA;AACtB,WAAK,UAAU,WAAW;AAC1B,WAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,eAAe,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF,GAAG,YAAY,UAAU;AAEzB,MAAI,QAAQ,iBAAiB,OAAO,kBAAkB;AAAA,IACpD;AAAA,IACA;AAAA,EACF,GAAG,UAAUA,OAAM,CAAC,IAAI;AAAA,IACtB;AAAA,IACA;AAAA,EACF;AAEA,MAAI,MAAM;AACV,MAAI,MAAM;AAEV,MAAI,iBAAiB;AACnB,QAAI;AAEJ,WAAO,OAAO,OAAO,CAAC,GAAG,eAAe,iBAAiB,CAAC,GAAG,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,aAAa,IAAI,oBAAoB,MAAM,IAAI,eAAe,IAAI,SAAS,IAAI,QAAQ,iBAAiB,IAAI,SAAS,IAAI,UAAU,eAAe;AAAA,EAClT;AAEA,SAAO,OAAO,OAAO,CAAC,GAAG,eAAe,kBAAkB,CAAC,GAAG,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,KAAK,IAAI,OAAO,IAAI,OAAO,IAAI,gBAAgB,YAAY,IAAI,gBAAgB;AAC9M;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,wBAAwB,QAAQ,iBAChC,kBAAkB,0BAA0B,SAAS,OAAO,uBAC5D,oBAAoB,QAAQ,UAC5B,WAAW,sBAAsB,SAAS,OAAO,mBACjD,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,OAAO;AAC7D,MAAI,eAAe;AAAA,IACjB,WAAW,iBAAiB,MAAM,SAAS;AAAA,IAC3C,WAAW,aAAa,MAAM,SAAS;AAAA,IACvC,QAAQ,MAAM,SAAS;AAAA,IACvB,YAAY,MAAM,MAAM;AAAA,IACxB;AAAA,IACA,SAAS,MAAM,QAAQ,aAAa;AAAA,EACtC;AAEA,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,OAAO,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACvG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU,MAAM,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,MAAI,MAAM,cAAc,SAAS,MAAM;AACrC,UAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,OAAO,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACrG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,yBAAyB,MAAM;AAAA,EACjC,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;AExKA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAEA,IAAI,UAAU;AAAA,EACZ,SAAS;AACX;AAEA,SAASC,QAAO,MAAM;AACpB,MAAI,QAAQ,KAAK,OACb,WAAW,KAAK,UAChB,UAAU,KAAK;AACnB,MAAI,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO;AACjD,MAAIC,UAAS,UAAU,MAAM,SAAS,MAAM;AAC5C,MAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM,cAAc,WAAW,MAAM,cAAc,MAAM;AAEvF,MAAI,QAAQ;AACV,kBAAc,QAAQ,SAAU,cAAc;AAC5C,mBAAa,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAClE,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ;AACV,IAAAA,QAAO,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,EAC5D;AAEA,SAAO,WAAY;AACjB,QAAI,QAAQ;AACV,oBAAc,QAAQ,SAAU,cAAc;AAC5C,qBAAa,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,MACrE,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ;AACV,MAAAA,QAAO,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC/D;AAAA,EACF;AACF;AAGA,IAAO,yBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI,SAAS,KAAK;AAAA,EAAC;AAAA,EACnB,QAAQD;AAAA,EACR,MAAM,CAAC;AACT;;;AChDA,IAAAE,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACe,SAAR,qBAAsC,WAAW;AACtD,SAAO,UAAU,QAAQ,0BAA0B,SAAU,SAAS;AACpE,WAAO,KAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACVA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAIC,QAAO;AAAA,EACT,OAAO;AAAA,EACP,KAAK;AACP;AACe,SAAR,8BAA+C,WAAW;AAC/D,SAAO,UAAU,QAAQ,cAAc,SAAU,SAAS;AACxD,WAAOA,MAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACRA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,MAAM,UAAU,IAAI;AACxB,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;ADNe,SAAR,oBAAqC,SAAS;AAQnD,SAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO,gBAAgB,OAAO,EAAE;AAC5F;;;ADRe,SAAR,gBAAiC,SAAS,UAAU;AACzD,MAAI,MAAM,UAAU,OAAO;AAC3B,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,iBAAiB,IAAI;AACzB,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,QAAI,iBAAiB,iBAAiB;AAEtC,QAAI,kBAAkB,CAAC,kBAAkB,aAAa,SAAS;AAC7D,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG,IAAI,oBAAoB,OAAO;AAAA,IAClC;AAAA,EACF;AACF;;;AG9BA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAOe,SAAR,gBAAiC,SAAS;AAC/C,MAAI;AAEJ,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,YAAY,gBAAgB,OAAO;AACvC,MAAI,QAAQ,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACpG,MAAI,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,cAAc,CAAC;AAC5G,MAAI,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,OAAO,KAAK,eAAe,GAAG,OAAO,KAAK,eAAe,CAAC;AACjH,MAAI,IAAI,CAAC,UAAU,aAAa,oBAAoB,OAAO;AAC3D,MAAI,IAAI,CAAC,UAAU;AAEnB,MAAI,iBAAiB,QAAQ,IAAI,EAAE,cAAc,OAAO;AACtD,SAAK,IAAI,KAAK,aAAa,OAAO,KAAK,cAAc,CAAC,IAAI;AAAA,EAC5D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC5BA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACe,SAAR,eAAgC,SAAS;AAE9C,MAAI,oBAAoB,iBAAiB,OAAO,GAC5C,WAAW,kBAAkB,UAC7B,YAAY,kBAAkB,WAC9B,YAAY,kBAAkB;AAElC,SAAO,6BAA6B,KAAK,WAAW,YAAY,SAAS;AAC3E;;;ADLe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,CAAC,QAAQ,QAAQ,WAAW,EAAE,QAAQ,YAAY,IAAI,CAAC,KAAK,GAAG;AAEjE,WAAO,KAAK,cAAc;AAAA,EAC5B;AAEA,MAAI,cAAc,IAAI,KAAK,eAAe,IAAI,GAAG;AAC/C,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,cAAc,IAAI,CAAC;AAC5C;;;ADJe,SAAR,kBAAmC,SAAS,MAAM;AACvD,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,eAAe,gBAAgB,OAAO;AAC1C,MAAI,SAAS,mBAAmB,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACxH,MAAI,MAAM,UAAU,YAAY;AAChC,MAAI,SAAS,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,kBAAkB,CAAC,GAAG,eAAe,YAAY,IAAI,eAAe,CAAC,CAAC,IAAI;AACjH,MAAI,cAAc,KAAK,OAAO,MAAM;AACpC,SAAO,SAAS;AAAA;AAAA,IAChB,YAAY,OAAO,kBAAkB,cAAc,MAAM,CAAC,CAAC;AAAA;AAC7D;;;AGzBA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,iBAAkC,MAAM;AAC7C,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,IAC7B,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,OAAO,KAAK,IAAI,KAAK;AAAA,IACrB,QAAQ,KAAK,IAAI,KAAK;AAAA,EACxB,CAAC;AACH;;;ARQA,SAAS,2BAA2B,SAAS,UAAU;AACrD,MAAI,OAAO,sBAAsB,SAAS,OAAO,aAAa,OAAO;AACrE,OAAK,MAAM,KAAK,MAAM,QAAQ;AAC9B,OAAK,OAAO,KAAK,OAAO,QAAQ;AAChC,OAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,OAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,SAAO;AACT;AAEA,SAAS,2BAA2B,SAAS,gBAAgB,UAAU;AACrE,SAAO,mBAAmB,WAAW,iBAAiB,gBAAgB,SAAS,QAAQ,CAAC,IAAI,UAAU,cAAc,IAAI,2BAA2B,gBAAgB,QAAQ,IAAI,iBAAiB,gBAAgB,mBAAmB,OAAO,CAAC,CAAC;AAC9O;AAKA,SAAS,mBAAmB,SAAS;AACnC,MAAIC,mBAAkB,kBAAkB,cAAc,OAAO,CAAC;AAC9D,MAAI,oBAAoB,CAAC,YAAY,OAAO,EAAE,QAAQ,iBAAiB,OAAO,EAAE,QAAQ,KAAK;AAC7F,MAAI,iBAAiB,qBAAqB,cAAc,OAAO,IAAI,gBAAgB,OAAO,IAAI;AAE9F,MAAI,CAAC,UAAU,cAAc,GAAG;AAC9B,WAAO,CAAC;AAAA,EACV;AAGA,SAAOA,iBAAgB,OAAO,SAAU,gBAAgB;AACtD,WAAO,UAAU,cAAc,KAAK,SAAS,gBAAgB,cAAc,KAAK,YAAY,cAAc,MAAM;AAAA,EAClH,CAAC;AACH;AAIe,SAAR,gBAAiC,SAAS,UAAU,cAAc,UAAU;AACjF,MAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,OAAO,IAAI,CAAC,EAAE,OAAO,QAAQ;AAC3G,MAAIA,mBAAkB,CAAC,EAAE,OAAO,qBAAqB,CAAC,YAAY,CAAC;AACnE,MAAI,sBAAsBA,iBAAgB,CAAC;AAC3C,MAAI,eAAeA,iBAAgB,OAAO,SAAU,SAAS,gBAAgB;AAC3E,QAAI,OAAO,2BAA2B,SAAS,gBAAgB,QAAQ;AACvE,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,2BAA2B,SAAS,qBAAqB,QAAQ,CAAC;AACrE,eAAa,QAAQ,aAAa,QAAQ,aAAa;AACvD,eAAa,SAAS,aAAa,SAAS,aAAa;AACzD,eAAa,IAAI,aAAa;AAC9B,eAAa,IAAI,aAAa;AAC9B,SAAO;AACT;;;ASrEA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAIe,SAAR,eAAgC,MAAM;AAC3C,MAAIC,aAAY,KAAK,WACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACrB,MAAI,gBAAgB,YAAY,iBAAiB,SAAS,IAAI;AAC9D,MAAI,YAAY,YAAY,aAAa,SAAS,IAAI;AACtD,MAAI,UAAUA,WAAU,IAAIA,WAAU,QAAQ,IAAI,QAAQ,QAAQ;AAClE,MAAI,UAAUA,WAAU,IAAIA,WAAU,SAAS,IAAI,QAAQ,SAAS;AACpE,MAAI;AAEJ,UAAQ,eAAe;AAAA,IACrB,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAI,QAAQ;AAAA,MAC3B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAIA,WAAU;AAAA,MAC7B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAIA,WAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAI,QAAQ;AAAA,QACzB,GAAG;AAAA,MACL;AACA;AAAA,IAEF;AACE,gBAAU;AAAA,QACR,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,MACf;AAAA,EACJ;AAEA,MAAI,WAAW,gBAAgB,yBAAyB,aAAa,IAAI;AAEzE,MAAI,YAAY,MAAM;AACpB,QAAI,MAAM,aAAa,MAAM,WAAW;AAExC,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AV3De,SAAR,eAAgC,OAAO,SAAS;AACrD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,qBAAqB,SAAS,WAC9B,YAAY,uBAAuB,SAAS,MAAM,YAAY,oBAC9D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,MAAM,WAAW,mBAC3D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,kBAAkB,mBAC5D,wBAAwB,SAAS,cACjC,eAAe,0BAA0B,SAAS,WAAW,uBAC7D,wBAAwB,SAAS,gBACjC,iBAAiB,0BAA0B,SAAS,SAAS,uBAC7D,uBAAuB,SAAS,aAChC,cAAc,yBAAyB,SAAS,QAAQ,sBACxD,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,IAAI;AAChD,MAAI,gBAAgB,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AACvH,MAAI,aAAa,mBAAmB,SAAS,YAAY;AACzD,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,UAAU,MAAM,SAAS,cAAc,aAAa,cAAc;AACtE,MAAI,qBAAqB,gBAAgB,UAAU,OAAO,IAAI,UAAU,QAAQ,kBAAkB,mBAAmB,MAAM,SAAS,MAAM,GAAG,UAAU,cAAc,QAAQ;AAC7K,MAAI,sBAAsB,sBAAsB,MAAM,SAAS,SAAS;AACxE,MAAIC,iBAAgB,eAAe;AAAA,IACjC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,iBAAiB,OAAO,OAAO,CAAC,GAAG,YAAYA,cAAa,CAAC;AACpF,MAAI,oBAAoB,mBAAmB,SAAS,mBAAmB;AAGvE,MAAI,kBAAkB;AAAA,IACpB,KAAK,mBAAmB,MAAM,kBAAkB,MAAM,cAAc;AAAA,IACpE,QAAQ,kBAAkB,SAAS,mBAAmB,SAAS,cAAc;AAAA,IAC7E,MAAM,mBAAmB,OAAO,kBAAkB,OAAO,cAAc;AAAA,IACvE,OAAO,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,MAAM,cAAc;AAErC,MAAI,mBAAmB,UAAU,YAAY;AAC3C,QAAIC,UAAS,WAAW,SAAS;AACjC,WAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,KAAK;AAClD,UAAI,WAAW,CAAC,OAAO,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,IAAI;AACvD,UAAI,OAAO,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,MAAM;AACnD,sBAAgB,GAAG,KAAKA,QAAO,IAAI,IAAI;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AWhEA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAIe,SAAR,qBAAsC,OAAO,SAAS;AAC3D,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,YAAY,SAAS,WACrB,WAAW,SAAS,UACpB,eAAe,SAAS,cACxB,UAAU,SAAS,SACnB,iBAAiB,SAAS,gBAC1B,wBAAwB,SAAS,uBACjC,wBAAwB,0BAA0B,SAAS,aAAgB;AAC/E,MAAI,YAAY,aAAa,SAAS;AACtC,MAAIC,cAAa,YAAY,iBAAiB,sBAAsB,oBAAoB,OAAO,SAAUC,YAAW;AAClH,WAAO,aAAaA,UAAS,MAAM;AAAA,EACrC,CAAC,IAAI;AACL,MAAI,oBAAoBD,YAAW,OAAO,SAAUC,YAAW;AAC7D,WAAO,sBAAsB,QAAQA,UAAS,KAAK;AAAA,EACrD,CAAC;AAED,MAAI,kBAAkB,WAAW,GAAG;AAClC,wBAAoBD;AAAA,EACtB;AAGA,MAAI,YAAY,kBAAkB,OAAO,SAAU,KAAKC,YAAW;AACjE,QAAIA,UAAS,IAAI,eAAe,OAAO;AAAA,MACrC,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,iBAAiBA,UAAS,CAAC;AAC9B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,KAAK,SAAS,EAAE,KAAK,SAAU,GAAG,GAAG;AACjD,WAAO,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,EACnC,CAAC;AACH;;;AdlCA,SAAS,8BAA8B,WAAW;AAChD,MAAI,iBAAiB,SAAS,MAAM,MAAM;AACxC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,oBAAoB,qBAAqB,SAAS;AACtD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAEhB,MAAI,MAAM,cAAc,IAAI,EAAE,OAAO;AACnC;AAAA,EACF;AAEA,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,OAAO,kBACpD,8BAA8B,QAAQ,oBACtC,UAAU,QAAQ,SAClB,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,wBAAwB,QAAQ,gBAChC,iBAAiB,0BAA0B,SAAS,OAAO,uBAC3D,wBAAwB,QAAQ;AACpC,MAAI,qBAAqB,MAAM,QAAQ;AACvC,MAAI,gBAAgB,iBAAiB,kBAAkB;AACvD,MAAI,kBAAkB,kBAAkB;AACxC,MAAI,qBAAqB,gCAAgC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,kBAAkB,CAAC,IAAI,8BAA8B,kBAAkB;AAC3L,MAAIC,cAAa,CAAC,kBAAkB,EAAE,OAAO,kBAAkB,EAAE,OAAO,SAAU,KAAKC,YAAW;AAChG,WAAO,IAAI,OAAO,iBAAiBA,UAAS,MAAM,OAAO,qBAAqB,OAAO;AAAA,MACnF,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAIA,UAAS;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,qBAAqB;AACzB,MAAI,wBAAwBD,YAAW,CAAC;AAExC,WAAS,IAAI,GAAG,IAAIA,YAAW,QAAQ,KAAK;AAC1C,QAAI,YAAYA,YAAW,CAAC;AAE5B,QAAI,iBAAiB,iBAAiB,SAAS;AAE/C,QAAI,mBAAmB,aAAa,SAAS,MAAM;AACnD,QAAI,aAAa,CAAC,KAAK,MAAM,EAAE,QAAQ,cAAc,KAAK;AAC1D,QAAI,MAAM,aAAa,UAAU;AACjC,QAAI,WAAW,eAAe,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,oBAAoB,aAAa,mBAAmB,QAAQ,OAAO,mBAAmB,SAAS;AAEnG,QAAI,cAAc,GAAG,IAAI,WAAW,GAAG,GAAG;AACxC,0BAAoB,qBAAqB,iBAAiB;AAAA,IAC5D;AAEA,QAAI,mBAAmB,qBAAqB,iBAAiB;AAC7D,QAAI,SAAS,CAAC;AAEd,QAAI,eAAe;AACjB,aAAO,KAAK,SAAS,cAAc,KAAK,CAAC;AAAA,IAC3C;AAEA,QAAI,cAAc;AAChB,aAAO,KAAK,SAAS,iBAAiB,KAAK,GAAG,SAAS,gBAAgB,KAAK,CAAC;AAAA,IAC/E;AAEA,QAAI,OAAO,MAAM,SAAU,OAAO;AAChC,aAAO;AAAA,IACT,CAAC,GAAG;AACF,8BAAwB;AACxB,2BAAqB;AACrB;AAAA,IACF;AAEA,cAAU,IAAI,WAAW,MAAM;AAAA,EACjC;AAEA,MAAI,oBAAoB;AAEtB,QAAI,iBAAiB,iBAAiB,IAAI;AAE1C,QAAI,QAAQ,SAASE,OAAMC,KAAI;AAC7B,UAAI,mBAAmBH,YAAW,KAAK,SAAUC,YAAW;AAC1D,YAAIG,UAAS,UAAU,IAAIH,UAAS;AAEpC,YAAIG,SAAQ;AACV,iBAAOA,QAAO,MAAM,GAAGD,GAAE,EAAE,MAAM,SAAU,OAAO;AAChD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,kBAAkB;AACpB,gCAAwB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,KAAK,gBAAgB,KAAK,GAAG,MAAM;AAC1C,UAAI,OAAO,MAAM,EAAE;AAEnB,UAAI,SAAS,QAAS;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,MAAM,cAAc,uBAAuB;AAC7C,UAAM,cAAc,IAAI,EAAE,QAAQ;AAClC,UAAM,YAAY;AAClB,UAAM,QAAQ;AAAA,EAChB;AACF;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAAA,EAC3B,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AACF;;;AelJA,IAAAE,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAGA,SAAS,eAAe,UAAU,MAAM,kBAAkB;AACxD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK,SAAS,iBAAiB;AAAA,IACnD,OAAO,SAAS,QAAQ,KAAK,QAAQ,iBAAiB;AAAA,IACtD,QAAQ,SAAS,SAAS,KAAK,SAAS,iBAAiB;AAAA,IACzD,MAAM,SAAS,OAAO,KAAK,QAAQ,iBAAiB;AAAA,EACtD;AACF;AAEA,SAAS,sBAAsB,UAAU;AACvC,SAAO,CAAC,KAAK,OAAO,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrD,WAAO,SAAS,IAAI,KAAK;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAChB,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,mBAAmB,MAAM,cAAc;AAC3C,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,aAAa;AAAA,EACf,CAAC;AACD,MAAI,2BAA2B,eAAe,mBAAmB,aAAa;AAC9E,MAAI,sBAAsB,eAAe,mBAAmB,YAAY,gBAAgB;AACxF,MAAI,oBAAoB,sBAAsB,wBAAwB;AACtE,MAAI,mBAAmB,sBAAsB,mBAAmB;AAChE,QAAM,cAAc,IAAI,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,gCAAgC;AAAA,IAChC,uBAAuB;AAAA,EACzB,CAAC;AACH;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,kBAAkB,CAAC,iBAAiB;AAAA,EACpC,IAAI;AACN;;;AC5DA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAGO,SAAS,wBAAwB,WAAW,OAAOC,SAAQ;AAChE,MAAI,gBAAgB,iBAAiB,SAAS;AAC9C,MAAI,iBAAiB,CAAC,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI,KAAK;AAEpE,MAAI,OAAO,OAAOA,YAAW,aAAaA,QAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IACxE;AAAA,EACF,CAAC,CAAC,IAAIA,SACF,WAAW,KAAK,CAAC,GACjB,WAAW,KAAK,CAAC;AAErB,aAAW,YAAY;AACvB,cAAY,YAAY,KAAK;AAC7B,SAAO,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK,IAAI;AAAA,IACjD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM,SAChB,OAAO,MAAM;AACjB,MAAI,kBAAkB,QAAQ,QAC1BA,UAAS,oBAAoB,SAAS,CAAC,GAAG,CAAC,IAAI;AACnD,MAAI,OAAO,WAAW,OAAO,SAAU,KAAK,WAAW;AACrD,QAAI,SAAS,IAAI,wBAAwB,WAAW,MAAM,OAAOA,OAAM;AACvE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,wBAAwB,KAAK,MAAM,SAAS,GAC5C,IAAI,sBAAsB,GAC1B,IAAI,sBAAsB;AAE9B,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,cAAc,cAAc,KAAK;AACvC,UAAM,cAAc,cAAc,KAAK;AAAA,EACzC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,iBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU,CAAC,eAAe;AAAA,EAC1B,IAAI;AACN;;;ACrDA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAKhB,QAAM,cAAc,IAAI,IAAI,eAAe;AAAA,IACzC,WAAW,MAAM,MAAM;AAAA,IACvB,SAAS,MAAM,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW,MAAM;AAAA,EACnB,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACxBA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,WAA4B,MAAM;AACvC,SAAO,SAAS,MAAM,MAAM;AAC9B;;;ADUA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAChB,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,QAAQ,kBACrD,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,UAAU,QAAQ,SAClB,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,IAAI;AAC1D,MAAI,WAAW,eAAe,OAAO;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,YAAY,aAAa,MAAM,SAAS;AAC5C,MAAI,kBAAkB,CAAC;AACvB,MAAI,WAAW,yBAAyB,aAAa;AACrD,MAAI,UAAU,WAAW,QAAQ;AACjC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IACvG,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,MAAI,8BAA8B,OAAO,sBAAsB,WAAW;AAAA,IACxE,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI,OAAO,OAAO;AAAA,IAChB,UAAU;AAAA,IACV,SAAS;AAAA,EACX,GAAG,iBAAiB;AACpB,MAAI,sBAAsB,MAAM,cAAc,SAAS,MAAM,cAAc,OAAO,MAAM,SAAS,IAAI;AACrG,MAAI,OAAO;AAAA,IACT,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,CAACA,gBAAe;AAClB;AAAA,EACF;AAEA,MAAI,eAAe;AACjB,QAAI;AAEJ,QAAI,WAAW,aAAa,MAAM,MAAM;AACxC,QAAI,UAAU,aAAa,MAAM,SAAS;AAC1C,QAAI,MAAM,aAAa,MAAM,WAAW;AACxC,QAAIC,UAASD,eAAc,QAAQ;AACnC,QAAIE,OAAMD,UAAS,SAAS,QAAQ;AACpC,QAAIE,OAAMF,UAAS,SAAS,OAAO;AACnC,QAAI,WAAW,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI;AAC/C,QAAI,SAAS,cAAc,QAAQ,cAAc,GAAG,IAAI,WAAW,GAAG;AACtE,QAAI,SAAS,cAAc,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG;AAGxE,QAAI,eAAe,MAAM,SAAS;AAClC,QAAI,YAAY,UAAU,eAAe,cAAc,YAAY,IAAI;AAAA,MACrE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,QAAI,qBAAqB,MAAM,cAAc,kBAAkB,IAAI,MAAM,cAAc,kBAAkB,EAAE,UAAU,mBAAmB;AACxI,QAAI,kBAAkB,mBAAmB,QAAQ;AACjD,QAAI,kBAAkB,mBAAmB,OAAO;AAMhD,QAAI,WAAW,OAAO,GAAG,cAAc,GAAG,GAAG,UAAU,GAAG,CAAC;AAC3D,QAAI,YAAY,kBAAkB,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC5M,QAAI,YAAY,kBAAkB,CAAC,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC7M,QAAI,oBAAoB,MAAM,SAAS,SAAS,gBAAgB,MAAM,SAAS,KAAK;AACpF,QAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,aAAa,IAAI,kBAAkB,cAAc,IAAI;AACjI,QAAI,uBAAuB,wBAAwB,uBAAuB,OAAO,SAAS,oBAAoB,QAAQ,MAAM,OAAO,wBAAwB;AAC3J,QAAI,YAAYA,UAAS,YAAY,sBAAsB;AAC3D,QAAI,YAAYA,UAAS,YAAY;AACrC,QAAI,kBAAkB,OAAO,SAAS,IAAQC,MAAK,SAAS,IAAIA,MAAKD,SAAQ,SAAS,IAAQE,MAAK,SAAS,IAAIA,IAAG;AACnH,IAAAH,eAAc,QAAQ,IAAI;AAC1B,SAAK,QAAQ,IAAI,kBAAkBC;AAAA,EACrC;AAEA,MAAI,cAAc;AAChB,QAAI;AAEJ,QAAI,YAAY,aAAa,MAAM,MAAM;AAEzC,QAAI,WAAW,aAAa,MAAM,SAAS;AAE3C,QAAI,UAAUD,eAAc,OAAO;AAEnC,QAAI,OAAO,YAAY,MAAM,WAAW;AAExC,QAAI,OAAO,UAAU,SAAS,SAAS;AAEvC,QAAI,OAAO,UAAU,SAAS,QAAQ;AAEtC,QAAI,eAAe,CAAC,KAAK,IAAI,EAAE,QAAQ,aAAa,MAAM;AAE1D,QAAI,wBAAwB,yBAAyB,uBAAuB,OAAO,SAAS,oBAAoB,OAAO,MAAM,OAAO,yBAAyB;AAE7J,QAAI,aAAa,eAAe,OAAO,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B;AAE7I,QAAI,aAAa,eAAe,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B,UAAU;AAEhJ,QAAI,mBAAmB,UAAU,eAAe,eAAe,YAAY,SAAS,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa,IAAI;AAExK,IAAAA,eAAc,OAAO,IAAI;AACzB,SAAK,OAAO,IAAI,mBAAmB;AAAA,EACrC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,0BAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAC7B;;;AE7IA,IAAAI,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,qBAAsC,SAAS;AACpD,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;;;ADDe,SAAR,cAA+B,MAAM;AAC1C,MAAI,SAAS,UAAU,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AACpD,WAAO,gBAAgB,IAAI;AAAA,EAC7B,OAAO;AACL,WAAO,qBAAqB,IAAI;AAAA,EAClC;AACF;;;ADDA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,eAAe;AACxD,MAAI,SAAS,MAAM,KAAK,MAAM,IAAI,QAAQ,gBAAgB;AAC1D,SAAO,WAAW,KAAK,WAAW;AACpC;AAIe,SAAR,iBAAkC,yBAAyB,cAAc,SAAS;AACvF,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAI,0BAA0B,cAAc,YAAY;AACxD,MAAI,uBAAuB,cAAc,YAAY,KAAK,gBAAgB,YAAY;AACtF,MAAI,kBAAkB,mBAAmB,YAAY;AACrD,MAAI,OAAO,sBAAsB,yBAAyB,sBAAsB,OAAO;AACvF,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM;AAAA,IAClC,eAAe,eAAe,GAAG;AAC/B,eAAS,cAAc,YAAY;AAAA,IACrC;AAEA,QAAI,cAAc,YAAY,GAAG;AAC/B,gBAAU,sBAAsB,cAAc,IAAI;AAClD,cAAQ,KAAK,aAAa;AAC1B,cAAQ,KAAK,aAAa;AAAA,IAC5B,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,OAAO,aAAa,QAAQ;AAAA,IAC3C,GAAG,KAAK,MAAM,OAAO,YAAY,QAAQ;AAAA,IACzC,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;;;AGzDA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,SAAS,MAAM,WAAW;AACxB,MAAI,MAAM,oBAAI,IAAI;AAClB,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,SAAS,CAAC;AACd,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,IAAI,SAAS,MAAM,QAAQ;AAAA,EACjC,CAAC;AAED,WAAS,KAAK,UAAU;AACtB,YAAQ,IAAI,SAAS,IAAI;AACzB,QAAI,WAAW,CAAC,EAAE,OAAO,SAAS,YAAY,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC;AACjF,aAAS,QAAQ,SAAU,KAAK;AAC9B,UAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACrB,YAAI,cAAc,IAAI,IAAI,GAAG;AAE7B,YAAI,aAAa;AACf,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,GAAG;AAE/B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEe,SAAR,eAAgC,WAAW;AAEhD,MAAI,mBAAmB,MAAM,SAAS;AAEtC,SAAO,eAAe,OAAO,SAAU,KAAK,OAAO;AACjD,WAAO,IAAI,OAAO,iBAAiB,OAAO,SAAU,UAAU;AAC5D,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,CAAC;AACP;;;AC3CA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,SAA0BC,KAAI;AACnC,MAAI;AACJ,SAAO,WAAY;AACjB,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,QAAQ,SAAU,SAAS;AACvC,gBAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,oBAAU;AACV,kBAAQA,IAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;;;ACdA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,YAA6B,WAAW;AAC7C,MAAI,SAAS,UAAU,OAAO,SAAUC,SAAQ,SAAS;AACvD,QAAI,WAAWA,QAAO,QAAQ,IAAI;AAClC,IAAAA,QAAO,QAAQ,IAAI,IAAI,WAAW,OAAO,OAAO,CAAC,GAAG,UAAU,SAAS;AAAA,MACrE,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS,SAAS,QAAQ,OAAO;AAAA,MAC5D,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,MAAM,QAAQ,IAAI;AAAA,IACrD,CAAC,IAAI;AACL,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAC5C,WAAO,OAAO,GAAG;AAAA,EACnB,CAAC;AACH;;;ANJA,IAAI,kBAAkB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW,CAAC;AAAA,EACZ,UAAU;AACZ;AAEA,SAAS,mBAAmB;AAC1B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,SAAO,CAAC,KAAK,KAAK,SAAU,SAAS;AACnC,WAAO,EAAE,WAAW,OAAO,QAAQ,0BAA0B;AAAA,EAC/D,CAAC;AACH;AAEO,SAAS,gBAAgB,kBAAkB;AAChD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB,CAAC;AAAA,EACtB;AAEA,MAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,kBAC1CC,oBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,yBAAyB,kBAAkB,gBAC3C,iBAAiB,2BAA2B,SAAS,kBAAkB;AAC3E,SAAO,SAASC,cAAaC,YAAWC,SAAQ,SAAS;AACvD,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ;AAAA,MACV,WAAW;AAAA,MACX,kBAAkB,CAAC;AAAA,MACnB,SAAS,OAAO,OAAO,CAAC,GAAG,iBAAiB,cAAc;AAAA,MAC1D,eAAe,CAAC;AAAA,MAChB,UAAU;AAAA,QACR,WAAWD;AAAA,QACX,QAAQC;AAAA,MACV;AAAA,MACA,YAAY,CAAC;AAAA,MACb,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,cAAc;AAClB,QAAI,WAAW;AAAA,MACb;AAAA,MACA,YAAY,SAAS,WAAW,kBAAkB;AAChD,YAAIC,WAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;AACzF,+BAAuB;AACvB,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,MAAM,SAASA,QAAO;AACxE,cAAM,gBAAgB;AAAA,UACpB,WAAW,UAAUF,UAAS,IAAI,kBAAkBA,UAAS,IAAIA,WAAU,iBAAiB,kBAAkBA,WAAU,cAAc,IAAI,CAAC;AAAA,UAC3I,QAAQ,kBAAkBC,OAAM;AAAA,QAClC;AAGA,YAAI,mBAAmB,eAAe,YAAY,CAAC,EAAE,OAAOH,mBAAkB,MAAM,QAAQ,SAAS,CAAC,CAAC;AAEvG,cAAM,mBAAmB,iBAAiB,OAAO,SAAU,GAAG;AAC5D,iBAAO,EAAE;AAAA,QACX,CAAC;AACD,2BAAmB;AACnB,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,SAAS,cAAc;AAClC,YAAI,aAAa;AACf;AAAA,QACF;AAEA,YAAI,kBAAkB,MAAM,UACxBE,aAAY,gBAAgB,WAC5BC,UAAS,gBAAgB;AAG7B,YAAI,CAAC,iBAAiBD,YAAWC,OAAM,GAAG;AACxC;AAAA,QACF;AAGA,cAAM,QAAQ;AAAA,UACZ,WAAW,iBAAiBD,YAAW,gBAAgBC,OAAM,GAAG,MAAM,QAAQ,aAAa,OAAO;AAAA,UAClG,QAAQ,cAAcA,OAAM;AAAA,QAC9B;AAMA,cAAM,QAAQ;AACd,cAAM,YAAY,MAAM,QAAQ;AAKhC,cAAM,iBAAiB,QAAQ,SAAU,UAAU;AACjD,iBAAO,MAAM,cAAc,SAAS,IAAI,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,QAC7E,CAAC;AAED,iBAAS,QAAQ,GAAG,QAAQ,MAAM,iBAAiB,QAAQ,SAAS;AAClE,cAAI,MAAM,UAAU,MAAM;AACxB,kBAAM,QAAQ;AACd,oBAAQ;AACR;AAAA,UACF;AAEA,cAAI,wBAAwB,MAAM,iBAAiB,KAAK,GACpDE,MAAK,sBAAsB,IAC3B,yBAAyB,sBAAsB,SAC/C,WAAW,2BAA2B,SAAS,CAAC,IAAI,wBACpD,OAAO,sBAAsB;AAEjC,cAAI,OAAOA,QAAO,YAAY;AAC5B,oBAAQA,IAAG;AAAA,cACT;AAAA,cACA,SAAS;AAAA,cACT;AAAA,cACA;AAAA,YACF,CAAC,KAAK;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,MAGA,QAAQ,SAAS,WAAY;AAC3B,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,mBAAS,YAAY;AACrB,kBAAQ,KAAK;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAAA,MACD,SAAS,SAAS,UAAU;AAC1B,+BAAuB;AACvB,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,CAAC,iBAAiBH,YAAWC,OAAM,GAAG;AACxC,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,OAAO,EAAE,KAAK,SAAUG,QAAO;AACjD,UAAI,CAAC,eAAe,QAAQ,eAAe;AACzC,gBAAQ,cAAcA,MAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AAMD,aAAS,qBAAqB;AAC5B,YAAM,iBAAiB,QAAQ,SAAU,MAAM;AAC7C,YAAI,OAAO,KAAK,MACZ,eAAe,KAAK,SACpBF,WAAU,iBAAiB,SAAS,CAAC,IAAI,cACzCG,UAAS,KAAK;AAElB,YAAI,OAAOA,YAAW,YAAY;AAChC,cAAI,YAAYA,QAAO;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAASH;AAAA,UACX,CAAC;AAED,cAAI,SAAS,SAASI,UAAS;AAAA,UAAC;AAEhC,2BAAiB,KAAK,aAAa,MAAM;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB;AAChC,uBAAiB,QAAQ,SAAUH,KAAI;AACrC,eAAOA,IAAG;AAAA,MACZ,CAAC;AACD,yBAAmB,CAAC;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AACF;AACO,IAAI,eAA4B,gBAAgB;;;AOpMvD,IAAAI,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAKA,IAAI,mBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,mBAAW;AACjF,IAAIC,gBAA4B,gBAAgB;AAAA,EAC9C;AACF,CAAC;;;ADED,IAAIC,oBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,qBAAa,gBAAQ,cAAM,yBAAiB,eAAO,YAAI;AAC7H,IAAIC,gBAA4B,gBAAgB;AAAA,EAC9C,kBAAkBD;AACpB,CAAC;;;AxDbygB,SAAS,EAAE,GAAE,GAAE;AAAC,GAAC,QAAM,KAAG,IAAE,EAAE,YAAU,IAAE,EAAE;AAAQ,WAAQ,IAAE,GAAE,IAAE,MAAM,CAAC,GAAE,IAAE,GAAE,IAAI,GAAE,CAAC,IAAE,EAAE,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,GAAE;AAAC,QAAG,YAAU,OAAO,EAAE,QAAO,EAAE,GAAE,CAAC;AAAE,QAAI,IAAE,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAE,EAAE;AAAE,WAAM,aAAW,KAAG,EAAE,gBAAc,IAAE,EAAE,YAAY,OAAM,UAAQ,KAAG,UAAQ,IAAE,MAAM,KAAK,CAAC,IAAE,gBAAc,KAAG,2CAA2C,KAAK,CAAC,IAAE,EAAE,GAAE,CAAC,IAAE;AAAA,EAAM;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,SAASE,IAAE;AAAC,QAAG,MAAM,QAAQA,EAAC,EAAE,QAAO,EAAEA,EAAC;AAAA,EAAC,EAAE,CAAC,KAAG,SAASA,IAAE;AAAC,QAAG,eAAa,OAAO,UAAQ,QAAMA,GAAE,OAAO,QAAQ,KAAG,QAAMA,GAAE,YAAY,EAAE,QAAO,MAAM,KAAKA,EAAC;AAAA,EAAC,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,WAAU;AAAC,UAAM,IAAI,UAAU,sIAAsI;AAAA,EAAC,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,WAAO,OAAOA;AAAA,EAAC,IAAE,SAASA,IAAE;AAAC,WAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,EAAC,GAAE,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAG;AAAC,QAAI,IAAE,EAAE,CAAC,EAAE,CAAC,GAAE,IAAE,EAAE;AAAA,EAAK,SAAOA,IAAE;AAAC,WAAO,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAC,IAAE,OAAK,EAAE,CAAC,IAAE,QAAQ,QAAQ,CAAC,EAAE,KAAK,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,WAAU;AAAC,QAAI,IAAE,MAAK,IAAE;AAAU,WAAO,IAAI,QAAS,SAAS,GAAE,GAAE;AAAC,UAAI,IAAE,EAAE,MAAM,GAAE,CAAC;AAAE,eAAS,EAAEA,IAAE;AAAC,UAAE,GAAE,GAAE,GAAE,GAAE,GAAE,QAAOA,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,UAAE,GAAE,GAAE,GAAE,GAAE,GAAE,SAAQA,EAAC;AAAA,MAAC;AAAC,QAAE,MAAM;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAjB,IAAmB,IAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,CAAC,SAAS,GAAE;AAAC,WAAS,EAAE,GAAE;AAAC,WAAO,EAAE,UAAQ,IAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,aAAO,OAAOA;AAAA,IAAC,IAAE,SAASA,IAAE;AAAC,aAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,IAAC,GAAE,EAAE,QAAQ,aAAW,MAAG,EAAE,QAAQ,UAAQ,EAAE,SAAQ,EAAE,CAAC;AAAA,EAAC;AAAC,IAAE,UAAQ,GAAE,EAAE,QAAQ,aAAW,MAAG,EAAE,QAAQ,UAAQ,EAAE;AAAO,EAAE,CAAC,GAAE,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,QAAQ;AAAQ,WAAS,IAAG;AAAC,MAAE,UAAQ,IAAE,WAAU;AAAC,aAAO;AAAA,IAAC,GAAE,EAAE,QAAQ,aAAW,MAAG,EAAE,QAAQ,UAAQ,EAAE;AAAQ,QAAI,GAAE,IAAE,CAAC,GAAE,IAAE,OAAO,WAAU,IAAE,EAAE,gBAAe,IAAE,OAAO,kBAAgB,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAAF,GAAEC,EAAC,IAAEC,GAAE;AAAA,IAAK,GAAE,IAAE,cAAY,OAAO,SAAO,SAAO,CAAC,GAAE,IAAE,EAAE,YAAU,cAAa,IAAE,EAAE,iBAAe,mBAAkB,IAAE,EAAE,eAAa;AAAgB,aAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,aAAO,OAAO,eAAeF,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,GAAEF,GAAEC,EAAC;AAAA,IAAC;AAAC,QAAG;AAAC,QAAE,CAAC,GAAE,EAAE;AAAA,IAAC,SAAOE,IAAE;AAAC,UAAE,SAASH,IAAEC,IAAEC,IAAE;AAAC,eAAOF,GAAEC,EAAC,IAAEC;AAAA,MAAC;AAAA,IAAC;AAAC,aAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAIC,KAAEH,MAAGA,GAAE,qBAAqB,IAAEA,KAAE,GAAEI,KAAE,OAAO,OAAOD,GAAE,SAAS,GAAEE,KAAE,IAAIC,GAAEJ,MAAG,CAAC,CAAC;AAAE,aAAO,EAAEE,IAAE,WAAU,EAAC,OAAMG,GAAER,IAAEE,IAAEI,EAAC,EAAC,CAAC,GAAED;AAAA,IAAC;AAAC,aAAS,EAAEL,IAAEC,IAAEC,IAAE;AAAC,UAAG;AAAC,eAAM,EAAC,MAAK,UAAS,KAAIF,GAAE,KAAKC,IAAEC,EAAC,EAAC;AAAA,MAAC,SAAOF,IAAE;AAAC,eAAM,EAAC,MAAK,SAAQ,KAAIA,GAAC;AAAA,MAAC;AAAA,IAAC;AAAC,MAAE,OAAK;AAAE,QAAI,IAAE,kBAAiB,IAAE,kBAAiB,IAAE,aAAY,IAAE,aAAY,IAAE,CAAC;AAAE,aAAS,IAAG;AAAA,IAAC;AAAC,aAAS,IAAG;AAAA,IAAC;AAAC,aAAS,IAAG;AAAA,IAAC;AAAC,QAAI,IAAE,CAAC;AAAE,MAAE,GAAE,GAAG,WAAU;AAAC,aAAO;AAAA,IAAI,CAAE;AAAE,QAAI,IAAE,OAAO,gBAAe,IAAE,KAAG,EAAE,EAAES,GAAE,CAAC,CAAC,CAAC,CAAC;AAAE,SAAG,MAAI,KAAG,EAAE,KAAK,GAAE,CAAC,MAAI,IAAE;AAAG,QAAI,IAAE,EAAE,YAAU,EAAE,YAAU,OAAO,OAAO,CAAC;AAAE,aAAS,EAAET,IAAE;AAAC,OAAC,QAAO,SAAQ,QAAQ,EAAE,QAAS,SAASC,IAAE;AAAC,UAAED,IAAEC,IAAG,SAASD,IAAE;AAAC,iBAAO,KAAK,QAAQC,IAAED,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,aAAS,EAAEA,IAAEE,IAAE;AAAC,eAASC,GAAEC,IAAEC,IAAEK,IAAEC,IAAE;AAAC,YAAIC,KAAE,EAAEZ,GAAEI,EAAC,GAAEJ,IAAEK,EAAC;AAAE,YAAG,YAAUO,GAAE,MAAK;AAAC,cAAIC,KAAED,GAAE,KAAIE,KAAED,GAAE;AAAM,iBAAOC,MAAG,YAAU,EAAEA,EAAC,KAAG,EAAE,KAAKA,IAAE,SAAS,IAAEZ,GAAE,QAAQY,GAAE,OAAO,EAAE,KAAM,SAASd,IAAE;AAAC,YAAAG,GAAE,QAAOH,IAAEU,IAAEC,EAAC;AAAA,UAAC,GAAI,SAASX,IAAE;AAAC,YAAAG,GAAE,SAAQH,IAAEU,IAAEC,EAAC;AAAA,UAAC,CAAE,IAAET,GAAE,QAAQY,EAAC,EAAE,KAAM,SAASd,IAAE;AAAC,YAAAa,GAAE,QAAMb,IAAEU,GAAEG,EAAC;AAAA,UAAC,GAAI,SAASb,IAAE;AAAC,mBAAOG,GAAE,SAAQH,IAAEU,IAAEC,EAAC;AAAA,UAAC,CAAE;AAAA,QAAC;AAAC,QAAAA,GAAEC,GAAE,GAAG;AAAA,MAAC;AAAC,UAAIR;AAAE,QAAE,MAAK,WAAU,EAAC,OAAM,SAASJ,IAAEC,IAAE;AAAC,iBAASI,KAAG;AAAC,iBAAO,IAAIH,GAAG,SAASA,IAAEE,IAAE;AAAC,YAAAD,GAAEH,IAAEC,IAAEC,IAAEE,EAAC;AAAA,UAAC,CAAE;AAAA,QAAC;AAAC,eAAOA,KAAEA,KAAEA,GAAE,KAAKC,IAAEA,EAAC,IAAEA,GAAE;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAC,aAASG,GAAER,IAAEC,IAAEC,IAAE;AAAC,UAAIE,KAAE;AAAE,aAAO,SAASC,IAAEC,IAAE;AAAC,YAAGF,OAAI,EAAE,OAAM,MAAM,8BAA8B;AAAE,YAAGA,OAAI,GAAE;AAAC,cAAG,YAAUC,GAAE,OAAMC;AAAE,iBAAM,EAAC,OAAM,GAAE,MAAK,KAAE;AAAA,QAAC;AAAC,aAAIJ,GAAE,SAAOG,IAAEH,GAAE,MAAII,QAAI;AAAC,cAAII,KAAER,GAAE;AAAS,cAAGQ,IAAE;AAAC,gBAAIC,KAAEI,GAAEL,IAAER,EAAC;AAAE,gBAAGS,IAAE;AAAC,kBAAGA,OAAI,EAAE;AAAS,qBAAOA;AAAA,YAAC;AAAA,UAAC;AAAC,cAAG,WAAST,GAAE,OAAO,CAAAA,GAAE,OAAKA,GAAE,QAAMA,GAAE;AAAA,mBAAY,YAAUA,GAAE,QAAO;AAAC,gBAAGE,OAAI,EAAE,OAAMA,KAAE,GAAEF,GAAE;AAAI,YAAAA,GAAE,kBAAkBA,GAAE,GAAG;AAAA,UAAC,MAAK,cAAWA,GAAE,UAAQA,GAAE,OAAO,UAASA,GAAE,GAAG;AAAE,UAAAE,KAAE;AAAE,cAAIQ,KAAE,EAAEZ,IAAEC,IAAEC,EAAC;AAAE,cAAG,aAAWU,GAAE,MAAK;AAAC,gBAAGR,KAAEF,GAAE,OAAK,IAAE,GAAEU,GAAE,QAAM,EAAE;AAAS,mBAAM,EAAC,OAAMA,GAAE,KAAI,MAAKV,GAAE,KAAI;AAAA,UAAC;AAAC,sBAAUU,GAAE,SAAOR,KAAE,GAAEF,GAAE,SAAO,SAAQA,GAAE,MAAIU,GAAE;AAAA,QAAI;AAAA,MAAC;AAAA,IAAC;AAAC,aAASG,GAAEf,IAAEC,IAAE;AAAC,UAAIC,KAAED,GAAE,QAAOG,KAAEJ,GAAE,SAASE,EAAC;AAAE,UAAGE,OAAI,EAAE,QAAOH,GAAE,WAAS,MAAK,YAAUC,MAAGF,GAAE,SAAS,WAASC,GAAE,SAAO,UAASA,GAAE,MAAI,GAAEc,GAAEf,IAAEC,EAAC,GAAE,YAAUA,GAAE,WAAS,aAAWC,OAAID,GAAE,SAAO,SAAQA,GAAE,MAAI,IAAI,UAAU,sCAAoCC,KAAE,UAAU,IAAG;AAAE,UAAIG,KAAE,EAAED,IAAEJ,GAAE,UAASC,GAAE,GAAG;AAAE,UAAG,YAAUI,GAAE,KAAK,QAAOJ,GAAE,SAAO,SAAQA,GAAE,MAAII,GAAE,KAAIJ,GAAE,WAAS,MAAK;AAAE,UAAIK,KAAED,GAAE;AAAI,aAAOC,KAAEA,GAAE,QAAML,GAAED,GAAE,UAAU,IAAEM,GAAE,OAAML,GAAE,OAAKD,GAAE,SAAQ,aAAWC,GAAE,WAASA,GAAE,SAAO,QAAOA,GAAE,MAAI,IAAGA,GAAE,WAAS,MAAK,KAAGK,MAAGL,GAAE,SAAO,SAAQA,GAAE,MAAI,IAAI,UAAU,kCAAkC,GAAEA,GAAE,WAAS,MAAK;AAAA,IAAE;AAAC,aAASe,GAAEhB,IAAE;AAAC,UAAIC,KAAE,EAAC,QAAOD,GAAE,CAAC,EAAC;AAAE,WAAKA,OAAIC,GAAE,WAASD,GAAE,CAAC,IAAG,KAAKA,OAAIC,GAAE,aAAWD,GAAE,CAAC,GAAEC,GAAE,WAASD,GAAE,CAAC,IAAG,KAAK,WAAW,KAAKC,EAAC;AAAA,IAAC;AAAC,aAASgB,GAAEjB,IAAE;AAAC,UAAIC,KAAED,GAAE,cAAY,CAAC;AAAE,MAAAC,GAAE,OAAK,UAAS,OAAOA,GAAE,KAAID,GAAE,aAAWC;AAAA,IAAC;AAAC,aAASM,GAAEP,IAAE;AAAC,WAAK,aAAW,CAAC,EAAC,QAAO,OAAM,CAAC,GAAEA,GAAE,QAAQgB,IAAE,IAAI,GAAE,KAAK,MAAM,IAAE;AAAA,IAAC;AAAC,aAASP,GAAET,IAAE;AAAC,UAAGA,MAAG,OAAKA,IAAE;AAAC,YAAIE,KAAEF,GAAE,CAAC;AAAE,YAAGE,GAAE,QAAOA,GAAE,KAAKF,EAAC;AAAE,YAAG,cAAY,OAAOA,GAAE,KAAK,QAAOA;AAAE,YAAG,CAAC,MAAMA,GAAE,MAAM,GAAE;AAAC,cAAII,KAAE,IAAGC,KAAE,SAASJ,KAAG;AAAC,mBAAK,EAAEG,KAAEJ,GAAE,SAAQ,KAAG,EAAE,KAAKA,IAAEI,EAAC,EAAE,QAAOH,GAAE,QAAMD,GAAEI,EAAC,GAAEH,GAAE,OAAK,OAAGA;AAAE,mBAAOA,GAAE,QAAM,GAAEA,GAAE,OAAK,MAAGA;AAAA,UAAC;AAAE,iBAAOI,GAAE,OAAKA;AAAA,QAAC;AAAA,MAAC;AAAC,YAAM,IAAI,UAAU,EAAEL,EAAC,IAAE,kBAAkB;AAAA,IAAC;AAAC,WAAO,EAAE,YAAU,GAAE,EAAE,GAAE,eAAc,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC,GAAE,EAAE,GAAE,eAAc,EAAC,OAAM,GAAE,cAAa,KAAE,CAAC,GAAE,EAAE,cAAY,EAAE,GAAE,GAAE,mBAAmB,GAAE,EAAE,sBAAoB,SAASA,IAAE;AAAC,UAAIC,KAAE,cAAY,OAAOD,MAAGA,GAAE;AAAY,aAAM,CAAC,CAACC,OAAIA,OAAI,KAAG,yBAAuBA,GAAE,eAAaA,GAAE;AAAA,IAAM,GAAE,EAAE,OAAK,SAASD,IAAE;AAAC,aAAO,OAAO,iBAAe,OAAO,eAAeA,IAAE,CAAC,KAAGA,GAAE,YAAU,GAAE,EAAEA,IAAE,GAAE,mBAAmB,IAAGA,GAAE,YAAU,OAAO,OAAO,CAAC,GAAEA;AAAA,IAAC,GAAE,EAAE,QAAM,SAASA,IAAE;AAAC,aAAM,EAAC,SAAQA,GAAC;AAAA,IAAC,GAAE,EAAE,EAAE,SAAS,GAAE,EAAE,EAAE,WAAU,GAAG,WAAU;AAAC,aAAO;AAAA,IAAI,CAAE,GAAE,EAAE,gBAAc,GAAE,EAAE,QAAM,SAASA,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,iBAASA,OAAIA,KAAE;AAAS,UAAIC,KAAE,IAAI,EAAE,EAAEN,IAAEC,IAAEC,IAAEC,EAAC,GAAEE,EAAC;AAAE,aAAO,EAAE,oBAAoBJ,EAAC,IAAEK,KAAEA,GAAE,KAAK,EAAE,KAAM,SAASN,IAAE;AAAC,eAAOA,GAAE,OAAKA,GAAE,QAAMM,GAAE,KAAK;AAAA,MAAC,CAAE;AAAA,IAAC,GAAE,EAAE,CAAC,GAAE,EAAE,GAAE,GAAE,WAAW,GAAE,EAAE,GAAE,GAAG,WAAU;AAAC,aAAO;AAAA,IAAI,CAAE,GAAE,EAAE,GAAE,YAAY,WAAU;AAAC,aAAM;AAAA,IAAoB,CAAE,GAAE,EAAE,OAAK,SAASN,IAAE;AAAC,UAAIC,KAAE,OAAOD,EAAC,GAAEE,KAAE,CAAC;AAAE,eAAQC,MAAKF,GAAE,CAAAC,GAAE,KAAKC,EAAC;AAAE,aAAOD,GAAE,QAAQ,GAAE,SAASF,KAAG;AAAC,eAAKE,GAAE,UAAQ;AAAC,cAAIC,KAAED,GAAE,IAAI;AAAE,cAAGC,MAAKF,GAAE,QAAOD,GAAE,QAAMG,IAAEH,GAAE,OAAK,OAAGA;AAAA,QAAC;AAAC,eAAOA,GAAE,OAAK,MAAGA;AAAA,MAAC;AAAA,IAAC,GAAE,EAAE,SAAOS,IAAEF,GAAE,YAAU,EAAC,aAAYA,IAAE,OAAM,SAASP,IAAE;AAAC,UAAG,KAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,KAAK,QAAM,GAAE,KAAK,OAAK,OAAG,KAAK,WAAS,MAAK,KAAK,SAAO,QAAO,KAAK,MAAI,GAAE,KAAK,WAAW,QAAQiB,EAAC,GAAE,CAACjB,GAAE,UAAQC,MAAK,KAAK,SAAMA,GAAE,OAAO,CAAC,KAAG,EAAE,KAAK,MAAKA,EAAC,KAAG,CAAC,MAAM,CAACA,GAAE,MAAM,CAAC,CAAC,MAAI,KAAKA,EAAC,IAAE;AAAA,IAAE,GAAE,MAAK,WAAU;AAAC,WAAK,OAAK;AAAG,UAAID,KAAE,KAAK,WAAW,CAAC,EAAE;AAAW,UAAG,YAAUA,GAAE,KAAK,OAAMA,GAAE;AAAI,aAAO,KAAK;AAAA,IAAI,GAAE,mBAAkB,SAASA,IAAE;AAAC,UAAG,KAAK,KAAK,OAAMA;AAAE,UAAIC,KAAE;AAAK,eAASC,GAAEA,IAAEE,IAAE;AAAC,eAAOM,GAAE,OAAK,SAAQA,GAAE,MAAIV,IAAEC,GAAE,OAAKC,IAAEE,OAAIH,GAAE,SAAO,QAAOA,GAAE,MAAI,IAAG,CAAC,CAACG;AAAA,MAAC;AAAC,eAAQA,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,YAAIC,KAAE,KAAK,WAAWD,EAAC,GAAEM,KAAEL,GAAE;AAAW,YAAG,WAASA,GAAE,OAAO,QAAOH,GAAE,KAAK;AAAE,YAAGG,GAAE,UAAQ,KAAK,MAAK;AAAC,cAAIM,KAAE,EAAE,KAAKN,IAAE,UAAU,GAAEO,KAAE,EAAE,KAAKP,IAAE,YAAY;AAAE,cAAGM,MAAGC,IAAE;AAAC,gBAAG,KAAK,OAAKP,GAAE,SAAS,QAAOH,GAAEG,GAAE,UAAS,IAAE;AAAE,gBAAG,KAAK,OAAKA,GAAE,WAAW,QAAOH,GAAEG,GAAE,UAAU;AAAA,UAAC,WAASM,IAAE;AAAC,gBAAG,KAAK,OAAKN,GAAE,SAAS,QAAOH,GAAEG,GAAE,UAAS,IAAE;AAAA,UAAC,OAAK;AAAC,gBAAG,CAACO,GAAE,OAAM,MAAM,wCAAwC;AAAE,gBAAG,KAAK,OAAKP,GAAE,WAAW,QAAOH,GAAEG,GAAE,UAAU;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,GAAE,QAAO,SAASL,IAAEC,IAAE;AAAC,eAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,YAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,YAAGC,GAAE,UAAQ,KAAK,QAAM,EAAE,KAAKA,IAAE,YAAY,KAAG,KAAK,OAAKA,GAAE,YAAW;AAAC,cAAIC,KAAED;AAAE;AAAA,QAAK;AAAA,MAAC;AAAC,MAAAC,OAAI,YAAUJ,MAAG,eAAaA,OAAII,GAAE,UAAQH,MAAGA,MAAGG,GAAE,eAAaA,KAAE;AAAM,UAAIC,KAAED,KAAEA,GAAE,aAAW,CAAC;AAAE,aAAOC,GAAE,OAAKL,IAAEK,GAAE,MAAIJ,IAAEG,MAAG,KAAK,SAAO,QAAO,KAAK,OAAKA,GAAE,YAAW,KAAG,KAAK,SAASC,EAAC;AAAA,IAAC,GAAE,UAAS,SAASL,IAAEC,IAAE;AAAC,UAAG,YAAUD,GAAE,KAAK,OAAMA,GAAE;AAAI,aAAM,YAAUA,GAAE,QAAM,eAAaA,GAAE,OAAK,KAAK,OAAKA,GAAE,MAAI,aAAWA,GAAE,QAAM,KAAK,OAAK,KAAK,MAAIA,GAAE,KAAI,KAAK,SAAO,UAAS,KAAK,OAAK,SAAO,aAAWA,GAAE,QAAMC,OAAI,KAAK,OAAKA,KAAG;AAAA,IAAC,GAAE,QAAO,SAASD,IAAE;AAAC,eAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,YAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,YAAGC,GAAE,eAAaF,GAAE,QAAO,KAAK,SAASE,GAAE,YAAWA,GAAE,QAAQ,GAAEe,GAAEf,EAAC,GAAE;AAAA,MAAC;AAAA,IAAC,GAAE,OAAM,SAASF,IAAE;AAAC,eAAQC,KAAE,KAAK,WAAW,SAAO,GAAEA,MAAG,GAAE,EAAEA,IAAE;AAAC,YAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,YAAGC,GAAE,WAASF,IAAE;AAAC,cAAIG,KAAED,GAAE;AAAW,cAAG,YAAUC,GAAE,MAAK;AAAC,gBAAIC,KAAED,GAAE;AAAI,YAAAc,GAAEf,EAAC;AAAA,UAAC;AAAC,iBAAOE;AAAA,QAAC;AAAA,MAAC;AAAC,YAAM,MAAM,uBAAuB;AAAA,IAAC,GAAE,eAAc,SAASJ,IAAEC,IAAEC,IAAE;AAAC,aAAO,KAAK,WAAS,EAAC,UAASO,GAAET,EAAC,GAAE,YAAWC,IAAE,SAAQC,GAAC,GAAE,WAAS,KAAK,WAAS,KAAK,MAAI,IAAG;AAAA,IAAC,EAAC,GAAE;AAAA,EAAC;AAAC,IAAE,UAAQ,GAAE,EAAE,QAAQ,aAAW,MAAG,EAAE,QAAQ,UAAQ,EAAE;AAAO,EAAE,CAAC;AAAE,IAAI,IAAE,EAAE,QAAQ;AAAhB,IAAkB,IAAE;AAAE,IAAG;AAAC,uBAAmB;AAAC,SAAO,GAAE;AAAC,cAAU,OAAO,aAAW,WAAW,qBAAmB,IAAE,SAAS,KAAI,wBAAwB,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,SAASF,IAAE;AAAC,QAAG,MAAM,QAAQA,EAAC,EAAE,QAAOA;AAAA,EAAC,EAAE,CAAC,KAAG,SAASA,IAAEC,IAAE;AAAC,QAAI,IAAE,QAAMD,KAAE,OAAK,eAAa,OAAO,UAAQA,GAAE,OAAO,QAAQ,KAAGA,GAAE,YAAY;AAAE,QAAG,QAAM,GAAE;AAAC,UAAI,GAAE,GAAE,GAAE,GAAE,IAAE,CAAC,GAAE,IAAE,MAAG,IAAE;AAAG,UAAG;AAAC,YAAG,KAAG,IAAE,EAAE,KAAKA,EAAC,GAAG,MAAK,MAAIC,IAAE;AAAC,cAAG,OAAO,CAAC,MAAI,EAAE;AAAO,cAAE;AAAA,QAAE,MAAM,QAAK,EAAE,KAAG,IAAE,EAAE,KAAK,CAAC,GAAG,UAAQ,EAAE,KAAK,EAAE,KAAK,GAAE,EAAE,WAASA,KAAG,IAAE,KAAG;AAAA,MAAC,SAAOD,IAAE;AAAC,YAAE,MAAG,IAAEA;AAAA,MAAC,UAAC;AAAQ,YAAG;AAAC,cAAG,CAAC,KAAG,QAAM,EAAE,WAAS,IAAE,EAAE,OAAO,GAAE,OAAO,CAAC,MAAI,GAAG;AAAA,QAAM,UAAC;AAAQ,cAAG,EAAE,OAAM;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO;AAAA,IAAC;AAAA,EAAC,EAAE,GAAE,CAAC,KAAG,EAAE,GAAE,CAAC,KAAG,WAAU;AAAC,UAAM,IAAI,UAAU,2IAA2I;AAAA,EAAC,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,MAAI,IAAE,SAASA,IAAEC,IAAE;AAAC,QAAG,YAAU,EAAED,EAAC,KAAG,CAACA,GAAE,QAAOA;AAAE,QAAI,IAAEA,GAAE,OAAO,WAAW;AAAE,QAAG,WAAS,GAAE;AAAC,UAAI,IAAE,EAAE,KAAKA,IAAEC,MAAG,SAAS;AAAE,UAAG,YAAU,EAAE,CAAC,EAAE,QAAO;AAAE,YAAM,IAAI,UAAU,8CAA8C;AAAA,IAAC;AAAC,YAAO,aAAWA,KAAE,SAAO,QAAQD,EAAC;AAAA,EAAC,EAAE,GAAE,QAAQ;AAAE,SAAM,YAAU,EAAE,CAAC,IAAE,IAAE,IAAE;AAAE;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,UAAO,IAAE,EAAE,CAAC,MAAK,IAAE,OAAO,eAAe,GAAE,GAAE,EAAC,OAAM,GAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAE,EAAE,CAAC,IAAE,GAAE;AAAC;AAAC,IAAI,IAAE,gBAAE,EAAC,MAAK,cAAa,OAAM,EAAC,MAAK,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,KAAI,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,YAAW,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,OAAM,CAAC,QAAQ,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,SAAG,WAAU;AAAC,WAAM,EAAC,OAAM,GAAG,OAAO,EAAE,MAAK,IAAI,GAAE,QAAO,GAAG,OAAO,EAAE,MAAK,IAAI,GAAE,YAAW,OAAO,OAAO,EAAE,KAAI,cAAc,EAAC;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,iBAAgB,GAAE,aAAY,SAAG,WAAU;AAAC,WAAM,EAAC,KAAI,GAAG,QAAQ,MAAI,EAAE,SAAO,MAAI,EAAE,OAAK,GAAE,IAAI,GAAE,MAAK,GAAG,OAAO,EAAE,aAAW,EAAE,OAAK,MAAI,GAAE,IAAI,GAAE,OAAM,GAAG,OAAO,IAAG,IAAI,GAAE,QAAO,GAAG,OAAO,IAAG,IAAI,EAAC;AAAA,EAAC,CAAE,GAAE,UAAS,SAAS,GAAE;AAAC,QAAIE,KAAE,EAAE,OAAO,sBAAsB,GAAEE,KAAEF,GAAE,MAAK,IAAEA,GAAE,KAAI,IAAE,SAASD,IAAE;AAAC,UAAIC,IAAEI;AAAE,MAAAL,cAAa,cAAYC,KAAED,GAAE,SAAQK,KAAEL,GAAE,WAASA,cAAa,eAAaC,KAAED,GAAE,QAAQ,CAAC,EAAE,SAAQK,KAAEL,GAAE,QAAQ,CAAC,EAAE;AAAS,UAAIS,KAAER,KAAEE,IAAE,IAAEE,KAAE;AAAE,MAAAI,KAAE,MAAIA,KAAE,IAAG,IAAE,MAAI,IAAE,IAAGA,KAAE,EAAE,SAAOA,KAAE,EAAE,OAAM,IAAE,EAAE,SAAO,IAAE,EAAE;AAAM,UAAI,IAAEA,KAAE,EAAE,OAAK,KAAI,IAAE,MAAI,IAAE,EAAE,OAAK;AAAI,QAAE,UAAS,GAAE,CAAC;AAAA,IAAC;AAAE,MAAE,CAAC;AAAE,QAAI,IAAE,WAAU;AAAC,eAAS,oBAAoB,aAAY,CAAC,GAAE,SAAS,oBAAoB,WAAU,CAAC,GAAE,SAAS,oBAAoB,aAAY,CAAC,GAAE,SAAS,oBAAoB,YAAW,CAAC;AAAA,IAAC;AAAE,MAAE,CAAC,GAAE,aAAa,eAAa,SAAS,iBAAiB,aAAY,CAAC,GAAE,SAAS,iBAAiB,WAAU,CAAC,IAAG,aAAa,eAAa,EAAE,eAAe,GAAE,SAAS,iBAAiB,aAAY,GAAE,EAAC,SAAQ,MAAE,CAAC,GAAE,SAAS,iBAAiB,YAAW,CAAC;AAAA,EAAE,EAAC;AAAC,EAAC,CAAC;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE;AAAS,MAAG,KAAG,eAAa,OAAO,UAAS;AAAC,QAAI,IAAE,SAAS,QAAM,SAAS,qBAAqB,MAAM,EAAE,CAAC,GAAE,IAAE,SAAS,cAAc,OAAO;AAAE,MAAE,OAAK,YAAW,UAAQ,KAAG,EAAE,aAAW,EAAE,aAAa,GAAE,EAAE,UAAU,IAAE,EAAE,YAAY,CAAC,GAAE,EAAE,aAAW,EAAE,WAAW,UAAQ,IAAE,EAAE,YAAY,SAAS,eAAe,CAAC,CAAC;AAAA,EAAC;AAAC;AAAC,EAAE,ihBAAihB,GAAE,EAAE,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,cAAa,OAAM,eAAE,EAAE,eAAe,GAAE,aAAY,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,SAAS,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,IAAG,cAAa,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,SAAS,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,GAAE,GAAE,CAAC,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,gBAAE,OAAM,EAAC,OAAM,mBAAkB,GAAE,MAAK,EAAE,IAAG,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,gBAAE,OAAM,EAAC,OAAM,mBAAkB,GAAE,MAAK,EAAE,IAAG,gBAAE,OAAM,EAAC,OAAM,UAAS,OAAM,eAAE,EAAE,WAAW,EAAC,GAAE,MAAK,CAAC,CAAC,GAAE,EAAE;AAAC,GAAE,EAAE,YAAU,mBAAkB,EAAE,SAAO;AAA4B,IAAI,IAAE,gBAAE,EAAC,MAAK,OAAM,OAAM,EAAC,OAAM,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,QAAO,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,KAAI,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,OAAM,CAAC,QAAQ,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,SAAG,WAAU;AAAC,WAAM,EAAC,KAAI,GAAG,QAAQ,IAAE,EAAE,MAAI,OAAK,EAAE,SAAO,GAAE,IAAI,GAAE,QAAO,GAAG,OAAO,GAAE,IAAI,EAAC;AAAA,EAAC,CAAE,GAAE,IAAE,IAAE;AAAE,YAAG,WAAU;AAAC,KAAC,WAAU;AAAC,UAAG,EAAE,OAAM;AAAC,UAAE,MAAM,QAAM,EAAE,OAAM,EAAE,MAAM,SAAO,EAAE;AAAO,YAAI,IAAE,EAAE,MAAM,WAAW,IAAI;AAAE,YAAG,GAAE;AAAC,cAAIR,KAAE,EAAE,qBAAqB,GAAE,GAAE,GAAE,EAAE,MAAM;AAAE,UAAAA,GAAE,aAAa,GAAE,SAAS,GAAEA,GAAE,aAAa,MAAI,SAAS,GAAEA,GAAE,aAAa,MAAI,SAAS,GAAEA,GAAE,aAAa,MAAI,SAAS,GAAEA,GAAE,aAAa,MAAI,SAAS,GAAEA,GAAE,aAAa,OAAI,GAAE,SAAS,GAAEA,GAAE,aAAa,GAAE,SAAS,GAAE,EAAE,YAAUA,IAAE,EAAE,SAAS,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,EAAE;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,QAAO,GAAE,aAAY,GAAE,UAAS,SAAS,GAAE;AAAC,QAAIA,KAAE,EAAE,OAAO,sBAAsB,EAAE,KAAIE,KAAE,SAASH,IAAE;AAAC,UAAIG;AAAE,MAAAH,cAAa,aAAWG,KAAEH,GAAE,UAAQA,cAAa,eAAaG,KAAEH,GAAE,QAAQ,CAAC,EAAE;AAAS,UAAII,KAAED,KAAEF;AAAE,MAAAG,KAAE,MAAIA,KAAE,IAAGA,KAAE,EAAE,WAASA,KAAE,EAAE;AAAQ,UAAI,IAAE,OAAKA,KAAE,EAAE,SAAO;AAAI,QAAE,UAAS,MAAI,IAAE,GAAG;AAAA,IAAC,GAAEA,KAAE,WAAU;AAAC,eAAS,oBAAoB,aAAYD,EAAC,GAAE,SAAS,oBAAoB,WAAUC,EAAC,GAAE,SAAS,oBAAoB,aAAYD,EAAC,GAAE,SAAS,oBAAoB,YAAWC,EAAC;AAAA,IAAC;AAAE,IAAAD,GAAE,CAAC,GAAE,aAAa,eAAa,SAAS,iBAAiB,aAAYA,EAAC,GAAE,SAAS,iBAAiB,WAAUC,EAAC,IAAG,aAAa,eAAa,EAAE,eAAe,GAAE,SAAS,iBAAiB,aAAYD,IAAE,EAAC,SAAQ,MAAE,CAAC,GAAE,SAAS,iBAAiB,YAAWC,EAAC;AAAA,EAAE,EAAC;AAAC,EAAC,CAAC;AAA/5C,IAAi6C,IAAE,EAAC,KAAI,SAAQ;AAAE,EAAE,2MAA2M,GAAE,EAAE,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,OAAM,aAAY,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,SAAS,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,IAAG,cAAa,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,SAAS,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,GAAE,GAAE,CAAC,gBAAE,UAAS,GAAE,MAAK,GAAG,GAAE,gBAAE,OAAM,EAAC,OAAM,UAAS,OAAM,eAAE,EAAE,WAAW,EAAC,GAAE,MAAK,CAAC,CAAC,GAAE,EAAE;AAAC,GAAE,EAAE,YAAU,mBAAkB,EAAE,SAAO;AAAqB,IAAI,IAAE,gBAAE,EAAC,MAAK,SAAQ,OAAM,EAAC,OAAM,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,QAAO,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,UAAS,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,EAAC,EAAC,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,SAAG,WAAU;AAAC,WAAM,EAAC,KAAI,GAAG,OAAO,EAAE,QAAM,EAAE,SAAO,GAAE,IAAI,GAAE,QAAO,GAAG,OAAO,GAAE,IAAI,EAAC;AAAA,EAAC,CAAE,GAAE,IAAE,IAAE,GAAE,IAAE,WAAU;AAAC,QAAI,IAAE,EAAE,MAAM,WAAW,IAAI;AAAE,MAAE,MAAM,QAAM,EAAE,OAAM,EAAE,MAAM,SAAO,EAAE;AAAO,QAAIH,KAAE,SAASF,IAAE;AAAC,UAAIC,KAAE,SAAS,cAAc,QAAQ,GAAEC,KAAED,GAAE,WAAW,IAAI,GAAEE,KAAE,IAAEH;AAAE,aAAOC,GAAE,QAAME,IAAEF,GAAE,SAAOE,IAAED,GAAE,YAAU,WAAUA,GAAE,SAAS,GAAE,GAAEC,IAAEA,EAAC,GAAED,GAAE,YAAU,WAAUA,GAAE,SAAS,GAAE,GAAEF,IAAEA,EAAC,GAAEE,GAAE,SAASF,IAAEA,IAAEA,IAAEA,EAAC,GAAEC;AAAA,IAAC,EAAE,CAAC;AAAE,MAAE,YAAU,EAAE,cAAcC,IAAE,QAAQ,GAAE,EAAE,SAAS,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM;AAAE,QAAIC,KAAE,EAAE,qBAAqB,GAAE,GAAE,GAAE,EAAE,MAAM;AAAE,IAAAA,GAAE,aAAa,MAAI,qBAAqB,GAAEA,GAAE,aAAa,MAAI,EAAE,KAAK,GAAE,EAAE,YAAUA,IAAE,EAAE,SAAS,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM;AAAA,EAAC;AAAE,QAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAK,GAAI,WAAU;AAAC,MAAE;AAAA,EAAC,CAAE,GAAE,UAAG,WAAU;AAAC,MAAE;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,QAAO,GAAE,aAAY,GAAE,UAAS,SAAS,GAAE;AAAC,QAAID,KAAE,EAAE,OAAO,sBAAsB,EAAE,KAAIE,KAAE,SAASH,IAAE;AAAC,UAAIG;AAAE,MAAAH,cAAa,aAAWG,KAAEH,GAAE,UAAQA,cAAa,eAAaG,KAAEH,GAAE,QAAQ,CAAC,EAAE;AAAS,UAAII,KAAED,KAAEF;AAAE,MAAAG,KAAE,MAAIA,KAAE,IAAGA,KAAE,EAAE,WAASA,KAAE,EAAE;AAAQ,UAAIC,KAAE,YAAYD,KAAE,EAAE,QAAQ,QAAQ,CAAC,CAAC;AAAE,QAAE,UAASC,EAAC;AAAA,IAAC,GAAED,KAAE,WAAU;AAAC,eAAS,oBAAoB,aAAYD,EAAC,GAAE,SAAS,oBAAoB,WAAUC,EAAC,GAAE,SAAS,oBAAoB,aAAYD,EAAC,GAAE,SAAS,oBAAoB,YAAWC,EAAC;AAAA,IAAC;AAAE,IAAAD,GAAE,CAAC,GAAE,aAAa,eAAa,SAAS,iBAAiB,aAAYA,EAAC,GAAE,SAAS,iBAAiB,WAAUC,EAAC,IAAG,aAAa,eAAa,EAAE,eAAe,GAAE,SAAS,iBAAiB,aAAYD,IAAE,EAAC,SAAQ,MAAE,CAAC,GAAE,SAAS,iBAAiB,YAAWC,EAAC;AAAA,EAAE,EAAC;AAAC,EAAC,CAAC;AAAxnD,IAA0nD,IAAE,EAAC,KAAI,SAAQ;AAAE,EAAE,6MAA6M,GAAE,EAAE,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,SAAQ,aAAY,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,SAAS,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,IAAG,cAAa,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,YAAU,EAAE,SAAS,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,GAAE,GAAE,CAAC,gBAAE,UAAS,GAAE,MAAK,GAAG,GAAE,gBAAE,OAAM,EAAC,OAAM,UAAS,OAAM,eAAE,EAAE,WAAW,EAAC,GAAE,MAAK,CAAC,CAAC,GAAE,EAAE;AAAC,GAAE,EAAE,YAAU,mBAAkB,EAAE,SAAO;AAAuB,IAAI,IAAE,EAAC,KAAI,QAAO,KAAI,OAAM,KAAI,QAAO,KAAI,OAAM;AAAjD,IAAmD,IAAE,EAAC,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,MAAK;AAA7F,IAA+F,IAAE,EAAC,KAAI,OAAM,MAAK,OAAM,KAAI,OAAM,KAAI,OAAM,MAAK,OAAM,KAAI,OAAM,MAAK,MAAK;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,MAAI,IAAE,OAAO,KAAK,CAAC;AAAE,MAAG,OAAO,uBAAsB;AAAC,QAAI,IAAE,OAAO,sBAAsB,CAAC;AAAE,UAAI,IAAE,EAAE,OAAQ,SAASJ,IAAE;AAAC,aAAO,OAAO,yBAAyB,GAAEA,EAAC,EAAE;AAAA,IAAU,CAAE,IAAG,EAAE,KAAK,MAAM,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,QAAI,IAAE,QAAM,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,QAAE,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,EAAE,QAAS,SAASA,IAAE;AAAC,QAAE,GAAEA,IAAE,EAAEA,EAAC,CAAC;AAAA,IAAC,CAAE,IAAE,OAAO,4BAA0B,OAAO,iBAAiB,GAAE,OAAO,0BAA0B,CAAC,CAAC,IAAE,EAAE,OAAO,CAAC,CAAC,EAAE,QAAS,SAASA,IAAE;AAAC,aAAO,eAAe,GAAEA,IAAE,OAAO,yBAAyB,GAAEA,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,IAAI,IAAE;AAAN,IAAW,KAAG,SAAS,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,IAAE,CAAC,CAAC,GAAE,IAAE,KAAG,CAAC,GAAE,IAAE,EAAE,WAAU,IAAE,EAAE,cAAa,IAAE,EAAC,UAAS,EAAE,YAAU,YAAW,WAAU,KAAG,QAAO,eAAc,WAAU;AAAC,MAAE,OAAO;AAAA,EAAC,GAAE,WAAU,CAAC,EAAC,MAAK,UAAS,SAAQ,EAAC,QAAO,CAAC,GAAE,CAAC,EAAC,EAAC,GAAE,EAAC,MAAK,iBAAgB,SAAQ,EAAC,iBAAgB,OAAG,UAAS,KAAE,EAAC,GAAE,EAAC,MAAK,QAAO,SAAQ,EAAC,uBAAsB,CAAC,OAAM,QAAQ,EAAC,EAAC,GAAE,EAAC,MAAK,eAAc,SAAQ,MAAE,GAAE,EAAC,MAAK,eAAc,SAAQ,MAAG,OAAM,SAAQ,UAAS,CAAC,eAAe,GAAE,IAAG,SAASD,IAAE;AAAC,QAAIC,KAAED,GAAE,OAAME,KAAED,GAAE,QAAOG,KAAEH,GAAE,WAAUI,KAAEH,GAAE;AAAO,MAAE,QAAM,EAAE,EAAE,EAAE,CAAC,GAAEG,EAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAC,iBAAgB,UAAQD,KAAE,kBAAgB,aAAY,CAAC;AAAA,EAAC,EAAC,CAAC,EAAC;AAAE,SAAO,MAAG,WAAU;AAAC,WAAM,CAAC,MAAE,CAAC,GAAE,MAAE,CAAC,CAAC;AAAA,EAAC,GAAI,SAASJ,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,EAAEH,IAAE,CAAC,GAAEI,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEG,KAAE,EAAEL,IAAE,CAAC,GAAE,IAAEK,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC;AAAE,QAAGF,MAAGC,OAAI,MAAID,MAAG,MAAI,IAAG;AAAC,gBAAQF,KAAE,MAAI,WAASA,MAAGA,GAAE,QAAQ;AAAE,UAAI,IAAEE,GAAE,OAAKA,IAAE,IAAEC,GAAE,OAAKA;AAAE,eAAG,WAAU;AAAC,YAAEa,cAAE,GAAE,GAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,gBAAG,WAAU;AAAC,QAAIlB;AAAE,UAAI,UAAQA,KAAE,MAAI,WAASA,MAAGA,GAAE,QAAQ,GAAE,IAAE;AAAA,EAAK,CAAE,GAAE,EAAC,UAAS,GAAE,OAAM,EAAC;AAAC;AAAj6B,IAAm6B,KAAG,gBAAE,EAAC,OAAM,EAAC,OAAM,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,WAAU,EAAC,MAAK,QAAO,GAAE,SAAQ,EAAC,MAAK,CAAC,SAAQ,KAAK,EAAC,EAAC,GAAE,OAAM,CAAC,QAAQ,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,IAAE,IAAI,GAAE,IAAE,IAAE,IAAI,GAAE,IAAE,IAAE,KAAE,GAAE,IAAE,GAAG,GAAE,GAAE,EAAC,UAAS,SAAQ,cAAa,EAAC,QAAO,EAAC,EAAC,CAAC,EAAE,OAAM,IAAE,SAAG,WAAU;AAAC,WAAO,MAAM,QAAQ,EAAE,OAAO,KAAG,EAAE,QAAQ,SAAO;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,QAAI,IAAE,EAAE,SAAQE,KAAE,EAAE,WAAUC,KAAE,EAAE;AAAM,WAAO,MAAM,QAAQ,CAAC,IAAED,KAAE,EAAE,IAAK,SAASF,IAAE;AAAC,aAAO,EAAEA,EAAC;AAAA,IAAC,CAAE,EAAE,OAAQ,SAASA,IAAE;AAAC,aAAM,CAACA,GAAE,SAASG,EAAC;AAAA,IAAC,CAAE,IAAE,EAAE,IAAK,SAASH,IAAE;AAAC,aAAO,EAAEA,EAAC;AAAA,IAAC,CAAE,EAAE,OAAQ,SAASA,IAAE;AAAC,aAAM,CAACA,GAAE,SAASG,EAAC;AAAA,IAAC,CAAE,IAAE,CAAC;AAAA,EAAC,CAAE,GAAE,IAAE,SAASH,IAAE;AAAC,QAAI,GAAEE,IAAEC,KAAEH,GAAE;AAAO,MAAE,UAAQ,IAAE,MAAE,CAAC,MAAI,WAAS,KAAG,EAAE,YAAYG,EAAC,OAAK,UAAQD,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,SAASC,EAAC,OAAK,EAAE,QAAM;AAAA,EAAG;AAAE,SAAO,UAAG,WAAU;AAAC,aAAS,iBAAiB,WAAU,GAAE,KAAE;AAAA,EAAC,CAAE,GAAE,YAAG,WAAU;AAAC,aAAS,oBAAoB,WAAU,GAAE,KAAE;AAAA,EAAC,CAAE,GAAE,EAAC,WAAU,GAAE,aAAY,GAAE,eAAc,GAAE,gBAAe,GAAE,cAAa,GAAE,eAAc,GAAE,QAAO,WAAU;AAAC,MAAE,UAAQ,EAAE,QAAM;AAAA,EAAG,GAAE,gBAAe,SAASH,IAAE;AAAC,MAAE,UAAS,EAAEA,EAAC,CAAC;AAAA,EAAC,EAAC;AAAC,EAAC,CAAC;AAAx5D,IAA05D,KAAG,EAAC,OAAM,SAAQ;AAA56D,IAA86D,KAAG,EAAC,KAAI,GAAE,OAAM,QAAO;AAAr8D,IAAu8D,KAAG,CAAC,SAAS;AAAE,EAAE,iiDAAiiD,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,IAAG,CAAC,gBAAE,OAAM,EAAC,OAAM,SAAQ,KAAI,aAAY,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,WAAU;AAAC,WAAO,EAAE,UAAQ,EAAE,OAAO,MAAM,GAAE,SAAS;AAAA,EAAC,GAAE,GAAE,CAAC,gBAAE,QAAO,MAAK,gBAAE,EAAE,KAAK,GAAE,CAAC,GAAE,EAAE,gBAAc,UAAE,GAAE,mBAAE,OAAM,EAAE,KAAG,mBAAE,QAAO,IAAE,CAAC,GAAE,GAAG,GAAE,YAAE,YAAE,MAAK,EAAC,SAAQ,QAAG,WAAU;AAAC,WAAM,CAAC,EAAE,kBAAgB,UAAE,GAAE,mBAAE,OAAM,EAAC,KAAI,GAAE,OAAM,YAAW,KAAI,eAAc,OAAM,eAAE,EAAE,aAAa,EAAC,GAAE,EAAE,UAAE,IAAE,GAAE,mBAAE,UAAE,MAAK,WAAE,EAAE,eAAe,SAASC,IAAE;AAAC,aAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,iBAAgB,KAAIA,IAAE,SAAQ,SAAS,GAAE;AAAC,eAAO,EAAE,eAAeA,EAAC;AAAA,MAAC,EAAC,GAAE,gBAAEA,EAAC,GAAE,GAAE,EAAE;AAAA,IAAC,CAAE,GAAE,GAAG,EAAE,GAAE,CAAC,KAAG,mBAAE,QAAO,IAAE,CAAC;AAAA,EAAC,CAAE,GAAE,GAAE,EAAC,CAAC,CAAC,CAAC;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;AAAyC,IAAI,KAAG,gBAAE,EAAC,MAAK,SAAQ,YAAW,EAAC,aAAY,GAAE,GAAE,OAAM,EAAC,QAAO,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,OAAM,GAAE,WAAU,EAAC,MAAK,QAAO,GAAE,eAAc,EAAC,MAAK,CAAC,SAAQ,KAAK,EAAC,EAAC,GAAE,OAAM,CAAC,UAAS,SAAQ,QAAO,SAAQ,cAAc,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE;AAAK,SAAM,EAAC,SAAQ,SAASD,IAAE;AAAC,QAAI;AAAE,MAAE,UAAS,UAAQ,IAAEA,GAAE,WAAS,WAAS,IAAE,SAAO,EAAE,KAAK;AAAA,EAAC,GAAE,YAAW,SAAG,WAAU;AAAC,WAAM,EAAC,UAAS,GAAG,OAAO,EAAE,OAAM,IAAI,GAAE,UAAS,GAAG,OAAO,EAAE,OAAM,IAAI,GAAE,OAAM,GAAG,OAAO,EAAE,OAAM,IAAI,EAAC;AAAA,EAAC,CAAE,GAAE,SAAQ,WAAU;AAAC,MAAE,OAAO;AAAA,EAAC,GAAE,QAAO,WAAU;AAAC,MAAE,MAAM;AAAA,EAAC,GAAE,SAAQ,WAAU;AAAC,MAAE,OAAO;AAAA,EAAC,GAAE,gBAAe,SAASA,IAAE;AAAC,MAAE,gBAAeA,EAAC;AAAA,EAAC,EAAC;AAAC,EAAC,CAAC;AAAtpB,IAAwpB,KAAG,EAAC,OAAM,QAAO;AAAzqB,IAA2qB,KAAG,CAAC,QAAQ;AAAE,SAAS,GAAG,GAAE,GAAE;AAAC,MAAI,IAAE,OAAO,KAAK,CAAC;AAAE,MAAG,OAAO,uBAAsB;AAAC,QAAI,IAAE,OAAO,sBAAsB,CAAC;AAAE,UAAI,IAAE,EAAE,OAAQ,SAASC,IAAE;AAAC,aAAO,OAAO,yBAAyB,GAAEA,EAAC,EAAE;AAAA,IAAU,CAAE,IAAG,EAAE,KAAK,MAAM,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,QAAI,IAAE,QAAM,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,QAAE,IAAE,GAAG,OAAO,CAAC,GAAE,IAAE,EAAE,QAAS,SAASA,IAAE;AAAC,QAAE,GAAEA,IAAE,EAAEA,EAAC,CAAC;AAAA,IAAC,CAAE,IAAE,OAAO,4BAA0B,OAAO,iBAAiB,GAAE,OAAO,0BAA0B,CAAC,CAAC,IAAE,GAAG,OAAO,CAAC,CAAC,EAAE,QAAS,SAASA,IAAE;AAAC,aAAO,eAAe,GAAEA,IAAE,OAAO,yBAAyB,GAAEA,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,EAAE,4WAA4W,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,iBAAE,aAAa;AAAE,SAAO,UAAE,GAAE,mBAAE,OAAM,IAAG,CAAC,YAAE,GAAE,EAAC,OAAM,EAAE,QAAO,WAAU,EAAE,WAAU,SAAQ,EAAE,eAAc,UAAS,EAAE,eAAc,GAAE,MAAK,GAAE,CAAC,SAAQ,aAAY,WAAU,UAAU,CAAC,GAAE,gBAAE,SAAQ,EAAC,OAAM,SAAQ,OAAM,eAAE,EAAE,UAAU,GAAE,UAAS,EAAE,OAAM,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,WAAU;AAAC,WAAO,EAAE,WAAS,EAAE,QAAQ,MAAM,GAAE,SAAS;AAAA,EAAC,IAAG,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,WAAU;AAAC,WAAO,EAAE,WAAS,EAAE,QAAQ,MAAM,GAAE,SAAS;AAAA,EAAC,IAAG,QAAO,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,WAAU;AAAC,WAAO,EAAE,UAAQ,EAAE,OAAO,MAAM,GAAE,SAAS;AAAA,EAAC,IAAG,WAAU,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,SAAG,WAAU;AAAC,WAAO,EAAE,WAAS,EAAE,QAAQ,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,OAAO,CAAC,GAAE,GAAE,MAAK,IAAG,EAAE,CAAC,CAAC;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;AAAwC,IAAI,KAAG,SAAS,GAAE,GAAE,GAAE;AAAC,SAAM,CAAC,GAAE,IAAE,MAAI,KAAG,IAAE,KAAG,KAAG,IAAE,IAAE,IAAE,MAAI,GAAE,IAAE,CAAC;AAAC;AAAjE,IAAmE,KAAG,EAAC,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,IAAG;AAAhH,IAAkH,KAAG,SAAS,GAAE;AAAC,MAAE,KAAK,IAAI,KAAK,MAAM,CAAC,GAAE,GAAG;AAAE,MAAI,IAAE,KAAK,MAAM,IAAE,EAAE,GAAE,IAAE,IAAE;AAAG,SAAM,GAAG,OAAO,GAAG,CAAC,KAAG,CAAC,EAAE,OAAO,GAAG,CAAC,KAAG,CAAC;AAAC;AAAvO,IAAyO,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,SAAO,MAAM,CAAC,KAAG,MAAM,CAAC,KAAG,MAAM,CAAC,IAAE,KAAG,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC;AAAC;AAAlW,IAAoW,KAAG,SAAS,GAAE,GAAE;AAAC,MAAI;AAAE,cAAU,QAAO,IAAE,MAAI,OAAK,EAAE,QAAQ,GAAG,KAAG,MAAI,WAAW,CAAC,MAAI,IAAE;AAAQ,MAAI,IAAE,SAASD,IAAE;AAAC,WAAM,YAAU,OAAOA,MAAG,OAAKA,GAAE,QAAQ,GAAG;AAAA,EAAC,EAAE,CAAC;AAAE,SAAO,IAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAE,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAE,MAAI,IAAE,SAAS,GAAG,OAAO,IAAE,CAAC,GAAE,EAAE,IAAE,MAAK,KAAK,IAAI,IAAE,CAAC,IAAE,OAAK,IAAE,IAAE,IAAE,WAAW,CAAC;AAAC;AAAhpB,IAAkpB,KAAG,SAAS,GAAE,GAAE,GAAE;AAAC,MAAE,IAAE,GAAG,GAAE,GAAG,GAAE,IAAE,GAAG,GAAE,GAAG,GAAE,IAAE,GAAG,GAAE,GAAG;AAAE,MAAI,IAAE,KAAK,MAAM,CAAC,GAAE,IAAE,IAAE,GAAE,IAAE,KAAG,IAAE,IAAG,IAAE,KAAG,IAAE,IAAE,IAAG,IAAE,KAAG,KAAG,IAAE,KAAG,IAAG,IAAE,IAAE,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,EAAE,CAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,EAAE,CAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,EAAE,CAAC;AAAE,SAAM,EAAC,GAAE,KAAK,MAAM,MAAI,CAAC,GAAE,GAAE,KAAK,MAAM,MAAI,CAAC,GAAE,GAAE,KAAK,MAAM,MAAI,CAAC,EAAC;AAAC;AAA74B,IAA+4B,KAAG,SAAS,GAAE,GAAE,GAAE;AAAC,MAAI,GAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,MAAG,EAAE,SAAO,CAAC,OAAM,OAAM,KAAK,EAAE,SAAS,CAAC,MAAI,KAAG,KAAG,IAAE,GAAG,SAAS,EAAE,MAAM,kBAAkB,MAAI,EAAE,CAAC,EAAE,SAAO,IAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAE,IAAG,GAAE;AAAA,IAAC,KAAI;AAAM,UAAI,IAAE,GAAG,GAAE,IAAE,KAAI,IAAE,GAAG;AAAE,aAAM,QAAQ,OAAO,EAAE,QAAQ,CAAC,GAAE,IAAI,EAAE,OAAO,KAAK,MAAM,MAAI,EAAE,CAAC,CAAC,GAAE,KAAK,EAAE,OAAO,KAAK,MAAM,MAAI,EAAE,CAAC,CAAC,GAAE,KAAK,EAAE,OAAO,GAAE,GAAG;AAAA,IAAE,KAAI;AAAM,aAAM,QAAQ,OAAO,EAAE,QAAQ,CAAC,GAAE,IAAI,EAAE,OAAO,KAAK,MAAM,CAAC,GAAE,KAAK,EAAE,OAAO,KAAK,MAAM,CAAC,GAAE,KAAK,EAAE,OAAO,GAAE,GAAG;AAAA,IAAE,KAAI;AAAM,UAAI,IAAE,GAAG,GAAE,GAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,aAAM,QAAQ,OAAO,GAAE,IAAI,EAAE,OAAO,GAAE,IAAI,EAAE,OAAO,GAAE,IAAI,EAAE,OAAO,GAAE,GAAG;AAAA,IAAE;AAAQ,aAAM,GAAG,OAAO,GAAG,GAAG,GAAE,GAAE,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,MAAI,CAAC,CAAC;AAAA,EAAC;AAAA,MAAM,SAAO,GAAE;AAAA,IAAC,KAAI;AAAM,UAAI,IAAE,GAAG,GAAE,IAAE,KAAI,IAAE,GAAG;AAAE,aAAM,OAAO,OAAO,EAAE,QAAQ,CAAC,GAAE,IAAI,EAAE,OAAO,KAAK,MAAM,MAAI,EAAE,CAAC,CAAC,GAAE,KAAK,EAAE,OAAO,KAAK,MAAM,MAAI,EAAE,CAAC,CAAC,GAAE,IAAI;AAAA,IAAE,KAAI;AAAM,aAAM,OAAO,OAAO,EAAE,QAAQ,CAAC,GAAE,IAAI,EAAE,OAAO,KAAK,MAAM,CAAC,GAAE,KAAK,EAAE,OAAO,KAAK,MAAM,CAAC,GAAE,IAAI;AAAA,IAAE,KAAI;AAAM,UAAI,IAAE,GAAG,GAAE,GAAE,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,aAAM,OAAO,OAAO,GAAE,IAAI,EAAE,OAAO,GAAE,IAAI,EAAE,OAAO,GAAE,GAAG;AAAA,IAAE;AAAQ,aAAO,GAAG,GAAG,GAAE,GAAE,CAAC,CAAC;AAAA,EAAC;AAAC;AAA15D,IAA45D,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,MAAE,GAAG,GAAE,GAAG,GAAE,IAAE,GAAG,GAAE,GAAG,GAAE,IAAE,GAAG,GAAE,GAAG;AAAE,MAAI,GAAE,IAAE,KAAK,IAAI,GAAE,GAAE,CAAC,GAAE,IAAE,KAAK,IAAI,GAAE,GAAE,CAAC,GAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,MAAI,IAAE,IAAE,IAAE;AAAE,MAAG,MAAI,EAAE,KAAE;AAAA,OAAM;AAAC,YAAO,GAAE;AAAA,MAAC,KAAK;AAAE,aAAG,IAAE,KAAG,KAAG,IAAE,IAAE,IAAE;AAAG;AAAA,MAAM,KAAK;AAAE,aAAG,IAAE,KAAG,IAAE;AAAE;AAAA,MAAM,KAAK;AAAE,aAAG,IAAE,KAAG,IAAE;AAAA,IAAC;AAAC,SAAG;AAAA,EAAC;AAAC,SAAM,EAAC,GAAE,MAAI,GAAE,GAAE,MAAI,GAAE,GAAE,MAAI,EAAC;AAAC;AAAprE,IAAsrE,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,OAAG;AAAI,MAAI,IAAE,KAAG,KAAI,IAAE,KAAK,IAAI,GAAE,IAAG;AAAE,SAAO,MAAI,KAAG,MAAI,IAAE,IAAE,IAAE,GAAE,KAAG,KAAG,IAAE,IAAE,IAAE,GAAE,EAAC,GAAE,CAAC,GAAE,GAAE,OAAK,MAAI,IAAE,IAAE,KAAG,IAAE,KAAG,IAAE,KAAG,IAAE,KAAI,GAAE,QAAM,IAAE,KAAG,GAAE;AAAC;AAAj2E,IAAm2E,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,CAAC;AAAE,MAAG,EAAE,MAAM,wBAAwB,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,KAAK,SAAS,OAAK,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AAAA,WAAU,EAAE,MAAM,qCAAqC,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAG,EAAE,GAAE,KAAK,SAAS,OAAK,EAAE,MAAM,GAAE,IAAE,CAAC,CAAC,CAAC;AAAE,SAAM,EAAC,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,EAAC;AAAC;AAA7nF,IAA+nF,KAAG,SAAS,GAAE,GAAE,GAAE;AAAC,MAAG,YAAU,OAAO,KAAG,OAAK,GAAE;AAAC,QAAI,IAAE,GAAG,GAAE,GAAG,CAAC,GAAE,CAAC,GAAE,IAAE,GAAG,CAAC;AAAE,WAAO,QAAM,IAAE,KAAG,GAAG,GAAE,GAAE,CAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAAjvF,IAAmvF,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,EAAE,MAAM,gBAAgB,GAAE,CAAC;AAAE,SAAM,EAAC,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,EAAC;AAAC;AAA10F,IAA40F,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,EAAE,MAAM,gBAAgB,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,SAAM,EAAC,GAAE,GAAE,GAAE,WAAW,CAAC,GAAE,GAAE,WAAW,CAAC,GAAE,EAAG;AAAC;AAA38F,IAA68F,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,EAAE,MAAM,gBAAgB,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC;AAAE,SAAM,EAAC,GAAE,WAAW,CAAC,GAAE,GAAE,WAAW,CAAC,GAAE,GAAE,WAAW,CAAC,GAAE,GAAE,WAAW,CAAC,EAAC;AAAC;AAApmG,IAAsmG,KAAG,SAAS,GAAE,GAAE;AAAC,MAAG,EAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,MAAI,UAAU,CAAC,EAAE,SAAO,GAAE;AAAA,IAAC,KAAI;AAAM,UAAI,IAAE,GAAG,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,aAAO,GAAG,GAAG,CAAC,GAAE,GAAG,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,CAAC,EAAC,CAAC;AAAA,IAAE,KAAI;AAAM,UAAI,IAAE,GAAG,CAAC;AAAE,aAAM,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,GAAE,GAAE,EAAE,GAAE,GAAE,EAAE,EAAC;AAAA,IAAE,KAAI;AAAM,UAAI,IAAE,GAAG,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,aAAO,GAAG,GAAG,CAAC,GAAE,GAAG,EAAC,GAAE,GAAE,GAAI,GAAE,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,CAAC,EAAC,CAAC;AAAA,IAAE;AAAQ,UAAI,IAAE,GAAG,CAAC,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,aAAO,GAAG,GAAG,CAAC,GAAE,GAAG,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,IAAE,IAAG,CAAC;AAAA,EAAC;AAAA,OAAK;AAAC,YAAO,GAAE;AAAA,MAAC,KAAI;AAAM,eAAO,GAAG,GAAG,CAAC,GAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC;AAAA,MAAE,KAAI;AAAM,YAAI,IAAE,GAAG,CAAC;AAAE,eAAM,EAAC,GAAE,EAAE,GAAE,GAAE,EAAE,GAAE,GAAE,EAAE,GAAE,GAAE,EAAC;AAAA,MAAE,KAAI;AAAM,eAAO,GAAG,GAAG,CAAC,GAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC;AAAA,MAAE;AAAQ,eAAO,GAAG,GAAG,CAAC,GAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAztH,IAA2tH,KAAG,SAAS,GAAE;AAAC,SAAO,EAAE,MAAM,IAAI,IAAE,QAAM,EAAE,MAAM,MAAM,IAAE,QAAM,EAAE,MAAM,MAAM,IAAE,QAAM,EAAE,MAAM,MAAM,IAAE,QAAM;AAAK;AAA50H,IAA80H,KAAG,SAAS,GAAE;AAAC,MAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE;AAAE,SAAO,MAAM,CAAC,KAAG,MAAM,CAAC,KAAG,MAAM,CAAC,IAAE,QAAM,MAAM,CAAC,MAAI,IAAE,IAAG,MAAM,CAAC,MAAI,IAAE,IAAG,MAAM,CAAC,MAAI,IAAE,IAAG,MAAM,CAAC,MAAI,IAAE,IAAG,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAE;AAAr/H,IAAu/H,KAAG,gBAAE,EAAC,MAAK,aAAY,OAAM,EAAC,MAAK,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,GAAE,QAAO,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,cAAa,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,WAAU,EAAC,MAAK,SAAQ,SAAQ,MAAE,EAAC,GAAE,OAAM,CAAC,QAAQ,GAAE,OAAM,SAAS,GAAE;AAAC,MAAI,IAAE,IAAE,GAAE,IAAE,OAAE,SAAQ,EAAC,OAAM,QAAO,CAAC,EAAE,OAAM,IAAE,SAAG,WAAU;AAAC,WAAO,YAAY,EAAE,SAAO,EAAE,QAAM,EAAE;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,WAAO,YAAY,EAAE,UAAQ,EAAE,QAAM,EAAE;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,WAAM,EAAC,OAAM,GAAG,OAAO,MAAE,CAAC,GAAE,IAAI,GAAE,QAAO,GAAG,OAAO,MAAE,CAAC,GAAE,IAAI,GAAE,QAAO,EAAE,SAAO,aAAa,OAAO,WAAS,MAAE,CAAC,IAAE,YAAU,SAAS,IAAE,IAAG,cAAa,GAAG,OAAO,EAAE,cAAa,IAAI,GAAE,WAAU,EAAE,WAAS,eAAe,OAAO,WAAS,MAAE,CAAC,IAAE,YAAU,SAAS,IAAE,GAAE;AAAA,EAAC,CAAE,GAAE,IAAE,WAAU;AAAC,QAAI,IAAE,EAAE,MAAM,WAAW,IAAI;AAAE,MAAE,MAAM,QAAM,MAAE,CAAC,GAAE,EAAE,MAAM,SAAO,MAAE,CAAC;AAAE,QAAIG,KAAE,SAASH,IAAE;AAAC,UAAIC,KAAE,SAAS,cAAc,QAAQ,GAAEC,KAAED,GAAE,WAAW,IAAI,GAAEE,KAAE,IAAEH;AAAE,aAAOC,GAAE,QAAME,IAAEF,GAAE,SAAOE,IAAED,GAAE,YAAU,WAAUA,GAAE,SAAS,GAAE,GAAEC,IAAEA,EAAC,GAAED,GAAE,YAAU,WAAUA,GAAE,SAAS,GAAE,GAAEF,IAAEA,EAAC,GAAEE,GAAE,SAASF,IAAEA,IAAEA,IAAEA,EAAC,GAAEC;AAAA,IAAC,EAAE,CAAC;AAAE,MAAE,YAAU,EAAE,cAAcE,IAAE,QAAQ,GAAE,EAAE,SAAS,GAAE,GAAE,MAAE,CAAC,GAAE,MAAE,CAAC,CAAC,GAAE,EAAE,YAAU,EAAE,OAAM,EAAE,SAAS,GAAE,GAAE,MAAE,CAAC,GAAE,MAAE,CAAC,CAAC;AAAA,EAAC;AAAE,SAAO,MAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAK,GAAI,WAAU;AAAC,MAAE;AAAA,EAAC,CAAE,GAAE,UAAG,WAAU;AAAC,MAAE;AAAA,EAAC,CAAE,GAAE,EAAC,QAAO,GAAE,gBAAe,EAAC;AAAC,EAAC,CAAC;AAAjxK,IAAmxK,KAAG,CAAC,WAAW;AAAE,EAAE,uEAAuE,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,UAAS,EAAC,OAAM,cAAa,OAAM,eAAE,EAAE,cAAc,GAAE,KAAI,UAAS,WAAU,EAAE,UAAS,GAAE,MAAK,IAAG,EAAE;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;AAA+B,IAAI,KAAG,gBAAE,EAAC,MAAK,UAAS,YAAW,EAAC,WAAU,GAAE,GAAE,OAAM,EAAC,QAAO,EAAC,MAAK,OAAM,SAAQ,WAAU;AAAC,SAAM,CAAC;AAAC,EAAC,GAAE,eAAc,EAAC,MAAK,QAAO,SAAQ,GAAE,EAAC,GAAE,OAAM,CAAC,QAAQ,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE;AAAK,SAAM,EAAC,eAAc,SAASH,IAAE,GAAE;AAAC,MAAE,UAASA,IAAE,CAAC;AAAA,EAAC,GAAE,WAAU,SAAG,WAAU;AAAC,WAAO,EAAE,OAAO,IAAK,SAASA,IAAE;AAAC,aAAO,GAAGA,IAAE,OAAM,IAAE;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE,EAAC;AAAC,EAAC,CAAC;AAA/U,IAAiV,KAAG,EAAC,OAAM,SAAQ;AAAE,SAAS,GAAG,GAAE,GAAE;AAAC,MAAI,IAAE,OAAO,KAAK,CAAC;AAAE,MAAG,OAAO,uBAAsB;AAAC,QAAI,IAAE,OAAO,sBAAsB,CAAC;AAAE,UAAI,IAAE,EAAE,OAAQ,SAASC,IAAE;AAAC,aAAO,OAAO,yBAAyB,GAAEA,EAAC,EAAE;AAAA,IAAU,CAAE,IAAG,EAAE,KAAK,MAAM,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,WAAQ,IAAE,GAAE,IAAE,UAAU,QAAO,KAAI;AAAC,QAAI,IAAE,QAAM,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,QAAE,IAAE,GAAG,OAAO,CAAC,GAAE,IAAE,EAAE,QAAS,SAASA,IAAE;AAAC,QAAE,GAAEA,IAAE,EAAEA,EAAC,CAAC;AAAA,IAAC,CAAE,IAAE,OAAO,4BAA0B,OAAO,iBAAiB,GAAE,OAAO,0BAA0B,CAAC,CAAC,IAAE,GAAG,OAAO,CAAC,CAAC,EAAE,QAAS,SAASA,IAAE;AAAC,aAAO,eAAe,GAAEA,IAAE,OAAO,yBAAyB,GAAEA,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,EAAE,iIAAiI,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,iBAAE,YAAY;AAAE,SAAO,UAAE,GAAE,mBAAE,OAAM,IAAG,EAAE,UAAE,IAAE,GAAE,mBAAE,UAAE,MAAK,WAAE,EAAE,WAAW,SAASA,IAAE,GAAE;AAAC,WAAO,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,OAAM,cAAa,MAAK,IAAG,OAAMA,IAAE,QAAO,OAAG,iBAAgB,GAAE,UAAS,EAAE,kBAAgB,GAAE,SAAQ,cAAG,SAAS,GAAE;AAAC,aAAO,EAAE,cAAcA,IAAE,CAAC,CAAC;AAAA,IAAC,GAAG,CAAC,QAAO,SAAS,CAAC,EAAC,GAAE,MAAK,GAAE,CAAC,SAAQ,YAAW,SAAS,CAAC;AAAA,EAAC,CAAE,GAAE,GAAG,EAAE,CAAC;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;AAAwB,IAAI,KAAG,gBAAE,EAAC,MAAK,UAAS,YAAW,EAAC,QAAO,IAAG,YAAW,GAAE,KAAI,GAAE,OAAM,GAAE,YAAW,GAAE,GAAE,OAAM,EAAC,QAAO,EAAC,MAAK,QAAO,SAAQ,MAAK,GAAE,WAAU,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,QAAO,EAAC,MAAK,OAAM,SAAQ,WAAU;AAAC,SAAM,CAAC;AAAC,EAAC,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,WAAU;AAAC,SAAM,CAAC;AAAC,EAAC,GAAE,eAAc,EAAC,MAAK,CAAC,SAAQ,KAAK,EAAC,EAAC,GAAE,OAAM,CAAC,UAAS,cAAc,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,SAAG,WAAU;AAAC,WAAO,GAAG,GAAG,CAAC,GAAE,EAAE,KAAK,GAAE,CAAC,GAAE,EAAC,OAAM,EAAE,YAAU,UAAQ,QAAO,CAAC;AAAA,EAAC,CAAE,GAAE,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,WAAU;AAAC,QAAI,GAAEC,KAAE,UAAQ,IAAE,EAAE,UAAQ,WAAS,IAAE,SAAO,EAAE,KAAK;AAAE,QAAG,QAAMA,MAAG,OAAKA,IAAE;AAAC,UAAIC,KAAE,GAAGD,EAAC;AAAE,aAAO,GAAG,GAAGA,IAAEC,IAAE,EAAE,SAAS,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAE,QAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAK,GAAI,WAAU;AAAC,MAAE,UAAQ,MAAE,CAAC,MAAI,EAAE,QAAM,EAAE;AAAA,EAAE,GAAG,EAAC,WAAU,KAAE,CAAC,GAAE,MAAG,WAAU;AAAC,WAAM,CAAC,MAAE,CAAC,GAAE,EAAE,MAAM;AAAA,EAAC,GAAI,SAAS,GAAE;AAAC,QAAID,KAAE,EAAE,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEG,KAAEH,GAAE,CAAC,GAAE,IAAE;AAAG,YAAME,MAAG,IAAE,GAAG,GAAG,CAAC,GAAEA,EAAC,GAAE,EAAE,QAAO,EAAE,SAAS,GAAE,EAAE,QAAM,GAAG,CAAC,GAAEA,EAAC,KAAG,EAAE,QAAM,MAAK,EAAE,QAAM;AAAE,QAAI,IAAE,EAAE;AAAE,SAAK,UAAUA,EAAC,MAAI,KAAK,UAAU,CAAC,KAAG,KAAK,UAAUA,EAAC,MAAI,KAAK,UAAUC,EAAC,KAAG,EAAE,UAAS,CAAC;AAAA,EAAC,GAAG,EAAC,WAAU,KAAE,CAAC;AAAE,MAAI,IAAE,SAAG,WAAU;AAAC,QAAIL;AAAE,YAAO,UAAQA,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,MAAI;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,QAAIA;AAAE,YAAO,UAAQA,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,MAAI;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,QAAIA;AAAE,YAAO,UAAQA,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,MAAI;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,QAAIA,IAAE;AAAE,WAAO,SAAO,UAAQA,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,KAAG,UAAQ,IAAE,MAAE,CAAC,MAAI,WAAS,IAAE,SAAO,EAAE,IAAE;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,WAAO,GAAG,MAAE,CAAC,GAAE,MAAE,CAAC,GAAE,MAAE,CAAC,CAAC;AAAA,EAAC,CAAE,GAAE,IAAE,SAAG,WAAU;AAAC,WAAM,OAAO,OAAO,MAAE,CAAC,EAAE,GAAE,IAAI,EAAE,OAAO,MAAE,CAAC,EAAE,GAAE,IAAI,EAAE,OAAO,MAAE,CAAC,EAAE,GAAE,GAAG;AAAA,EAAC,CAAE,GAAE,IAAE,IAAE,EAAE;AAAE,SAAM,EAAC,GAAE,GAAE,GAAE,GAAE,GAAI,GAAE,GAAE,QAAO,GAAE,oBAAmB,SAASA,IAAE,GAAE;AAAC,MAAE,QAAM,IAAG,QAAM,MAAE,CAAC,IAAE,EAAE,QAAM,EAAC,GAAE,MAAE,CAAC,GAAE,GAAEA,IAAE,GAAE,GAAE,GAAE,MAAE,CAAC,EAAC,IAAE,EAAE,QAAM,GAAG,GAAG,CAAC,GAAE,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAEA,IAAE,GAAE,EAAC,CAAC;AAAA,EAAC,GAAE,aAAY,SAASA,IAAE;AAAC,MAAE,QAAM,IAAG,QAAM,MAAE,CAAC,MAAI,EAAE,QAAM,EAAC,GAAEA,IAAE,GAAE,MAAE,CAAC,GAAE,GAAE,MAAE,CAAC,GAAE,GAAE,MAAE,CAAC,EAAC,IAAG,EAAE,QAAM,GAAG,GAAG,CAAC,GAAE,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAEA,GAAC,CAAC;AAAA,EAAC,GAAE,eAAc,SAASA,IAAE;AAAC,MAAE,QAAM,IAAG,QAAM,MAAE,CAAC,IAAE,EAAE,QAAM,EAAC,GAAE,MAAE,CAAC,GAAE,GAAE,MAAE,CAAC,GAAE,GAAE,MAAE,CAAC,GAAE,GAAEA,GAAC,IAAE,EAAE,QAAM,GAAG,GAAG,CAAC,GAAE,MAAE,CAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAEA,GAAC,CAAC;AAAA,EAAC,GAAE,mBAAkB,SAAS,GAAE;AAAC,WAAO,EAAE,YAAU,EAAE,CAAC,IAAE,EAAE,CAAC;AAAA,EAAC,GAAE,YAAW,GAAE,aAAY,GAAE,eAAc,SAASA,IAAE;AAAC,MAAE,QAAM,IAAG,EAAE,QAAMA;AAAA,EAAC,GAAE,cAAa,WAAU;AAAC,QAAI,GAAEE,KAAE,UAAQ,IAAE,MAAE,CAAC,MAAI,WAAS,IAAE,SAAO,EAAE,KAAK;AAAE,QAAG,OAAKA,IAAE;AAAC,UAAIC,KAAE,EAAE,WAAUC,KAAE,GAAGF,EAAC,GAAE,IAAE,GAAG,GAAGA,IAAEE,IAAED,EAAC,CAAC,GAAE,IAAE,SAASH,IAAE;AAAC,YAAG,CAACA,GAAE,QAAM;AAAG,YAAIC,KAAED,GAAE,GAAEE,KAAEF,GAAE,GAAEG,KAAEH,GAAE,GAAEI,KAAEJ,GAAE;AAAE,eAAM,EAAE,MAAMC,EAAC,KAAG,MAAMC,EAAC,KAAG,MAAMC,EAAC,KAAG,MAAMC,EAAC;AAAA,MAAE,EAAE,CAAC;AAAE,UAAE,EAAE,QAAM,IAAE,QAAM,MAAE,CAAC,IAAE,EAAE,QAAM,MAAE,CAAC,IAAE,EAAE,QAAM;AAAA,IAAE,MAAM,GAAE,QAAM;AAAA,EAAI,GAAE,kBAAiB,GAAE,eAAc,SAASJ,IAAE,GAAE;AAAC,QAAG,EAAE,QAAM,GAAE,QAAMA,KAAEA,GAAE,KAAK,GAAG,GAAE,QAAM;AAAA,SAAS;AAAC,UAAIE,KAAE,GAAGF,EAAC;AAAE,MAAAE,KAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,YAAG,MAAIF,GAAE,QAAO;AAAC,cAAIG,KAAE,GAAGH,IAAEC,IAAEC,EAAC,GAAEE,KAAED,GAAE,GAAEG,KAAEH,GAAE,GAAE,IAAEA,GAAE;AAAE,cAAG,EAAE,MAAMC,EAAC,KAAG,MAAME,EAAC,KAAG,MAAM,CAAC,IAAG;AAAC,gBAAIK,KAAE,GAAE,IAAER,GAAE;AAAE,kBAAM,CAAC,MAAIQ,KAAE,IAAG,EAAE,QAAM,EAAC,GAAEP,IAAE,GAAEE,IAAE,GAAE,GAAE,GAAEK,GAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,EAAEX,IAAEE,IAAE,IAAE,IAAE,EAAE,QAAM;AAAA,IAAI;AAAA,EAAC,GAAE,gBAAe,SAASF,IAAE;AAAC,MAAE,gBAAeA,EAAC;AAAA,EAAC,EAAC;AAAC,EAAC,CAAC;AAAvoF,IAAyoF,KAAG,EAAC,OAAM,eAAc;AAAjqF,IAAmqF,KAAG,EAAC,OAAM,gBAAe;AAAE,EAAE,yaAAya,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,iBAAE,YAAY,GAAE,IAAE,iBAAE,KAAK,GAAE,IAAE,iBAAE,OAAO,GAAE,IAAE,iBAAE,aAAa,GAAE,IAAE,iBAAE,QAAQ;AAAE,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,UAAS,OAAM,eAAE,EAAE,WAAW,EAAC,GAAE,CAAC,gBAAE,OAAM,IAAG,CAAC,gBAAE,OAAM,IAAG,CAAC,YAAE,GAAE,EAAC,OAAM,cAAa,KAAI,EAAE,GAAE,YAAW,EAAE,GAAE,OAAM,EAAE,GAAE,UAAS,EAAE,mBAAkB,GAAE,MAAK,GAAE,CAAC,OAAM,cAAa,SAAQ,UAAU,CAAC,GAAE,YAAE,GAAE,EAAC,OAAM,OAAM,KAAI,EAAE,GAAE,UAAS,EAAE,YAAW,GAAE,MAAK,GAAE,CAAC,OAAM,UAAU,CAAC,GAAE,EAAE,aAAW,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,OAAM,SAAQ,OAAM,EAAE,GAAE,OAAM,EAAE,QAAO,UAAS,EAAE,cAAa,GAAE,MAAK,GAAE,CAAC,SAAQ,SAAQ,UAAU,CAAC,KAAG,mBAAE,QAAO,IAAE,CAAC,CAAC,GAAE,YAAE,GAAE,EAAC,QAAO,EAAE,kBAAkB,EAAE,MAAM,GAAE,OAAM,EAAE,YAAW,WAAU,EAAE,WAAU,OAAM,EAAE,YAAU,MAAI,KAAI,eAAc,EAAE,eAAc,UAAS,EAAE,eAAc,QAAO,EAAE,cAAa,SAAQ,EAAE,cAAa,gBAAe,EAAE,eAAc,GAAE,MAAK,GAAE,CAAC,UAAS,SAAQ,aAAY,SAAQ,iBAAgB,YAAW,UAAS,WAAU,gBAAgB,CAAC,GAAE,EAAE,OAAO,SAAO,KAAG,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,OAAM,UAAS,QAAO,EAAE,QAAO,kBAAiB,EAAE,kBAAiB,UAAS,EAAE,cAAa,GAAE,MAAK,GAAE,CAAC,UAAS,kBAAiB,UAAU,CAAC,KAAG,mBAAE,QAAO,IAAE,CAAC,CAAC,CAAC,GAAE,CAAC;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;AAAwB,IAAI,KAAG,gBAAE,EAAC,MAAK,gBAAe,OAAM,EAAC,MAAK,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,EAAC,GAAE,OAAM,SAAS,GAAE;AAAC,MAAI,IAAE,OAAE,SAAQ,EAAC,OAAM,QAAO,CAAC,EAAE;AAAM,SAAM,EAAC,mBAAkB,SAAG,WAAU;AAAC,WAAM,EAAC,OAAM,GAAG,OAAO,EAAE,MAAK,IAAI,GAAE,QAAO,GAAG,OAAO,EAAE,MAAK,IAAI,GAAE,YAAW,GAAG,OAAO,EAAE,MAAK,IAAI,GAAE,WAAU,EAAE,WAAS,eAAe,OAAO,WAAS,MAAE,CAAC,IAAE,YAAU,SAAS,IAAE,GAAE;AAAA,EAAC,CAAE,EAAC;AAAC,EAAC,CAAC;AAAE,EAAE,4YAA4Y,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,kBAAiB,OAAM,eAAE,EAAE,iBAAiB,GAAE,SAAQ,iBAAgB,OAAM,6BAA4B,GAAE,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAC,gBAAE,KAAI,EAAC,OAAM,YAAW,GAAE,CAAC,gBAAE,QAAO,EAAC,GAAE,sDAAqD,CAAC,CAAC,GAAE,EAAE,CAAC,IAAG,CAAC;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;AAAsC,IAAI,KAAG,gBAAE,EAAC,MAAK,eAAc,YAAW,EAAC,WAAU,IAAG,QAAO,IAAG,cAAa,GAAE,GAAE,OAAM,EAAC,OAAM,EAAC,MAAK,CAAC,QAAO,KAAK,EAAC,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,QAAO,GAAE,MAAK,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,GAAE,GAAE,OAAM,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,GAAE,QAAO,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,GAAE,QAAO,EAAC,MAAK,OAAM,GAAE,YAAW,EAAC,MAAK,SAAQ,SAAQ,OAAM,GAAE,WAAU,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,aAAY,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,KAAI,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,gBAAe,EAAC,MAAK,CAAC,QAAO,QAAO,OAAO,GAAE,SAAQ,OAAM,GAAE,QAAO,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,QAAO,EAAC,MAAK,OAAM,SAAQ,WAAU;AAAC,SAAM,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS;AAAC,EAAC,GAAE,UAAS,EAAC,MAAK,OAAM,GAAE,WAAU,EAAC,MAAK,OAAM,GAAE,eAAc,EAAC,MAAK,CAAC,SAAQ,KAAK,GAAE,SAAQ,MAAE,EAAC,GAAE,OAAM,CAAC,UAAS,gBAAe,qBAAoB,eAAc,eAAc,cAAc,GAAE,OAAM,SAAS,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,MAAK,IAAE,IAAE,CAAC,CAAC,GAAE,IAAE,SAAG,WAAU;AAAC,WAAO,MAAE,CAAC,EAAE,IAAK,SAAS,GAAE;AAAC,aAAO,GAAG,GAAE,OAAM,EAAE,SAAS;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE,GAAE,IAAE,IAAE,KAAK;AAAE,QAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAM,GAAI,WAAU;AAAC,MAAE,QAAM,EAAE;AAAA,EAAM,GAAG,EAAC,WAAU,KAAE,CAAC;AAAE,QAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAK,GAAI,WAAU;AAAC,QAAI,IAAE,EAAE,SAAO,IAAGE,KAAE,MAAM,QAAQ,CAAC,IAAE,IAAE,CAAC,CAAC;AAAE,MAAE,QAAMA,GAAE,IAAK,SAASD,IAAE;AAAC,aAAO,GAAGA,IAAE,MAAE,CAAC,GAAE,EAAE,SAAS;AAAA,IAAC,CAAE;AAAA,EAAC,GAAG,EAAC,WAAU,KAAE,CAAC;AAAE,MAAI,IAAE,IAAE,MAAM,GAAE,IAAE,SAAG,WAAU;AAAC,WAAO,MAAE,CAAC,EAAE,MAAE,CAAC,CAAC;AAAA,EAAC,CAAE,GAAE,IAAE,IAAE,KAAE;AAAE,QAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAU,GAAI,WAAU;AAAC,MAAE,QAAM,EAAE;AAAA,EAAU,GAAG,EAAC,WAAU,KAAE,CAAC;AAAE,MAAI,GAAE,GAAE,IAAE,IAAE,IAAI,GAAE,IAAE,IAAE,IAAI,GAAE,IAAE,GAAG,GAAE,GAAE,EAAC,cAAa,EAAC,QAAO,EAAE,OAAM,GAAE,UAAS,EAAE,UAAS,WAAU,EAAE,UAAS,CAAC,EAAE,OAAM,IAAE,WAAU;AAAC,YAAM,MAAE,CAAC,MAAI,EAAE,QAAM,MAAEkB,EAAC,EAAE,CAAC,IAAG,QAAM,MAAE,CAAC,MAAI,EAAE,QAAM,IAAG,WAAS,EAAE,aAAW,EAAE,QAAM,OAAG,EAAE,qBAAoB,IAAE;AAAA,EAAC,GAAE,IAAE,WAAU;AAAC,MAAE,QAAM,QAAO,WAAS,EAAE,aAAW,EAAE,QAAM,QAAG,EAAE,qBAAoB,KAAE,GAAE,EAAE,eAAc,MAAE,MAAM,QAAQ,EAAE,KAAK,KAAG,EAAE,WAAS,MAAE,CAAC,IAAE,MAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAAC,GAAE,IAAE,IAAE,GAAE,IAAE,WAAU;AAAC,QAAInB,KAAE,EAAE,EAAE,KAAM,SAASA,GAAE,GAAE;AAAC,UAAIE,IAAEC,IAAEC,IAAEC;AAAE,aAAO,EAAE,KAAM,SAASL,IAAE;AAAC,kBAAO,SAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,UAAC,KAAK;AAAE,gBAAGG,KAAE,EAAE,QAAO,SAAOC,KAAE,UAAQF,KAAEC,GAAE,YAAU,WAASD,KAAE,SAAOA,GAAE,UAAQ,OAAKE,IAAE;AAAC,cAAAJ,GAAE,OAAK;AAAE;AAAA,YAAK;AAAC,mBAAOA,GAAE,OAAO,QAAQ;AAAA,UAAE,KAAK;AAAE,gBAAGK,KAAE,CAACD,IAAE,MAAE,CAAC,MAAIC,IAAE;AAAC,cAAAL,GAAE,OAAK;AAAE;AAAA,YAAK;AAAC,mBAAOA,GAAE,OAAO,QAAQ;AAAA,UAAE,KAAK;AAAE,oBAAM,MAAE,CAAC,KAAG,MAAE,CAAC,MAAIK,MAAG,EAAE,GAAE,KAAG,aAAa,CAAC,GAAE,IAAE,WAAY,WAAU;AAAC,gBAAE,GAAE,aAAa,CAAC;AAAA,YAAC,GAAG,GAAG,KAAG,EAAE,GAAE,EAAE,QAAMA,IAAE,EAAE,QAAMF;AAAA,UAAE,KAAK;AAAA,UAAG,KAAI;AAAM,mBAAOH,GAAE,KAAK;AAAA,QAAC;AAAA,MAAC,GAAGA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAE,WAAO,SAAS,GAAE;AAAC,aAAOA,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC;AAAA,EAAC,EAAE,GAAE,IAAE,WAAU;AAAC,QAAIA,KAAE,EAAE,EAAE,KAAM,SAASA,GAAE,GAAE;AAAC,UAAIE,IAAEC,IAAEC,IAAEC,IAAEC;AAAE,aAAO,EAAE,KAAM,SAASN,IAAE;AAAC,kBAAO,SAAOA,GAAE,OAAKA,GAAE,MAAK;AAAA,UAAC,KAAK;AAAE,gBAAGK,KAAE,EAAE,QAAO,EAAE,EAAE,UAAQH,KAAE,MAAE,CAAC,MAAI,WAASA,MAAGA,GAAE,YAAYG,EAAC,OAAK,UAAQF,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,SAASE,EAAC,KAAI;AAAC,cAAAL,GAAE,OAAK;AAAE;AAAA,YAAK;AAAC,mBAAOA,GAAE,OAAO,QAAQ;AAAA,UAAE,KAAK;AAAE,gBAAGM,KAAE,UAAQF,KAAE,MAAE,CAAC,MAAI,WAASA,KAAE,SAAOA,GAAE,KAAI,GAAG,QAAME,KAAE,SAAOA,GAAE,SAASD,EAAC,MAAI,QAAI;AAAC,cAAAL,GAAE,OAAK;AAAE;AAAA,YAAK;AAAC,mBAAOA,GAAE,OAAO,QAAQ;AAAA,UAAE,KAAK;AAAE,iBAAG,aAAa,CAAC,GAAE,IAAE,WAAY,WAAU;AAAC,oBAAE,CAAC,KAAG,EAAE;AAAA,YAAC,GAAG,CAAC;AAAA,UAAE,KAAK;AAAA,UAAG,KAAI;AAAM,mBAAOA,GAAE,KAAK;AAAA,QAAC;AAAA,MAAC,GAAGA,EAAC;AAAA,IAAC,CAAE,CAAC;AAAE,WAAO,SAAS,GAAE;AAAC,aAAOA,GAAE,MAAM,MAAK,SAAS;AAAA,IAAC;AAAA,EAAC,EAAE;AAAE,QAAE,GAAG,WAAU;AAAC,UAAE,CAAC,MAAI,EAAE,GAAE,aAAa,CAAC;AAAA,EAAE,CAAE;AAAE,MAAI,GAAEQ,KAAE,IAAE,EAAE,MAAI,MAAE,CAAC,EAAE,MAAM,GAAEO,KAAE,SAAG,WAAU;AAAC,WAAM,YAAU,OAAO,EAAE,kBAAgB,aAAW,EAAE,EAAE,cAAc,KAAG,QAAM,EAAE,iBAAe,EAAE,iBAAe;AAAA,EAAM,CAAE,GAAER,KAAE,SAAG,WAAU;AAAC,WAAM,aAAW,OAAO,EAAE,kBAAgB,UAAK,EAAE;AAAA,EAAc,CAAE,GAAEa,KAAE,SAAG,WAAU;AAAC,WAAO,EAAE;AAAA,EAAK,CAAE;AAAE,QAAG,WAAU;AAAC,WAAM,CAAC,EAAE,OAAM,MAAE,CAAC,CAAC;AAAA,EAAC,GAAI,WAAU;AAAC,aAAG,WAAU;AAAC,UAAIpB,IAAE;AAAE,gBAAQA,KAAE,MAAE,CAAC,MAAI,WAASA,MAAGA,GAAE,aAAa,qBAAoB,MAAEoB,EAAC,CAAC,GAAE,UAAQ,IAAE,MAAE,CAAC,MAAI,WAAS,KAAG,UAAQ,IAAE,EAAE,QAAM,WAAS,KAAG,EAAE,aAAa,qBAAoB,MAAEA,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAG,EAAC,WAAU,KAAE,CAAC,GAAE,QAAE,SAAQ,EAAC,OAAMA,GAAC,CAAC;AAAE,MAAID,KAAE,IAAE,CAAC,CAAC;AAAE,SAAO,UAAG,WAAU;AAAC,aAAS,iBAAiB,WAAU,GAAE,KAAE,GAAE,EAAE,cAAY,EAAE;AAAA,EAAC,CAAE,GAAE,YAAG,WAAU;AAAC,aAAS,oBAAoB,WAAU,GAAE,KAAE,GAAE,MAAI,aAAa,CAAC,GAAE,IAAE,OAAM,MAAI,aAAa,CAAC,GAAE,IAAE;AAAA,EAAK,CAAE,GAAE,EAAC,WAAU,GAAE,mBAAkB,SAAS,GAAE;AAAC,YAAO,EAAE,WAAS,MAAE,CAAC,EAAE,SAAO,IAAE,MAAE,CAAC,EAAE,SAAO,MAAI,MAAE,CAAC,MAAI;AAAA,EAAC,GAAE,eAAc,GAAE,eAAc,GAAE,cAAa,GAAE,kBAAiBX,IAAE,gBAAe,SAAS,GAAE;AAAC,QAAIN,KAAE,MAAE,CAAC,GAAEG,KAAE,MAAE,CAAC,EAAE,MAAM,GAAEC,KAAE,MAAE,CAAC,EAAE;AAAO,QAAG,QAAMJ,IAAE;AAAC,MAAAA,MAAG,IAAEG,GAAEH,EAAC,IAAE,KAAG,EAAE,QAAMI,IAAED,GAAE,KAAK,CAAC;AAAG,UAAI,IAAE;AAAG,UAAE,MAAM,QAAQ,EAAE,KAAK,KAAG,EAAE,WAASA,KAAE,GAAE,EAAE,QAAM,MAAM,QAAQ,CAAC,IAAE,IAAE,CAAC,CAAC,GAAE,EAAE,gBAAe,CAAC,GAAE,EAAE,UAAS,GAAE,GAAEH,EAAC,GAAE,EAAE,YAAUI,MAAG,EAAE,QAAME,GAAE,QAAM,OAAG,EAAE,aAAa;AAAA,IAAE;AAAA,EAAC,GAAE,aAAY,GAAE,cAAa,GAAE,WAAU,GAAE,sBAAqB,SAASR,IAAE;AAAC,IAAAA,GAAE,aAAa,gBAAc;AAAO,QAAI,IAAEA,GAAE;AAAO,QAAE,CAAC,EAAE,QAAQ;AAAA,EAAK,GAAE,qBAAoB,SAASA,IAAE;AAAA,EAAC,GAAE,iBAAgB,SAASA,IAAE;AAAC,QAAI,IAAE,CAACA,GAAE,OAAO,QAAQ,OAAME,KAAE,EAAE,MAAE,CAAC,CAAC,GAAEG,KAAEH,GAAE,CAAC;AAAE,IAAAA,GAAE,OAAO,GAAE,CAAC;AAAE,QAAII,KAAEJ,GAAE,MAAM,GAAE,CAAC,GAAE,IAAEA,GAAE,OAAO,CAAC,GAAE,IAAEI,GAAE,OAAO,CAACD,EAAC,CAAC,EAAE,OAAO,CAAC;AAAE,MAAE,gBAAe,CAAC,GAAE,EAAE,UAAS,GAAE,EAAE,CAAC,GAAE,CAAC;AAAA,EAAC,GAAE,eAAcc,IAAE,aAAY,GAAE,QAAO,GAAE,kBAAiBZ,IAAE,kBAAiBQ,IAAE,aAAY,GAAE,gBAAe,SAASf,IAAE;AAAC,MAAE,QAAMA,IAAE,EAAE,gBAAeA,EAAC;AAAA,EAAC,EAAC;AAAC,EAAC,CAAC;AAAE,EAAE,2dAA2d,GAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAI,IAAE,iBAAE,YAAY,GAAE,IAAE,iBAAE,gBAAgB,GAAE,IAAE,iBAAE,QAAQ;AAAE,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,gBAAe,KAAI,eAAc,aAAY,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,wBAAsB,EAAE,qBAAqB,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,MAAM,CAAC,IAAG,YAAW,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,uBAAqB,EAAE,oBAAoB,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,IAAG,QAAO,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,mBAAiB,EAAE,gBAAgB,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,WAAU,MAAM,CAAC,IAAG,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,cAAG,WAAU;AAAC,WAAO,EAAE,gBAAc,EAAE,aAAa,MAAM,GAAE,SAAS;AAAA,EAAC,GAAG,CAAC,MAAM,CAAC,GAAE,GAAE,EAAE,UAAE,IAAE,GAAE,mBAAE,UAAE,MAAK,WAAE,EAAE,QAAQ,SAASC,IAAE,GAAE;AAAC,WAAO,UAAE,GAAE,YAAE,GAAE,EAAC,OAAM,cAAa,KAAI,GAAE,SAAQ,MAAG,KAAI,SAASA,IAAE;AAAC,aAAO,EAAE,cAAc,CAAC,IAAEA;AAAA,IAAC,GAAE,MAAK,EAAE,MAAK,OAAM,EAAE,OAAM,QAAO,EAAE,QAAO,OAAMA,IAAE,UAAS,EAAE,kBAAkB,CAAC,GAAE,cAAa,GAAE,WAAU,EAAE,UAAU,SAAO,GAAE,QAAO,EAAE,YAAW,GAAE,MAAK,GAAE,CAAC,QAAO,SAAQ,UAAS,SAAQ,YAAW,cAAa,aAAY,QAAQ,CAAC;AAAA,EAAC,CAAE,GAAE,GAAG,IAAG,EAAE,YAAU,EAAE,oBAAkB,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,OAAM,kBAAiB,KAAI,gBAAe,UAAS,EAAE,kBAAkB,EAAE,GAAE,cAAa,GAAE,GAAE,MAAK,GAAE,CAAC,UAAU,CAAC,KAAG,mBAAE,QAAO,IAAE,IAAG,UAAE,GAAE,YAAE,UAAE,EAAC,IAAG,EAAE,kBAAiB,UAAS,EAAE,iBAAgB,GAAE,CAAC,YAAE,YAAE,MAAK,EAAC,SAAQ,QAAG,WAAU;AAAC,WAAM,CAAC,EAAE,gBAAc,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,OAAM,UAAS,OAAM,eAAE,EAAE,WAAW,GAAE,KAAI,aAAY,OAAM,EAAE,eAAc,QAAO,EAAE,aAAY,cAAa,EAAE,WAAU,QAAO,EAAE,QAAO,eAAc,EAAE,eAAc,UAAS,EAAE,gBAAe,gBAAe,EAAE,eAAc,GAAE,MAAK,GAAE,CAAC,SAAQ,SAAQ,UAAS,cAAa,UAAS,iBAAgB,YAAW,gBAAgB,CAAC,KAAG,mBAAE,QAAO,IAAE,CAAC;AAAA,EAAC,CAAE,GAAE,GAAE,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,MAAK,UAAU,CAAC,EAAE,GAAE,GAAG;AAAC,GAAE,GAAG,YAAU,mBAAkB,GAAG,SAAO;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "name", "style", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "window", "import_dist", "import_dist", "min", "max", "import_dist", "import_dist", "import_dist", "toPaddingObject", "popperOffsets", "min", "max", "offset", "effect", "import_dist", "import_dist", "popper", "import_dist", "effect", "window", "import_dist", "import_dist", "import_dist", "hash", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "clippingParents", "import_dist", "reference", "popperOffsets", "offset", "import_dist", "placements", "placement", "placements", "placement", "_loop", "_i", "checks", "import_dist", "import_dist", "offset", "import_dist", "import_dist", "import_dist", "popperOffsets", "offset", "min", "max", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "fn", "import_dist", "merged", "defaultModifiers", "createPopper", "reference", "popper", "options", "fn", "state", "effect", "noopFn", "import_dist", "import_dist", "createPopper", "defaultModifiers", "createPopper", "e", "t", "r", "n", "o", "a", "i", "P", "I", "j", "c", "l", "u", "s", "f", "F", "_", "L", "createPopper", "B", "M"]}