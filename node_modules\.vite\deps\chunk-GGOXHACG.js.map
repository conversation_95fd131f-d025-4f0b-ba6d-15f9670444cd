{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-K557N5IZ.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/subGraphTitleMargins.ts\nvar getSubGraphTitleMargins = /* @__PURE__ */ __name(({\n  flowchart\n}) => {\n  const subGraphTitleTopMargin = flowchart?.subGraphTitleMargin?.top ?? 0;\n  const subGraphTitleBottomMargin = flowchart?.subGraphTitleMargin?.bottom ?? 0;\n  const subGraphTitleTotalMargin = subGraphTitleTopMargin + subGraphTitleBottomMargin;\n  return {\n    subGraphTitleTopMargin,\n    subGraphTitleBottomMargin,\n    subGraphTitleTotalMargin\n  };\n}, \"getSubGraphTitleMargins\");\n\nexport {\n  getSubGraphTitleMargins\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAKA,IAAI,0BAA0C,OAAO,CAAC;AAAA,EACpD;AACF,MAAM;AAPN;AAQE,QAAM,2BAAyB,4CAAW,wBAAX,mBAAgC,QAAO;AACtE,QAAM,8BAA4B,4CAAW,wBAAX,mBAAgC,WAAU;AAC5E,QAAM,2BAA2B,yBAAyB;AAC1D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,GAAG,yBAAyB;", "names": ["import_dist"]}