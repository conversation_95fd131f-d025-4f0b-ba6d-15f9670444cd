{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/infoDiagram-PH2N3AL5.mjs"], "sourcesContent": ["import {\n  package_default\n} from \"./chunk-5NNNAHNI.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/info/infoParser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"info\", input);\n    log.debug(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/info/infoDb.ts\nvar DEFAULT_INFO_DB = { version: package_default.version };\nvar getVersion = /* @__PURE__ */ __name(() => DEFAULT_INFO_DB.version, \"getVersion\");\nvar db = {\n  getVersion\n};\n\n// src/diagrams/info/infoRenderer.ts\nvar draw = /* @__PURE__ */ __name((text, id, version) => {\n  log.debug(\"rendering info diagram\\n\" + text);\n  const svg = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n  const group = svg.append(\"g\");\n  group.append(\"text\").attr(\"x\", 100).attr(\"y\", 40).attr(\"class\", \"version\").attr(\"font-size\", 32).style(\"text-anchor\", \"middle\").text(`v${version}`);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/info/infoDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAcA,IAAI,SAAS;AAAA,EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,GAAG;AAAA,EACf,GAAG,OAAO;AACZ;AAGA,IAAI,kBAAkB,EAAE,SAAS,gBAAgB,QAAQ;AACzD,IAAI,aAA6B,OAAO,MAAM,gBAAgB,SAAS,YAAY;AACnF,IAAI,KAAK;AAAA,EACP;AACF;AAGA,IAAI,OAAuB,OAAO,CAAC,MAAM,IAAI,YAAY;AACvD,MAAI,MAAM,6BAA6B,IAAI;AAC3C,QAAM,MAAM,iBAAiB,EAAE;AAC/B,mBAAiB,KAAK,KAAK,KAAK,IAAI;AACpC,QAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAM,OAAO,MAAM,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,KAAK,EAAE,EAAE,KAAK,SAAS,SAAS,EAAE,KAAK,aAAa,EAAE,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,IAAI,OAAO,EAAE;AACpJ,GAAG,MAAM;AACT,IAAI,WAAW,EAAE,KAAK;AAGtB,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF;", "names": ["import_dist"]}