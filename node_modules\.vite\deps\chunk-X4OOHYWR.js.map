{"version": 3, "sources": ["../../@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-ROXG7S4E.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  AbstractMermaidValueConverter,\n  MermaidGeneratedSharedModule,\n  PieGeneratedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/pie/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/pie/tokenBuilder.ts\nvar PieTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"PieTokenBuilder\");\n  }\n  constructor() {\n    super([\"pie\", \"showData\"]);\n  }\n};\n\n// src/language/pie/valueConverter.ts\nvar PieValueConverter = class extends AbstractMermaidValueConverter {\n  static {\n    __name(this, \"PieValueConverter\");\n  }\n  runCustomConverter(rule, input, _cstNode) {\n    if (rule.name !== \"PIE_SECTION_LABEL\") {\n      return void 0;\n    }\n    return input.replace(/\"/g, \"\").trim();\n  }\n};\n\n// src/language/pie/module.ts\nvar PieModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new PieTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new PieValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createPieServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Pie = inject(\n    createDefaultCoreModule({ shared }),\n    PieGeneratedModule,\n    PieModule\n  );\n  shared.ServiceRegistry.register(Pie);\n  return { shared, Pie };\n}\n__name(createPieServices, \"createPieServices\");\n\nexport {\n  PieModule,\n  createPieServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA;AAiBA,IAAI,mBAAkB,mBAAc,4BAA4B;AAAA,EAI9D,cAAc;AACZ,UAAM,CAAC,OAAO,UAAU,CAAC;AAAA,EAC3B;AACF,GALI,OAAO,IAAM,iBAAiB,GAFZ;AAjBtB,IAAAC;AA2BA,IAAI,qBAAoBA,MAAA,cAAc,8BAA8B;AAAA,EAIlE,mBAAmB,MAAM,OAAO,UAAU;AACxC,QAAI,KAAK,SAAS,qBAAqB;AACrC,aAAO;AAAA,IACT;AACA,WAAO,MAAM,QAAQ,MAAM,EAAE,EAAE,KAAK;AAAA,EACtC;AACF,GARI,OAAOA,KAAM,mBAAmB,GAFZA;AAaxB,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,IACN,cAA8B,OAAO,MAAM,IAAI,gBAAgB,GAAG,cAAc;AAAA,IAChF,gBAAgC,OAAO,MAAM,IAAI,kBAAkB,GAAG,gBAAgB;AAAA,EACxF;AACF;AACA,SAAS,kBAAkB,UAAU,iBAAiB;AACpD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,MAAM;AAAA,IACV,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,GAAG;AACnC,SAAO,EAAE,QAAQ,IAAI;AACvB;AACA,OAAO,mBAAmB,mBAAmB;", "names": ["import_dist", "_a"]}