{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/gitGraphDiagram-7IBYFJ6S.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-XZIHB7SX.mjs\";\nimport {\n  cleanAndMerge,\n  random,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  clear,\n  common_default,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getConfig2,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox2 as setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/git/gitGraphParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/git/gitGraphTypes.ts\nvar commitType = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4\n};\n\n// src/diagrams/git/gitGraphAst.ts\nvar DEFAULT_GITGRAPH_CONFIG = defaultConfig_default.gitGraph;\nvar getConfig3 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_GITGRAPH_CONFIG,\n    ...getConfig().gitGraph\n  });\n  return config;\n}, \"getConfig\");\nvar state = new ImperativeState(() => {\n  const config = getConfig3();\n  const mainBranchName = config.mainBranchName;\n  const mainBranchOrder = config.mainBranchOrder;\n  return {\n    mainBranchName,\n    commits: /* @__PURE__ */ new Map(),\n    head: null,\n    branchConfig: /* @__PURE__ */ new Map([[mainBranchName, { name: mainBranchName, order: mainBranchOrder }]]),\n    branches: /* @__PURE__ */ new Map([[mainBranchName, null]]),\n    currBranch: mainBranchName,\n    direction: \"LR\",\n    seq: 0,\n    options: {}\n  };\n});\nfunction getID() {\n  return random({ length: 7 });\n}\n__name(getID, \"getID\");\nfunction uniqBy(list, fn) {\n  const recordMap = /* @__PURE__ */ Object.create(null);\n  return list.reduce((out, item) => {\n    const key = fn(item);\n    if (!recordMap[key]) {\n      recordMap[key] = true;\n      out.push(item);\n    }\n    return out;\n  }, []);\n}\n__name(uniqBy, \"uniqBy\");\nvar setDirection = /* @__PURE__ */ __name(function(dir2) {\n  state.records.direction = dir2;\n}, \"setDirection\");\nvar setOptions = /* @__PURE__ */ __name(function(rawOptString) {\n  log.debug(\"options str\", rawOptString);\n  rawOptString = rawOptString?.trim();\n  rawOptString = rawOptString || \"{}\";\n  try {\n    state.records.options = JSON.parse(rawOptString);\n  } catch (e) {\n    log.error(\"error while parsing gitGraph options\", e.message);\n  }\n}, \"setOptions\");\nvar getOptions = /* @__PURE__ */ __name(function() {\n  return state.records.options;\n}, \"getOptions\");\nvar commit = /* @__PURE__ */ __name(function(commitDB) {\n  let msg = commitDB.msg;\n  let id = commitDB.id;\n  const type = commitDB.type;\n  let tags = commitDB.tags;\n  log.info(\"commit\", msg, id, type, tags);\n  log.debug(\"Entering commit:\", msg, id, type, tags);\n  const config = getConfig3();\n  id = common_default.sanitizeText(id, config);\n  msg = common_default.sanitizeText(msg, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  const newCommit = {\n    id: id ? id : state.records.seq + \"-\" + getID(),\n    message: msg,\n    seq: state.records.seq++,\n    type: type ?? commitType.NORMAL,\n    tags: tags ?? [],\n    parents: state.records.head == null ? [] : [state.records.head.id],\n    branch: state.records.currBranch\n  };\n  state.records.head = newCommit;\n  log.info(\"main branch\", config.mainBranchName);\n  state.records.commits.set(newCommit.id, newCommit);\n  state.records.branches.set(state.records.currBranch, newCommit.id);\n  log.debug(\"in pushCommit \" + newCommit.id);\n}, \"commit\");\nvar branch = /* @__PURE__ */ __name(function(branchDB) {\n  let name = branchDB.name;\n  const order = branchDB.order;\n  name = common_default.sanitizeText(name, getConfig3());\n  if (state.records.branches.has(name)) {\n    throw new Error(\n      `Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using \"checkout ${name}\")`\n    );\n  }\n  state.records.branches.set(name, state.records.head != null ? state.records.head.id : null);\n  state.records.branchConfig.set(name, { name, order });\n  checkout(name);\n  log.debug(\"in createBranch\");\n}, \"branch\");\nvar merge = /* @__PURE__ */ __name((mergeDB) => {\n  let otherBranch = mergeDB.branch;\n  let customId = mergeDB.id;\n  const overrideType = mergeDB.type;\n  const customTags = mergeDB.tags;\n  const config = getConfig3();\n  otherBranch = common_default.sanitizeText(otherBranch, config);\n  if (customId) {\n    customId = common_default.sanitizeText(customId, config);\n  }\n  const currentBranchCheck = state.records.branches.get(state.records.currBranch);\n  const otherBranchCheck = state.records.branches.get(otherBranch);\n  const currentCommit = currentBranchCheck ? state.records.commits.get(currentBranchCheck) : void 0;\n  const otherCommit = otherBranchCheck ? state.records.commits.get(otherBranchCheck) : void 0;\n  if (currentCommit && otherCommit && currentCommit.branch === otherBranch) {\n    throw new Error(`Cannot merge branch '${otherBranch}' into itself.`);\n  }\n  if (state.records.currBranch === otherBranch) {\n    const error = new Error('Incorrect usage of \"merge\". Cannot merge a branch to itself');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (currentCommit === void 0 || !currentCommit) {\n    const error = new Error(\n      `Incorrect usage of \"merge\". Current branch (${state.records.currBranch})has no commits`\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"commit\"]\n    };\n    throw error;\n  }\n  if (!state.records.branches.has(otherBranch)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") does not exist\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [`branch ${otherBranch}`]\n    };\n    throw error;\n  }\n  if (otherCommit === void 0 || !otherCommit) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") has no commits\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: ['\"commit\"']\n    };\n    throw error;\n  }\n  if (currentCommit === otherCommit) {\n    const error = new Error('Incorrect usage of \"merge\". Both branches have same head');\n    error.hash = {\n      text: `merge ${otherBranch}`,\n      token: `merge ${otherBranch}`,\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  }\n  if (customId && state.records.commits.has(customId)) {\n    const error = new Error(\n      'Incorrect usage of \"merge\". Commit with id:' + customId + \" already exists, use different custom Id\"\n    );\n    error.hash = {\n      text: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      token: `merge ${otherBranch} ${customId} ${overrideType} ${customTags?.join(\" \")}`,\n      expected: [\n        `merge ${otherBranch} ${customId}_UNIQUE ${overrideType} ${customTags?.join(\" \")}`\n      ]\n    };\n    throw error;\n  }\n  const verifiedBranch = otherBranchCheck ? otherBranchCheck : \"\";\n  const commit2 = {\n    id: customId || `${state.records.seq}-${getID()}`,\n    message: `merged branch ${otherBranch} into ${state.records.currBranch}`,\n    seq: state.records.seq++,\n    parents: state.records.head == null ? [] : [state.records.head.id, verifiedBranch],\n    branch: state.records.currBranch,\n    type: commitType.MERGE,\n    customType: overrideType,\n    customId: customId ? true : false,\n    tags: customTags ?? []\n  };\n  state.records.head = commit2;\n  state.records.commits.set(commit2.id, commit2);\n  state.records.branches.set(state.records.currBranch, commit2.id);\n  log.debug(state.records.branches);\n  log.debug(\"in mergeBranch\");\n}, \"merge\");\nvar cherryPick = /* @__PURE__ */ __name(function(cherryPickDB) {\n  let sourceId = cherryPickDB.id;\n  let targetId = cherryPickDB.targetId;\n  let tags = cherryPickDB.tags;\n  let parentCommitId = cherryPickDB.parent;\n  log.debug(\"Entering cherryPick:\", sourceId, targetId, tags);\n  const config = getConfig3();\n  sourceId = common_default.sanitizeText(sourceId, config);\n  targetId = common_default.sanitizeText(targetId, config);\n  tags = tags?.map((tag) => common_default.sanitizeText(tag, config));\n  parentCommitId = common_default.sanitizeText(parentCommitId, config);\n  if (!sourceId || !state.records.commits.has(sourceId)) {\n    const error = new Error(\n      'Incorrect usage of \"cherryPick\". Source commit id should exist and provided'\n    );\n    error.hash = {\n      text: `cherryPick ${sourceId} ${targetId}`,\n      token: `cherryPick ${sourceId} ${targetId}`,\n      expected: [\"cherry-pick abc\"]\n    };\n    throw error;\n  }\n  const sourceCommit = state.records.commits.get(sourceId);\n  if (sourceCommit === void 0 || !sourceCommit) {\n    throw new Error('Incorrect usage of \"cherryPick\". Source commit id should exist and provided');\n  }\n  if (parentCommitId && !(Array.isArray(sourceCommit.parents) && sourceCommit.parents.includes(parentCommitId))) {\n    const error = new Error(\n      \"Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.\"\n    );\n    throw error;\n  }\n  const sourceCommitBranch = sourceCommit.branch;\n  if (sourceCommit.type === commitType.MERGE && !parentCommitId) {\n    const error = new Error(\n      \"Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.\"\n    );\n    throw error;\n  }\n  if (!targetId || !state.records.commits.has(targetId)) {\n    if (sourceCommitBranch === state.records.currBranch) {\n      const error = new Error(\n        'Incorrect usage of \"cherryPick\". Source commit is already on current branch'\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommitId = state.records.branches.get(state.records.currBranch);\n    if (currentCommitId === void 0 || !currentCommitId) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommit = state.records.commits.get(currentCommitId);\n    if (currentCommit === void 0 || !currentCommit) {\n      const error = new Error(\n        `Incorrect usage of \"cherry-pick\". Current branch (${state.records.currBranch})has no commits`\n      );\n      error.hash = {\n        text: `cherryPick ${sourceId} ${targetId}`,\n        token: `cherryPick ${sourceId} ${targetId}`,\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const commit2 = {\n      id: state.records.seq + \"-\" + getID(),\n      message: `cherry-picked ${sourceCommit?.message} into ${state.records.currBranch}`,\n      seq: state.records.seq++,\n      parents: state.records.head == null ? [] : [state.records.head.id, sourceCommit.id],\n      branch: state.records.currBranch,\n      type: commitType.CHERRY_PICK,\n      tags: tags ? tags.filter(Boolean) : [\n        `cherry-pick:${sourceCommit.id}${sourceCommit.type === commitType.MERGE ? `|parent:${parentCommitId}` : \"\"}`\n      ]\n    };\n    state.records.head = commit2;\n    state.records.commits.set(commit2.id, commit2);\n    state.records.branches.set(state.records.currBranch, commit2.id);\n    log.debug(state.records.branches);\n    log.debug(\"in cherryPick\");\n  }\n}, \"cherryPick\");\nvar checkout = /* @__PURE__ */ __name(function(branch2) {\n  branch2 = common_default.sanitizeText(branch2, getConfig3());\n  if (!state.records.branches.has(branch2)) {\n    const error = new Error(\n      `Trying to checkout branch which is not yet created. (Help try using \"branch ${branch2}\")`\n    );\n    error.hash = {\n      text: `checkout ${branch2}`,\n      token: `checkout ${branch2}`,\n      expected: [`branch ${branch2}`]\n    };\n    throw error;\n  } else {\n    state.records.currBranch = branch2;\n    const id = state.records.branches.get(state.records.currBranch);\n    if (id === void 0 || !id) {\n      state.records.head = null;\n    } else {\n      state.records.head = state.records.commits.get(id) ?? null;\n    }\n  }\n}, \"checkout\");\nfunction upsert(arr, key, newVal) {\n  const index = arr.indexOf(key);\n  if (index === -1) {\n    arr.push(newVal);\n  } else {\n    arr.splice(index, 1, newVal);\n  }\n}\n__name(upsert, \"upsert\");\nfunction prettyPrintCommitHistory(commitArr) {\n  const commit2 = commitArr.reduce((out, commit3) => {\n    if (out.seq > commit3.seq) {\n      return out;\n    }\n    return commit3;\n  }, commitArr[0]);\n  let line = \"\";\n  commitArr.forEach(function(c) {\n    if (c === commit2) {\n      line += \"\t*\";\n    } else {\n      line += \"\t|\";\n    }\n  });\n  const label = [line, commit2.id, commit2.seq];\n  for (const branch2 in state.records.branches) {\n    if (state.records.branches.get(branch2) === commit2.id) {\n      label.push(branch2);\n    }\n  }\n  log.debug(label.join(\" \"));\n  if (commit2.parents && commit2.parents.length == 2 && commit2.parents[0] && commit2.parents[1]) {\n    const newCommit = state.records.commits.get(commit2.parents[0]);\n    upsert(commitArr, commit2, newCommit);\n    if (commit2.parents[1]) {\n      commitArr.push(state.records.commits.get(commit2.parents[1]));\n    }\n  } else if (commit2.parents.length == 0) {\n    return;\n  } else {\n    if (commit2.parents[0]) {\n      const newCommit = state.records.commits.get(commit2.parents[0]);\n      upsert(commitArr, commit2, newCommit);\n    }\n  }\n  commitArr = uniqBy(commitArr, (c) => c.id);\n  prettyPrintCommitHistory(commitArr);\n}\n__name(prettyPrintCommitHistory, \"prettyPrintCommitHistory\");\nvar prettyPrint = /* @__PURE__ */ __name(function() {\n  log.debug(state.records.commits);\n  const node = getCommitsArray()[0];\n  prettyPrintCommitHistory([node]);\n}, \"prettyPrint\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  state.reset();\n  clear();\n}, \"clear\");\nvar getBranchesAsObjArray = /* @__PURE__ */ __name(function() {\n  const branchesArray = [...state.records.branchConfig.values()].map((branchConfig, i) => {\n    if (branchConfig.order !== null && branchConfig.order !== void 0) {\n      return branchConfig;\n    }\n    return {\n      ...branchConfig,\n      order: parseFloat(`0.${i}`)\n    };\n  }).sort((a, b) => (a.order ?? 0) - (b.order ?? 0)).map(({ name }) => ({ name }));\n  return branchesArray;\n}, \"getBranchesAsObjArray\");\nvar getBranches = /* @__PURE__ */ __name(function() {\n  return state.records.branches;\n}, \"getBranches\");\nvar getCommits = /* @__PURE__ */ __name(function() {\n  return state.records.commits;\n}, \"getCommits\");\nvar getCommitsArray = /* @__PURE__ */ __name(function() {\n  const commitArr = [...state.records.commits.values()];\n  commitArr.forEach(function(o) {\n    log.debug(o.id);\n  });\n  commitArr.sort((a, b) => a.seq - b.seq);\n  return commitArr;\n}, \"getCommitsArray\");\nvar getCurrentBranch = /* @__PURE__ */ __name(function() {\n  return state.records.currBranch;\n}, \"getCurrentBranch\");\nvar getDirection = /* @__PURE__ */ __name(function() {\n  return state.records.direction;\n}, \"getDirection\");\nvar getHead = /* @__PURE__ */ __name(function() {\n  return state.records.head;\n}, \"getHead\");\nvar db = {\n  commitType,\n  getConfig: getConfig3,\n  setDirection,\n  setOptions,\n  getOptions,\n  commit,\n  branch,\n  merge,\n  cherryPick,\n  checkout,\n  //reset,\n  prettyPrint,\n  clear: clear2,\n  getBranchesAsObjArray,\n  getBranches,\n  getCommits,\n  getCommitsArray,\n  getCurrentBranch,\n  getDirection,\n  getHead,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  setDiagramTitle,\n  getDiagramTitle\n};\n\n// src/diagrams/git/gitGraphParser.ts\nvar populate = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  if (ast.dir) {\n    db2.setDirection(ast.dir);\n  }\n  for (const statement of ast.statements) {\n    parseStatement(statement, db2);\n  }\n}, \"populate\");\nvar parseStatement = /* @__PURE__ */ __name((statement, db2) => {\n  const parsers = {\n    Commit: /* @__PURE__ */ __name((stmt) => db2.commit(parseCommit(stmt)), \"Commit\"),\n    Branch: /* @__PURE__ */ __name((stmt) => db2.branch(parseBranch(stmt)), \"Branch\"),\n    Merge: /* @__PURE__ */ __name((stmt) => db2.merge(parseMerge(stmt)), \"Merge\"),\n    Checkout: /* @__PURE__ */ __name((stmt) => db2.checkout(parseCheckout(stmt)), \"Checkout\"),\n    CherryPicking: /* @__PURE__ */ __name((stmt) => db2.cherryPick(parseCherryPicking(stmt)), \"CherryPicking\")\n  };\n  const parser2 = parsers[statement.$type];\n  if (parser2) {\n    parser2(statement);\n  } else {\n    log.error(`Unknown statement type: ${statement.$type}`);\n  }\n}, \"parseStatement\");\nvar parseCommit = /* @__PURE__ */ __name((commit2) => {\n  const commitDB = {\n    id: commit2.id,\n    msg: commit2.message ?? \"\",\n    type: commit2.type !== void 0 ? commitType[commit2.type] : commitType.NORMAL,\n    tags: commit2.tags ?? void 0\n  };\n  return commitDB;\n}, \"parseCommit\");\nvar parseBranch = /* @__PURE__ */ __name((branch2) => {\n  const branchDB = {\n    name: branch2.name,\n    order: branch2.order ?? 0\n  };\n  return branchDB;\n}, \"parseBranch\");\nvar parseMerge = /* @__PURE__ */ __name((merge2) => {\n  const mergeDB = {\n    branch: merge2.branch,\n    id: merge2.id ?? \"\",\n    type: merge2.type !== void 0 ? commitType[merge2.type] : void 0,\n    tags: merge2.tags ?? void 0\n  };\n  return mergeDB;\n}, \"parseMerge\");\nvar parseCheckout = /* @__PURE__ */ __name((checkout2) => {\n  const branch2 = checkout2.branch;\n  return branch2;\n}, \"parseCheckout\");\nvar parseCherryPicking = /* @__PURE__ */ __name((cherryPicking) => {\n  const cherryPickDB = {\n    id: cherryPicking.id,\n    targetId: \"\",\n    tags: cherryPicking.tags?.length === 0 ? void 0 : cherryPicking.tags,\n    parent: cherryPicking.parent\n  };\n  return cherryPickDB;\n}, \"parseCherryPicking\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"gitGraph\", input);\n    log.debug(ast);\n    populate(ast, db);\n  }, \"parse\")\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  const mockDB = {\n    commitType,\n    setDirection: vi.fn(),\n    commit: vi.fn(),\n    branch: vi.fn(),\n    merge: vi.fn(),\n    cherryPick: vi.fn(),\n    checkout: vi.fn()\n  };\n  describe(\"GitGraph Parser\", () => {\n    it(\"should parse a commit statement\", () => {\n      const commit2 = {\n        $type: \"Commit\",\n        id: \"1\",\n        message: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(commit2, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a branch statement\", () => {\n      const branch2 = {\n        $type: \"Branch\",\n        name: \"newBranch\",\n        order: 1\n      };\n      parseStatement(branch2, mockDB);\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n    });\n    it(\"should parse a checkout statement\", () => {\n      const checkout2 = {\n        $type: \"Checkout\",\n        branch: \"newBranch\"\n      };\n      parseStatement(checkout2, mockDB);\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n    it(\"should parse a merge statement\", () => {\n      const merge2 = {\n        $type: \"Merge\",\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: \"NORMAL\"\n      };\n      parseStatement(merge2, mockDB);\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n    });\n    it(\"should parse a cherry picking statement\", () => {\n      const cherryPick2 = {\n        $type: \"CherryPicking\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        parent: \"2\"\n      };\n      parseStatement(cherryPick2, mockDB);\n      expect(mockDB.cherryPick).toHaveBeenCalledWith({\n        id: \"1\",\n        targetId: \"\",\n        parent: \"2\",\n        tags: [\"tag1\", \"tag2\"]\n      });\n    });\n    it(\"should parse a langium generated gitGraph ast\", () => {\n      const dummy = {\n        $type: \"GitGraph\",\n        statements: []\n      };\n      const gitGraphAst = {\n        $type: \"GitGraph\",\n        statements: [\n          {\n            $container: dummy,\n            $type: \"Commit\",\n            id: \"1\",\n            message: \"test\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Branch\",\n            name: \"newBranch\",\n            order: 1\n          },\n          {\n            $container: dummy,\n            $type: \"Merge\",\n            branch: \"newBranch\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            type: \"NORMAL\"\n          },\n          {\n            $container: dummy,\n            $type: \"Checkout\",\n            branch: \"newBranch\"\n          },\n          {\n            $container: dummy,\n            $type: \"CherryPicking\",\n            id: \"1\",\n            tags: [\"tag1\", \"tag2\"],\n            parent: \"2\"\n          }\n        ]\n      };\n      populate(gitGraphAst, mockDB);\n      expect(mockDB.commit).toHaveBeenCalledWith({\n        id: \"1\",\n        msg: \"test\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.branch).toHaveBeenCalledWith({ name: \"newBranch\", order: 1 });\n      expect(mockDB.merge).toHaveBeenCalledWith({\n        branch: \"newBranch\",\n        id: \"1\",\n        tags: [\"tag1\", \"tag2\"],\n        type: 0\n      });\n      expect(mockDB.checkout).toHaveBeenCalledWith(\"newBranch\");\n    });\n  });\n}\n\n// src/diagrams/git/gitGraphRenderer.ts\nimport { select } from \"d3\";\nvar DEFAULT_CONFIG = getConfig2();\nvar DEFAULT_GITGRAPH_CONFIG2 = DEFAULT_CONFIG?.gitGraph;\nvar LAYOUT_OFFSET = 10;\nvar COMMIT_STEP = 40;\nvar PX = 4;\nvar PY = 2;\nvar THEME_COLOR_LIMIT = 8;\nvar branchPos = /* @__PURE__ */ new Map();\nvar commitPos = /* @__PURE__ */ new Map();\nvar defaultPos = 30;\nvar allCommitsDict = /* @__PURE__ */ new Map();\nvar lanes = [];\nvar maxPos = 0;\nvar dir = \"LR\";\nvar clear3 = /* @__PURE__ */ __name(() => {\n  branchPos.clear();\n  commitPos.clear();\n  allCommitsDict.clear();\n  maxPos = 0;\n  lanes = [];\n  dir = \"LR\";\n}, \"clear\");\nvar drawText = /* @__PURE__ */ __name((txt) => {\n  const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  const rows = typeof txt === \"string\" ? txt.split(/\\\\n|\\n|<br\\s*\\/?>/gi) : txt;\n  rows.forEach((row) => {\n    const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n    tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n    tspan.setAttribute(\"dy\", \"1em\");\n    tspan.setAttribute(\"x\", \"0\");\n    tspan.setAttribute(\"class\", \"row\");\n    tspan.textContent = row.trim();\n    svgLabel.appendChild(tspan);\n  });\n  return svgLabel;\n}, \"drawText\");\nvar findClosestParent = /* @__PURE__ */ __name((parents) => {\n  let closestParent;\n  let comparisonFunc;\n  let targetPosition;\n  if (dir === \"BT\") {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a <= b, \"comparisonFunc\");\n    targetPosition = Infinity;\n  } else {\n    comparisonFunc = /* @__PURE__ */ __name((a, b) => a >= b, \"comparisonFunc\");\n    targetPosition = 0;\n  }\n  parents.forEach((parent) => {\n    const parentPosition = dir === \"TB\" || dir == \"BT\" ? commitPos.get(parent)?.y : commitPos.get(parent)?.x;\n    if (parentPosition !== void 0 && comparisonFunc(parentPosition, targetPosition)) {\n      closestParent = parent;\n      targetPosition = parentPosition;\n    }\n  });\n  return closestParent;\n}, \"findClosestParent\");\nvar findClosestParentBT = /* @__PURE__ */ __name((parents) => {\n  let closestParent = \"\";\n  let maxPosition = Infinity;\n  parents.forEach((parent) => {\n    const parentPosition = commitPos.get(parent).y;\n    if (parentPosition <= maxPosition) {\n      closestParent = parent;\n      maxPosition = parentPosition;\n    }\n  });\n  return closestParent || void 0;\n}, \"findClosestParentBT\");\nvar setParallelBTPos = /* @__PURE__ */ __name((sortedKeys, commits, defaultPos2) => {\n  let curPos = defaultPos2;\n  let maxPosition = defaultPos2;\n  const roots = [];\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (commit2.parents.length) {\n      curPos = calculateCommitPosition(commit2);\n      maxPosition = Math.max(curPos, maxPosition);\n    } else {\n      roots.push(commit2);\n    }\n    setCommitPosition(commit2, curPos);\n  });\n  curPos = maxPosition;\n  roots.forEach((commit2) => {\n    setRootPosition(commit2, curPos, defaultPos2);\n  });\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2?.parents.length) {\n      const closestParent = findClosestParentBT(commit2.parents);\n      curPos = commitPos.get(closestParent).y - COMMIT_STEP;\n      if (curPos <= maxPosition) {\n        maxPosition = curPos;\n      }\n      const x = branchPos.get(commit2.branch).pos;\n      const y = curPos - LAYOUT_OFFSET;\n      commitPos.set(commit2.id, { x, y });\n    }\n  });\n}, \"setParallelBTPos\");\nvar findClosestParentPos = /* @__PURE__ */ __name((commit2) => {\n  const closestParent = findClosestParent(commit2.parents.filter((p) => p !== null));\n  if (!closestParent) {\n    throw new Error(`Closest parent not found for commit ${commit2.id}`);\n  }\n  const closestParentPos = commitPos.get(closestParent)?.y;\n  if (closestParentPos === void 0) {\n    throw new Error(`Closest parent position not found for commit ${commit2.id}`);\n  }\n  return closestParentPos;\n}, \"findClosestParentPos\");\nvar calculateCommitPosition = /* @__PURE__ */ __name((commit2) => {\n  const closestParentPos = findClosestParentPos(commit2);\n  return closestParentPos + COMMIT_STEP;\n}, \"calculateCommitPosition\");\nvar setCommitPosition = /* @__PURE__ */ __name((commit2, curPos) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const x = branch2.pos;\n  const y = curPos + LAYOUT_OFFSET;\n  commitPos.set(commit2.id, { x, y });\n  return { x, y };\n}, \"setCommitPosition\");\nvar setRootPosition = /* @__PURE__ */ __name((commit2, curPos, defaultPos2) => {\n  const branch2 = branchPos.get(commit2.branch);\n  if (!branch2) {\n    throw new Error(`Branch not found for commit ${commit2.id}`);\n  }\n  const y = curPos + defaultPos2;\n  const x = branch2.pos;\n  commitPos.set(commit2.id, { x, y });\n}, \"setRootPosition\");\nvar drawCommitBullet = /* @__PURE__ */ __name((gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType) => {\n  if (commitSymbolType === commitType.HIGHLIGHT) {\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 10).attr(\"y\", commitPosition.y - 10).attr(\"width\", 20).attr(\"height\", 20).attr(\n      \"class\",\n      `commit ${commit2.id} commit-highlight${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-outer`\n    );\n    gBullets.append(\"rect\").attr(\"x\", commitPosition.x - 6).attr(\"y\", commitPosition.y - 6).attr(\"width\", 12).attr(\"height\", 12).attr(\n      \"class\",\n      `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT} ${typeClass}-inner`\n    );\n  } else if (commitSymbolType === commitType.CHERRY_PICK) {\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x).attr(\"cy\", commitPosition.y).attr(\"r\", 10).attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x - 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"circle\").attr(\"cx\", commitPosition.x + 3).attr(\"cy\", commitPosition.y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x + 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n    gBullets.append(\"line\").attr(\"x1\", commitPosition.x - 3).attr(\"y1\", commitPosition.y + 1).attr(\"x2\", commitPosition.x).attr(\"y2\", commitPosition.y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n  } else {\n    const circle = gBullets.append(\"circle\");\n    circle.attr(\"cx\", commitPosition.x);\n    circle.attr(\"cy\", commitPosition.y);\n    circle.attr(\"r\", commit2.type === commitType.MERGE ? 9 : 10);\n    circle.attr(\"class\", `commit ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    if (commitSymbolType === commitType.MERGE) {\n      const circle2 = gBullets.append(\"circle\");\n      circle2.attr(\"cx\", commitPosition.x);\n      circle2.attr(\"cy\", commitPosition.y);\n      circle2.attr(\"r\", 6);\n      circle2.attr(\n        \"class\",\n        `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`\n      );\n    }\n    if (commitSymbolType === commitType.REVERSE) {\n      const cross = gBullets.append(\"path\");\n      cross.attr(\n        \"d\",\n        `M ${commitPosition.x - 5},${commitPosition.y - 5}L${commitPosition.x + 5},${commitPosition.y + 5}M${commitPosition.x - 5},${commitPosition.y + 5}L${commitPosition.x + 5},${commitPosition.y - 5}`\n      ).attr(\"class\", `commit ${typeClass} ${commit2.id} commit${branchIndex % THEME_COLOR_LIMIT}`);\n    }\n  }\n}, \"drawCommitBullet\");\nvar drawCommitLabel = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.type !== commitType.CHERRY_PICK && (commit2.customId && commit2.type === commitType.MERGE || commit2.type !== commitType.MERGE) && DEFAULT_GITGRAPH_CONFIG2?.showCommitLabel) {\n    const wrapper = gLabels.append(\"g\");\n    const labelBkg = wrapper.insert(\"rect\").attr(\"class\", \"commit-label-bkg\");\n    const text = wrapper.append(\"text\").attr(\"x\", pos).attr(\"y\", commitPosition.y + 25).attr(\"class\", \"commit-label\").text(commit2.id);\n    const bbox = text.node()?.getBBox();\n    if (bbox) {\n      labelBkg.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2 - PY).attr(\"y\", commitPosition.y + 13.5).attr(\"width\", bbox.width + 2 * PY).attr(\"height\", bbox.height + 2 * PY);\n      if (dir === \"TB\" || dir === \"BT\") {\n        labelBkg.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX + 5)).attr(\"y\", commitPosition.y - 12);\n        text.attr(\"x\", commitPosition.x - (bbox.width + 4 * PX)).attr(\"y\", commitPosition.y + bbox.height - 12);\n      } else {\n        text.attr(\"x\", commitPosition.posWithOffset - bbox.width / 2);\n      }\n      if (DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel) {\n        if (dir === \"TB\" || dir === \"BT\") {\n          text.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n          labelBkg.attr(\n            \"transform\",\n            \"rotate(-45, \" + commitPosition.x + \", \" + commitPosition.y + \")\"\n          );\n        } else {\n          const r_x = -7.5 - (bbox.width + 10) / 25 * 9.5;\n          const r_y = 10 + bbox.width / 25 * 8.5;\n          wrapper.attr(\n            \"transform\",\n            \"translate(\" + r_x + \", \" + r_y + \") rotate(-45, \" + pos + \", \" + commitPosition.y + \")\"\n          );\n        }\n      }\n    }\n  }\n}, \"drawCommitLabel\");\nvar drawCommitTags = /* @__PURE__ */ __name((gLabels, commit2, commitPosition, pos) => {\n  if (commit2.tags.length > 0) {\n    let yOffset = 0;\n    let maxTagBboxWidth = 0;\n    let maxTagBboxHeight = 0;\n    const tagElements = [];\n    for (const tagValue of commit2.tags.reverse()) {\n      const rect = gLabels.insert(\"polygon\");\n      const hole = gLabels.append(\"circle\");\n      const tag = gLabels.append(\"text\").attr(\"y\", commitPosition.y - 16 - yOffset).attr(\"class\", \"tag-label\").text(tagValue);\n      const tagBbox = tag.node()?.getBBox();\n      if (!tagBbox) {\n        throw new Error(\"Tag bbox not found\");\n      }\n      maxTagBboxWidth = Math.max(maxTagBboxWidth, tagBbox.width);\n      maxTagBboxHeight = Math.max(maxTagBboxHeight, tagBbox.height);\n      tag.attr(\"x\", commitPosition.posWithOffset - tagBbox.width / 2);\n      tagElements.push({\n        tag,\n        hole,\n        rect,\n        yOffset\n      });\n      yOffset += 20;\n    }\n    for (const { tag, hole, rect, yOffset: yOffset2 } of tagElements) {\n      const h2 = maxTagBboxHeight / 2;\n      const ly = commitPosition.y - 19.2 - yOffset2;\n      rect.attr(\"class\", \"tag-label-bkg\").attr(\n        \"points\",\n        `\n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly + PY}  \n      ${pos - maxTagBboxWidth / 2 - PX / 2},${ly - PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly - h2 - PY}\n      ${commitPosition.posWithOffset + maxTagBboxWidth / 2 + PX},${ly + h2 + PY}\n      ${commitPosition.posWithOffset - maxTagBboxWidth / 2 - PX},${ly + h2 + PY}`\n      );\n      hole.attr(\"cy\", ly).attr(\"cx\", pos - maxTagBboxWidth / 2 + PX / 2).attr(\"r\", 1.5).attr(\"class\", \"tag-hole\");\n      if (dir === \"TB\" || dir === \"BT\") {\n        const yOrigin = pos + yOffset2;\n        rect.attr(\"class\", \"tag-label-bkg\").attr(\n          \"points\",\n          `\n        ${commitPosition.x},${yOrigin + 2}\n        ${commitPosition.x},${yOrigin - 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin - h2 - 2}\n        ${commitPosition.x + LAYOUT_OFFSET + maxTagBboxWidth + 4},${yOrigin + h2 + 2}\n        ${commitPosition.x + LAYOUT_OFFSET},${yOrigin + h2 + 2}`\n        ).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        hole.attr(\"cx\", commitPosition.x + PX / 2).attr(\"cy\", yOrigin).attr(\"transform\", \"translate(12,12) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n        tag.attr(\"x\", commitPosition.x + 5).attr(\"y\", yOrigin + 3).attr(\"transform\", \"translate(14,14) rotate(45, \" + commitPosition.x + \",\" + pos + \")\");\n      }\n    }\n  }\n}, \"drawCommitTags\");\nvar getCommitClassType = /* @__PURE__ */ __name((commit2) => {\n  const commitSymbolType = commit2.customType ?? commit2.type;\n  switch (commitSymbolType) {\n    case commitType.NORMAL:\n      return \"commit-normal\";\n    case commitType.REVERSE:\n      return \"commit-reverse\";\n    case commitType.HIGHLIGHT:\n      return \"commit-highlight\";\n    case commitType.MERGE:\n      return \"commit-merge\";\n    case commitType.CHERRY_PICK:\n      return \"commit-cherry-pick\";\n    default:\n      return \"commit-normal\";\n  }\n}, \"getCommitClassType\");\nvar calculatePosition = /* @__PURE__ */ __name((commit2, dir2, pos, commitPos2) => {\n  const defaultCommitPosition = { x: 0, y: 0 };\n  if (commit2.parents.length > 0) {\n    const closestParent = findClosestParent(commit2.parents);\n    if (closestParent) {\n      const parentPosition = commitPos2.get(closestParent) ?? defaultCommitPosition;\n      if (dir2 === \"TB\") {\n        return parentPosition.y + COMMIT_STEP;\n      } else if (dir2 === \"BT\") {\n        const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n        return currentPosition.y - COMMIT_STEP;\n      } else {\n        return parentPosition.x + COMMIT_STEP;\n      }\n    }\n  } else {\n    if (dir2 === \"TB\") {\n      return defaultPos;\n    } else if (dir2 === \"BT\") {\n      const currentPosition = commitPos2.get(commit2.id) ?? defaultCommitPosition;\n      return currentPosition.y - COMMIT_STEP;\n    } else {\n      return 0;\n    }\n  }\n  return 0;\n}, \"calculatePosition\");\nvar getCommitPosition = /* @__PURE__ */ __name((commit2, pos, isParallelCommits) => {\n  const posWithOffset = dir === \"BT\" && isParallelCommits ? pos : pos + LAYOUT_OFFSET;\n  const y = dir === \"TB\" || dir === \"BT\" ? posWithOffset : branchPos.get(commit2.branch)?.pos;\n  const x = dir === \"TB\" || dir === \"BT\" ? branchPos.get(commit2.branch)?.pos : posWithOffset;\n  if (x === void 0 || y === void 0) {\n    throw new Error(`Position were undefined for commit ${commit2.id}`);\n  }\n  return { x, y, posWithOffset };\n}, \"getCommitPosition\");\nvar drawCommits = /* @__PURE__ */ __name((svg, commits, modifyGraph) => {\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const gBullets = svg.append(\"g\").attr(\"class\", \"commit-bullets\");\n  const gLabels = svg.append(\"g\").attr(\"class\", \"commit-labels\");\n  let pos = dir === \"TB\" || dir === \"BT\" ? defaultPos : 0;\n  const keys = [...commits.keys()];\n  const isParallelCommits = DEFAULT_GITGRAPH_CONFIG2?.parallelCommits ?? false;\n  const sortKeys = /* @__PURE__ */ __name((a, b) => {\n    const seqA = commits.get(a)?.seq;\n    const seqB = commits.get(b)?.seq;\n    return seqA !== void 0 && seqB !== void 0 ? seqA - seqB : 0;\n  }, \"sortKeys\");\n  let sortedKeys = keys.sort(sortKeys);\n  if (dir === \"BT\") {\n    if (isParallelCommits) {\n      setParallelBTPos(sortedKeys, commits, pos);\n    }\n    sortedKeys = sortedKeys.reverse();\n  }\n  sortedKeys.forEach((key) => {\n    const commit2 = commits.get(key);\n    if (!commit2) {\n      throw new Error(`Commit not found for key ${key}`);\n    }\n    if (isParallelCommits) {\n      pos = calculatePosition(commit2, dir, pos, commitPos);\n    }\n    const commitPosition = getCommitPosition(commit2, pos, isParallelCommits);\n    if (modifyGraph) {\n      const typeClass = getCommitClassType(commit2);\n      const commitSymbolType = commit2.customType ?? commit2.type;\n      const branchIndex = branchPos.get(commit2.branch)?.index ?? 0;\n      drawCommitBullet(gBullets, commit2, commitPosition, typeClass, branchIndex, commitSymbolType);\n      drawCommitLabel(gLabels, commit2, commitPosition, pos);\n      drawCommitTags(gLabels, commit2, commitPosition, pos);\n    }\n    if (dir === \"TB\" || dir === \"BT\") {\n      commitPos.set(commit2.id, { x: commitPosition.x, y: commitPosition.posWithOffset });\n    } else {\n      commitPos.set(commit2.id, { x: commitPosition.posWithOffset, y: commitPosition.y });\n    }\n    pos = dir === \"BT\" && isParallelCommits ? pos + COMMIT_STEP : pos + COMMIT_STEP + LAYOUT_OFFSET;\n    if (pos > maxPos) {\n      maxPos = pos;\n    }\n  });\n}, \"drawCommits\");\nvar shouldRerouteArrow = /* @__PURE__ */ __name((commitA, commitB, p1, p2, allCommits) => {\n  const commitBIsFurthest = dir === \"TB\" || dir === \"BT\" ? p1.x < p2.x : p1.y < p2.y;\n  const branchToGetCurve = commitBIsFurthest ? commitB.branch : commitA.branch;\n  const isOnBranchToGetCurve = /* @__PURE__ */ __name((x) => x.branch === branchToGetCurve, \"isOnBranchToGetCurve\");\n  const isBetweenCommits = /* @__PURE__ */ __name((x) => x.seq > commitA.seq && x.seq < commitB.seq, \"isBetweenCommits\");\n  return [...allCommits.values()].some((commitX) => {\n    return isBetweenCommits(commitX) && isOnBranchToGetCurve(commitX);\n  });\n}, \"shouldRerouteArrow\");\nvar findLane = /* @__PURE__ */ __name((y1, y2, depth = 0) => {\n  const candidate = y1 + Math.abs(y1 - y2) / 2;\n  if (depth > 5) {\n    return candidate;\n  }\n  const ok = lanes.every((lane) => Math.abs(lane - candidate) >= 10);\n  if (ok) {\n    lanes.push(candidate);\n    return candidate;\n  }\n  const diff = Math.abs(y1 - y2);\n  return findLane(y1, y2 - diff / 5, depth + 1);\n}, \"findLane\");\nvar drawArrow = /* @__PURE__ */ __name((svg, commitA, commitB, allCommits) => {\n  const p1 = commitPos.get(commitA.id);\n  const p2 = commitPos.get(commitB.id);\n  if (p1 === void 0 || p2 === void 0) {\n    throw new Error(`Commit positions not found for commits ${commitA.id} and ${commitB.id}`);\n  }\n  const arrowNeedsRerouting = shouldRerouteArrow(commitA, commitB, p1, p2, allCommits);\n  let arc = \"\";\n  let arc2 = \"\";\n  let radius = 0;\n  let offset = 0;\n  let colorClassNum = branchPos.get(commitB.branch)?.index;\n  if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n    colorClassNum = branchPos.get(commitA.branch)?.index;\n  }\n  let lineDef;\n  if (arrowNeedsRerouting) {\n    arc = \"A 10 10, 0, 0, 0,\";\n    arc2 = \"A 10 10, 0, 0, 1,\";\n    radius = 10;\n    offset = 10;\n    const lineY = p1.y < p2.y ? findLane(p1.y, p2.y) : findLane(p2.y, p1.y);\n    const lineX = p1.x < p2.x ? findLane(p1.x, p2.x) : findLane(p2.x, p1.x);\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc2} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc2} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc2} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc2} ${lineX} ${p1.y - offset} L ${lineX} ${p2.y + radius} ${arc} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY - radius} ${arc} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc2} ${p2.x} ${lineY + offset} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos.get(commitA.branch)?.index;\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY + radius} ${arc2} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc} ${p2.x} ${lineY - offset} L ${p2.x} ${p2.y}`;\n      }\n    }\n  } else {\n    arc = \"A 20 20, 0, 0, 0,\";\n    arc2 = \"A 20 20, 0, 0, 1,\";\n    radius = 20;\n    offset = 20;\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc2} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x + radius} ${p1.y} ${arc} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else if (dir === \"BT\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y > p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y === p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    }\n  }\n  if (lineDef === void 0) {\n    throw new Error(\"Line definition not found\");\n  }\n  svg.append(\"path\").attr(\"d\", lineDef).attr(\"class\", \"arrow arrow\" + colorClassNum % THEME_COLOR_LIMIT);\n}, \"drawArrow\");\nvar drawArrows = /* @__PURE__ */ __name((svg, commits) => {\n  const gArrows = svg.append(\"g\").attr(\"class\", \"commit-arrows\");\n  [...commits.keys()].forEach((key) => {\n    const commit2 = commits.get(key);\n    if (commit2.parents && commit2.parents.length > 0) {\n      commit2.parents.forEach((parent) => {\n        drawArrow(gArrows, commits.get(parent), commit2, commits);\n      });\n    }\n  });\n}, \"drawArrows\");\nvar drawBranches = /* @__PURE__ */ __name((svg, branches) => {\n  const g = svg.append(\"g\");\n  branches.forEach((branch2, index) => {\n    const adjustIndexForTheme = index % THEME_COLOR_LIMIT;\n    const pos = branchPos.get(branch2.name)?.pos;\n    if (pos === void 0) {\n      throw new Error(`Position not found for branch ${branch2.name}`);\n    }\n    const line = g.append(\"line\");\n    line.attr(\"x1\", 0);\n    line.attr(\"y1\", pos);\n    line.attr(\"x2\", maxPos);\n    line.attr(\"y2\", pos);\n    line.attr(\"class\", \"branch branch\" + adjustIndexForTheme);\n    if (dir === \"TB\") {\n      line.attr(\"y1\", defaultPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", maxPos);\n      line.attr(\"x2\", pos);\n    } else if (dir === \"BT\") {\n      line.attr(\"y1\", maxPos);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", defaultPos);\n      line.attr(\"x2\", pos);\n    }\n    lanes.push(pos);\n    const name = branch2.name;\n    const labelElement = drawText(name);\n    const bkg = g.insert(\"rect\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\" + adjustIndexForTheme);\n    label.node().appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    bkg.attr(\"class\", \"branchLabelBkg label\" + adjustIndexForTheme).attr(\"rx\", 4).attr(\"ry\", 4).attr(\"x\", -bbox.width - 4 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)).attr(\"y\", -bbox.height / 2 + 8).attr(\"width\", bbox.width + 18).attr(\"height\", bbox.height + 4);\n    label.attr(\n      \"transform\",\n      \"translate(\" + (-bbox.width - 14 - (DEFAULT_GITGRAPH_CONFIG2?.rotateCommitLabel === true ? 30 : 0)) + \", \" + (pos - bbox.height / 2 - 1) + \")\"\n    );\n    if (dir === \"TB\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", 0);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", 0)\");\n    } else if (dir === \"BT\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", maxPos);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", \" + maxPos + \")\");\n    } else {\n      bkg.attr(\"transform\", \"translate(-19, \" + (pos - bbox.height / 2) + \")\");\n    }\n  });\n}, \"drawBranches\");\nvar setBranchPosition = /* @__PURE__ */ __name(function(name, pos, index, bbox, rotateCommitLabel) {\n  branchPos.set(name, { pos, index });\n  pos += 50 + (rotateCommitLabel ? 40 : 0) + (dir === \"TB\" || dir === \"BT\" ? bbox.width / 2 : 0);\n  return pos;\n}, \"setBranchPosition\");\nvar draw = /* @__PURE__ */ __name(function(txt, id, ver, diagObj) {\n  clear3();\n  log.debug(\"in gitgraph renderer\", txt + \"\\n\", \"id:\", id, ver);\n  if (!DEFAULT_GITGRAPH_CONFIG2) {\n    throw new Error(\"GitGraph config not found\");\n  }\n  const rotateCommitLabel = DEFAULT_GITGRAPH_CONFIG2.rotateCommitLabel ?? false;\n  const db2 = diagObj.db;\n  allCommitsDict = db2.getCommits();\n  const branches = db2.getBranchesAsObjArray();\n  dir = db2.getDirection();\n  const diagram2 = select(`[id=\"${id}\"]`);\n  let pos = 0;\n  branches.forEach((branch2, index) => {\n    const labelElement = drawText(branch2.name);\n    const g = diagram2.append(\"g\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\");\n    label.node()?.appendChild(labelElement);\n    const bbox = labelElement.getBBox();\n    pos = setBranchPosition(branch2.name, pos, index, bbox, rotateCommitLabel);\n    label.remove();\n    branchLabel.remove();\n    g.remove();\n  });\n  drawCommits(diagram2, allCommitsDict, false);\n  if (DEFAULT_GITGRAPH_CONFIG2.showBranches) {\n    drawBranches(diagram2, branches);\n  }\n  drawArrows(diagram2, allCommitsDict);\n  drawCommits(diagram2, allCommitsDict, true);\n  utils_default.insertTitle(\n    diagram2,\n    \"gitTitleText\",\n    DEFAULT_GITGRAPH_CONFIG2.titleTopMargin ?? 0,\n    db2.getDiagramTitle()\n  );\n  setupGraphViewbox(\n    void 0,\n    diagram2,\n    DEFAULT_GITGRAPH_CONFIG2.diagramPadding,\n    DEFAULT_GITGRAPH_CONFIG2.useMaxWidth\n  );\n}, \"draw\");\nvar gitGraphRenderer_default = {\n  draw\n};\nif (void 0) {\n  const { it, expect, describe } = void 0;\n  describe(\"drawText\", () => {\n    it(\"should drawText\", () => {\n      const svgLabel = drawText(\"main\");\n      expect(svgLabel).toBeDefined();\n      expect(svgLabel.children[0].innerHTML).toBe(\"main\");\n    });\n  });\n  describe(\"branchPosition\", () => {\n    const bbox = {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 10,\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      toJSON: /* @__PURE__ */ __name(() => \"\", \"toJSON\")\n    };\n    it(\"should setBranchPositions LR with two branches\", () => {\n      dir = \"LR\";\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(90);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(180);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n    it(\"should setBranchPositions TB with two branches\", () => {\n      dir = \"TB\";\n      bbox.width = 34.9921875;\n      const pos = setBranchPosition(\"main\", 0, 0, bbox, true);\n      expect(pos).toBe(107.49609375);\n      expect(branchPos.get(\"main\")).toEqual({ pos: 0, index: 0 });\n      bbox.width = 56.421875;\n      const posNext = setBranchPosition(\"develop\", pos, 1, bbox, true);\n      expect(posNext).toBe(225.70703125);\n      expect(branchPos.get(\"develop\")).toEqual({ pos, index: 1 });\n    });\n  });\n  describe(\"commitPosition\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"commitZero\",\n        {\n          id: \"ZERO\",\n          message: \"\",\n          seq: 0,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"commitA\",\n        {\n          id: \"A\",\n          message: \"\",\n          seq: 1,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitB\",\n        {\n          id: \"B\",\n          message: \"\",\n          seq: 2,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"A\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"commitM\",\n        {\n          id: \"M\",\n          message: \"merged branch feature into main\",\n          seq: 3,\n          type: commitType.MERGE,\n          tags: [],\n          parents: [\"ZERO\", \"B\"],\n          branch: \"main\",\n          customId: true\n        }\n      ],\n      [\n        \"commitC\",\n        {\n          id: \"C\",\n          message: \"\",\n          seq: 4,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"ZERO\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit5_8928ea0\",\n        {\n          id: \"5-8928ea0\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 5,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"C\", \"M\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commitD\",\n        {\n          id: \"D\",\n          message: \"\",\n          seq: 6,\n          type: commitType.NORMAL,\n          tags: [],\n          parents: [\"5-8928ea0\"],\n          branch: \"release\"\n        }\n      ],\n      [\n        \"commit7_ed848ba\",\n        {\n          id: \"7-ed848ba\",\n          message: \"cherry-picked [object Object] into release\",\n          seq: 7,\n          type: commitType.CHERRY_PICK,\n          tags: [],\n          parents: [\"D\", \"M\"],\n          branch: \"release\"\n        }\n      ]\n    ]);\n    let pos = 0;\n    branchPos.set(\"main\", { pos: 0, index: 0 });\n    branchPos.set(\"feature\", { pos: 107.49609375, index: 1 });\n    branchPos.set(\"release\", { pos: 224.03515625, index: 2 });\n    describe(\"TB\", () => {\n      pos = 30;\n      dir = \"TB\";\n      const expectedCommitPositionTB = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos, false);\n          expect(position).toEqual(expectedCommitPositionTB.get(key));\n          pos += 50;\n        });\n      });\n    });\n    describe(\"LR\", () => {\n      let pos2 = 30;\n      dir = \"LR\";\n      const expectedCommitPositionLR = /* @__PURE__ */ new Map([\n        [\"commitZero\", { x: 0, y: 40, posWithOffset: 40 }],\n        [\"commitA\", { x: 107.49609375, y: 90, posWithOffset: 90 }],\n        [\"commitB\", { x: 107.49609375, y: 140, posWithOffset: 140 }],\n        [\"commitM\", { x: 0, y: 190, posWithOffset: 190 }],\n        [\"commitC\", { x: 224.03515625, y: 240, posWithOffset: 240 }],\n        [\"commit5_8928ea0\", { x: 224.03515625, y: 290, posWithOffset: 290 }],\n        [\"commitD\", { x: 224.03515625, y: 340, posWithOffset: 340 }],\n        [\"commit7_ed848ba\", { x: 224.03515625, y: 390, posWithOffset: 390 }]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct position for commit ${key}`, () => {\n          const position = getCommitPosition(commit2, pos2, false);\n          expect(position).toEqual(expectedCommitPositionLR.get(key));\n          pos2 += 50;\n        });\n      });\n    });\n    describe(\"getCommitClassType\", () => {\n      const expectedCommitClassType = /* @__PURE__ */ new Map([\n        [\"commitZero\", \"commit-normal\"],\n        [\"commitA\", \"commit-normal\"],\n        [\"commitB\", \"commit-normal\"],\n        [\"commitM\", \"commit-merge\"],\n        [\"commitC\", \"commit-normal\"],\n        [\"commit5_8928ea0\", \"commit-cherry-pick\"],\n        [\"commitD\", \"commit-normal\"],\n        [\"commit7_ed848ba\", \"commit-cherry-pick\"]\n      ]);\n      commits.forEach((commit2, key) => {\n        it(`should give the correct class type for commit ${key}`, () => {\n          const classType = getCommitClassType(commit2);\n          expect(classType).toBe(expectedCommitClassType.get(key));\n        });\n      });\n    });\n  });\n  describe(\"building BT parallel commit diagram\", () => {\n    const commits = /* @__PURE__ */ new Map([\n      [\n        \"1-abcdefg\",\n        {\n          id: \"1-abcdefg\",\n          message: \"\",\n          seq: 0,\n          type: 0,\n          tags: [],\n          parents: [],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"2-abcdefg\",\n        {\n          id: \"2-abcdefg\",\n          message: \"\",\n          seq: 1,\n          type: 0,\n          tags: [],\n          parents: [\"1-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"3-abcdefg\",\n        {\n          id: \"3-abcdefg\",\n          message: \"\",\n          seq: 2,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"4-abcdefg\",\n        {\n          id: \"4-abcdefg\",\n          message: \"\",\n          seq: 3,\n          type: 0,\n          tags: [],\n          parents: [\"3-abcdefg\"],\n          branch: \"develop\"\n        }\n      ],\n      [\n        \"5-abcdefg\",\n        {\n          id: \"5-abcdefg\",\n          message: \"\",\n          seq: 4,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"6-abcdefg\",\n        {\n          id: \"6-abcdefg\",\n          message: \"\",\n          seq: 5,\n          type: 0,\n          tags: [],\n          parents: [\"5-abcdefg\"],\n          branch: \"feature\"\n        }\n      ],\n      [\n        \"7-abcdefg\",\n        {\n          id: \"7-abcdefg\",\n          message: \"\",\n          seq: 6,\n          type: 0,\n          tags: [],\n          parents: [\"2-abcdefg\"],\n          branch: \"main\"\n        }\n      ],\n      [\n        \"8-abcdefg\",\n        {\n          id: \"8-abcdefg\",\n          message: \"\",\n          seq: 7,\n          type: 0,\n          tags: [],\n          parents: [\"7-abcdefg\"],\n          branch: \"main\"\n        }\n      ]\n    ]);\n    const expectedCommitPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 40 }],\n      [\"2-abcdefg\", { x: 0, y: 90 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 140 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 190 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 140 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 190 }],\n      [\"7-abcdefg\", { x: 0, y: 140 }],\n      [\"8-abcdefg\", { x: 0, y: 190 }]\n    ]);\n    const expectedCommitPositionAfterParallel = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", { x: 0, y: 210 }],\n      [\"2-abcdefg\", { x: 0, y: 160 }],\n      [\"3-abcdefg\", { x: 107.49609375, y: 110 }],\n      [\"4-abcdefg\", { x: 107.49609375, y: 60 }],\n      [\"5-abcdefg\", { x: 225.70703125, y: 110 }],\n      [\"6-abcdefg\", { x: 225.70703125, y: 60 }],\n      [\"7-abcdefg\", { x: 0, y: 110 }],\n      [\"8-abcdefg\", { x: 0, y: 60 }]\n    ]);\n    const expectedCommitCurrentPosition = /* @__PURE__ */ new Map([\n      [\"1-abcdefg\", 30],\n      [\"2-abcdefg\", 80],\n      [\"3-abcdefg\", 130],\n      [\"4-abcdefg\", 180],\n      [\"5-abcdefg\", 130],\n      [\"6-abcdefg\", 180],\n      [\"7-abcdefg\", 130],\n      [\"8-abcdefg\", 180]\n    ]);\n    const sortedKeys = [...expectedCommitPosition.keys()];\n    it(\"should get the correct commit position and current position\", () => {\n      dir = \"BT\";\n      let curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      DEFAULT_GITGRAPH_CONFIG2.parallelCommits = true;\n      commits.forEach((commit2, key) => {\n        if (commit2.parents.length > 0) {\n          curPos = calculateCommitPosition(commit2);\n        }\n        const position = setCommitPosition(commit2, curPos);\n        expect(position).toEqual(expectedCommitPosition.get(key));\n        expect(curPos).toEqual(expectedCommitCurrentPosition.get(key));\n      });\n    });\n    it(\"should get the correct commit position after parallel commits\", () => {\n      commitPos.clear();\n      branchPos.clear();\n      dir = \"BT\";\n      const curPos = 30;\n      commitPos.clear();\n      branchPos.clear();\n      branchPos.set(\"main\", { pos: 0, index: 0 });\n      branchPos.set(\"develop\", { pos: 107.49609375, index: 1 });\n      branchPos.set(\"feature\", { pos: 225.70703125, index: 2 });\n      setParallelBTPos(sortedKeys, commits, curPos);\n      sortedKeys.forEach((commit2) => {\n        const position = commitPos.get(commit2);\n        expect(position).toEqual(expectedCommitPositionAfterParallel.get(commit2));\n      });\n    });\n  });\n  DEFAULT_GITGRAPH_CONFIG2.parallelCommits = false;\n  it(\"add\", () => {\n    commitPos.set(\"parent1\", { x: 1, y: 1 });\n    commitPos.set(\"parent2\", { x: 2, y: 2 });\n    commitPos.set(\"parent3\", { x: 3, y: 3 });\n    dir = \"LR\";\n    const parents = [\"parent1\", \"parent2\", \"parent3\"];\n    const closestParent = findClosestParent(parents);\n    expect(closestParent).toBe(\"parent3\");\n    commitPos.clear();\n  });\n}\n\n// src/diagrams/git/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0, 1, 2, 3, 4, 5, 6, 7].map(\n  (i) => `\n        .branch-label${i} { fill: ${options[\"gitBranchLabel\" + i]}; }\n        .commit${i} { stroke: ${options[\"git\" + i]}; fill: ${options[\"git\" + i]}; }\n        .commit-highlight${i} { stroke: ${options[\"gitInv\" + i]}; fill: ${options[\"gitInv\" + i]}; }\n        .label${i}  { fill: ${options[\"git\" + i]}; }\n        .arrow${i} { stroke: ${options[\"git\" + i]}; }\n        `\n).join(\"\\n\")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${options.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelColor};}\n  .commit-label-bkg { font-size: ${options.commitLabelFontSize}; fill: ${options.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${options.tagLabelFontSize}; fill: ${options.tagLabelColor};}\n  .tag-label-bkg { fill: ${options.tagLabelBackground}; stroke: ${options.tagLabelBorder}; }\n  .tag-hole { fill: ${options.textColor}; }\n\n  .commit-merge {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${options.primaryColor};\n    fill: ${options.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.textColor};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/git/gitGraphDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer: gitGraphRenderer_default,\n  styles: styles_default\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAgCA,IAAI,aAAa;AAAA,EACf,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,OAAO;AAAA,EACP,aAAa;AACf;AAGA,IAAI,0BAA0B,sBAAsB;AACpD,IAAI,aAA6B,OAAO,MAAM;AAC5C,QAAM,SAAS,cAAc;AAAA,IAC3B,GAAG;AAAA,IACH,GAAG,UAAU,EAAE;AAAA,EACjB,CAAC;AACD,SAAO;AACT,GAAG,WAAW;AACd,IAAI,QAAQ,IAAI,gBAAgB,MAAM;AACpC,QAAM,SAAS,WAAW;AAC1B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,kBAAkB,OAAO;AAC/B,SAAO;AAAA,IACL;AAAA,IACA,SAAyB,oBAAI,IAAI;AAAA,IACjC,MAAM;AAAA,IACN,cAA8B,oBAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM,gBAAgB,OAAO,gBAAgB,CAAC,CAAC,CAAC;AAAA,IAC1G,UAA0B,oBAAI,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC;AAAA,IAC1D,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,KAAK;AAAA,IACL,SAAS,CAAC;AAAA,EACZ;AACF,CAAC;AACD,SAAS,QAAQ;AACf,SAAO,OAAO,EAAE,QAAQ,EAAE,CAAC;AAC7B;AACA,OAAO,OAAO,OAAO;AACrB,SAAS,OAAO,MAAM,IAAI;AACxB,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,SAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAChC,UAAM,MAAM,GAAG,IAAI;AACnB,QAAI,CAAC,UAAU,GAAG,GAAG;AACnB,gBAAU,GAAG,IAAI;AACjB,UAAI,KAAK,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,OAAO,QAAQ,QAAQ;AACvB,IAAI,eAA+B,OAAO,SAAS,MAAM;AACvD,QAAM,QAAQ,YAAY;AAC5B,GAAG,cAAc;AACjB,IAAI,aAA6B,OAAO,SAAS,cAAc;AAC7D,MAAI,MAAM,eAAe,YAAY;AACrC,iBAAe,6CAAc;AAC7B,iBAAe,gBAAgB;AAC/B,MAAI;AACF,UAAM,QAAQ,UAAU,KAAK,MAAM,YAAY;AAAA,EACjD,SAAS,GAAG;AACV,QAAI,MAAM,wCAAwC,EAAE,OAAO;AAAA,EAC7D;AACF,GAAG,YAAY;AACf,IAAI,aAA6B,OAAO,WAAW;AACjD,SAAO,MAAM,QAAQ;AACvB,GAAG,YAAY;AACf,IAAI,SAAyB,OAAO,SAAS,UAAU;AACrD,MAAI,MAAM,SAAS;AACnB,MAAI,KAAK,SAAS;AAClB,QAAM,OAAO,SAAS;AACtB,MAAI,OAAO,SAAS;AACpB,MAAI,KAAK,UAAU,KAAK,IAAI,MAAM,IAAI;AACtC,MAAI,MAAM,oBAAoB,KAAK,IAAI,MAAM,IAAI;AACjD,QAAM,SAAS,WAAW;AAC1B,OAAK,eAAe,aAAa,IAAI,MAAM;AAC3C,QAAM,eAAe,aAAa,KAAK,MAAM;AAC7C,SAAO,6BAAM,IAAI,CAAC,QAAQ,eAAe,aAAa,KAAK,MAAM;AACjE,QAAM,YAAY;AAAA,IAChB,IAAI,KAAK,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM;AAAA,IAC9C,SAAS;AAAA,IACT,KAAK,MAAM,QAAQ;AAAA,IACnB,MAAM,QAAQ,WAAW;AAAA,IACzB,MAAM,QAAQ,CAAC;AAAA,IACf,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,EAAE;AAAA,IACjE,QAAQ,MAAM,QAAQ;AAAA,EACxB;AACA,QAAM,QAAQ,OAAO;AACrB,MAAI,KAAK,eAAe,OAAO,cAAc;AAC7C,QAAM,QAAQ,QAAQ,IAAI,UAAU,IAAI,SAAS;AACjD,QAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAY,UAAU,EAAE;AACjE,MAAI,MAAM,mBAAmB,UAAU,EAAE;AAC3C,GAAG,QAAQ;AACX,IAAI,SAAyB,OAAO,SAAS,UAAU;AACrD,MAAI,OAAO,SAAS;AACpB,QAAM,QAAQ,SAAS;AACvB,SAAO,eAAe,aAAa,MAAM,WAAW,CAAC;AACrD,MAAI,MAAM,QAAQ,SAAS,IAAI,IAAI,GAAG;AACpC,UAAM,IAAI;AAAA,MACR,4HAA4H,IAAI;AAAA,IAClI;AAAA,EACF;AACA,QAAM,QAAQ,SAAS,IAAI,MAAM,MAAM,QAAQ,QAAQ,OAAO,MAAM,QAAQ,KAAK,KAAK,IAAI;AAC1F,QAAM,QAAQ,aAAa,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AACpD,WAAS,IAAI;AACb,MAAI,MAAM,iBAAiB;AAC7B,GAAG,QAAQ;AACX,IAAI,QAAwB,OAAO,CAAC,YAAY;AAC9C,MAAI,cAAc,QAAQ;AAC1B,MAAI,WAAW,QAAQ;AACvB,QAAM,eAAe,QAAQ;AAC7B,QAAM,aAAa,QAAQ;AAC3B,QAAM,SAAS,WAAW;AAC1B,gBAAc,eAAe,aAAa,aAAa,MAAM;AAC7D,MAAI,UAAU;AACZ,eAAW,eAAe,aAAa,UAAU,MAAM;AAAA,EACzD;AACA,QAAM,qBAAqB,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,UAAU;AAC9E,QAAM,mBAAmB,MAAM,QAAQ,SAAS,IAAI,WAAW;AAC/D,QAAM,gBAAgB,qBAAqB,MAAM,QAAQ,QAAQ,IAAI,kBAAkB,IAAI;AAC3F,QAAM,cAAc,mBAAmB,MAAM,QAAQ,QAAQ,IAAI,gBAAgB,IAAI;AACrF,MAAI,iBAAiB,eAAe,cAAc,WAAW,aAAa;AACxE,UAAM,IAAI,MAAM,wBAAwB,WAAW,gBAAgB;AAAA,EACrE;AACA,MAAI,MAAM,QAAQ,eAAe,aAAa;AAC5C,UAAM,QAAQ,IAAI,MAAM,6DAA6D;AACrF,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,YAAY;AAAA,IACzB;AACA,UAAM;AAAA,EACR;AACA,MAAI,kBAAkB,UAAU,CAAC,eAAe;AAC9C,UAAM,QAAQ,IAAI;AAAA,MAChB,+CAA+C,MAAM,QAAQ,UAAU;AAAA,IACzE;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,QAAQ;AAAA,IACrB;AACA,UAAM;AAAA,EACR;AACA,MAAI,CAAC,MAAM,QAAQ,SAAS,IAAI,WAAW,GAAG;AAC5C,UAAM,QAAQ,IAAI;AAAA,MAChB,sDAAsD,cAAc;AAAA,IACtE;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,UAAU,WAAW,EAAE;AAAA,IACpC;AACA,UAAM;AAAA,EACR;AACA,MAAI,gBAAgB,UAAU,CAAC,aAAa;AAC1C,UAAM,QAAQ,IAAI;AAAA,MAChB,sDAAsD,cAAc;AAAA,IACtE;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,UAAU;AAAA,IACvB;AACA,UAAM;AAAA,EACR;AACA,MAAI,kBAAkB,aAAa;AACjC,UAAM,QAAQ,IAAI,MAAM,0DAA0D;AAClF,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW;AAAA,MAC1B,OAAO,SAAS,WAAW;AAAA,MAC3B,UAAU,CAAC,YAAY;AAAA,IACzB;AACA,UAAM;AAAA,EACR;AACA,MAAI,YAAY,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACnD,UAAM,QAAQ,IAAI;AAAA,MAChB,gDAAgD,WAAW;AAAA,IAC7D;AACA,UAAM,OAAO;AAAA,MACX,MAAM,SAAS,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,yCAAY,KAAK,IAAI;AAAA,MAC/E,OAAO,SAAS,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,yCAAY,KAAK,IAAI;AAAA,MAChF,UAAU;AAAA,QACR,SAAS,WAAW,IAAI,QAAQ,WAAW,YAAY,IAAI,yCAAY,KAAK,IAAI;AAAA,MAClF;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACA,QAAM,iBAAiB,mBAAmB,mBAAmB;AAC7D,QAAM,UAAU;AAAA,IACd,IAAI,YAAY,GAAG,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC;AAAA,IAC/C,SAAS,iBAAiB,WAAW,SAAS,MAAM,QAAQ,UAAU;AAAA,IACtE,KAAK,MAAM,QAAQ;AAAA,IACnB,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,cAAc;AAAA,IACjF,QAAQ,MAAM,QAAQ;AAAA,IACtB,MAAM,WAAW;AAAA,IACjB,YAAY;AAAA,IACZ,UAAU,WAAW,OAAO;AAAA,IAC5B,MAAM,cAAc,CAAC;AAAA,EACvB;AACA,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,QAAQ,IAAI,QAAQ,IAAI,OAAO;AAC7C,QAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAY,QAAQ,EAAE;AAC/D,MAAI,MAAM,MAAM,QAAQ,QAAQ;AAChC,MAAI,MAAM,gBAAgB;AAC5B,GAAG,OAAO;AACV,IAAI,aAA6B,OAAO,SAAS,cAAc;AAC7D,MAAI,WAAW,aAAa;AAC5B,MAAI,WAAW,aAAa;AAC5B,MAAI,OAAO,aAAa;AACxB,MAAI,iBAAiB,aAAa;AAClC,MAAI,MAAM,wBAAwB,UAAU,UAAU,IAAI;AAC1D,QAAM,SAAS,WAAW;AAC1B,aAAW,eAAe,aAAa,UAAU,MAAM;AACvD,aAAW,eAAe,aAAa,UAAU,MAAM;AACvD,SAAO,6BAAM,IAAI,CAAC,QAAQ,eAAe,aAAa,KAAK,MAAM;AACjE,mBAAiB,eAAe,aAAa,gBAAgB,MAAM;AACnE,MAAI,CAAC,YAAY,CAAC,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACrD,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM,OAAO;AAAA,MACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,MACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,MACzC,UAAU,CAAC,iBAAiB;AAAA,IAC9B;AACA,UAAM;AAAA,EACR;AACA,QAAM,eAAe,MAAM,QAAQ,QAAQ,IAAI,QAAQ;AACvD,MAAI,iBAAiB,UAAU,CAAC,cAAc;AAC5C,UAAM,IAAI,MAAM,6EAA6E;AAAA,EAC/F;AACA,MAAI,kBAAkB,EAAE,MAAM,QAAQ,aAAa,OAAO,KAAK,aAAa,QAAQ,SAAS,cAAc,IAAI;AAC7G,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACA,QAAM,qBAAqB,aAAa;AACxC,MAAI,aAAa,SAAS,WAAW,SAAS,CAAC,gBAAgB;AAC7D,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM;AAAA,EACR;AACA,MAAI,CAAC,YAAY,CAAC,MAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG;AACrD,QAAI,uBAAuB,MAAM,QAAQ,YAAY;AACnD,YAAM,QAAQ,IAAI;AAAA,QAChB;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACzC,UAAU,CAAC,iBAAiB;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AACA,UAAM,kBAAkB,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,UAAU;AAC3E,QAAI,oBAAoB,UAAU,CAAC,iBAAiB;AAClD,YAAM,QAAQ,IAAI;AAAA,QAChB,qDAAqD,MAAM,QAAQ,UAAU;AAAA,MAC/E;AACA,YAAM,OAAO;AAAA,QACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACzC,UAAU,CAAC,iBAAiB;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AACA,UAAM,gBAAgB,MAAM,QAAQ,QAAQ,IAAI,eAAe;AAC/D,QAAI,kBAAkB,UAAU,CAAC,eAAe;AAC9C,YAAM,QAAQ,IAAI;AAAA,QAChB,qDAAqD,MAAM,QAAQ,UAAU;AAAA,MAC/E;AACA,YAAM,OAAO;AAAA,QACX,MAAM,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACxC,OAAO,cAAc,QAAQ,IAAI,QAAQ;AAAA,QACzC,UAAU,CAAC,iBAAiB;AAAA,MAC9B;AACA,YAAM;AAAA,IACR;AACA,UAAM,UAAU;AAAA,MACd,IAAI,MAAM,QAAQ,MAAM,MAAM,MAAM;AAAA,MACpC,SAAS,iBAAiB,6CAAc,OAAO,SAAS,MAAM,QAAQ,UAAU;AAAA,MAChF,KAAK,MAAM,QAAQ;AAAA,MACnB,SAAS,MAAM,QAAQ,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,KAAK,IAAI,aAAa,EAAE;AAAA,MAClF,QAAQ,MAAM,QAAQ;AAAA,MACtB,MAAM,WAAW;AAAA,MACjB,MAAM,OAAO,KAAK,OAAO,OAAO,IAAI;AAAA,QAClC,eAAe,aAAa,EAAE,GAAG,aAAa,SAAS,WAAW,QAAQ,WAAW,cAAc,KAAK,EAAE;AAAA,MAC5G;AAAA,IACF;AACA,UAAM,QAAQ,OAAO;AACrB,UAAM,QAAQ,QAAQ,IAAI,QAAQ,IAAI,OAAO;AAC7C,UAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAY,QAAQ,EAAE;AAC/D,QAAI,MAAM,MAAM,QAAQ,QAAQ;AAChC,QAAI,MAAM,eAAe;AAAA,EAC3B;AACF,GAAG,YAAY;AACf,IAAI,WAA2B,OAAO,SAAS,SAAS;AACtD,YAAU,eAAe,aAAa,SAAS,WAAW,CAAC;AAC3D,MAAI,CAAC,MAAM,QAAQ,SAAS,IAAI,OAAO,GAAG;AACxC,UAAM,QAAQ,IAAI;AAAA,MAChB,+EAA+E,OAAO;AAAA,IACxF;AACA,UAAM,OAAO;AAAA,MACX,MAAM,YAAY,OAAO;AAAA,MACzB,OAAO,YAAY,OAAO;AAAA,MAC1B,UAAU,CAAC,UAAU,OAAO,EAAE;AAAA,IAChC;AACA,UAAM;AAAA,EACR,OAAO;AACL,UAAM,QAAQ,aAAa;AAC3B,UAAM,KAAK,MAAM,QAAQ,SAAS,IAAI,MAAM,QAAQ,UAAU;AAC9D,QAAI,OAAO,UAAU,CAAC,IAAI;AACxB,YAAM,QAAQ,OAAO;AAAA,IACvB,OAAO;AACL,YAAM,QAAQ,OAAO,MAAM,QAAQ,QAAQ,IAAI,EAAE,KAAK;AAAA,IACxD;AAAA,EACF;AACF,GAAG,UAAU;AACb,SAAS,OAAO,KAAK,KAAK,QAAQ;AAChC,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,MAAI,UAAU,IAAI;AAChB,QAAI,KAAK,MAAM;AAAA,EACjB,OAAO;AACL,QAAI,OAAO,OAAO,GAAG,MAAM;AAAA,EAC7B;AACF;AACA,OAAO,QAAQ,QAAQ;AACvB,SAAS,yBAAyB,WAAW;AAC3C,QAAM,UAAU,UAAU,OAAO,CAAC,KAAK,YAAY;AACjD,QAAI,IAAI,MAAM,QAAQ,KAAK;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG,UAAU,CAAC,CAAC;AACf,MAAI,OAAO;AACX,YAAU,QAAQ,SAAS,GAAG;AAC5B,QAAI,MAAM,SAAS;AACjB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,CAAC,MAAM,QAAQ,IAAI,QAAQ,GAAG;AAC5C,aAAW,WAAW,MAAM,QAAQ,UAAU;AAC5C,QAAI,MAAM,QAAQ,SAAS,IAAI,OAAO,MAAM,QAAQ,IAAI;AACtD,YAAM,KAAK,OAAO;AAAA,IACpB;AAAA,EACF;AACA,MAAI,MAAM,MAAM,KAAK,GAAG,CAAC;AACzB,MAAI,QAAQ,WAAW,QAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ,CAAC,KAAK,QAAQ,QAAQ,CAAC,GAAG;AAC9F,UAAM,YAAY,MAAM,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,CAAC;AAC9D,WAAO,WAAW,SAAS,SAAS;AACpC,QAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,gBAAU,KAAK,MAAM,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,CAAC,CAAC;AAAA,IAC9D;AAAA,EACF,WAAW,QAAQ,QAAQ,UAAU,GAAG;AACtC;AAAA,EACF,OAAO;AACL,QAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,YAAM,YAAY,MAAM,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,CAAC;AAC9D,aAAO,WAAW,SAAS,SAAS;AAAA,IACtC;AAAA,EACF;AACA,cAAY,OAAO,WAAW,CAAC,MAAM,EAAE,EAAE;AACzC,2BAAyB,SAAS;AACpC;AACA,OAAO,0BAA0B,0BAA0B;AAC3D,IAAI,cAA8B,OAAO,WAAW;AAClD,MAAI,MAAM,MAAM,QAAQ,OAAO;AAC/B,QAAM,OAAO,gBAAgB,EAAE,CAAC;AAChC,2BAAyB,CAAC,IAAI,CAAC;AACjC,GAAG,aAAa;AAChB,IAAI,SAAyB,OAAO,WAAW;AAC7C,QAAM,MAAM;AACZ,QAAM;AACR,GAAG,OAAO;AACV,IAAI,wBAAwC,OAAO,WAAW;AAC5D,QAAM,gBAAgB,CAAC,GAAG,MAAM,QAAQ,aAAa,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,MAAM;AACtF,QAAI,aAAa,UAAU,QAAQ,aAAa,UAAU,QAAQ;AAChE,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,OAAO,WAAW,KAAK,CAAC,EAAE;AAAA,IAC5B;AAAA,EACF,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,OAAO,EAAE,KAAK,EAAE;AAC/E,SAAO;AACT,GAAG,uBAAuB;AAC1B,IAAI,cAA8B,OAAO,WAAW;AAClD,SAAO,MAAM,QAAQ;AACvB,GAAG,aAAa;AAChB,IAAI,aAA6B,OAAO,WAAW;AACjD,SAAO,MAAM,QAAQ;AACvB,GAAG,YAAY;AACf,IAAI,kBAAkC,OAAO,WAAW;AACtD,QAAM,YAAY,CAAC,GAAG,MAAM,QAAQ,QAAQ,OAAO,CAAC;AACpD,YAAU,QAAQ,SAAS,GAAG;AAC5B,QAAI,MAAM,EAAE,EAAE;AAAA,EAChB,CAAC;AACD,YAAU,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG;AACtC,SAAO;AACT,GAAG,iBAAiB;AACpB,IAAI,mBAAmC,OAAO,WAAW;AACvD,SAAO,MAAM,QAAQ;AACvB,GAAG,kBAAkB;AACrB,IAAI,eAA+B,OAAO,WAAW;AACnD,SAAO,MAAM,QAAQ;AACvB,GAAG,cAAc;AACjB,IAAI,UAA0B,OAAO,WAAW;AAC9C,SAAO,MAAM,QAAQ;AACvB,GAAG,SAAS;AACZ,IAAI,KAAK;AAAA,EACP;AAAA,EACA,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,WAA2B,OAAO,CAAC,KAAK,QAAQ;AAClD,mBAAiB,KAAK,GAAG;AACzB,MAAI,IAAI,KAAK;AACX,QAAI,aAAa,IAAI,GAAG;AAAA,EAC1B;AACA,aAAW,aAAa,IAAI,YAAY;AACtC,mBAAe,WAAW,GAAG;AAAA,EAC/B;AACF,GAAG,UAAU;AACb,IAAI,iBAAiC,OAAO,CAAC,WAAW,QAAQ;AAC9D,QAAM,UAAU;AAAA,IACd,QAAwB,OAAO,CAAC,SAAS,IAAI,OAAO,YAAY,IAAI,CAAC,GAAG,QAAQ;AAAA,IAChF,QAAwB,OAAO,CAAC,SAAS,IAAI,OAAO,YAAY,IAAI,CAAC,GAAG,QAAQ;AAAA,IAChF,OAAuB,OAAO,CAAC,SAAS,IAAI,MAAM,WAAW,IAAI,CAAC,GAAG,OAAO;AAAA,IAC5E,UAA0B,OAAO,CAAC,SAAS,IAAI,SAAS,cAAc,IAAI,CAAC,GAAG,UAAU;AAAA,IACxF,eAA+B,OAAO,CAAC,SAAS,IAAI,WAAW,mBAAmB,IAAI,CAAC,GAAG,eAAe;AAAA,EAC3G;AACA,QAAM,UAAU,QAAQ,UAAU,KAAK;AACvC,MAAI,SAAS;AACX,YAAQ,SAAS;AAAA,EACnB,OAAO;AACL,QAAI,MAAM,2BAA2B,UAAU,KAAK,EAAE;AAAA,EACxD;AACF,GAAG,gBAAgB;AACnB,IAAI,cAA8B,OAAO,CAAC,YAAY;AACpD,QAAM,WAAW;AAAA,IACf,IAAI,QAAQ;AAAA,IACZ,KAAK,QAAQ,WAAW;AAAA,IACxB,MAAM,QAAQ,SAAS,SAAS,WAAW,QAAQ,IAAI,IAAI,WAAW;AAAA,IACtE,MAAM,QAAQ,QAAQ;AAAA,EACxB;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,cAA8B,OAAO,CAAC,YAAY;AACpD,QAAM,WAAW;AAAA,IACf,MAAM,QAAQ;AAAA,IACd,OAAO,QAAQ,SAAS;AAAA,EAC1B;AACA,SAAO;AACT,GAAG,aAAa;AAChB,IAAI,aAA6B,OAAO,CAAC,WAAW;AAClD,QAAM,UAAU;AAAA,IACd,QAAQ,OAAO;AAAA,IACf,IAAI,OAAO,MAAM;AAAA,IACjB,MAAM,OAAO,SAAS,SAAS,WAAW,OAAO,IAAI,IAAI;AAAA,IACzD,MAAM,OAAO,QAAQ;AAAA,EACvB;AACA,SAAO;AACT,GAAG,YAAY;AACf,IAAI,gBAAgC,OAAO,CAAC,cAAc;AACxD,QAAM,UAAU,UAAU;AAC1B,SAAO;AACT,GAAG,eAAe;AAClB,IAAI,qBAAqC,OAAO,CAAC,kBAAkB;AA/gBnE;AAghBE,QAAM,eAAe;AAAA,IACnB,IAAI,cAAc;AAAA,IAClB,UAAU;AAAA,IACV,QAAM,mBAAc,SAAd,mBAAoB,YAAW,IAAI,SAAS,cAAc;AAAA,IAChE,QAAQ,cAAc;AAAA,EACxB;AACA,SAAO;AACT,GAAG,oBAAoB;AACvB,IAAI,SAAS;AAAA,EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,YAAY,KAAK;AACzC,QAAI,MAAM,GAAG;AACb,aAAS,KAAK,EAAE;AAAA,EAClB,GAAG,OAAO;AACZ;AACA,IAAI,QAAQ;AACV,QAAM,EAAE,IAAI,QAAQ,SAAS,IAAI;AACjC,QAAM,SAAS;AAAA,IACb;AAAA,IACA,cAAc,GAAG,GAAG;AAAA,IACpB,QAAQ,GAAG,GAAG;AAAA,IACd,QAAQ,GAAG,GAAG;AAAA,IACd,OAAO,GAAG,GAAG;AAAA,IACb,YAAY,GAAG,GAAG;AAAA,IAClB,UAAU,GAAG,GAAG;AAAA,EAClB;AACA,WAAS,mBAAmB,MAAM;AAChC,OAAG,mCAAmC,MAAM;AAC1C,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR;AACA,qBAAe,SAAS,MAAM;AAC9B,aAAO,OAAO,MAAM,EAAE,qBAAqB;AAAA,QACzC,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,OAAG,mCAAmC,MAAM;AAC1C,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AACA,qBAAe,SAAS,MAAM;AAC9B,aAAO,OAAO,MAAM,EAAE,qBAAqB,EAAE,MAAM,aAAa,OAAO,EAAE,CAAC;AAAA,IAC5E,CAAC;AACD,OAAG,qCAAqC,MAAM;AAC5C,YAAM,YAAY;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,qBAAe,WAAW,MAAM;AAChC,aAAO,OAAO,QAAQ,EAAE,qBAAqB,WAAW;AAAA,IAC1D,CAAC;AACD,OAAG,kCAAkC,MAAM;AACzC,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR;AACA,qBAAe,QAAQ,MAAM;AAC7B,aAAO,OAAO,KAAK,EAAE,qBAAqB;AAAA,QACxC,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,OAAG,2CAA2C,MAAM;AAClD,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,QAAQ;AAAA,MACV;AACA,qBAAe,aAAa,MAAM;AAClC,aAAO,OAAO,UAAU,EAAE,qBAAqB;AAAA,QAC7C,IAAI;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AACD,OAAG,iDAAiD,MAAM;AACxD,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,YAAY,CAAC;AAAA,MACf;AACA,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,YAAY;AAAA,UACV;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,IAAI;AAAA,YACJ,SAAS;AAAA,YACT,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,IAAI;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,MAAM;AAAA,UACR;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,IAAI;AAAA,YACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,YACrB,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,eAAS,aAAa,MAAM;AAC5B,aAAO,OAAO,MAAM,EAAE,qBAAqB;AAAA,QACzC,IAAI;AAAA,QACJ,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AACD,aAAO,OAAO,MAAM,EAAE,qBAAqB,EAAE,MAAM,aAAa,OAAO,EAAE,CAAC;AAC1E,aAAO,OAAO,KAAK,EAAE,qBAAqB;AAAA,QACxC,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AACD,aAAO,OAAO,QAAQ,EAAE,qBAAqB,WAAW;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC;AACH;AAIA,IAAI,iBAAiB,WAAW;AAChC,IAAI,2BAA2B,iDAAgB;AAC/C,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI,oBAAoB;AACxB,IAAI,YAA4B,oBAAI,IAAI;AACxC,IAAI,YAA4B,oBAAI,IAAI;AACxC,IAAI,aAAa;AACjB,IAAI,iBAAiC,oBAAI,IAAI;AAC7C,IAAI,QAAQ,CAAC;AACb,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI,SAAyB,OAAO,MAAM;AACxC,YAAU,MAAM;AAChB,YAAU,MAAM;AAChB,iBAAe,MAAM;AACrB,WAAS;AACT,UAAQ,CAAC;AACT,QAAM;AACR,GAAG,OAAO;AACV,IAAI,WAA2B,OAAO,CAAC,QAAQ;AAC7C,QAAM,WAAW,SAAS,gBAAgB,8BAA8B,MAAM;AAC9E,QAAM,OAAO,OAAO,QAAQ,WAAW,IAAI,MAAM,qBAAqB,IAAI;AAC1E,OAAK,QAAQ,CAAC,QAAQ;AACpB,UAAM,QAAQ,SAAS,gBAAgB,8BAA8B,OAAO;AAC5E,UAAM,eAAe,wCAAwC,aAAa,UAAU;AACpF,UAAM,aAAa,MAAM,KAAK;AAC9B,UAAM,aAAa,KAAK,GAAG;AAC3B,UAAM,aAAa,SAAS,KAAK;AACjC,UAAM,cAAc,IAAI,KAAK;AAC7B,aAAS,YAAY,KAAK;AAAA,EAC5B,CAAC;AACD,SAAO;AACT,GAAG,UAAU;AACb,IAAI,oBAAoC,OAAO,CAAC,YAAY;AAC1D,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,MAAM;AAChB,qBAAiC,OAAO,CAAC,GAAG,MAAM,KAAK,GAAG,gBAAgB;AAC1E,qBAAiB;AAAA,EACnB,OAAO;AACL,qBAAiC,OAAO,CAAC,GAAG,MAAM,KAAK,GAAG,gBAAgB;AAC1E,qBAAiB;AAAA,EACnB;AACA,UAAQ,QAAQ,CAAC,WAAW;AA3tB9B;AA4tBI,UAAM,iBAAiB,QAAQ,QAAQ,OAAO,QAAO,eAAU,IAAI,MAAM,MAApB,mBAAuB,KAAI,eAAU,IAAI,MAAM,MAApB,mBAAuB;AACvG,QAAI,mBAAmB,UAAU,eAAe,gBAAgB,cAAc,GAAG;AAC/E,sBAAgB;AAChB,uBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACD,SAAO;AACT,GAAG,mBAAmB;AACtB,IAAI,sBAAsC,OAAO,CAAC,YAAY;AAC5D,MAAI,gBAAgB;AACpB,MAAI,cAAc;AAClB,UAAQ,QAAQ,CAAC,WAAW;AAC1B,UAAM,iBAAiB,UAAU,IAAI,MAAM,EAAE;AAC7C,QAAI,kBAAkB,aAAa;AACjC,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,iBAAiB;AAC1B,GAAG,qBAAqB;AACxB,IAAI,mBAAmC,OAAO,CAAC,YAAY,SAAS,gBAAgB;AAClF,MAAI,SAAS;AACb,MAAI,cAAc;AAClB,QAAM,QAAQ,CAAC;AACf,aAAW,QAAQ,CAAC,QAAQ;AAC1B,UAAM,UAAU,QAAQ,IAAI,GAAG;AAC/B,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,4BAA4B,GAAG,EAAE;AAAA,IACnD;AACA,QAAI,QAAQ,QAAQ,QAAQ;AAC1B,eAAS,wBAAwB,OAAO;AACxC,oBAAc,KAAK,IAAI,QAAQ,WAAW;AAAA,IAC5C,OAAO;AACL,YAAM,KAAK,OAAO;AAAA,IACpB;AACA,sBAAkB,SAAS,MAAM;AAAA,EACnC,CAAC;AACD,WAAS;AACT,QAAM,QAAQ,CAAC,YAAY;AACzB,oBAAgB,SAAS,QAAQ,WAAW;AAAA,EAC9C,CAAC;AACD,aAAW,QAAQ,CAAC,QAAQ;AAC1B,UAAM,UAAU,QAAQ,IAAI,GAAG;AAC/B,QAAI,mCAAS,QAAQ,QAAQ;AAC3B,YAAM,gBAAgB,oBAAoB,QAAQ,OAAO;AACzD,eAAS,UAAU,IAAI,aAAa,EAAE,IAAI;AAC1C,UAAI,UAAU,aAAa;AACzB,sBAAc;AAAA,MAChB;AACA,YAAM,IAAI,UAAU,IAAI,QAAQ,MAAM,EAAE;AACxC,YAAM,IAAI,SAAS;AACnB,gBAAU,IAAI,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC;AAAA,IACpC;AAAA,EACF,CAAC;AACH,GAAG,kBAAkB;AACrB,IAAI,uBAAuC,OAAO,CAAC,YAAY;AAnxB/D;AAoxBE,QAAM,gBAAgB,kBAAkB,QAAQ,QAAQ,OAAO,CAAC,MAAM,MAAM,IAAI,CAAC;AACjF,MAAI,CAAC,eAAe;AAClB,UAAM,IAAI,MAAM,uCAAuC,QAAQ,EAAE,EAAE;AAAA,EACrE;AACA,QAAM,oBAAmB,eAAU,IAAI,aAAa,MAA3B,mBAA8B;AACvD,MAAI,qBAAqB,QAAQ;AAC/B,UAAM,IAAI,MAAM,gDAAgD,QAAQ,EAAE,EAAE;AAAA,EAC9E;AACA,SAAO;AACT,GAAG,sBAAsB;AACzB,IAAI,0BAA0C,OAAO,CAAC,YAAY;AAChE,QAAM,mBAAmB,qBAAqB,OAAO;AACrD,SAAO,mBAAmB;AAC5B,GAAG,yBAAyB;AAC5B,IAAI,oBAAoC,OAAO,CAAC,SAAS,WAAW;AAClE,QAAM,UAAU,UAAU,IAAI,QAAQ,MAAM;AAC5C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,+BAA+B,QAAQ,EAAE,EAAE;AAAA,EAC7D;AACA,QAAM,IAAI,QAAQ;AAClB,QAAM,IAAI,SAAS;AACnB,YAAU,IAAI,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC;AAClC,SAAO,EAAE,GAAG,EAAE;AAChB,GAAG,mBAAmB;AACtB,IAAI,kBAAkC,OAAO,CAAC,SAAS,QAAQ,gBAAgB;AAC7E,QAAM,UAAU,UAAU,IAAI,QAAQ,MAAM;AAC5C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,+BAA+B,QAAQ,EAAE,EAAE;AAAA,EAC7D;AACA,QAAM,IAAI,SAAS;AACnB,QAAM,IAAI,QAAQ;AAClB,YAAU,IAAI,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC;AACpC,GAAG,iBAAiB;AACpB,IAAI,mBAAmC,OAAO,CAAC,UAAU,SAAS,gBAAgB,WAAW,aAAa,qBAAqB;AAC7H,MAAI,qBAAqB,WAAW,WAAW;AAC7C,aAAS,OAAO,MAAM,EAAE,KAAK,KAAK,eAAe,IAAI,EAAE,EAAE,KAAK,KAAK,eAAe,IAAI,EAAE,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE,EAAE;AAAA,MAC7H;AAAA,MACA,UAAU,QAAQ,EAAE,oBAAoB,cAAc,iBAAiB,IAAI,SAAS;AAAA,IACtF;AACA,aAAS,OAAO,MAAM,EAAE,KAAK,KAAK,eAAe,IAAI,CAAC,EAAE,KAAK,KAAK,eAAe,IAAI,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE,KAAK,UAAU,EAAE,EAAE;AAAA,MAC3H;AAAA,MACA,UAAU,QAAQ,EAAE,UAAU,cAAc,iBAAiB,IAAI,SAAS;AAAA,IAC5E;AAAA,EACF,WAAW,qBAAqB,WAAW,aAAa;AACtD,aAAS,OAAO,QAAQ,EAAE,KAAK,MAAM,eAAe,CAAC,EAAE,KAAK,MAAM,eAAe,CAAC,EAAE,KAAK,KAAK,EAAE,EAAE,KAAK,SAAS,UAAU,QAAQ,EAAE,IAAI,SAAS,EAAE;AACnJ,aAAS,OAAO,QAAQ,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,SAAS,UAAU,QAAQ,EAAE,IAAI,SAAS,EAAE;AAClL,aAAS,OAAO,QAAQ,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,SAAS,UAAU,QAAQ,EAAE,IAAI,SAAS,EAAE;AAClL,aAAS,OAAO,MAAM,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,eAAe,CAAC,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU,QAAQ,EAAE,IAAI,SAAS,EAAE;AAChO,aAAS,OAAO,MAAM,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,MAAM,eAAe,CAAC,EAAE,KAAK,MAAM,eAAe,IAAI,CAAC,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,SAAS,UAAU,QAAQ,EAAE,IAAI,SAAS,EAAE;AAAA,EAClO,OAAO;AACL,UAAM,SAAS,SAAS,OAAO,QAAQ;AACvC,WAAO,KAAK,MAAM,eAAe,CAAC;AAClC,WAAO,KAAK,MAAM,eAAe,CAAC;AAClC,WAAO,KAAK,KAAK,QAAQ,SAAS,WAAW,QAAQ,IAAI,EAAE;AAC3D,WAAO,KAAK,SAAS,UAAU,QAAQ,EAAE,UAAU,cAAc,iBAAiB,EAAE;AACpF,QAAI,qBAAqB,WAAW,OAAO;AACzC,YAAM,UAAU,SAAS,OAAO,QAAQ;AACxC,cAAQ,KAAK,MAAM,eAAe,CAAC;AACnC,cAAQ,KAAK,MAAM,eAAe,CAAC;AACnC,cAAQ,KAAK,KAAK,CAAC;AACnB,cAAQ;AAAA,QACN;AAAA,QACA,UAAU,SAAS,IAAI,QAAQ,EAAE,UAAU,cAAc,iBAAiB;AAAA,MAC5E;AAAA,IACF;AACA,QAAI,qBAAqB,WAAW,SAAS;AAC3C,YAAM,QAAQ,SAAS,OAAO,MAAM;AACpC,YAAM;AAAA,QACJ;AAAA,QACA,KAAK,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC;AAAA,MACnM,EAAE,KAAK,SAAS,UAAU,SAAS,IAAI,QAAQ,EAAE,UAAU,cAAc,iBAAiB,EAAE;AAAA,IAC9F;AAAA,EACF;AACF,GAAG,kBAAkB;AACrB,IAAI,kBAAkC,OAAO,CAAC,SAAS,SAAS,gBAAgB,QAAQ;AA91BxF;AA+1BE,MAAI,QAAQ,SAAS,WAAW,gBAAgB,QAAQ,YAAY,QAAQ,SAAS,WAAW,SAAS,QAAQ,SAAS,WAAW,WAAU,qEAA0B,kBAAiB;AACxL,UAAM,UAAU,QAAQ,OAAO,GAAG;AAClC,UAAM,WAAW,QAAQ,OAAO,MAAM,EAAE,KAAK,SAAS,kBAAkB;AACxE,UAAM,OAAO,QAAQ,OAAO,MAAM,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,KAAK,eAAe,IAAI,EAAE,EAAE,KAAK,SAAS,cAAc,EAAE,KAAK,QAAQ,EAAE;AACjI,UAAM,QAAO,UAAK,KAAK,MAAV,mBAAa;AAC1B,QAAI,MAAM;AACR,eAAS,KAAK,KAAK,eAAe,gBAAgB,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,eAAe,IAAI,IAAI,EAAE,KAAK,SAAS,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,UAAU,KAAK,SAAS,IAAI,EAAE;AAChL,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,iBAAS,KAAK,KAAK,eAAe,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,EAAE,KAAK,KAAK,eAAe,IAAI,EAAE;AAChG,aAAK,KAAK,KAAK,eAAe,KAAK,KAAK,QAAQ,IAAI,GAAG,EAAE,KAAK,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE;AAAA,MACxG,OAAO;AACL,aAAK,KAAK,KAAK,eAAe,gBAAgB,KAAK,QAAQ,CAAC;AAAA,MAC9D;AACA,UAAI,yBAAyB,mBAAmB;AAC9C,YAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,eAAK;AAAA,YACH;AAAA,YACA,iBAAiB,eAAe,IAAI,OAAO,eAAe,IAAI;AAAA,UAChE;AACA,mBAAS;AAAA,YACP;AAAA,YACA,iBAAiB,eAAe,IAAI,OAAO,eAAe,IAAI;AAAA,UAChE;AAAA,QACF,OAAO;AACL,gBAAM,MAAM,QAAQ,KAAK,QAAQ,MAAM,KAAK;AAC5C,gBAAM,MAAM,KAAK,KAAK,QAAQ,KAAK;AACnC,kBAAQ;AAAA,YACN;AAAA,YACA,eAAe,MAAM,OAAO,MAAM,mBAAmB,MAAM,OAAO,eAAe,IAAI;AAAA,UACvF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,GAAG,iBAAiB;AACpB,IAAI,iBAAiC,OAAO,CAAC,SAAS,SAAS,gBAAgB,QAAQ;AAl4BvF;AAm4BE,MAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,QAAI,UAAU;AACd,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AACvB,UAAM,cAAc,CAAC;AACrB,eAAW,YAAY,QAAQ,KAAK,QAAQ,GAAG;AAC7C,YAAM,OAAO,QAAQ,OAAO,SAAS;AACrC,YAAM,OAAO,QAAQ,OAAO,QAAQ;AACpC,YAAM,MAAM,QAAQ,OAAO,MAAM,EAAE,KAAK,KAAK,eAAe,IAAI,KAAK,OAAO,EAAE,KAAK,SAAS,WAAW,EAAE,KAAK,QAAQ;AACtH,YAAM,WAAU,SAAI,KAAK,MAAT,mBAAY;AAC5B,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AACA,wBAAkB,KAAK,IAAI,iBAAiB,QAAQ,KAAK;AACzD,yBAAmB,KAAK,IAAI,kBAAkB,QAAQ,MAAM;AAC5D,UAAI,KAAK,KAAK,eAAe,gBAAgB,QAAQ,QAAQ,CAAC;AAC9D,kBAAY,KAAK;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,iBAAW;AAAA,IACb;AACA,eAAW,EAAE,KAAK,MAAM,MAAM,SAAS,SAAS,KAAK,aAAa;AAChE,YAAM,KAAK,mBAAmB;AAC9B,YAAM,KAAK,eAAe,IAAI,OAAO;AACrC,WAAK,KAAK,SAAS,eAAe,EAAE;AAAA,QAClC;AAAA,QACA;AAAA,QACA,MAAM,kBAAkB,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE;AAAA,QAC7C,MAAM,kBAAkB,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE;AAAA,QAC7C,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,QACvE,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,QACvE,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,QACvE,eAAe,gBAAgB,kBAAkB,IAAI,EAAE,IAAI,KAAK,KAAK,EAAE;AAAA,MACzE;AACA,WAAK,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,MAAM,kBAAkB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,SAAS,UAAU;AAC1G,UAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,cAAM,UAAU,MAAM;AACtB,aAAK,KAAK,SAAS,eAAe,EAAE;AAAA,UAClC;AAAA,UACA;AAAA,UACA,eAAe,CAAC,IAAI,UAAU,CAAC;AAAA,UAC/B,eAAe,CAAC,IAAI,UAAU,CAAC;AAAA,UAC/B,eAAe,IAAI,aAAa,IAAI,UAAU,KAAK,CAAC;AAAA,UACpD,eAAe,IAAI,gBAAgB,kBAAkB,CAAC,IAAI,UAAU,KAAK,CAAC;AAAA,UAC1E,eAAe,IAAI,gBAAgB,kBAAkB,CAAC,IAAI,UAAU,KAAK,CAAC;AAAA,UAC1E,eAAe,IAAI,aAAa,IAAI,UAAU,KAAK,CAAC;AAAA,QACtD,EAAE,KAAK,aAAa,iCAAiC,eAAe,IAAI,MAAM,MAAM,GAAG;AACvF,aAAK,KAAK,MAAM,eAAe,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK,aAAa,iCAAiC,eAAe,IAAI,MAAM,MAAM,GAAG;AACpJ,YAAI,KAAK,KAAK,eAAe,IAAI,CAAC,EAAE,KAAK,KAAK,UAAU,CAAC,EAAE,KAAK,aAAa,iCAAiC,eAAe,IAAI,MAAM,MAAM,GAAG;AAAA,MAClJ;AAAA,IACF;AAAA,EACF;AACF,GAAG,gBAAgB;AACnB,IAAI,qBAAqC,OAAO,CAAC,YAAY;AAC3D,QAAM,mBAAmB,QAAQ,cAAc,QAAQ;AACvD,UAAQ,kBAAkB;AAAA,IACxB,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT,KAAK,WAAW;AACd,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF,GAAG,oBAAoB;AACvB,IAAI,oBAAoC,OAAO,CAAC,SAAS,MAAM,KAAK,eAAe;AACjF,QAAM,wBAAwB,EAAE,GAAG,GAAG,GAAG,EAAE;AAC3C,MAAI,QAAQ,QAAQ,SAAS,GAAG;AAC9B,UAAM,gBAAgB,kBAAkB,QAAQ,OAAO;AACvD,QAAI,eAAe;AACjB,YAAM,iBAAiB,WAAW,IAAI,aAAa,KAAK;AACxD,UAAI,SAAS,MAAM;AACjB,eAAO,eAAe,IAAI;AAAA,MAC5B,WAAW,SAAS,MAAM;AACxB,cAAM,kBAAkB,WAAW,IAAI,QAAQ,EAAE,KAAK;AACtD,eAAO,gBAAgB,IAAI;AAAA,MAC7B,OAAO;AACL,eAAO,eAAe,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT,WAAW,SAAS,MAAM;AACxB,YAAM,kBAAkB,WAAW,IAAI,QAAQ,EAAE,KAAK;AACtD,aAAO,gBAAgB,IAAI;AAAA,IAC7B,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT,GAAG,mBAAmB;AACtB,IAAI,oBAAoC,OAAO,CAAC,SAAS,KAAK,sBAAsB;AAv+BpF;AAw+BE,QAAM,gBAAgB,QAAQ,QAAQ,oBAAoB,MAAM,MAAM;AACtE,QAAM,IAAI,QAAQ,QAAQ,QAAQ,OAAO,iBAAgB,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B;AACxF,QAAM,IAAI,QAAQ,QAAQ,QAAQ,QAAO,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B,MAAM;AAC9E,MAAI,MAAM,UAAU,MAAM,QAAQ;AAChC,UAAM,IAAI,MAAM,sCAAsC,QAAQ,EAAE,EAAE;AAAA,EACpE;AACA,SAAO,EAAE,GAAG,GAAG,cAAc;AAC/B,GAAG,mBAAmB;AACtB,IAAI,cAA8B,OAAO,CAAC,KAAK,SAAS,gBAAgB;AACtE,MAAI,CAAC,0BAA0B;AAC7B,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,QAAM,WAAW,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,gBAAgB;AAC/D,QAAM,UAAU,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC7D,MAAI,MAAM,QAAQ,QAAQ,QAAQ,OAAO,aAAa;AACtD,QAAM,OAAO,CAAC,GAAG,QAAQ,KAAK,CAAC;AAC/B,QAAM,qBAAoB,qEAA0B,oBAAmB;AACvE,QAAM,WAA2B,OAAO,CAAC,GAAG,MAAM;AAz/BpD;AA0/BI,UAAM,QAAO,aAAQ,IAAI,CAAC,MAAb,mBAAgB;AAC7B,UAAM,QAAO,aAAQ,IAAI,CAAC,MAAb,mBAAgB;AAC7B,WAAO,SAAS,UAAU,SAAS,SAAS,OAAO,OAAO;AAAA,EAC5D,GAAG,UAAU;AACb,MAAI,aAAa,KAAK,KAAK,QAAQ;AACnC,MAAI,QAAQ,MAAM;AAChB,QAAI,mBAAmB;AACrB,uBAAiB,YAAY,SAAS,GAAG;AAAA,IAC3C;AACA,iBAAa,WAAW,QAAQ;AAAA,EAClC;AACA,aAAW,QAAQ,CAAC,QAAQ;AArgC9B;AAsgCI,UAAM,UAAU,QAAQ,IAAI,GAAG;AAC/B,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,4BAA4B,GAAG,EAAE;AAAA,IACnD;AACA,QAAI,mBAAmB;AACrB,YAAM,kBAAkB,SAAS,KAAK,KAAK,SAAS;AAAA,IACtD;AACA,UAAM,iBAAiB,kBAAkB,SAAS,KAAK,iBAAiB;AACxE,QAAI,aAAa;AACf,YAAM,YAAY,mBAAmB,OAAO;AAC5C,YAAM,mBAAmB,QAAQ,cAAc,QAAQ;AACvD,YAAM,gBAAc,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B,UAAS;AAC5D,uBAAiB,UAAU,SAAS,gBAAgB,WAAW,aAAa,gBAAgB;AAC5F,sBAAgB,SAAS,SAAS,gBAAgB,GAAG;AACrD,qBAAe,SAAS,SAAS,gBAAgB,GAAG;AAAA,IACtD;AACA,QAAI,QAAQ,QAAQ,QAAQ,MAAM;AAChC,gBAAU,IAAI,QAAQ,IAAI,EAAE,GAAG,eAAe,GAAG,GAAG,eAAe,cAAc,CAAC;AAAA,IACpF,OAAO;AACL,gBAAU,IAAI,QAAQ,IAAI,EAAE,GAAG,eAAe,eAAe,GAAG,eAAe,EAAE,CAAC;AAAA,IACpF;AACA,UAAM,QAAQ,QAAQ,oBAAoB,MAAM,cAAc,MAAM,cAAc;AAClF,QAAI,MAAM,QAAQ;AAChB,eAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH,GAAG,aAAa;AAChB,IAAI,qBAAqC,OAAO,CAAC,SAAS,SAAS,IAAI,IAAI,eAAe;AACxF,QAAM,oBAAoB,QAAQ,QAAQ,QAAQ,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AACjF,QAAM,mBAAmB,oBAAoB,QAAQ,SAAS,QAAQ;AACtE,QAAM,uBAAuC,OAAO,CAAC,MAAM,EAAE,WAAW,kBAAkB,sBAAsB;AAChH,QAAM,mBAAmC,OAAO,CAAC,MAAM,EAAE,MAAM,QAAQ,OAAO,EAAE,MAAM,QAAQ,KAAK,kBAAkB;AACrH,SAAO,CAAC,GAAG,WAAW,OAAO,CAAC,EAAE,KAAK,CAAC,YAAY;AAChD,WAAO,iBAAiB,OAAO,KAAK,qBAAqB,OAAO;AAAA,EAClE,CAAC;AACH,GAAG,oBAAoB;AACvB,IAAI,WAA2B,OAAO,CAAC,IAAI,IAAI,QAAQ,MAAM;AAC3D,QAAM,YAAY,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI;AAC3C,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,QAAM,KAAK,MAAM,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,SAAS,KAAK,EAAE;AACjE,MAAI,IAAI;AACN,UAAM,KAAK,SAAS;AACpB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,IAAI,KAAK,EAAE;AAC7B,SAAO,SAAS,IAAI,KAAK,OAAO,GAAG,QAAQ,CAAC;AAC9C,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,CAAC,KAAK,SAAS,SAAS,eAAe;AAvjC9E;AAwjCE,QAAM,KAAK,UAAU,IAAI,QAAQ,EAAE;AACnC,QAAM,KAAK,UAAU,IAAI,QAAQ,EAAE;AACnC,MAAI,OAAO,UAAU,OAAO,QAAQ;AAClC,UAAM,IAAI,MAAM,0CAA0C,QAAQ,EAAE,QAAQ,QAAQ,EAAE,EAAE;AAAA,EAC1F;AACA,QAAM,sBAAsB,mBAAmB,SAAS,SAAS,IAAI,IAAI,UAAU;AACnF,MAAI,MAAM;AACV,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,iBAAgB,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B;AACnD,MAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,qBAAgB,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B;AAAA,EACjD;AACA,MAAI;AACJ,MAAI,qBAAqB;AACvB,UAAM;AACN,WAAO;AACP,aAAS;AACT,aAAS;AACT,UAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AACtE,UAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AACtE,QAAI,QAAQ,MAAM;AAChB,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K,OAAO;AACL,yBAAgB,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B;AAC/C,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K;AAAA,IACF,WAAW,QAAQ,MAAM;AACvB,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K,OAAO;AACL,yBAAgB,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B;AAC/C,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,QAAQ,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K;AAAA,IACF,OAAO;AACL,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K,OAAO;AACL,yBAAgB,eAAU,IAAI,QAAQ,MAAM,MAA5B,mBAA+B;AAC/C,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1K;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM;AACN,WAAO;AACP,aAAS;AACT,aAAS;AACT,QAAI,QAAQ,MAAM;AAChB,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC3G;AAAA,MACF;AACA,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,cAAM;AACN,eAAO;AACP,iBAAS;AACT,iBAAS;AACT,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC3G,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,GAAG,MAAM,GAAG,GAAG;AACjB,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF,WAAW,QAAQ,MAAM;AACvB,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC3G,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,cAAM;AACN,eAAO;AACP,iBAAS;AACT,iBAAS;AACT,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,GAAG,MAAM,GAAG,GAAG;AACjB,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC3G,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G;AAAA,MACF;AACA,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,YAAI,QAAQ,SAAS,WAAW,SAAS,QAAQ,OAAO,QAAQ,QAAQ,CAAC,GAAG;AAC1E,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC1G,OAAO;AACL,oBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,QAC3G;AAAA,MACF;AACA,UAAI,GAAG,MAAM,GAAG,GAAG;AACjB,kBAAU,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,QAAQ;AACtB,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,MAAI,OAAO,MAAM,EAAE,KAAK,KAAK,OAAO,EAAE,KAAK,SAAS,gBAAgB,gBAAgB,iBAAiB;AACvG,GAAG,WAAW;AACd,IAAI,aAA6B,OAAO,CAAC,KAAK,YAAY;AACxD,QAAM,UAAU,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,eAAe;AAC7D,GAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,QAAQ,CAAC,QAAQ;AACnC,UAAM,UAAU,QAAQ,IAAI,GAAG;AAC/B,QAAI,QAAQ,WAAW,QAAQ,QAAQ,SAAS,GAAG;AACjD,cAAQ,QAAQ,QAAQ,CAAC,WAAW;AAClC,kBAAU,SAAS,QAAQ,IAAI,MAAM,GAAG,SAAS,OAAO;AAAA,MAC1D,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH,GAAG,YAAY;AACf,IAAI,eAA+B,OAAO,CAAC,KAAK,aAAa;AAC3D,QAAM,IAAI,IAAI,OAAO,GAAG;AACxB,WAAS,QAAQ,CAAC,SAAS,UAAU;AA3rCvC;AA4rCI,UAAM,sBAAsB,QAAQ;AACpC,UAAM,OAAM,eAAU,IAAI,QAAQ,IAAI,MAA1B,mBAA6B;AACzC,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI,MAAM,iCAAiC,QAAQ,IAAI,EAAE;AAAA,IACjE;AACA,UAAM,OAAO,EAAE,OAAO,MAAM;AAC5B,SAAK,KAAK,MAAM,CAAC;AACjB,SAAK,KAAK,MAAM,GAAG;AACnB,SAAK,KAAK,MAAM,MAAM;AACtB,SAAK,KAAK,MAAM,GAAG;AACnB,SAAK,KAAK,SAAS,kBAAkB,mBAAmB;AACxD,QAAI,QAAQ,MAAM;AAChB,WAAK,KAAK,MAAM,UAAU;AAC1B,WAAK,KAAK,MAAM,GAAG;AACnB,WAAK,KAAK,MAAM,MAAM;AACtB,WAAK,KAAK,MAAM,GAAG;AAAA,IACrB,WAAW,QAAQ,MAAM;AACvB,WAAK,KAAK,MAAM,MAAM;AACtB,WAAK,KAAK,MAAM,GAAG;AACnB,WAAK,KAAK,MAAM,UAAU;AAC1B,WAAK,KAAK,MAAM,GAAG;AAAA,IACrB;AACA,UAAM,KAAK,GAAG;AACd,UAAM,OAAO,QAAQ;AACrB,UAAM,eAAe,SAAS,IAAI;AAClC,UAAM,MAAM,EAAE,OAAO,MAAM;AAC3B,UAAM,cAAc,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AAC7D,UAAM,QAAQ,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,uBAAuB,mBAAmB;AAC9F,UAAM,KAAK,EAAE,YAAY,YAAY;AACrC,UAAM,OAAO,aAAa,QAAQ;AAClC,QAAI,KAAK,SAAS,yBAAyB,mBAAmB,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,MAAK,qEAA0B,uBAAsB,OAAO,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI,CAAC,EAAE,KAAK,SAAS,KAAK,QAAQ,EAAE,EAAE,KAAK,UAAU,KAAK,SAAS,CAAC;AACtR,UAAM;AAAA,MACJ;AAAA,MACA,gBAAgB,CAAC,KAAK,QAAQ,OAAM,qEAA0B,uBAAsB,OAAO,KAAK,MAAM,QAAQ,MAAM,KAAK,SAAS,IAAI,KAAK;AAAA,IAC7I;AACA,QAAI,QAAQ,MAAM;AAChB,UAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC;AACpD,YAAM,KAAK,aAAa,gBAAgB,MAAM,KAAK,QAAQ,IAAI,KAAK,MAAM;AAAA,IAC5E,WAAW,QAAQ,MAAM;AACvB,UAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,MAAM;AACzD,YAAM,KAAK,aAAa,gBAAgB,MAAM,KAAK,QAAQ,IAAI,KAAK,OAAO,SAAS,GAAG;AAAA,IACzF,OAAO;AACL,UAAI,KAAK,aAAa,qBAAqB,MAAM,KAAK,SAAS,KAAK,GAAG;AAAA,IACzE;AAAA,EACF,CAAC;AACH,GAAG,cAAc;AACjB,IAAI,oBAAoC,OAAO,SAAS,MAAM,KAAK,OAAO,MAAM,mBAAmB;AACjG,YAAU,IAAI,MAAM,EAAE,KAAK,MAAM,CAAC;AAClC,SAAO,MAAM,oBAAoB,KAAK,MAAM,QAAQ,QAAQ,QAAQ,OAAO,KAAK,QAAQ,IAAI;AAC5F,SAAO;AACT,GAAG,mBAAmB;AACtB,IAAI,OAAuB,OAAO,SAAS,KAAK,IAAI,KAAK,SAAS;AAChE,SAAO;AACP,MAAI,MAAM,wBAAwB,MAAM,MAAM,OAAO,IAAI,GAAG;AAC5D,MAAI,CAAC,0BAA0B;AAC7B,UAAM,IAAI,MAAM,2BAA2B;AAAA,EAC7C;AACA,QAAM,oBAAoB,yBAAyB,qBAAqB;AACxE,QAAM,MAAM,QAAQ;AACpB,mBAAiB,IAAI,WAAW;AAChC,QAAM,WAAW,IAAI,sBAAsB;AAC3C,QAAM,IAAI,aAAa;AACvB,QAAM,WAAW,eAAO,QAAQ,EAAE,IAAI;AACtC,MAAI,MAAM;AACV,WAAS,QAAQ,CAAC,SAAS,UAAU;AA5vCvC;AA6vCI,UAAM,eAAe,SAAS,QAAQ,IAAI;AAC1C,UAAM,IAAI,SAAS,OAAO,GAAG;AAC7B,UAAM,cAAc,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa;AAC7D,UAAM,QAAQ,YAAY,OAAO,GAAG,EAAE,KAAK,SAAS,oBAAoB;AACxE,gBAAM,KAAK,MAAX,mBAAc,YAAY;AAC1B,UAAM,OAAO,aAAa,QAAQ;AAClC,UAAM,kBAAkB,QAAQ,MAAM,KAAK,OAAO,MAAM,iBAAiB;AACzE,UAAM,OAAO;AACb,gBAAY,OAAO;AACnB,MAAE,OAAO;AAAA,EACX,CAAC;AACD,cAAY,UAAU,gBAAgB,KAAK;AAC3C,MAAI,yBAAyB,cAAc;AACzC,iBAAa,UAAU,QAAQ;AAAA,EACjC;AACA,aAAW,UAAU,cAAc;AACnC,cAAY,UAAU,gBAAgB,IAAI;AAC1C,gBAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA,yBAAyB,kBAAkB;AAAA,IAC3C,IAAI,gBAAgB;AAAA,EACtB;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC3B;AACF,GAAG,MAAM;AACT,IAAI,2BAA2B;AAAA,EAC7B;AACF;AACA,IAAI,QAAQ;AACV,QAAM,EAAE,IAAI,QAAQ,SAAS,IAAI;AACjC,WAAS,YAAY,MAAM;AACzB,OAAG,mBAAmB,MAAM;AAC1B,YAAM,WAAW,SAAS,MAAM;AAChC,aAAO,QAAQ,EAAE,YAAY;AAC7B,aAAO,SAAS,SAAS,CAAC,EAAE,SAAS,EAAE,KAAK,MAAM;AAAA,IACpD,CAAC;AAAA,EACH,CAAC;AACD,WAAS,kBAAkB,MAAM;AAC/B,UAAM,OAAO;AAAA,MACX,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAwB,OAAO,MAAM,IAAI,QAAQ;AAAA,IACnD;AACA,OAAG,kDAAkD,MAAM;AACzD,YAAM;AACN,YAAM,MAAM,kBAAkB,QAAQ,GAAG,GAAG,MAAM,IAAI;AACtD,aAAO,GAAG,EAAE,KAAK,EAAE;AACnB,aAAO,UAAU,IAAI,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1D,YAAM,UAAU,kBAAkB,WAAW,KAAK,GAAG,MAAM,IAAI;AAC/D,aAAO,OAAO,EAAE,KAAK,GAAG;AACxB,aAAO,UAAU,IAAI,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;AAAA,IAC5D,CAAC;AACD,OAAG,kDAAkD,MAAM;AACzD,YAAM;AACN,WAAK,QAAQ;AACb,YAAM,MAAM,kBAAkB,QAAQ,GAAG,GAAG,MAAM,IAAI;AACtD,aAAO,GAAG,EAAE,KAAK,YAAY;AAC7B,aAAO,UAAU,IAAI,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1D,WAAK,QAAQ;AACb,YAAM,UAAU,kBAAkB,WAAW,KAAK,GAAG,MAAM,IAAI;AAC/D,aAAO,OAAO,EAAE,KAAK,YAAY;AACjC,aAAO,UAAU,IAAI,SAAS,CAAC,EAAE,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH,CAAC;AACD,WAAS,kBAAkB,MAAM;AAC/B,UAAM,UAA0B,oBAAI,IAAI;AAAA,MACtC;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC;AAAA,UACV,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,MAAM;AAAA,UAChB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,GAAG;AAAA,UACb,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,QAAQ,GAAG;AAAA,UACrB,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,MAAM;AAAA,UAChB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,KAAK,GAAG;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM,WAAW;AAAA,UACjB,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,KAAK,GAAG;AAAA,UAClB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,MAAM;AACV,cAAU,IAAI,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1C,cAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,cAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,aAAS,MAAM,MAAM;AACnB,YAAM;AACN,YAAM;AACN,YAAM,2BAA2C,oBAAI,IAAI;AAAA,QACvD,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACjD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACzD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAChD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QACnE,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,MACrE,CAAC;AACD,cAAQ,QAAQ,CAAC,SAAS,QAAQ;AAChC,WAAG,+CAA+C,GAAG,IAAI,MAAM;AAC7D,gBAAM,WAAW,kBAAkB,SAAS,KAAK,KAAK;AACtD,iBAAO,QAAQ,EAAE,QAAQ,yBAAyB,IAAI,GAAG,CAAC;AAC1D,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,aAAS,MAAM,MAAM;AACnB,UAAI,OAAO;AACX,YAAM;AACN,YAAM,2BAA2C,oBAAI,IAAI;AAAA,QACvD,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACjD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,IAAI,eAAe,GAAG,CAAC;AAAA,QACzD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAChD,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QACnE,CAAC,WAAW,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,QAC3D,CAAC,mBAAmB,EAAE,GAAG,cAAc,GAAG,KAAK,eAAe,IAAI,CAAC;AAAA,MACrE,CAAC;AACD,cAAQ,QAAQ,CAAC,SAAS,QAAQ;AAChC,WAAG,+CAA+C,GAAG,IAAI,MAAM;AAC7D,gBAAM,WAAW,kBAAkB,SAAS,MAAM,KAAK;AACvD,iBAAO,QAAQ,EAAE,QAAQ,yBAAyB,IAAI,GAAG,CAAC;AAC1D,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,aAAS,sBAAsB,MAAM;AACnC,YAAM,0BAA0C,oBAAI,IAAI;AAAA,QACtD,CAAC,cAAc,eAAe;AAAA,QAC9B,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,WAAW,cAAc;AAAA,QAC1B,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,mBAAmB,oBAAoB;AAAA,QACxC,CAAC,WAAW,eAAe;AAAA,QAC3B,CAAC,mBAAmB,oBAAoB;AAAA,MAC1C,CAAC;AACD,cAAQ,QAAQ,CAAC,SAAS,QAAQ;AAChC,WAAG,iDAAiD,GAAG,IAAI,MAAM;AAC/D,gBAAM,YAAY,mBAAmB,OAAO;AAC5C,iBAAO,SAAS,EAAE,KAAK,wBAAwB,IAAI,GAAG,CAAC;AAAA,QACzD,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,WAAS,uCAAuC,MAAM;AACpD,UAAM,UAA0B,oBAAI,IAAI;AAAA,MACtC;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC;AAAA,UACV,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,IAAI;AAAA,UACJ,SAAS;AAAA,UACT,KAAK;AAAA,UACL,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,UACP,SAAS,CAAC,WAAW;AAAA,UACrB,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,yBAAyC,oBAAI,IAAI;AAAA,MACrD,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC7B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC7B,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,IAChC,CAAC;AACD,UAAM,sCAAsD,oBAAI,IAAI;AAAA,MAClE,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,GAAG,CAAC;AAAA,MACxC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,IAAI,CAAC;AAAA,MACzC,CAAC,aAAa,EAAE,GAAG,cAAc,GAAG,GAAG,CAAC;AAAA,MACxC,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC;AAAA,MAC9B,CAAC,aAAa,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAC/B,CAAC;AACD,UAAM,gCAAgD,oBAAI,IAAI;AAAA,MAC5D,CAAC,aAAa,EAAE;AAAA,MAChB,CAAC,aAAa,EAAE;AAAA,MAChB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,aAAa,GAAG;AAAA,IACnB,CAAC;AACD,UAAM,aAAa,CAAC,GAAG,uBAAuB,KAAK,CAAC;AACpD,OAAG,+DAA+D,MAAM;AACtE,YAAM;AACN,UAAI,SAAS;AACb,gBAAU,MAAM;AAChB,gBAAU,MAAM;AAChB,gBAAU,IAAI,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1C,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,+BAAyB,kBAAkB;AAC3C,cAAQ,QAAQ,CAAC,SAAS,QAAQ;AAChC,YAAI,QAAQ,QAAQ,SAAS,GAAG;AAC9B,mBAAS,wBAAwB,OAAO;AAAA,QAC1C;AACA,cAAM,WAAW,kBAAkB,SAAS,MAAM;AAClD,eAAO,QAAQ,EAAE,QAAQ,uBAAuB,IAAI,GAAG,CAAC;AACxD,eAAO,MAAM,EAAE,QAAQ,8BAA8B,IAAI,GAAG,CAAC;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AACD,OAAG,iEAAiE,MAAM;AACxE,gBAAU,MAAM;AAChB,gBAAU,MAAM;AAChB,YAAM;AACN,YAAM,SAAS;AACf,gBAAU,MAAM;AAChB,gBAAU,MAAM;AAChB,gBAAU,IAAI,QAAQ,EAAE,KAAK,GAAG,OAAO,EAAE,CAAC;AAC1C,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,gBAAU,IAAI,WAAW,EAAE,KAAK,cAAc,OAAO,EAAE,CAAC;AACxD,uBAAiB,YAAY,SAAS,MAAM;AAC5C,iBAAW,QAAQ,CAAC,YAAY;AAC9B,cAAM,WAAW,UAAU,IAAI,OAAO;AACtC,eAAO,QAAQ,EAAE,QAAQ,oCAAoC,IAAI,OAAO,CAAC;AAAA,MAC3E,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACD,2BAAyB,kBAAkB;AAC3C,KAAG,OAAO,MAAM;AACd,cAAU,IAAI,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC,cAAU,IAAI,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC,cAAU,IAAI,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACvC,UAAM;AACN,UAAM,UAAU,CAAC,WAAW,WAAW,SAAS;AAChD,UAAM,gBAAgB,kBAAkB,OAAO;AAC/C,WAAO,aAAa,EAAE,KAAK,SAAS;AACpC,cAAU,MAAM;AAAA,EAClB,CAAC;AACH;AAGA,IAAI,YAA4B,OAAO,CAAC,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAShD,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,EAC3B,CAAC,MAAM;AAAA,uBACc,CAAC,YAAY,QAAQ,mBAAmB,CAAC,CAAC;AAAA,iBAChD,CAAC,cAAc,QAAQ,QAAQ,CAAC,CAAC,WAAW,QAAQ,QAAQ,CAAC,CAAC;AAAA,2BACpD,CAAC,cAAc,QAAQ,WAAW,CAAC,CAAC,WAAW,QAAQ,WAAW,CAAC,CAAC;AAAA,gBAC/E,CAAC,aAAa,QAAQ,QAAQ,CAAC,CAAC;AAAA,gBAChC,CAAC,cAAc,QAAQ,QAAQ,CAAC,CAAC;AAAA;AAEjD,EAAE,KAAK,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,cAIE,QAAQ,SAAS;AAAA;AAAA;AAAA,+BAGA,QAAQ,mBAAmB,WAAW,QAAQ,gBAAgB;AAAA,mCAC1D,QAAQ,mBAAmB,WAAW,QAAQ,qBAAqB;AAAA,4BAC1E,QAAQ,gBAAgB,WAAW,QAAQ,aAAa;AAAA,2BACzD,QAAQ,kBAAkB,aAAa,QAAQ,cAAc;AAAA,sBAClE,QAAQ,SAAS;AAAA;AAAA;AAAA,cAGzB,QAAQ,YAAY;AAAA,YACtB,QAAQ,YAAY;AAAA;AAAA;AAAA,cAGlB,QAAQ,YAAY;AAAA,YACtB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMlB,QAAQ,YAAY;AAAA,YACtB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOpB,QAAQ,SAAS;AAAA;AAAA,GAE1B,WAAW;AACd,IAAI,iBAAiB;AAGrB,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,QAAQ;AACV;", "names": ["import_dist"]}