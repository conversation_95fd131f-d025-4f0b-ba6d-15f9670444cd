{"version": 3, "sources": ["../../wxt/dist/browser.mjs", "../../@wxt-dev/browser/src/index.mjs"], "sourcesContent": ["import { browser as _browser } from \"@wxt-dev/browser\";\nexport const browser = _browser;\nexport {};\n", "// #region snippet\nexport const browser = globalThis.browser?.runtime?.id\n  ? globalThis.browser\n  : globalThis.chrome;\n// #endregion snippet\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA;AACO,IAAM,YAAU,sBAAW,YAAX,mBAAoB,YAApB,mBAA6B,MAChD,WAAW,UACX,WAAW;;;ADFR,IAAMC,WAAU;", "names": ["import_dist", "import_dist", "browser"]}