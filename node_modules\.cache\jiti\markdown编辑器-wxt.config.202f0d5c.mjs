"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;
var _wxt = await jitiImport("wxt");
var _vite = _interopRequireDefault(await jitiImport("./vite.config"));function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };}

function getBuildOptions() {
  delete _vite.default.build.rollupOptions.output.manualChunks;
  return _vite.default.build;
}var _default = exports.default =
(0, _wxt.defineConfig)({
  srcDir: `src`,
  modulesDir: `src/modules`,
  manifest: ({ mode, browser }) => ({
    name: `公众号内容编辑器`,
    icons: {
      256: mode === `development` ? `/mpmd/icon-256-gray.png` : `/mpmd/icon-256.png`
    },
    permissions: [`storage`, `tabs`, `activeTab`, `sidePanel`, `contextMenus`],
    host_permissions: [
    `https://*.github.com/*`,
    `https://*.githubusercontent.com/*`,
    `https://*.gitee.com/*`,
    `https://*.weixin.qq.com/*`,
    // 微信公众号图片
    `https://*.qpic.cn/*`],

    web_accessible_resources: [
    {
      resources: [`*.png`, `*.svg`, `injected.js`],
      matches: [`<all_urls>`]
    }],

    side_panel: browser === `chrome` ?
    {
      default_path: `sidepanel.html`
    } :
    undefined,
    sidebar_action: browser === `firefox` ?
    {
      default_panel: `sidepanel.html`,
      default_icon: {
        256: `mpmd/icon-256.png`
      },
      default_title: `MD 公众号编辑器`
    } :
    undefined,
    commands: {
      _execute_sidebar_action: {
        description: `Open MD Editor Side Panel`,
        suggested_key: {
          default: `Ctrl+Shift+Y`
        }
      }
    }
  }),
  analysis: {
    open: true
  },
  vite: () => ({
    ..._vite.default,
    plugins: _vite.default.plugins.filter((plugin) =>
    typeof plugin === `object` &&
    plugin !== null &&
    !(`name` in plugin && plugin.name === `vite-plugin-Radar`)
    ),
    build: getBuildOptions(),
    base: `/`
  })
}); /* v9-f834b5e984b0379d */
