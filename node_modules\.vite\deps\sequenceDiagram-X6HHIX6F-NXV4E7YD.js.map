{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/sequenceDiagram-X6HHIX6F.mjs"], "sourcesContent": ["import {\n  drawBackgroundRect,\n  drawEmbeddedImage,\n  drawImage,\n  drawRect,\n  getNoteRect,\n  getTextObj\n} from \"./chunk-D6G4REZN.mjs\";\nimport {\n  ImperativeState\n} from \"./chunk-XZIHB7SX.mjs\";\nimport {\n  ZERO_WIDTH_SPACE,\n  parseFontSize,\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  assignWithDepth_default,\n  calculateMathMLDimensions,\n  clear,\n  common_default,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getConfig2,\n  getDiagramTitle,\n  hasKatex,\n  log,\n  renderKatex,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setConfig2 as setConfig,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/sequence/parser/sequenceDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 13], $V7 = [1, 14], $V8 = [1, 16], $V9 = [1, 17], $Va = [1, 18], $Vb = [1, 24], $Vc = [1, 25], $Vd = [1, 26], $Ve = [1, 27], $Vf = [1, 28], $Vg = [1, 29], $Vh = [1, 30], $Vi = [1, 31], $Vj = [1, 32], $Vk = [1, 33], $Vl = [1, 34], $Vm = [1, 35], $Vn = [1, 36], $Vo = [1, 37], $Vp = [1, 38], $Vq = [1, 39], $Vr = [1, 41], $Vs = [1, 42], $Vt = [1, 43], $Vu = [1, 44], $Vv = [1, 45], $Vw = [1, 46], $Vx = [1, 4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], $Vy = [4, 5, 16, 50, 52, 53], $Vz = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VA = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VB = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 48, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VC = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VD = [68, 69, 70], $VE = [1, 122];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NEWLINE\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"box_section\": 10, \"box_line\": 11, \"participant_statement\": 12, \"create\": 13, \"box\": 14, \"restOfLine\": 15, \"end\": 16, \"signal\": 17, \"autonumber\": 18, \"NUM\": 19, \"off\": 20, \"activate\": 21, \"actor\": 22, \"deactivate\": 23, \"note_statement\": 24, \"links_statement\": 25, \"link_statement\": 26, \"properties_statement\": 27, \"details_statement\": 28, \"title\": 29, \"legacy_title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"loop\": 36, \"rect\": 37, \"opt\": 38, \"alt\": 39, \"else_sections\": 40, \"par\": 41, \"par_sections\": 42, \"par_over\": 43, \"critical\": 44, \"option_sections\": 45, \"break\": 46, \"option\": 47, \"and\": 48, \"else\": 49, \"participant\": 50, \"AS\": 51, \"participant_actor\": 52, \"destroy\": 53, \"note\": 54, \"placement\": 55, \"text2\": 56, \"over\": 57, \"actor_pair\": 58, \"links\": 59, \"link\": 60, \"properties\": 61, \"details\": 62, \"spaceList\": 63, \",\": 64, \"left_of\": 65, \"right_of\": 66, \"signaltype\": 67, \"+\": 68, \"-\": 69, \"ACTOR\": 70, \"SOLID_OPEN_ARROW\": 71, \"DOTTED_OPEN_ARROW\": 72, \"SOLID_ARROW\": 73, \"BIDIRECTIONAL_SOLID_ARROW\": 74, \"DOTTED_ARROW\": 75, \"BIDIRECTIONAL_DOTTED_ARROW\": 76, \"SOLID_CROSS\": 77, \"DOTTED_CROSS\": 78, \"SOLID_POINT\": 79, \"DOTTED_POINT\": 80, \"TXT\": 81, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NEWLINE\", 6: \"SD\", 13: \"create\", 14: \"box\", 15: \"restOfLine\", 16: \"end\", 18: \"autonumber\", 19: \"NUM\", 20: \"off\", 21: \"activate\", 23: \"deactivate\", 29: \"title\", 30: \"legacy_title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"loop\", 37: \"rect\", 38: \"opt\", 39: \"alt\", 41: \"par\", 43: \"par_over\", 44: \"critical\", 46: \"break\", 47: \"option\", 48: \"and\", 49: \"else\", 50: \"participant\", 51: \"AS\", 52: \"participant_actor\", 53: \"destroy\", 54: \"note\", 57: \"over\", 59: \"links\", 60: \"link\", 61: \"properties\", 62: \"details\", 64: \",\", 65: \"left_of\", 66: \"right_of\", 68: \"+\", 69: \"-\", 70: \"ACTOR\", 71: \"SOLID_OPEN_ARROW\", 72: \"DOTTED_OPEN_ARROW\", 73: \"SOLID_ARROW\", 74: \"BIDIRECTIONAL_SOLID_ARROW\", 75: \"DOTTED_ARROW\", 76: \"BIDIRECTIONAL_DOTTED_ARROW\", 77: \"SOLID_CROSS\", 78: \"DOTTED_CROSS\", 79: \"SOLID_POINT\", 80: \"DOTTED_POINT\", 81: \"TXT\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [10, 0], [10, 2], [11, 2], [11, 1], [11, 1], [9, 1], [9, 2], [9, 4], [9, 2], [9, 4], [9, 3], [9, 3], [9, 2], [9, 3], [9, 3], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [45, 1], [45, 4], [42, 1], [42, 4], [40, 1], [40, 4], [12, 5], [12, 3], [12, 5], [12, 3], [12, 3], [24, 4], [24, 4], [25, 3], [26, 3], [27, 3], [28, 3], [63, 2], [63, 1], [58, 3], [58, 1], [55, 1], [55, 1], [17, 5], [17, 5], [17, 4], [22, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [56, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.apply($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n        case 9:\n          this.$ = [];\n          break;\n        case 5:\n        case 10:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 6:\n        case 7:\n        case 11:\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 8:\n        case 13:\n          this.$ = [];\n          break;\n        case 15:\n          $$[$0].type = \"createParticipant\";\n          this.$ = $$[$0];\n          break;\n        case 16:\n          $$[$0 - 1].unshift({ type: \"boxStart\", boxData: yy.parseBoxData($$[$0 - 2]) });\n          $$[$0 - 1].push({ type: \"boxEnd\", boxText: $$[$0 - 2] });\n          this.$ = $$[$0 - 1];\n          break;\n        case 18:\n          this.$ = { type: \"sequenceIndex\", sequenceIndex: Number($$[$0 - 2]), sequenceIndexStep: Number($$[$0 - 1]), sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 19:\n          this.$ = { type: \"sequenceIndex\", sequenceIndex: Number($$[$0 - 1]), sequenceIndexStep: 1, sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 20:\n          this.$ = { type: \"sequenceIndex\", sequenceVisible: false, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 21:\n          this.$ = { type: \"sequenceIndex\", sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 22:\n          this.$ = { type: \"activeStart\", signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0 - 1].actor };\n          break;\n        case 23:\n          this.$ = { type: \"activeEnd\", signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0 - 1].actor };\n          break;\n        case 29:\n          yy.setDiagramTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 30:\n          yy.setDiagramTitle($$[$0].substring(7));\n          this.$ = $$[$0].substring(7);\n          break;\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 34:\n          $$[$0 - 1].unshift({ type: \"loopStart\", loopText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.LOOP_START });\n          $$[$0 - 1].push({ type: \"loopEnd\", loopText: $$[$0 - 2], signalType: yy.LINETYPE.LOOP_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 35:\n          $$[$0 - 1].unshift({ type: \"rectStart\", color: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.RECT_START });\n          $$[$0 - 1].push({ type: \"rectEnd\", color: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.RECT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 36:\n          $$[$0 - 1].unshift({ type: \"optStart\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.OPT_START });\n          $$[$0 - 1].push({ type: \"optEnd\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.OPT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 37:\n          $$[$0 - 1].unshift({ type: \"altStart\", altText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.ALT_START });\n          $$[$0 - 1].push({ type: \"altEnd\", signalType: yy.LINETYPE.ALT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 38:\n          $$[$0 - 1].unshift({ type: \"parStart\", parText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.PAR_START });\n          $$[$0 - 1].push({ type: \"parEnd\", signalType: yy.LINETYPE.PAR_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 39:\n          $$[$0 - 1].unshift({ type: \"parStart\", parText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.PAR_OVER_START });\n          $$[$0 - 1].push({ type: \"parEnd\", signalType: yy.LINETYPE.PAR_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 40:\n          $$[$0 - 1].unshift({ type: \"criticalStart\", criticalText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.CRITICAL_START });\n          $$[$0 - 1].push({ type: \"criticalEnd\", signalType: yy.LINETYPE.CRITICAL_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 41:\n          $$[$0 - 1].unshift({ type: \"breakStart\", breakText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.BREAK_START });\n          $$[$0 - 1].push({ type: \"breakEnd\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.BREAK_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 43:\n          this.$ = $$[$0 - 3].concat([{ type: \"option\", optionText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.CRITICAL_OPTION }, $$[$0]]);\n          break;\n        case 45:\n          this.$ = $$[$0 - 3].concat([{ type: \"and\", parText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.PAR_AND }, $$[$0]]);\n          break;\n        case 47:\n          this.$ = $$[$0 - 3].concat([{ type: \"else\", altText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.ALT_ELSE }, $$[$0]]);\n          break;\n        case 48:\n          $$[$0 - 3].draw = \"participant\";\n          $$[$0 - 3].type = \"addParticipant\";\n          $$[$0 - 3].description = yy.parseMessage($$[$0 - 1]);\n          this.$ = $$[$0 - 3];\n          break;\n        case 49:\n          $$[$0 - 1].draw = \"participant\";\n          $$[$0 - 1].type = \"addParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 50:\n          $$[$0 - 3].draw = \"actor\";\n          $$[$0 - 3].type = \"addParticipant\";\n          $$[$0 - 3].description = yy.parseMessage($$[$0 - 1]);\n          this.$ = $$[$0 - 3];\n          break;\n        case 51:\n          $$[$0 - 1].draw = \"actor\";\n          $$[$0 - 1].type = \"addParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 52:\n          $$[$0 - 1].type = \"destroyParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 53:\n          this.$ = [$$[$0 - 1], { type: \"addNote\", placement: $$[$0 - 2], actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 54:\n          $$[$0 - 2] = [].concat($$[$0 - 1], $$[$0 - 1]).slice(0, 2);\n          $$[$0 - 2][0] = $$[$0 - 2][0].actor;\n          $$[$0 - 2][1] = $$[$0 - 2][1].actor;\n          this.$ = [$$[$0 - 1], { type: \"addNote\", placement: yy.PLACEMENT.OVER, actor: $$[$0 - 2].slice(0, 2), text: $$[$0] }];\n          break;\n        case 55:\n          this.$ = [$$[$0 - 1], { type: \"addLinks\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 56:\n          this.$ = [$$[$0 - 1], { type: \"addALink\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 57:\n          this.$ = [$$[$0 - 1], { type: \"addProperties\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 58:\n          this.$ = [$$[$0 - 1], { type: \"addDetails\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 61:\n          this.$ = [$$[$0 - 2], $$[$0]];\n          break;\n        case 62:\n          this.$ = $$[$0];\n          break;\n        case 63:\n          this.$ = yy.PLACEMENT.LEFTOF;\n          break;\n        case 64:\n          this.$ = yy.PLACEMENT.RIGHTOF;\n          break;\n        case 65:\n          this.$ = [\n            $$[$0 - 4],\n            $$[$0 - 1],\n            { type: \"addMessage\", from: $$[$0 - 4].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 3], msg: $$[$0], activate: true },\n            { type: \"activeStart\", signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0 - 1].actor }\n          ];\n          break;\n        case 66:\n          this.$ = [\n            $$[$0 - 4],\n            $$[$0 - 1],\n            { type: \"addMessage\", from: $$[$0 - 4].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 3], msg: $$[$0] },\n            { type: \"activeEnd\", signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0 - 4].actor }\n          ];\n          break;\n        case 67:\n          this.$ = [$$[$0 - 3], $$[$0 - 1], { type: \"addMessage\", from: $$[$0 - 3].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 2], msg: $$[$0] }];\n          break;\n        case 68:\n          this.$ = { type: \"addParticipant\", actor: $$[$0] };\n          break;\n        case 69:\n          this.$ = yy.LINETYPE.SOLID_OPEN;\n          break;\n        case 70:\n          this.$ = yy.LINETYPE.DOTTED_OPEN;\n          break;\n        case 71:\n          this.$ = yy.LINETYPE.SOLID;\n          break;\n        case 72:\n          this.$ = yy.LINETYPE.BIDIRECTIONAL_SOLID;\n          break;\n        case 73:\n          this.$ = yy.LINETYPE.DOTTED;\n          break;\n        case 74:\n          this.$ = yy.LINETYPE.BIDIRECTIONAL_DOTTED;\n          break;\n        case 75:\n          this.$ = yy.LINETYPE.SOLID_CROSS;\n          break;\n        case 76:\n          this.$ = yy.LINETYPE.DOTTED_CROSS;\n          break;\n        case 77:\n          this.$ = yy.LINETYPE.SOLID_POINT;\n          break;\n        case 78:\n          this.$ = yy.LINETYPE.DOTTED_POINT;\n          break;\n        case 79:\n          this.$ = yy.parseMessage($$[$0].trim().substring(1));\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 13, 14, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, o($Vx, [2, 5]), { 9: 47, 12: 12, 13: $V6, 14: $V7, 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, o($Vx, [2, 7]), o($Vx, [2, 8]), o($Vx, [2, 14]), { 12: 48, 50: $Vo, 52: $Vp, 53: $Vq }, { 15: [1, 49] }, { 5: [1, 50] }, { 5: [1, 53], 19: [1, 51], 20: [1, 52] }, { 22: 54, 70: $Vw }, { 22: 55, 70: $Vw }, { 5: [1, 56] }, { 5: [1, 57] }, { 5: [1, 58] }, { 5: [1, 59] }, { 5: [1, 60] }, o($Vx, [2, 29]), o($Vx, [2, 30]), { 32: [1, 61] }, { 34: [1, 62] }, o($Vx, [2, 33]), { 15: [1, 63] }, { 15: [1, 64] }, { 15: [1, 65] }, { 15: [1, 66] }, { 15: [1, 67] }, { 15: [1, 68] }, { 15: [1, 69] }, { 15: [1, 70] }, { 22: 71, 70: $Vw }, { 22: 72, 70: $Vw }, { 22: 73, 70: $Vw }, { 67: 74, 71: [1, 75], 72: [1, 76], 73: [1, 77], 74: [1, 78], 75: [1, 79], 76: [1, 80], 77: [1, 81], 78: [1, 82], 79: [1, 83], 80: [1, 84] }, { 55: 85, 57: [1, 86], 65: [1, 87], 66: [1, 88] }, { 22: 89, 70: $Vw }, { 22: 90, 70: $Vw }, { 22: 91, 70: $Vw }, { 22: 92, 70: $Vw }, o([5, 51, 64, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], [2, 68]), o($Vx, [2, 6]), o($Vx, [2, 15]), o($Vy, [2, 9], { 10: 93 }), o($Vx, [2, 17]), { 5: [1, 95], 19: [1, 94] }, { 5: [1, 96] }, o($Vx, [2, 21]), { 5: [1, 97] }, { 5: [1, 98] }, o($Vx, [2, 24]), o($Vx, [2, 25]), o($Vx, [2, 26]), o($Vx, [2, 27]), o($Vx, [2, 28]), o($Vx, [2, 31]), o($Vx, [2, 32]), o($Vz, $V3, { 7: 99 }), o($Vz, $V3, { 7: 100 }), o($Vz, $V3, { 7: 101 }), o($VA, $V3, { 40: 102, 7: 103 }), o($VB, $V3, { 42: 104, 7: 105 }), o($VB, $V3, { 7: 105, 42: 106 }), o($VC, $V3, { 45: 107, 7: 108 }), o($Vz, $V3, { 7: 109 }), { 5: [1, 111], 51: [1, 110] }, { 5: [1, 113], 51: [1, 112] }, { 5: [1, 114] }, { 22: 117, 68: [1, 115], 69: [1, 116], 70: $Vw }, o($VD, [2, 69]), o($VD, [2, 70]), o($VD, [2, 71]), o($VD, [2, 72]), o($VD, [2, 73]), o($VD, [2, 74]), o($VD, [2, 75]), o($VD, [2, 76]), o($VD, [2, 77]), o($VD, [2, 78]), { 22: 118, 70: $Vw }, { 22: 120, 58: 119, 70: $Vw }, { 70: [2, 63] }, { 70: [2, 64] }, { 56: 121, 81: $VE }, { 56: 123, 81: $VE }, { 56: 124, 81: $VE }, { 56: 125, 81: $VE }, { 4: [1, 128], 5: [1, 130], 11: 127, 12: 129, 16: [1, 126], 50: $Vo, 52: $Vp, 53: $Vq }, { 5: [1, 131] }, o($Vx, [2, 19]), o($Vx, [2, 20]), o($Vx, [2, 22]), o($Vx, [2, 23]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 132], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 133], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 134], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 135] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 46], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 49: [1, 136], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 137] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 44], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 48: [1, 138], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 139] }, { 16: [1, 140] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 42], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 47: [1, 141], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 142], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 15: [1, 143] }, o($Vx, [2, 49]), { 15: [1, 144] }, o($Vx, [2, 51]), o($Vx, [2, 52]), { 22: 145, 70: $Vw }, { 22: 146, 70: $Vw }, { 56: 147, 81: $VE }, { 56: 148, 81: $VE }, { 56: 149, 81: $VE }, { 64: [1, 150], 81: [2, 62] }, { 5: [2, 55] }, { 5: [2, 79] }, { 5: [2, 56] }, { 5: [2, 57] }, { 5: [2, 58] }, o($Vx, [2, 16]), o($Vy, [2, 10]), { 12: 151, 50: $Vo, 52: $Vp, 53: $Vq }, o($Vy, [2, 12]), o($Vy, [2, 13]), o($Vx, [2, 18]), o($Vx, [2, 34]), o($Vx, [2, 35]), o($Vx, [2, 36]), o($Vx, [2, 37]), { 15: [1, 152] }, o($Vx, [2, 38]), { 15: [1, 153] }, o($Vx, [2, 39]), o($Vx, [2, 40]), { 15: [1, 154] }, o($Vx, [2, 41]), { 5: [1, 155] }, { 5: [1, 156] }, { 56: 157, 81: $VE }, { 56: 158, 81: $VE }, { 5: [2, 67] }, { 5: [2, 53] }, { 5: [2, 54] }, { 22: 159, 70: $Vw }, o($Vy, [2, 11]), o($VA, $V3, { 7: 103, 40: 160 }), o($VB, $V3, { 7: 105, 42: 161 }), o($VC, $V3, { 7: 108, 45: 162 }), o($Vx, [2, 48]), o($Vx, [2, 50]), { 5: [2, 65] }, { 5: [2, 66] }, { 81: [2, 61] }, { 16: [2, 47] }, { 16: [2, 45] }, { 16: [2, 43] }],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 87: [2, 63], 88: [2, 64], 121: [2, 55], 122: [2, 79], 123: [2, 56], 124: [2, 57], 125: [2, 58], 147: [2, 67], 148: [2, 53], 149: [2, 54], 157: [2, 65], 158: [2, 66], 159: [2, 61], 160: [2, 47], 161: [2, 45], 162: [2, 43] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 5;\n            break;\n          case 1:\n            break;\n          case 2:\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            break;\n          case 6:\n            return 19;\n            break;\n          case 7:\n            this.begin(\"LINE\");\n            return 14;\n            break;\n          case 8:\n            this.begin(\"ID\");\n            return 50;\n            break;\n          case 9:\n            this.begin(\"ID\");\n            return 52;\n            break;\n          case 10:\n            return 13;\n            break;\n          case 11:\n            this.begin(\"ID\");\n            return 53;\n            break;\n          case 12:\n            yy_.yytext = yy_.yytext.trim();\n            this.begin(\"ALIAS\");\n            return 70;\n            break;\n          case 13:\n            this.popState();\n            this.popState();\n            this.begin(\"LINE\");\n            return 51;\n            break;\n          case 14:\n            this.popState();\n            this.popState();\n            return 5;\n            break;\n          case 15:\n            this.begin(\"LINE\");\n            return 36;\n            break;\n          case 16:\n            this.begin(\"LINE\");\n            return 37;\n            break;\n          case 17:\n            this.begin(\"LINE\");\n            return 38;\n            break;\n          case 18:\n            this.begin(\"LINE\");\n            return 39;\n            break;\n          case 19:\n            this.begin(\"LINE\");\n            return 49;\n            break;\n          case 20:\n            this.begin(\"LINE\");\n            return 41;\n            break;\n          case 21:\n            this.begin(\"LINE\");\n            return 43;\n            break;\n          case 22:\n            this.begin(\"LINE\");\n            return 48;\n            break;\n          case 23:\n            this.begin(\"LINE\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"LINE\");\n            return 47;\n            break;\n          case 25:\n            this.begin(\"LINE\");\n            return 46;\n            break;\n          case 26:\n            this.popState();\n            return 15;\n            break;\n          case 27:\n            return 16;\n            break;\n          case 28:\n            return 65;\n            break;\n          case 29:\n            return 66;\n            break;\n          case 30:\n            return 59;\n            break;\n          case 31:\n            return 60;\n            break;\n          case 32:\n            return 61;\n            break;\n          case 33:\n            return 62;\n            break;\n          case 34:\n            return 57;\n            break;\n          case 35:\n            return 54;\n            break;\n          case 36:\n            this.begin(\"ID\");\n            return 21;\n            break;\n          case 37:\n            this.begin(\"ID\");\n            return 23;\n            break;\n          case 38:\n            return 29;\n            break;\n          case 39:\n            return 30;\n            break;\n          case 40:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 41:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 42:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 43:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 44:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 45:\n            this.popState();\n            break;\n          case 46:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 47:\n            return 6;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 20;\n            break;\n          case 50:\n            return 64;\n            break;\n          case 51:\n            return 5;\n            break;\n          case 52:\n            yy_.yytext = yy_.yytext.trim();\n            return 70;\n            break;\n          case 53:\n            return 73;\n            break;\n          case 54:\n            return 74;\n            break;\n          case 55:\n            return 75;\n            break;\n          case 56:\n            return 76;\n            break;\n          case 57:\n            return 71;\n            break;\n          case 58:\n            return 72;\n            break;\n          case 59:\n            return 77;\n            break;\n          case 60:\n            return 78;\n            break;\n          case 61:\n            return 79;\n            break;\n          case 62:\n            return 80;\n            break;\n          case 63:\n            return 81;\n            break;\n          case 64:\n            return 68;\n            break;\n          case 65:\n            return 69;\n            break;\n          case 66:\n            return 5;\n            break;\n          case 67:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[0-9]+(?=[ \\n]+))/i, /^(?:box\\b)/i, /^(?:participant\\b)/i, /^(?:actor\\b)/i, /^(?:create\\b)/i, /^(?:destroy\\b)/i, /^(?:[^\\<->\\->:\\n,;]+?([\\-]*[^\\<->\\->:\\n,;]+?)*?(?=((?!\\n)\\s)+as(?!\\n)\\s|[#\\n;]|$))/i, /^(?:as\\b)/i, /^(?:(?:))/i, /^(?:loop\\b)/i, /^(?:rect\\b)/i, /^(?:opt\\b)/i, /^(?:alt\\b)/i, /^(?:else\\b)/i, /^(?:par\\b)/i, /^(?:par_over\\b)/i, /^(?:and\\b)/i, /^(?:critical\\b)/i, /^(?:option\\b)/i, /^(?:break\\b)/i, /^(?:(?:[:]?(?:no)?wrap)?[^#\\n;]*)/i, /^(?:end\\b)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:links\\b)/i, /^(?:link\\b)/i, /^(?:properties\\b)/i, /^(?:details\\b)/i, /^(?:over\\b)/i, /^(?:note\\b)/i, /^(?:activate\\b)/i, /^(?:deactivate\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:title:\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:sequenceDiagram\\b)/i, /^(?:autonumber\\b)/i, /^(?:off\\b)/i, /^(?:,)/i, /^(?:;)/i, /^(?:[^\\+\\<->\\->:\\n,;]+((?!(-x|--x|-\\)|--\\)))[\\-]*[^\\+\\<->\\->:\\n,;]+)*)/i, /^(?:->>)/i, /^(?:<<->>)/i, /^(?:-->>)/i, /^(?:<<-->>)/i, /^(?:->)/i, /^(?:-->)/i, /^(?:-[x])/i, /^(?:--[x])/i, /^(?:-[\\)])/i, /^(?:--[\\)])/i, /^(?::(?:(?:no)?wrap)?[^#\\n;]+)/i, /^(?:\\+)/i, /^(?:-)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [45, 46], \"inclusive\": false }, \"acc_descr\": { \"rules\": [43], \"inclusive\": false }, \"acc_title\": { \"rules\": [41], \"inclusive\": false }, \"ID\": { \"rules\": [2, 3, 12], \"inclusive\": false }, \"ALIAS\": { \"rules\": [2, 3, 13, 14], \"inclusive\": false }, \"LINE\": { \"rules\": [2, 3, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sequenceDiagram_default = parser;\n\n// src/diagrams/sequence/sequenceDb.ts\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n  AUTONUMBER: 26,\n  CRITICAL_START: 27,\n  CRITICAL_OPTION: 28,\n  CRITICAL_END: 29,\n  BREAK_START: 30,\n  BREAK_END: 31,\n  PAR_OVER_START: 32,\n  BIDIRECTIONAL_SOLID: 33,\n  BIDIRECTIONAL_DOTTED: 34\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar SequenceDB = class {\n  constructor() {\n    this.state = new ImperativeState(() => ({\n      prevActor: void 0,\n      actors: /* @__PURE__ */ new Map(),\n      createdActors: /* @__PURE__ */ new Map(),\n      destroyedActors: /* @__PURE__ */ new Map(),\n      boxes: [],\n      messages: [],\n      notes: [],\n      sequenceNumbersEnabled: false,\n      wrapEnabled: void 0,\n      currentBox: void 0,\n      lastCreated: void 0,\n      lastDestroyed: void 0\n    }));\n    this.setAccTitle = setAccTitle;\n    this.setAccDescription = setAccDescription;\n    this.setDiagramTitle = setDiagramTitle;\n    this.getAccTitle = getAccTitle;\n    this.getAccDescription = getAccDescription;\n    this.getDiagramTitle = getDiagramTitle;\n    this.apply = this.apply.bind(this);\n    this.parseBoxData = this.parseBoxData.bind(this);\n    this.parseMessage = this.parseMessage.bind(this);\n    this.clear();\n    this.setWrap(getConfig2().wrap);\n    this.LINETYPE = LINETYPE;\n    this.ARROWTYPE = ARROWTYPE;\n    this.PLACEMENT = PLACEMENT;\n  }\n  static {\n    __name(this, \"SequenceDB\");\n  }\n  addBox(data) {\n    this.state.records.boxes.push({\n      name: data.text,\n      wrap: data.wrap ?? this.autoWrap(),\n      fill: data.color,\n      actorKeys: []\n    });\n    this.state.records.currentBox = this.state.records.boxes.slice(-1)[0];\n  }\n  addActor(id, name, description, type) {\n    let assignedBox = this.state.records.currentBox;\n    const old = this.state.records.actors.get(id);\n    if (old) {\n      if (this.state.records.currentBox && old.box && this.state.records.currentBox !== old.box) {\n        throw new Error(\n          `A same participant should only be defined in one Box: ${old.name} can't be in '${old.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`\n        );\n      }\n      assignedBox = old.box ? old.box : this.state.records.currentBox;\n      old.box = assignedBox;\n      if (old && name === old.name && description == null) {\n        return;\n      }\n    }\n    if (description?.text == null) {\n      description = { text: name, type };\n    }\n    if (type == null || description.text == null) {\n      description = { text: name, type };\n    }\n    this.state.records.actors.set(id, {\n      box: assignedBox,\n      name,\n      description: description.text,\n      wrap: description.wrap ?? this.autoWrap(),\n      prevActor: this.state.records.prevActor,\n      links: {},\n      properties: {},\n      actorCnt: null,\n      rectData: null,\n      type: type ?? \"participant\"\n    });\n    if (this.state.records.prevActor) {\n      const prevActorInRecords = this.state.records.actors.get(this.state.records.prevActor);\n      if (prevActorInRecords) {\n        prevActorInRecords.nextActor = id;\n      }\n    }\n    if (this.state.records.currentBox) {\n      this.state.records.currentBox.actorKeys.push(id);\n    }\n    this.state.records.prevActor = id;\n  }\n  activationCount(part) {\n    let i;\n    let count = 0;\n    if (!part) {\n      return 0;\n    }\n    for (i = 0; i < this.state.records.messages.length; i++) {\n      if (this.state.records.messages[i].type === this.LINETYPE.ACTIVE_START && this.state.records.messages[i].from === part) {\n        count++;\n      }\n      if (this.state.records.messages[i].type === this.LINETYPE.ACTIVE_END && this.state.records.messages[i].from === part) {\n        count--;\n      }\n    }\n    return count;\n  }\n  addMessage(idFrom, idTo, message, answer) {\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      answer\n    });\n  }\n  addSignal(idFrom, idTo, message, messageType, activate = false) {\n    if (messageType === this.LINETYPE.ACTIVE_END) {\n      const cnt = this.activationCount(idFrom ?? \"\");\n      if (cnt < 1) {\n        const error = new Error(\"Trying to inactivate an inactive participant (\" + idFrom + \")\");\n        error.hash = {\n          text: \"->>-\",\n          token: \"->>-\",\n          line: \"1\",\n          loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n          expected: [\"'ACTIVE_PARTICIPANT'\"]\n        };\n        throw error;\n      }\n    }\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message?.text ?? \"\",\n      wrap: message?.wrap ?? this.autoWrap(),\n      type: messageType,\n      activate\n    });\n    return true;\n  }\n  hasAtLeastOneBox() {\n    return this.state.records.boxes.length > 0;\n  }\n  hasAtLeastOneBoxWithTitle() {\n    return this.state.records.boxes.some((b) => b.name);\n  }\n  getMessages() {\n    return this.state.records.messages;\n  }\n  getBoxes() {\n    return this.state.records.boxes;\n  }\n  getActors() {\n    return this.state.records.actors;\n  }\n  getCreatedActors() {\n    return this.state.records.createdActors;\n  }\n  getDestroyedActors() {\n    return this.state.records.destroyedActors;\n  }\n  getActor(id) {\n    return this.state.records.actors.get(id);\n  }\n  getActorKeys() {\n    return [...this.state.records.actors.keys()];\n  }\n  enableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = true;\n  }\n  disableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = false;\n  }\n  showSequenceNumbers() {\n    return this.state.records.sequenceNumbersEnabled;\n  }\n  setWrap(wrapSetting) {\n    this.state.records.wrapEnabled = wrapSetting;\n  }\n  extractWrap(text) {\n    if (text === void 0) {\n      return {};\n    }\n    text = text.trim();\n    const wrap = /^:?wrap:/.exec(text) !== null ? true : /^:?nowrap:/.exec(text) !== null ? false : void 0;\n    const cleanedText = (wrap === void 0 ? text : text.replace(/^:?(?:no)?wrap:/, \"\")).trim();\n    return { cleanedText, wrap };\n  }\n  autoWrap() {\n    if (this.state.records.wrapEnabled !== void 0) {\n      return this.state.records.wrapEnabled;\n    }\n    return getConfig2().sequence?.wrap ?? false;\n  }\n  clear() {\n    this.state.reset();\n    clear();\n  }\n  parseMessage(str) {\n    const trimmedStr = str.trim();\n    const { wrap, cleanedText } = this.extractWrap(trimmedStr);\n    const message = {\n      text: cleanedText,\n      wrap\n    };\n    log.debug(`parseMessage: ${JSON.stringify(message)}`);\n    return message;\n  }\n  // We expect the box statement to be color first then description\n  // The color can be rgb,rgba,hsl,hsla, or css code names  #hex codes are not supported for now because of the way the char # is handled\n  // We extract first segment as color, the rest of the line is considered as text\n  parseBoxData(str) {\n    const match = /^((?:rgba?|hsla?)\\s*\\(.*\\)|\\w*)(.*)$/.exec(str);\n    let color = match?.[1] ? match[1].trim() : \"transparent\";\n    let title = match?.[2] ? match[2].trim() : void 0;\n    if (window?.CSS) {\n      if (!window.CSS.supports(\"color\", color)) {\n        color = \"transparent\";\n        title = str.trim();\n      }\n    } else {\n      const style = new Option().style;\n      style.color = color;\n      if (style.color !== color) {\n        color = \"transparent\";\n        title = str.trim();\n      }\n    }\n    const { wrap, cleanedText } = this.extractWrap(title);\n    return {\n      text: cleanedText ? sanitizeText(cleanedText, getConfig2()) : void 0,\n      color,\n      wrap\n    };\n  }\n  addNote(actor, placement, message) {\n    const note = {\n      actor,\n      placement,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap()\n    };\n    const actors = [].concat(actor, actor);\n    this.state.records.notes.push(note);\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: actors[0],\n      to: actors[1],\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      type: this.LINETYPE.NOTE,\n      placement\n    });\n  }\n  addLinks(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      let sanitizedText = sanitizeText(text.text, getConfig2());\n      sanitizedText = sanitizedText.replace(/&equals;/g, \"=\");\n      sanitizedText = sanitizedText.replace(/&amp;/g, \"&\");\n      const links = JSON.parse(sanitizedText);\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error(\"error while parsing actor link text\", e);\n    }\n  }\n  addALink(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      const links = {};\n      let sanitizedText = sanitizeText(text.text, getConfig2());\n      const sep = sanitizedText.indexOf(\"@\");\n      sanitizedText = sanitizedText.replace(/&equals;/g, \"=\");\n      sanitizedText = sanitizedText.replace(/&amp;/g, \"&\");\n      const label = sanitizedText.slice(0, sep - 1).trim();\n      const link = sanitizedText.slice(sep + 1).trim();\n      links[label] = link;\n      this.insertLinks(actor, links);\n    } catch (e) {\n      log.error(\"error while parsing actor link text\", e);\n    }\n  }\n  insertLinks(actor, links) {\n    if (actor.links == null) {\n      actor.links = links;\n    } else {\n      for (const key in links) {\n        actor.links[key] = links[key];\n      }\n    }\n  }\n  addProperties(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      const sanitizedText = sanitizeText(text.text, getConfig2());\n      const properties = JSON.parse(sanitizedText);\n      this.insertProperties(actor, properties);\n    } catch (e) {\n      log.error(\"error while parsing actor properties text\", e);\n    }\n  }\n  insertProperties(actor, properties) {\n    if (actor.properties == null) {\n      actor.properties = properties;\n    } else {\n      for (const key in properties) {\n        actor.properties[key] = properties[key];\n      }\n    }\n  }\n  boxEnd() {\n    this.state.records.currentBox = void 0;\n  }\n  addDetails(actorId, text) {\n    const actor = this.getActor(actorId);\n    const elem = document.getElementById(text.text);\n    try {\n      const text2 = elem.innerHTML;\n      const details = JSON.parse(text2);\n      if (details.properties) {\n        this.insertProperties(actor, details.properties);\n      }\n      if (details.links) {\n        this.insertLinks(actor, details.links);\n      }\n    } catch (e) {\n      log.error(\"error while parsing actor details text\", e);\n    }\n  }\n  getActorProperty(actor, key) {\n    if (actor?.properties !== void 0) {\n      return actor.properties[key];\n    }\n    return void 0;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-redundant-type-constituents\n  apply(param) {\n    if (Array.isArray(param)) {\n      param.forEach((item) => {\n        this.apply(item);\n      });\n    } else {\n      switch (param.type) {\n        case \"sequenceIndex\":\n          this.state.records.messages.push({\n            id: this.state.records.messages.length.toString(),\n            from: void 0,\n            to: void 0,\n            message: {\n              start: param.sequenceIndex,\n              step: param.sequenceIndexStep,\n              visible: param.sequenceVisible\n            },\n            wrap: false,\n            type: param.signalType\n          });\n          break;\n        case \"addParticipant\":\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          break;\n        case \"createParticipant\":\n          if (this.state.records.actors.has(param.actor)) {\n            throw new Error(\n              \"It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior\"\n            );\n          }\n          this.state.records.lastCreated = param.actor;\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          this.state.records.createdActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case \"destroyParticipant\":\n          this.state.records.lastDestroyed = param.actor;\n          this.state.records.destroyedActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case \"activeStart\":\n          this.addSignal(param.actor, void 0, void 0, param.signalType);\n          break;\n        case \"activeEnd\":\n          this.addSignal(param.actor, void 0, void 0, param.signalType);\n          break;\n        case \"addNote\":\n          this.addNote(param.actor, param.placement, param.text);\n          break;\n        case \"addLinks\":\n          this.addLinks(param.actor, param.text);\n          break;\n        case \"addALink\":\n          this.addALink(param.actor, param.text);\n          break;\n        case \"addProperties\":\n          this.addProperties(param.actor, param.text);\n          break;\n        case \"addDetails\":\n          this.addDetails(param.actor, param.text);\n          break;\n        case \"addMessage\":\n          if (this.state.records.lastCreated) {\n            if (param.to !== this.state.records.lastCreated) {\n              throw new Error(\n                \"The created participant \" + this.state.records.lastCreated.name + \" does not have an associated creating message after its declaration. Please check the sequence diagram.\"\n              );\n            } else {\n              this.state.records.lastCreated = void 0;\n            }\n          } else if (this.state.records.lastDestroyed) {\n            if (param.to !== this.state.records.lastDestroyed && param.from !== this.state.records.lastDestroyed) {\n              throw new Error(\n                \"The destroyed participant \" + this.state.records.lastDestroyed.name + \" does not have an associated destroying message after its declaration. Please check the sequence diagram.\"\n              );\n            } else {\n              this.state.records.lastDestroyed = void 0;\n            }\n          }\n          this.addSignal(param.from, param.to, param.msg, param.signalType, param.activate);\n          break;\n        case \"boxStart\":\n          this.addBox(param.boxData);\n          break;\n        case \"boxEnd\":\n          this.boxEnd();\n          break;\n        case \"loopStart\":\n          this.addSignal(void 0, void 0, param.loopText, param.signalType);\n          break;\n        case \"loopEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"rectStart\":\n          this.addSignal(void 0, void 0, param.color, param.signalType);\n          break;\n        case \"rectEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"optStart\":\n          this.addSignal(void 0, void 0, param.optText, param.signalType);\n          break;\n        case \"optEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"altStart\":\n          this.addSignal(void 0, void 0, param.altText, param.signalType);\n          break;\n        case \"else\":\n          this.addSignal(void 0, void 0, param.altText, param.signalType);\n          break;\n        case \"altEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"setAccTitle\":\n          setAccTitle(param.text);\n          break;\n        case \"parStart\":\n          this.addSignal(void 0, void 0, param.parText, param.signalType);\n          break;\n        case \"and\":\n          this.addSignal(void 0, void 0, param.parText, param.signalType);\n          break;\n        case \"parEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"criticalStart\":\n          this.addSignal(void 0, void 0, param.criticalText, param.signalType);\n          break;\n        case \"option\":\n          this.addSignal(void 0, void 0, param.optionText, param.signalType);\n          break;\n        case \"criticalEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"breakStart\":\n          this.addSignal(void 0, void 0, param.breakText, param.signalType);\n          break;\n        case \"breakEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n      }\n    }\n  }\n  getConfig() {\n    return getConfig2().sequence;\n  }\n};\n\n// src/diagrams/sequence/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.actor {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${options.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${options.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${options.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${options.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${options.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .messageText {\n    fill: ${options.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${options.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${options.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${options.noteBorderColor};\n    fill: ${options.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${options.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${options.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n    stroke-width: 2px;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sequence/sequenceRenderer.ts\nimport { select } from \"d3\";\n\n// src/diagrams/sequence/svgDraw.js\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar ACTOR_TYPE_WIDTH = 18 * 2;\nvar TOP_ACTOR_CLASS = \"actor-top\";\nvar BOTTOM_ACTOR_CLASS = \"actor-bottom\";\nvar ACTOR_BOX_CLASS = \"actor-box\";\nvar ACTOR_MAN_FIGURE_CLASS = \"actor-man\";\nvar drawRect2 = /* @__PURE__ */ __name(function(elem, rectData) {\n  return drawRect(elem, rectData);\n}, \"drawRect\");\nvar drawPopup = /* @__PURE__ */ __name(function(elem, actor, minMenuWidth, textAttrs, forceMenus) {\n  if (actor.links === void 0 || actor.links === null || Object.keys(actor.links).length === 0) {\n    return { height: 0, width: 0 };\n  }\n  const links = actor.links;\n  const actorCnt2 = actor.actorCnt;\n  const rectData = actor.rectData;\n  var displayValue = \"none\";\n  if (forceMenus) {\n    displayValue = \"block !important\";\n  }\n  const g = elem.append(\"g\");\n  g.attr(\"id\", \"actor\" + actorCnt2 + \"_popup\");\n  g.attr(\"class\", \"actorPopupMenu\");\n  g.attr(\"display\", displayValue);\n  var actorClass = \"\";\n  if (rectData.class !== void 0) {\n    actorClass = \" \" + rectData.class;\n  }\n  let menuWidth = rectData.width > minMenuWidth ? rectData.width : minMenuWidth;\n  const rectElem = g.append(\"rect\");\n  rectElem.attr(\"class\", \"actorPopupMenuPanel\" + actorClass);\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.height);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", menuWidth);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (links != null) {\n    var linkY = 20;\n    for (let key in links) {\n      var linkElem = g.append(\"a\");\n      var sanitizedLink = sanitizeUrl(links[key]);\n      linkElem.attr(\"xlink:href\", sanitizedLink);\n      linkElem.attr(\"target\", \"_blank\");\n      _drawMenuItemTextCandidateFunc(textAttrs)(\n        key,\n        linkElem,\n        rectData.x + 10,\n        rectData.height + linkY,\n        menuWidth,\n        20,\n        { class: \"actor\" },\n        textAttrs\n      );\n      linkY += 30;\n    }\n  }\n  rectElem.attr(\"height\", linkY);\n  return { height: rectData.height + linkY, width: menuWidth };\n}, \"drawPopup\");\nvar popupMenuToggle = /* @__PURE__ */ __name(function(popId) {\n  return \"var pu = document.getElementById('\" + popId + \"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }\";\n}, \"popupMenuToggle\");\nvar drawKatex = /* @__PURE__ */ __name(async function(elem, textData, msgModel = null) {\n  let textElem = elem.append(\"foreignObject\");\n  const lines = await renderKatex(textData.text, getConfig());\n  const divElem = textElem.append(\"xhtml:div\").attr(\"style\", \"width: fit-content;\").attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\").html(lines);\n  const dim = divElem.node().getBoundingClientRect();\n  textElem.attr(\"height\", Math.round(dim.height)).attr(\"width\", Math.round(dim.width));\n  if (textData.class === \"noteText\") {\n    const rectElem = elem.node().firstChild;\n    rectElem.setAttribute(\"height\", dim.height + 2 * textData.textMargin);\n    const rectDim = rectElem.getBBox();\n    textElem.attr(\"x\", Math.round(rectDim.x + rectDim.width / 2 - dim.width / 2)).attr(\"y\", Math.round(rectDim.y + rectDim.height / 2 - dim.height / 2));\n  } else if (msgModel) {\n    let { startx, stopx, starty } = msgModel;\n    if (startx > stopx) {\n      const temp = startx;\n      startx = stopx;\n      stopx = temp;\n    }\n    textElem.attr(\"x\", Math.round(startx + Math.abs(startx - stopx) / 2 - dim.width / 2));\n    if (textData.class === \"loopText\") {\n      textElem.attr(\"y\", Math.round(starty));\n    } else {\n      textElem.attr(\"y\", Math.round(starty - dim.height));\n    }\n  }\n  return [textElem];\n}, \"drawKatex\");\nvar drawText = /* @__PURE__ */ __name(function(elem, textData) {\n  let prevTextHeight = 0;\n  let textHeight = 0;\n  const lines = textData.text.split(common_default.lineBreakRegex);\n  const [_textFontSize, _textFontSizePx] = parseFontSize(textData.fontSize);\n  let textElems = [];\n  let dy = 0;\n  let yfunc = /* @__PURE__ */ __name(() => textData.y, \"yfunc\");\n  if (textData.valign !== void 0 && textData.textMargin !== void 0 && textData.textMargin > 0) {\n    switch (textData.valign) {\n      case \"top\":\n      case \"start\":\n        yfunc = /* @__PURE__ */ __name(() => Math.round(textData.y + textData.textMargin), \"yfunc\");\n        break;\n      case \"middle\":\n      case \"center\":\n        yfunc = /* @__PURE__ */ __name(() => Math.round(textData.y + (prevTextHeight + textHeight + textData.textMargin) / 2), \"yfunc\");\n        break;\n      case \"bottom\":\n      case \"end\":\n        yfunc = /* @__PURE__ */ __name(() => Math.round(\n          textData.y + (prevTextHeight + textHeight + 2 * textData.textMargin) - textData.textMargin\n        ), \"yfunc\");\n        break;\n    }\n  }\n  if (textData.anchor !== void 0 && textData.textMargin !== void 0 && textData.width !== void 0) {\n    switch (textData.anchor) {\n      case \"left\":\n      case \"start\":\n        textData.x = Math.round(textData.x + textData.textMargin);\n        textData.anchor = \"start\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n      case \"middle\":\n      case \"center\":\n        textData.x = Math.round(textData.x + textData.width / 2);\n        textData.anchor = \"middle\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n      case \"right\":\n      case \"end\":\n        textData.x = Math.round(textData.x + textData.width - textData.textMargin);\n        textData.anchor = \"end\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n    }\n  }\n  for (let [i, line] of lines.entries()) {\n    if (textData.textMargin !== void 0 && textData.textMargin === 0 && _textFontSize !== void 0) {\n      dy = i * _textFontSize;\n    }\n    const textElem = elem.append(\"text\");\n    textElem.attr(\"x\", textData.x);\n    textElem.attr(\"y\", yfunc());\n    if (textData.anchor !== void 0) {\n      textElem.attr(\"text-anchor\", textData.anchor).attr(\"dominant-baseline\", textData.dominantBaseline).attr(\"alignment-baseline\", textData.alignmentBaseline);\n    }\n    if (textData.fontFamily !== void 0) {\n      textElem.style(\"font-family\", textData.fontFamily);\n    }\n    if (_textFontSizePx !== void 0) {\n      textElem.style(\"font-size\", _textFontSizePx);\n    }\n    if (textData.fontWeight !== void 0) {\n      textElem.style(\"font-weight\", textData.fontWeight);\n    }\n    if (textData.fill !== void 0) {\n      textElem.attr(\"fill\", textData.fill);\n    }\n    if (textData.class !== void 0) {\n      textElem.attr(\"class\", textData.class);\n    }\n    if (textData.dy !== void 0) {\n      textElem.attr(\"dy\", textData.dy);\n    } else if (dy !== 0) {\n      textElem.attr(\"dy\", dy);\n    }\n    const text = line || ZERO_WIDTH_SPACE;\n    if (textData.tspan) {\n      const span = textElem.append(\"tspan\");\n      span.attr(\"x\", textData.x);\n      if (textData.fill !== void 0) {\n        span.attr(\"fill\", textData.fill);\n      }\n      span.text(text);\n    } else {\n      textElem.text(text);\n    }\n    if (textData.valign !== void 0 && textData.textMargin !== void 0 && textData.textMargin > 0) {\n      textHeight += (textElem._groups || textElem)[0][0].getBBox().height;\n      prevTextHeight = textHeight;\n    }\n    textElems.push(textElem);\n  }\n  return textElems;\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ __name(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  __name(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, txtObject.width, txtObject.height, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.height / 2;\n  drawText(elem, txtObject);\n  return polygon;\n}, \"drawLabel\");\nvar actorCnt = -1;\nvar fixLifeLineHeights = /* @__PURE__ */ __name((diagram2, actors, actorKeys, conf2) => {\n  if (!diagram2.select) {\n    return;\n  }\n  actorKeys.forEach((actorKey) => {\n    const actor = actors.get(actorKey);\n    const actorDOM = diagram2.select(\"#actor\" + actor.actorCnt);\n    if (!conf2.mirrorActors && actor.stopy) {\n      actorDOM.attr(\"y2\", actor.stopy + actor.height / 2);\n    } else if (conf2.mirrorActors) {\n      actorDOM.attr(\"y2\", actor.stopy);\n    }\n  });\n}, \"fixLifeLineHeights\");\nvar drawActorTypeParticipant = /* @__PURE__ */ __name(function(elem, actor, conf2, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + actor.height;\n  const boxplusLineGroup = elem.append(\"g\").lower();\n  var g = boxplusLineGroup;\n  if (!isFooter) {\n    actorCnt++;\n    if (Object.keys(actor.links || {}).length && !conf2.forceMenus) {\n      g.attr(\"onclick\", popupMenuToggle(`actor${actorCnt}_popup`)).attr(\"cursor\", \"pointer\");\n    }\n    g.append(\"line\").attr(\"id\", \"actor\" + actorCnt).attr(\"x1\", center).attr(\"y1\", centerY).attr(\"x2\", center).attr(\"y2\", 2e3).attr(\"class\", \"actor-line 200\").attr(\"stroke-width\", \"0.5px\").attr(\"stroke\", \"#999\").attr(\"name\", actor.name);\n    g = boxplusLineGroup.append(\"g\");\n    actor.actorCnt = actorCnt;\n    if (actor.links != null) {\n      g.attr(\"id\", \"root-\" + actorCnt);\n    }\n  }\n  const rect = getNoteRect();\n  var cssclass = \"actor\";\n  if (actor.properties?.class) {\n    cssclass = actor.properties.class;\n  } else {\n    rect.fill = \"#eaeaea\";\n  }\n  if (isFooter) {\n    cssclass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssclass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = cssclass;\n  rect.rx = 3;\n  rect.ry = 3;\n  rect.name = actor.name;\n  const rectElem = drawRect2(g, rect);\n  actor.rectData = rect;\n  if (actor.properties?.icon) {\n    const iconSrc = actor.properties.icon.trim();\n    if (iconSrc.charAt(0) === \"@\") {\n      drawEmbeddedImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc.substr(1));\n    } else {\n      drawImage(g, rect.x + rect.width - 20, rect.y + 10, iconSrc);\n    }\n  }\n  _drawTextCandidateFunc(conf2, hasKatex(actor.description))(\n    actor.description,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_BOX_CLASS}` },\n    conf2\n  );\n  let height = actor.height;\n  if (rectElem.node) {\n    const bounds2 = rectElem.node().getBBox();\n    actor.height = bounds2.height;\n    height = bounds2.height;\n  }\n  return height;\n}, \"drawActorTypeParticipant\");\nvar drawActorTypeActor = /* @__PURE__ */ __name(function(elem, actor, conf2, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + 80;\n  const line = elem.append(\"g\").lower();\n  if (!isFooter) {\n    actorCnt++;\n    line.append(\"line\").attr(\"id\", \"actor\" + actorCnt).attr(\"x1\", center).attr(\"y1\", centerY).attr(\"x2\", center).attr(\"y2\", 2e3).attr(\"class\", \"actor-line 200\").attr(\"stroke-width\", \"0.5px\").attr(\"stroke\", \"#999\").attr(\"name\", actor.name);\n    actor.actorCnt = actorCnt;\n  }\n  const actElem = elem.append(\"g\");\n  let cssClass = ACTOR_MAN_FIGURE_CLASS;\n  if (isFooter) {\n    cssClass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssClass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  actElem.attr(\"class\", cssClass);\n  actElem.attr(\"name\", actor.name);\n  const rect = getNoteRect();\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.fill = \"#eaeaea\";\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = \"actor\";\n  rect.rx = 3;\n  rect.ry = 3;\n  actElem.append(\"line\").attr(\"id\", \"actor-man-torso\" + actorCnt).attr(\"x1\", center).attr(\"y1\", actorY + 25).attr(\"x2\", center).attr(\"y2\", actorY + 45);\n  actElem.append(\"line\").attr(\"id\", \"actor-man-arms\" + actorCnt).attr(\"x1\", center - ACTOR_TYPE_WIDTH / 2).attr(\"y1\", actorY + 33).attr(\"x2\", center + ACTOR_TYPE_WIDTH / 2).attr(\"y2\", actorY + 33);\n  actElem.append(\"line\").attr(\"x1\", center - ACTOR_TYPE_WIDTH / 2).attr(\"y1\", actorY + 60).attr(\"x2\", center).attr(\"y2\", actorY + 45);\n  actElem.append(\"line\").attr(\"x1\", center).attr(\"y1\", actorY + 45).attr(\"x2\", center + ACTOR_TYPE_WIDTH / 2 - 2).attr(\"y2\", actorY + 60);\n  const circle = actElem.append(\"circle\");\n  circle.attr(\"cx\", actor.x + actor.width / 2);\n  circle.attr(\"cy\", actorY + 10);\n  circle.attr(\"r\", 15);\n  circle.attr(\"width\", actor.width);\n  circle.attr(\"height\", actor.height);\n  const bounds2 = actElem.node().getBBox();\n  actor.height = bounds2.height;\n  _drawTextCandidateFunc(conf2, hasKatex(actor.description))(\n    actor.description,\n    actElem,\n    rect.x,\n    rect.y + 35,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_MAN_FIGURE_CLASS}` },\n    conf2\n  );\n  return actor.height;\n}, \"drawActorTypeActor\");\nvar drawActor = /* @__PURE__ */ __name(async function(elem, actor, conf2, isFooter) {\n  switch (actor.type) {\n    case \"actor\":\n      return await drawActorTypeActor(elem, actor, conf2, isFooter);\n    case \"participant\":\n      return await drawActorTypeParticipant(elem, actor, conf2, isFooter);\n  }\n}, \"drawActor\");\nvar drawBox = /* @__PURE__ */ __name(function(elem, box, conf2) {\n  const boxplusTextGroup = elem.append(\"g\");\n  const g = boxplusTextGroup;\n  drawBackgroundRect2(g, box);\n  if (box.name) {\n    _drawTextCandidateFunc(conf2)(\n      box.name,\n      g,\n      box.x,\n      box.y + (box.textMaxHeight || 0) / 2,\n      box.width,\n      0,\n      { class: \"text\" },\n      conf2\n    );\n  }\n  g.lower();\n}, \"drawBox\");\nvar anchorElement = /* @__PURE__ */ __name(function(elem) {\n  return elem.append(\"g\");\n}, \"anchorElement\");\nvar drawActivation = /* @__PURE__ */ __name(function(elem, bounds2, verticalPos, conf2, actorActivations2) {\n  const rect = getNoteRect();\n  const g = bounds2.anchored;\n  rect.x = bounds2.startx;\n  rect.y = bounds2.starty;\n  rect.class = \"activation\" + actorActivations2 % 3;\n  rect.width = bounds2.stopx - bounds2.startx;\n  rect.height = verticalPos - bounds2.starty;\n  drawRect2(g, rect);\n}, \"drawActivation\");\nvar drawLoop = /* @__PURE__ */ __name(async function(elem, loopModel, labelText, conf2) {\n  const {\n    boxMargin,\n    boxTextMargin,\n    labelBoxHeight,\n    labelBoxWidth,\n    messageFontFamily: fontFamily,\n    messageFontSize: fontSize,\n    messageFontWeight: fontWeight\n  } = conf2;\n  const g = elem.append(\"g\");\n  const drawLoopLine = /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    return g.append(\"line\").attr(\"x1\", startx).attr(\"y1\", starty).attr(\"x2\", stopx).attr(\"y2\", stopy).attr(\"class\", \"loopLine\");\n  }, \"drawLoopLine\");\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.stopx, loopModel.starty);\n  drawLoopLine(loopModel.stopx, loopModel.starty, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.stopy, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.startx, loopModel.stopy);\n  if (loopModel.sections !== void 0) {\n    loopModel.sections.forEach(function(item) {\n      drawLoopLine(loopModel.startx, item.y, loopModel.stopx, item.y).style(\n        \"stroke-dasharray\",\n        \"3, 3\"\n      );\n    });\n  }\n  let txt = getTextObj();\n  txt.text = labelText;\n  txt.x = loopModel.startx;\n  txt.y = loopModel.starty;\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.anchor = \"middle\";\n  txt.valign = \"middle\";\n  txt.tspan = false;\n  txt.width = labelBoxWidth || 50;\n  txt.height = labelBoxHeight || 20;\n  txt.textMargin = boxTextMargin;\n  txt.class = \"labelText\";\n  drawLabel(g, txt);\n  txt = getTextObj2();\n  txt.text = loopModel.title;\n  txt.x = loopModel.startx + labelBoxWidth / 2 + (loopModel.stopx - loopModel.startx) / 2;\n  txt.y = loopModel.starty + boxMargin + boxTextMargin;\n  txt.anchor = \"middle\";\n  txt.valign = \"middle\";\n  txt.textMargin = boxTextMargin;\n  txt.class = \"loopText\";\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.wrap = true;\n  let textElem = hasKatex(txt.text) ? await drawKatex(g, txt, loopModel) : drawText(g, txt);\n  if (loopModel.sectionTitles !== void 0) {\n    for (const [idx, item] of Object.entries(loopModel.sectionTitles)) {\n      if (item.message) {\n        txt.text = item.message;\n        txt.x = loopModel.startx + (loopModel.stopx - loopModel.startx) / 2;\n        txt.y = loopModel.sections[idx].y + boxMargin + boxTextMargin;\n        txt.class = \"loopText\";\n        txt.anchor = \"middle\";\n        txt.valign = \"middle\";\n        txt.tspan = false;\n        txt.fontFamily = fontFamily;\n        txt.fontSize = fontSize;\n        txt.fontWeight = fontWeight;\n        txt.wrap = loopModel.wrap;\n        if (hasKatex(txt.text)) {\n          loopModel.starty = loopModel.sections[idx].y;\n          await drawKatex(g, txt, loopModel);\n        } else {\n          drawText(g, txt);\n        }\n        let sectionHeight = Math.round(\n          textElem.map((te) => (te._groups || te)[0][0].getBBox().height).reduce((acc, curr) => acc + curr)\n        );\n        loopModel.sections[idx].height += sectionHeight - (boxMargin + boxTextMargin);\n      }\n    }\n  }\n  loopModel.height = Math.round(loopModel.stopy - loopModel.starty);\n  return g;\n}, \"drawLoop\");\nvar drawBackgroundRect2 = /* @__PURE__ */ __name(function(elem, bounds2) {\n  drawBackgroundRect(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar insertDatabaseIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 7.9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto-start-reverse\").append(\"path\").attr(\"d\", \"M -1 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowFilledHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 15.5).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertSequenceNumber = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertSequenceNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ __name(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 4).attr(\"refY\", 4.5);\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1pt\").attr(\"d\", \"M 1,2 L 6,7 M 6,2 L 1,7\");\n}, \"insertArrowCrossHead\");\nvar getTextObj2 = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    anchor: void 0,\n    style: \"#666\",\n    width: void 0,\n    height: void 0,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n    valign: void 0\n  };\n}, \"getTextObj\");\nvar getNoteRect2 = /* @__PURE__ */ __name(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n}, \"getNoteRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf2;\n    const [_actorFontSize, _actorFontSizePx] = parseFontSize(actorFontSize);\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * _actorFontSize - _actorFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").style(\"font-size\", _actorFontSizePx).style(\"font-weight\", actorFontWeight).style(\"font-family\", actorFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  async function byKatex(content, g, x, y, width, height, textAttrs, conf2) {\n    const dim = await calculateMathMLDimensions(content, getConfig());\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x + width / 2 - dim.width / 2).attr(\"y\", y + height / 2 - dim.height / 2).attr(\"width\", dim.width).attr(\"height\", dim.height);\n    const text = f.append(\"xhtml:div\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").html(await renderKatex(content, getConfig()));\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byKatex, \"byKatex\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2, hasKatex2 = false) {\n    if (hasKatex2) {\n      return byKatex;\n    }\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar _drawMenuItemTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x).attr(\"y\", y).style(\"text-anchor\", \"start\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf2;\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * actorFontSize - actorFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x).attr(\"y\", y).style(\"text-anchor\", \"start\").style(\"font-size\", actorFontSize).style(\"font-weight\", actorFontWeight).style(\"font-family\", actorFontFamily);\n      text.append(\"tspan\").attr(\"x\", x).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawText,\n  drawLabel,\n  drawActor,\n  drawBox,\n  drawPopup,\n  anchorElement,\n  drawActivation,\n  drawLoop,\n  drawBackgroundRect: drawBackgroundRect2,\n  insertArrowHead,\n  insertArrowFilledHead,\n  insertSequenceNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n  getTextObj: getTextObj2,\n  getNoteRect: getNoteRect2,\n  fixLifeLineHeights,\n  sanitizeUrl\n};\n\n// src/diagrams/sequence/sequenceRenderer.ts\nvar conf = {};\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  activations: [],\n  models: {\n    getHeight: /* @__PURE__ */ __name(function() {\n      return Math.max.apply(\n        null,\n        this.actors.length === 0 ? [0] : this.actors.map((actor) => actor.height || 0)\n      ) + (this.loops.length === 0 ? 0 : this.loops.map((it) => it.height || 0).reduce((acc, h) => acc + h)) + (this.messages.length === 0 ? 0 : this.messages.map((it) => it.height || 0).reduce((acc, h) => acc + h)) + (this.notes.length === 0 ? 0 : this.notes.map((it) => it.height || 0).reduce((acc, h) => acc + h));\n    }, \"getHeight\"),\n    clear: /* @__PURE__ */ __name(function() {\n      this.actors = [];\n      this.boxes = [];\n      this.loops = [];\n      this.messages = [];\n      this.notes = [];\n    }, \"clear\"),\n    addBox: /* @__PURE__ */ __name(function(boxModel) {\n      this.boxes.push(boxModel);\n    }, \"addBox\"),\n    addActor: /* @__PURE__ */ __name(function(actorModel) {\n      this.actors.push(actorModel);\n    }, \"addActor\"),\n    addLoop: /* @__PURE__ */ __name(function(loopModel) {\n      this.loops.push(loopModel);\n    }, \"addLoop\"),\n    addMessage: /* @__PURE__ */ __name(function(msgModel) {\n      this.messages.push(msgModel);\n    }, \"addMessage\"),\n    addNote: /* @__PURE__ */ __name(function(noteModel) {\n      this.notes.push(noteModel);\n    }, \"addNote\"),\n    lastActor: /* @__PURE__ */ __name(function() {\n      return this.actors[this.actors.length - 1];\n    }, \"lastActor\"),\n    lastLoop: /* @__PURE__ */ __name(function() {\n      return this.loops[this.loops.length - 1];\n    }, \"lastLoop\"),\n    lastMessage: /* @__PURE__ */ __name(function() {\n      return this.messages[this.messages.length - 1];\n    }, \"lastMessage\"),\n    lastNote: /* @__PURE__ */ __name(function() {\n      return this.notes[this.notes.length - 1];\n    }, \"lastNote\"),\n    actors: [],\n    boxes: [],\n    loops: [],\n    messages: [],\n    notes: []\n  },\n  init: /* @__PURE__ */ __name(function() {\n    this.sequenceItems = [];\n    this.activations = [];\n    this.models.clear();\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n    setConf(getConfig2());\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ __name(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ __name(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    __name(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n    this.activations.forEach(updateFn(\"activation\"));\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ __name(function(startx, starty, stopx, stopy) {\n    const _startx = common_default.getMin(startx, stopx);\n    const _stopx = common_default.getMax(startx, stopx);\n    const _starty = common_default.getMin(starty, stopy);\n    const _stopy = common_default.getMax(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  newActivation: /* @__PURE__ */ __name(function(message, diagram2, actors) {\n    const actorRect = actors.get(message.from);\n    const stackedSize = actorActivations(message.from).length || 0;\n    const x = actorRect.x + actorRect.width / 2 + (stackedSize - 1) * conf.activationWidth / 2;\n    this.activations.push({\n      startx: x,\n      starty: this.verticalPos + 2,\n      stopx: x + conf.activationWidth,\n      stopy: void 0,\n      actor: message.from,\n      anchored: svgDraw_default.anchorElement(diagram2)\n    });\n  }, \"newActivation\"),\n  endActivation: /* @__PURE__ */ __name(function(message) {\n    const lastActorActivationIdx = this.activations.map(function(activation) {\n      return activation.actor;\n    }).lastIndexOf(message.from);\n    return this.activations.splice(lastActorActivationIdx, 1)[0];\n  }, \"endActivation\"),\n  createLoop: /* @__PURE__ */ __name(function(title = { message: void 0, wrap: false, width: void 0 }, fill) {\n    return {\n      startx: void 0,\n      starty: this.verticalPos,\n      stopx: void 0,\n      stopy: void 0,\n      title: title.message,\n      wrap: title.wrap,\n      width: title.width,\n      height: 0,\n      fill\n    };\n  }, \"createLoop\"),\n  newLoop: /* @__PURE__ */ __name(function(title = { message: void 0, wrap: false, width: void 0 }, fill) {\n    this.sequenceItems.push(this.createLoop(title, fill));\n  }, \"newLoop\"),\n  endLoop: /* @__PURE__ */ __name(function() {\n    return this.sequenceItems.pop();\n  }, \"endLoop\"),\n  isLoopOverlap: /* @__PURE__ */ __name(function() {\n    return this.sequenceItems.length ? this.sequenceItems[this.sequenceItems.length - 1].overlap : false;\n  }, \"isLoopOverlap\"),\n  addSectionToLoop: /* @__PURE__ */ __name(function(message) {\n    const loop = this.sequenceItems.pop();\n    loop.sections = loop.sections || [];\n    loop.sectionTitles = loop.sectionTitles || [];\n    loop.sections.push({ y: bounds.getVerticalPos(), height: 0 });\n    loop.sectionTitles.push(message);\n    this.sequenceItems.push(loop);\n  }, \"addSectionToLoop\"),\n  saveVerticalPos: /* @__PURE__ */ __name(function() {\n    if (this.isLoopOverlap()) {\n      this.savedVerticalPos = this.verticalPos;\n    }\n  }, \"saveVerticalPos\"),\n  resetVerticalPos: /* @__PURE__ */ __name(function() {\n    if (this.isLoopOverlap()) {\n      this.verticalPos = this.savedVerticalPos;\n    }\n  }, \"resetVerticalPos\"),\n  bumpVerticalPos: /* @__PURE__ */ __name(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = common_default.getMax(this.data.stopy, this.verticalPos);\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ __name(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ __name(function() {\n    return { bounds: this.data, models: this.models };\n  }, \"getBounds\")\n};\nvar drawNote = /* @__PURE__ */ __name(async function(elem, noteModel) {\n  bounds.bumpVerticalPos(conf.boxMargin);\n  noteModel.height = conf.boxMargin;\n  noteModel.starty = bounds.getVerticalPos();\n  const rect = getNoteRect();\n  rect.x = noteModel.startx;\n  rect.y = noteModel.starty;\n  rect.width = noteModel.width || conf.width;\n  rect.class = \"note\";\n  const g = elem.append(\"g\");\n  const rectElem = svgDraw_default.drawRect(g, rect);\n  const textObj = getTextObj();\n  textObj.x = noteModel.startx;\n  textObj.y = noteModel.starty;\n  textObj.width = rect.width;\n  textObj.dy = \"1em\";\n  textObj.text = noteModel.message;\n  textObj.class = \"noteText\";\n  textObj.fontFamily = conf.noteFontFamily;\n  textObj.fontSize = conf.noteFontSize;\n  textObj.fontWeight = conf.noteFontWeight;\n  textObj.anchor = conf.noteAlign;\n  textObj.textMargin = conf.noteMargin;\n  textObj.valign = \"center\";\n  const textElem = hasKatex(textObj.text) ? await drawKatex(g, textObj) : drawText(g, textObj);\n  const textHeight = Math.round(\n    textElem.map((te) => (te._groups || te)[0][0].getBBox().height).reduce((acc, curr) => acc + curr)\n  );\n  rectElem.attr(\"height\", textHeight + 2 * conf.noteMargin);\n  noteModel.height += textHeight + 2 * conf.noteMargin;\n  bounds.bumpVerticalPos(textHeight + 2 * conf.noteMargin);\n  noteModel.stopy = noteModel.starty + textHeight + 2 * conf.noteMargin;\n  noteModel.stopx = noteModel.startx + rect.width;\n  bounds.insert(noteModel.startx, noteModel.starty, noteModel.stopx, noteModel.stopy);\n  bounds.models.addNote(noteModel);\n}, \"drawNote\");\nvar messageFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nvar noteFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.noteFontFamily,\n    fontSize: cnf.noteFontSize,\n    fontWeight: cnf.noteFontWeight\n  };\n}, \"noteFont\");\nvar actorFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.actorFontFamily,\n    fontSize: cnf.actorFontSize,\n    fontWeight: cnf.actorFontWeight\n  };\n}, \"actorFont\");\nasync function boundMessage(_diagram, msgModel) {\n  bounds.bumpVerticalPos(10);\n  const { startx, stopx, message } = msgModel;\n  const lines = common_default.splitBreaks(message).length;\n  const isKatexMsg = hasKatex(message);\n  const textDims = isKatexMsg ? await calculateMathMLDimensions(message, getConfig2()) : utils_default.calculateTextDimensions(message, messageFont(conf));\n  if (!isKatexMsg) {\n    const lineHeight = textDims.height / lines;\n    msgModel.height += lineHeight;\n    bounds.bumpVerticalPos(lineHeight);\n  }\n  let lineStartY;\n  let totalOffset = textDims.height - 10;\n  const textWidth = textDims.width;\n  if (startx === stopx) {\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    if (!conf.rightAngles) {\n      totalOffset += conf.boxMargin;\n      lineStartY = bounds.getVerticalPos() + totalOffset;\n    }\n    totalOffset += 30;\n    const dx = common_default.getMax(textWidth / 2, conf.width / 2);\n    bounds.insert(\n      startx - dx,\n      bounds.getVerticalPos() - 10 + totalOffset,\n      stopx + dx,\n      bounds.getVerticalPos() + 30 + totalOffset\n    );\n  } else {\n    totalOffset += conf.boxMargin;\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    bounds.insert(startx, lineStartY - 10, stopx, lineStartY);\n  }\n  bounds.bumpVerticalPos(totalOffset);\n  msgModel.height += totalOffset;\n  msgModel.stopy = msgModel.starty + msgModel.height;\n  bounds.insert(msgModel.fromBounds, msgModel.starty, msgModel.toBounds, msgModel.stopy);\n  return lineStartY;\n}\n__name(boundMessage, \"boundMessage\");\nvar drawMessage = /* @__PURE__ */ __name(async function(diagram2, msgModel, lineStartY, diagObj) {\n  const { startx, stopx, starty, message, type, sequenceIndex, sequenceVisible } = msgModel;\n  const textDims = utils_default.calculateTextDimensions(message, messageFont(conf));\n  const textObj = getTextObj();\n  textObj.x = startx;\n  textObj.y = starty + 10;\n  textObj.width = stopx - startx;\n  textObj.class = \"messageText\";\n  textObj.dy = \"1em\";\n  textObj.text = message;\n  textObj.fontFamily = conf.messageFontFamily;\n  textObj.fontSize = conf.messageFontSize;\n  textObj.fontWeight = conf.messageFontWeight;\n  textObj.anchor = conf.messageAlign;\n  textObj.valign = \"center\";\n  textObj.textMargin = conf.wrapPadding;\n  textObj.tspan = false;\n  if (hasKatex(textObj.text)) {\n    await drawKatex(diagram2, textObj, { startx, stopx, starty: lineStartY });\n  } else {\n    drawText(diagram2, textObj);\n  }\n  const textWidth = textDims.width;\n  let line;\n  if (startx === stopx) {\n    if (conf.rightAngles) {\n      line = diagram2.append(\"path\").attr(\n        \"d\",\n        `M  ${startx},${lineStartY} H ${startx + common_default.getMax(conf.width / 2, textWidth / 2)} V ${lineStartY + 25} H ${startx}`\n      );\n    } else {\n      line = diagram2.append(\"path\").attr(\n        \"d\",\n        \"M \" + startx + \",\" + lineStartY + \" C \" + (startx + 60) + \",\" + (lineStartY - 10) + \" \" + (startx + 60) + \",\" + (lineStartY + 30) + \" \" + startx + \",\" + (lineStartY + 20)\n      );\n    }\n  } else {\n    line = diagram2.append(\"line\");\n    line.attr(\"x1\", startx);\n    line.attr(\"y1\", lineStartY);\n    line.attr(\"x2\", stopx);\n    line.attr(\"y2\", lineStartY);\n  }\n  if (type === diagObj.db.LINETYPE.DOTTED || type === diagObj.db.LINETYPE.DOTTED_CROSS || type === diagObj.db.LINETYPE.DOTTED_POINT || type === diagObj.db.LINETYPE.DOTTED_OPEN || type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED) {\n    line.style(\"stroke-dasharray\", \"3, 3\");\n    line.attr(\"class\", \"messageLine1\");\n  } else {\n    line.attr(\"class\", \"messageLine0\");\n  }\n  let url = \"\";\n  if (conf.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  line.attr(\"stroke-width\", 2);\n  line.attr(\"stroke\", \"none\");\n  line.style(\"fill\", \"none\");\n  if (type === diagObj.db.LINETYPE.SOLID || type === diagObj.db.LINETYPE.DOTTED) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n  }\n  if (type === diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID || type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED) {\n    line.attr(\"marker-start\", \"url(\" + url + \"#arrowhead)\");\n    line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_POINT || type === diagObj.db.LINETYPE.DOTTED_POINT) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#filled-head)\");\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_CROSS || type === diagObj.db.LINETYPE.DOTTED_CROSS) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#crosshead)\");\n  }\n  if (sequenceVisible || conf.showSequenceNumbers) {\n    line.attr(\"marker-start\", \"url(\" + url + \"#sequencenumber)\");\n    diagram2.append(\"text\").attr(\"x\", startx).attr(\"y\", lineStartY + 4).attr(\"font-family\", \"sans-serif\").attr(\"font-size\", \"12px\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"sequenceNumber\").text(sequenceIndex);\n  }\n}, \"drawMessage\");\nvar addActorRenderingData = /* @__PURE__ */ __name(function(diagram2, actors, createdActors, actorKeys, verticalPos, messages, isFooter) {\n  let prevWidth = 0;\n  let prevMargin = 0;\n  let prevBox = void 0;\n  let maxHeight = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const box = actor.box;\n    if (prevBox && prevBox != box) {\n      if (!isFooter) {\n        bounds.models.addBox(prevBox);\n      }\n      prevMargin += conf.boxMargin + prevBox.margin;\n    }\n    if (box && box != prevBox) {\n      if (!isFooter) {\n        box.x = prevWidth + prevMargin;\n        box.y = verticalPos;\n      }\n      prevMargin += box.margin;\n    }\n    actor.width = actor.width || conf.width;\n    actor.height = common_default.getMax(actor.height || conf.height, conf.height);\n    actor.margin = actor.margin || conf.actorMargin;\n    maxHeight = common_default.getMax(maxHeight, actor.height);\n    if (createdActors.get(actor.name)) {\n      prevMargin += actor.width / 2;\n    }\n    actor.x = prevWidth + prevMargin;\n    actor.starty = bounds.getVerticalPos();\n    bounds.insert(actor.x, verticalPos, actor.x + actor.width, actor.height);\n    prevWidth += actor.width + prevMargin;\n    if (actor.box) {\n      actor.box.width = prevWidth + box.margin - actor.box.x;\n    }\n    prevMargin = actor.margin;\n    prevBox = actor.box;\n    bounds.models.addActor(actor);\n  }\n  if (prevBox && !isFooter) {\n    bounds.models.addBox(prevBox);\n  }\n  bounds.bumpVerticalPos(maxHeight);\n}, \"addActorRenderingData\");\nvar drawActors = /* @__PURE__ */ __name(async function(diagram2, actors, actorKeys, isFooter) {\n  if (!isFooter) {\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      await svgDraw_default.drawActor(diagram2, actor, conf, false);\n    }\n  } else {\n    let maxHeight = 0;\n    bounds.bumpVerticalPos(conf.boxMargin * 2);\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      if (!actor.stopy) {\n        actor.stopy = bounds.getVerticalPos();\n      }\n      const height = await svgDraw_default.drawActor(diagram2, actor, conf, true);\n      maxHeight = common_default.getMax(maxHeight, height);\n    }\n    bounds.bumpVerticalPos(maxHeight + conf.boxMargin);\n  }\n}, \"drawActors\");\nvar drawActorsPopup = /* @__PURE__ */ __name(function(diagram2, actors, actorKeys, doc) {\n  let maxHeight = 0;\n  let maxWidth = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const minMenuWidth = getRequiredPopupWidth(actor);\n    const menuDimensions = svgDraw_default.drawPopup(\n      diagram2,\n      actor,\n      minMenuWidth,\n      conf,\n      conf.forceMenus,\n      doc\n    );\n    if (menuDimensions.height > maxHeight) {\n      maxHeight = menuDimensions.height;\n    }\n    if (menuDimensions.width + actor.x > maxWidth) {\n      maxWidth = menuDimensions.width + actor.x;\n    }\n  }\n  return { maxHeight, maxWidth };\n}, \"drawActorsPopup\");\nvar setConf = /* @__PURE__ */ __name(function(cnf) {\n  assignWithDepth_default(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.actorFontFamily = conf.noteFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.actorFontSize = conf.noteFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.actorFontWeight = conf.noteFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar actorActivations = /* @__PURE__ */ __name(function(actor) {\n  return bounds.activations.filter(function(activation) {\n    return activation.actor === actor;\n  });\n}, \"actorActivations\");\nvar activationBounds = /* @__PURE__ */ __name(function(actor, actors) {\n  const actorObj = actors.get(actor);\n  const activations = actorActivations(actor);\n  const left = activations.reduce(\n    function(acc, activation) {\n      return common_default.getMin(acc, activation.startx);\n    },\n    actorObj.x + actorObj.width / 2 - 1\n  );\n  const right = activations.reduce(\n    function(acc, activation) {\n      return common_default.getMax(acc, activation.stopx);\n    },\n    actorObj.x + actorObj.width / 2 + 1\n  );\n  return [left, right];\n}, \"activationBounds\");\nfunction adjustLoopHeightForWrap(loopWidths, msg, preMargin, postMargin, addLoopFn) {\n  bounds.bumpVerticalPos(preMargin);\n  let heightAdjust = postMargin;\n  if (msg.id && msg.message && loopWidths[msg.id]) {\n    const loopWidth = loopWidths[msg.id].width;\n    const textConf = messageFont(conf);\n    msg.message = utils_default.wrapLabel(`[${msg.message}]`, loopWidth - 2 * conf.wrapPadding, textConf);\n    msg.width = loopWidth;\n    msg.wrap = true;\n    const textDims = utils_default.calculateTextDimensions(msg.message, textConf);\n    const totalOffset = common_default.getMax(textDims.height, conf.labelBoxHeight);\n    heightAdjust = postMargin + totalOffset;\n    log.debug(`${totalOffset} - ${msg.message}`);\n  }\n  addLoopFn(msg);\n  bounds.bumpVerticalPos(heightAdjust);\n}\n__name(adjustLoopHeightForWrap, \"adjustLoopHeightForWrap\");\nfunction adjustCreatedDestroyedData(msg, msgModel, lineStartY, index, actors, createdActors, destroyedActors) {\n  function receiverAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.from).x) {\n      bounds.insert(\n        msgModel.stopx - adjustment,\n        msgModel.starty,\n        msgModel.startx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.startx,\n        msgModel.starty,\n        msgModel.stopx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx - adjustment;\n    }\n  }\n  __name(receiverAdjustment, \"receiverAdjustment\");\n  function senderAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.to).x) {\n      bounds.insert(\n        msgModel.startx - adjustment,\n        msgModel.starty,\n        msgModel.stopx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.stopx,\n        msgModel.starty,\n        msgModel.startx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx - adjustment;\n    }\n  }\n  __name(senderAdjustment, \"senderAdjustment\");\n  if (createdActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n    receiverAdjustment(actor, adjustment);\n    actor.starty = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  } else if (destroyedActors.get(msg.from) == index) {\n    const actor = actors.get(msg.from);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 : actor.width / 2;\n      senderAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  } else if (destroyedActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n      receiverAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n}\n__name(adjustCreatedDestroyedData, \"adjustCreatedDestroyedData\");\nvar draw = /* @__PURE__ */ __name(async function(_text, id, _version, diagObj) {\n  const { securityLevel, sequence } = getConfig2();\n  conf = sequence;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  bounds.init();\n  log.debug(diagObj.db);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n  const actors = diagObj.db.getActors();\n  const createdActors = diagObj.db.getCreatedActors();\n  const destroyedActors = diagObj.db.getDestroyedActors();\n  const boxes = diagObj.db.getBoxes();\n  let actorKeys = diagObj.db.getActorKeys();\n  const messages = diagObj.db.getMessages();\n  const title = diagObj.db.getDiagramTitle();\n  const hasBoxes = diagObj.db.hasAtLeastOneBox();\n  const hasBoxTitles = diagObj.db.hasAtLeastOneBoxWithTitle();\n  const maxMessageWidthPerActor = await getMaxMessageWidthPerActor(actors, messages, diagObj);\n  conf.height = await calculateActorMargins(actors, maxMessageWidthPerActor, boxes);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n    if (hasBoxTitles) {\n      bounds.bumpVerticalPos(boxes[0].textMaxHeight);\n    }\n  }\n  if (conf.hideUnusedParticipants === true) {\n    const newActors = /* @__PURE__ */ new Set();\n    messages.forEach((message) => {\n      newActors.add(message.from);\n      newActors.add(message.to);\n    });\n    actorKeys = actorKeys.filter((actorKey) => newActors.has(actorKey));\n  }\n  addActorRenderingData(diagram2, actors, createdActors, actorKeys, 0, messages, false);\n  const loopWidths = await calculateLoopBounds(messages, actors, maxMessageWidthPerActor, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  svgDraw_default.insertSequenceNumber(diagram2);\n  function activeEnd(msg, verticalPos) {\n    const activationData = bounds.endActivation(msg);\n    if (activationData.starty + 18 > verticalPos) {\n      activationData.starty = verticalPos - 6;\n      verticalPos += 12;\n    }\n    svgDraw_default.drawActivation(\n      diagram2,\n      activationData,\n      verticalPos,\n      conf,\n      actorActivations(msg.from).length\n    );\n    bounds.insert(activationData.startx, verticalPos - 10, activationData.stopx, verticalPos);\n  }\n  __name(activeEnd, \"activeEnd\");\n  let sequenceIndex = 1;\n  let sequenceIndexStep = 1;\n  const messagesToDraw = [];\n  const backgrounds = [];\n  let index = 0;\n  for (const msg of messages) {\n    let loopModel, noteModel, msgModel;\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.NOTE:\n        bounds.resetVerticalPos();\n        noteModel = msg.noteModel;\n        await drawNote(diagram2, noteModel);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        bounds.newActivation(msg, diagram2, actors);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        activeEnd(msg, bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.LOOP_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"loop\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.RECT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin,\n          (message) => bounds.newLoop(void 0, message.message)\n        );\n        break;\n      case diagObj.db.LINETYPE.RECT_END:\n        loopModel = bounds.endLoop();\n        backgrounds.push(loopModel);\n        bounds.models.addLoop(loopModel);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.OPT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.OPT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"opt\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.ALT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"alt\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        bounds.saveVerticalPos();\n        break;\n      case diagObj.db.LINETYPE.PAR_AND:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.PAR_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"par\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.AUTONUMBER:\n        sequenceIndex = msg.message.start || sequenceIndex;\n        sequenceIndexStep = msg.message.step || sequenceIndexStep;\n        if (msg.message.visible) {\n          diagObj.db.enableSequenceNumbers();\n        } else {\n          diagObj.db.disableSequenceNumbers();\n        }\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"critical\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.BREAK_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.BREAK_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"break\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      default:\n        try {\n          msgModel = msg.msgModel;\n          msgModel.starty = bounds.getVerticalPos();\n          msgModel.sequenceIndex = sequenceIndex;\n          msgModel.sequenceVisible = diagObj.db.showSequenceNumbers();\n          const lineStartY = await boundMessage(diagram2, msgModel);\n          adjustCreatedDestroyedData(\n            msg,\n            msgModel,\n            lineStartY,\n            index,\n            actors,\n            createdActors,\n            destroyedActors\n          );\n          messagesToDraw.push({ messageModel: msgModel, lineStartY });\n          bounds.models.addMessage(msgModel);\n        } catch (e) {\n          log.error(\"error while drawing message\", e);\n        }\n    }\n    if ([\n      diagObj.db.LINETYPE.SOLID_OPEN,\n      diagObj.db.LINETYPE.DOTTED_OPEN,\n      diagObj.db.LINETYPE.SOLID,\n      diagObj.db.LINETYPE.DOTTED,\n      diagObj.db.LINETYPE.SOLID_CROSS,\n      diagObj.db.LINETYPE.DOTTED_CROSS,\n      diagObj.db.LINETYPE.SOLID_POINT,\n      diagObj.db.LINETYPE.DOTTED_POINT,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n    ].includes(msg.type)) {\n      sequenceIndex = sequenceIndex + sequenceIndexStep;\n    }\n    index++;\n  }\n  log.debug(\"createdActors\", createdActors);\n  log.debug(\"destroyedActors\", destroyedActors);\n  await drawActors(diagram2, actors, actorKeys, false);\n  for (const e of messagesToDraw) {\n    await drawMessage(diagram2, e.messageModel, e.lineStartY, diagObj);\n  }\n  if (conf.mirrorActors) {\n    await drawActors(diagram2, actors, actorKeys, true);\n  }\n  backgrounds.forEach((e) => svgDraw_default.drawBackgroundRect(diagram2, e));\n  fixLifeLineHeights(diagram2, actors, actorKeys, conf);\n  for (const box2 of bounds.models.boxes) {\n    box2.height = bounds.getVerticalPos() - box2.y;\n    bounds.insert(box2.x, box2.y, box2.x + box2.width, box2.height);\n    box2.startx = box2.x;\n    box2.starty = box2.y;\n    box2.stopx = box2.startx + box2.width;\n    box2.stopy = box2.starty + box2.height;\n    box2.stroke = \"rgb(0,0,0, 0.5)\";\n    svgDraw_default.drawBox(diagram2, box2, conf);\n  }\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n  }\n  const requiredBoxSize = drawActorsPopup(diagram2, actors, actorKeys, doc);\n  const { bounds: box } = bounds.getBounds();\n  if (box.startx === void 0) {\n    box.startx = 0;\n  }\n  if (box.starty === void 0) {\n    box.starty = 0;\n  }\n  if (box.stopx === void 0) {\n    box.stopx = 0;\n  }\n  if (box.stopy === void 0) {\n    box.stopy = 0;\n  }\n  let boxHeight = box.stopy - box.starty;\n  if (boxHeight < requiredBoxSize.maxHeight) {\n    boxHeight = requiredBoxSize.maxHeight;\n  }\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  if (conf.mirrorActors) {\n    height = height - conf.boxMargin + conf.bottomMarginAdj;\n  }\n  let boxWidth = box.stopx - box.startx;\n  if (boxWidth < requiredBoxSize.maxWidth) {\n    boxWidth = requiredBoxSize.maxWidth;\n  }\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", (box.stopx - box.startx) / 2 - 2 * conf.diagramMarginX).attr(\"y\", -25);\n  }\n  configureSvgSize(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title ? 40 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  log.debug(`models:`, bounds.models);\n}, \"draw\");\nasync function getMaxMessageWidthPerActor(actors, messages, diagObj) {\n  const maxMessageWidthPerActor = {};\n  for (const msg of messages) {\n    if (actors.get(msg.to) && actors.get(msg.from)) {\n      const actor = actors.get(msg.to);\n      if (msg.placement === diagObj.db.PLACEMENT.LEFTOF && !actor.prevActor) {\n        continue;\n      }\n      if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF && !actor.nextActor) {\n        continue;\n      }\n      const isNote = msg.placement !== void 0;\n      const isMessage = !isNote;\n      const textFont = isNote ? noteFont(conf) : messageFont(conf);\n      const wrappedMessage = msg.wrap ? utils_default.wrapLabel(msg.message, conf.width - 2 * conf.wrapPadding, textFont) : msg.message;\n      const messageDimensions = hasKatex(wrappedMessage) ? await calculateMathMLDimensions(msg.message, getConfig2()) : utils_default.calculateTextDimensions(wrappedMessage, textFont);\n      const messageWidth = messageDimensions.width + 2 * conf.wrapPadding;\n      if (isMessage && msg.from === actor.nextActor) {\n        maxMessageWidthPerActor[msg.to] = common_default.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === actor.prevActor) {\n        maxMessageWidthPerActor[msg.from] = common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === msg.to) {\n        maxMessageWidthPerActor[msg.from] = common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth / 2\n        );\n        maxMessageWidthPerActor[msg.to] = common_default.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth / 2\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n        maxMessageWidthPerActor[msg.from] = common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n        maxMessageWidthPerActor[actor.prevActor] = common_default.getMax(\n          maxMessageWidthPerActor[actor.prevActor] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.OVER) {\n        if (actor.prevActor) {\n          maxMessageWidthPerActor[actor.prevActor] = common_default.getMax(\n            maxMessageWidthPerActor[actor.prevActor] || 0,\n            messageWidth / 2\n          );\n        }\n        if (actor.nextActor) {\n          maxMessageWidthPerActor[msg.from] = common_default.getMax(\n            maxMessageWidthPerActor[msg.from] || 0,\n            messageWidth / 2\n          );\n        }\n      }\n    }\n  }\n  log.debug(\"maxMessageWidthPerActor:\", maxMessageWidthPerActor);\n  return maxMessageWidthPerActor;\n}\n__name(getMaxMessageWidthPerActor, \"getMaxMessageWidthPerActor\");\nvar getRequiredPopupWidth = /* @__PURE__ */ __name(function(actor) {\n  let requiredPopupWidth = 0;\n  const textFont = actorFont(conf);\n  for (const key in actor.links) {\n    const labelDimensions = utils_default.calculateTextDimensions(key, textFont);\n    const labelWidth = labelDimensions.width + 2 * conf.wrapPadding + 2 * conf.boxMargin;\n    if (requiredPopupWidth < labelWidth) {\n      requiredPopupWidth = labelWidth;\n    }\n  }\n  return requiredPopupWidth;\n}, \"getRequiredPopupWidth\");\nasync function calculateActorMargins(actors, actorToMessageWidth, boxes) {\n  let maxHeight = 0;\n  for (const prop of actors.keys()) {\n    const actor = actors.get(prop);\n    if (actor.wrap) {\n      actor.description = utils_default.wrapLabel(\n        actor.description,\n        conf.width - 2 * conf.wrapPadding,\n        actorFont(conf)\n      );\n    }\n    const actDims = hasKatex(actor.description) ? await calculateMathMLDimensions(actor.description, getConfig2()) : utils_default.calculateTextDimensions(actor.description, actorFont(conf));\n    actor.width = actor.wrap ? conf.width : common_default.getMax(conf.width, actDims.width + 2 * conf.wrapPadding);\n    actor.height = actor.wrap ? common_default.getMax(actDims.height, conf.height) : conf.height;\n    maxHeight = common_default.getMax(maxHeight, actor.height);\n  }\n  for (const actorKey in actorToMessageWidth) {\n    const actor = actors.get(actorKey);\n    if (!actor) {\n      continue;\n    }\n    const nextActor = actors.get(actor.nextActor);\n    if (!nextActor) {\n      const messageWidth2 = actorToMessageWidth[actorKey];\n      const actorWidth2 = messageWidth2 + conf.actorMargin - actor.width / 2;\n      actor.margin = common_default.getMax(actorWidth2, conf.actorMargin);\n      continue;\n    }\n    const messageWidth = actorToMessageWidth[actorKey];\n    const actorWidth = messageWidth + conf.actorMargin - actor.width / 2 - nextActor.width / 2;\n    actor.margin = common_default.getMax(actorWidth, conf.actorMargin);\n  }\n  let maxBoxHeight = 0;\n  boxes.forEach((box) => {\n    const textFont = messageFont(conf);\n    let totalWidth = box.actorKeys.reduce((total, aKey) => {\n      return total += actors.get(aKey).width + (actors.get(aKey).margin || 0);\n    }, 0);\n    totalWidth -= 2 * conf.boxTextMargin;\n    if (box.wrap) {\n      box.name = utils_default.wrapLabel(box.name, totalWidth - 2 * conf.wrapPadding, textFont);\n    }\n    const boxMsgDimensions = utils_default.calculateTextDimensions(box.name, textFont);\n    maxBoxHeight = common_default.getMax(boxMsgDimensions.height, maxBoxHeight);\n    const minWidth = common_default.getMax(totalWidth, boxMsgDimensions.width + 2 * conf.wrapPadding);\n    box.margin = conf.boxTextMargin;\n    if (totalWidth < minWidth) {\n      const missing = (minWidth - totalWidth) / 2;\n      box.margin += missing;\n    }\n  });\n  boxes.forEach((box) => box.textMaxHeight = maxBoxHeight);\n  return common_default.getMax(maxHeight, conf.height);\n}\n__name(calculateActorMargins, \"calculateActorMargins\");\nvar buildNoteModel = /* @__PURE__ */ __name(async function(msg, actors, diagObj) {\n  const fromActor = actors.get(msg.from);\n  const toActor = actors.get(msg.to);\n  const startx = fromActor.x;\n  const stopx = toActor.x;\n  const shouldWrap = msg.wrap && msg.message;\n  let textDimensions = hasKatex(msg.message) ? await calculateMathMLDimensions(msg.message, getConfig2()) : utils_default.calculateTextDimensions(\n    shouldWrap ? utils_default.wrapLabel(msg.message, conf.width, noteFont(conf)) : msg.message,\n    noteFont(conf)\n  );\n  const noteModel = {\n    width: shouldWrap ? conf.width : common_default.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin),\n    height: 0,\n    startx: fromActor.x,\n    stopx: 0,\n    starty: 0,\n    stopy: 0,\n    message: msg.message\n  };\n  if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n    noteModel.width = shouldWrap ? common_default.getMax(conf.width, textDimensions.width) : common_default.getMax(\n      fromActor.width / 2 + toActor.width / 2,\n      textDimensions.width + 2 * conf.noteMargin\n    );\n    noteModel.startx = startx + (fromActor.width + conf.actorMargin) / 2;\n  } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n    noteModel.width = shouldWrap ? common_default.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin) : common_default.getMax(\n      fromActor.width / 2 + toActor.width / 2,\n      textDimensions.width + 2 * conf.noteMargin\n    );\n    noteModel.startx = startx - noteModel.width + (fromActor.width - conf.actorMargin) / 2;\n  } else if (msg.to === msg.from) {\n    textDimensions = utils_default.calculateTextDimensions(\n      shouldWrap ? utils_default.wrapLabel(msg.message, common_default.getMax(conf.width, fromActor.width), noteFont(conf)) : msg.message,\n      noteFont(conf)\n    );\n    noteModel.width = shouldWrap ? common_default.getMax(conf.width, fromActor.width) : common_default.getMax(fromActor.width, conf.width, textDimensions.width + 2 * conf.noteMargin);\n    noteModel.startx = startx + (fromActor.width - noteModel.width) / 2;\n  } else {\n    noteModel.width = Math.abs(startx + fromActor.width / 2 - (stopx + toActor.width / 2)) + conf.actorMargin;\n    noteModel.startx = startx < stopx ? startx + fromActor.width / 2 - conf.actorMargin / 2 : stopx + toActor.width / 2 - conf.actorMargin / 2;\n  }\n  if (shouldWrap) {\n    noteModel.message = utils_default.wrapLabel(\n      msg.message,\n      noteModel.width - 2 * conf.wrapPadding,\n      noteFont(conf)\n    );\n  }\n  log.debug(\n    `NM:[${noteModel.startx},${noteModel.stopx},${noteModel.starty},${noteModel.stopy}:${noteModel.width},${noteModel.height}=${msg.message}]`\n  );\n  return noteModel;\n}, \"buildNoteModel\");\nvar buildMessageModel = /* @__PURE__ */ __name(function(msg, actors, diagObj) {\n  if (![\n    diagObj.db.LINETYPE.SOLID_OPEN,\n    diagObj.db.LINETYPE.DOTTED_OPEN,\n    diagObj.db.LINETYPE.SOLID,\n    diagObj.db.LINETYPE.DOTTED,\n    diagObj.db.LINETYPE.SOLID_CROSS,\n    diagObj.db.LINETYPE.DOTTED_CROSS,\n    diagObj.db.LINETYPE.SOLID_POINT,\n    diagObj.db.LINETYPE.DOTTED_POINT,\n    diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n    diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ].includes(msg.type)) {\n    return {};\n  }\n  const [fromLeft, fromRight] = activationBounds(msg.from, actors);\n  const [toLeft, toRight] = activationBounds(msg.to, actors);\n  const isArrowToRight = fromLeft <= toLeft;\n  let startx = isArrowToRight ? fromRight : fromLeft;\n  let stopx = isArrowToRight ? toLeft : toRight;\n  const isArrowToActivation = Math.abs(toLeft - toRight) > 2;\n  const adjustValue = /* @__PURE__ */ __name((value) => {\n    return isArrowToRight ? -value : value;\n  }, \"adjustValue\");\n  if (msg.from === msg.to) {\n    stopx = startx;\n  } else {\n    if (msg.activate && !isArrowToActivation) {\n      stopx += adjustValue(conf.activationWidth / 2 - 1);\n    }\n    if (![diagObj.db.LINETYPE.SOLID_OPEN, diagObj.db.LINETYPE.DOTTED_OPEN].includes(msg.type)) {\n      stopx += adjustValue(3);\n    }\n    if ([diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID, diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(\n      msg.type\n    )) {\n      startx -= adjustValue(3);\n    }\n  }\n  const allBounds = [fromLeft, fromRight, toLeft, toRight];\n  const boundedWidth = Math.abs(startx - stopx);\n  if (msg.wrap && msg.message) {\n    msg.message = utils_default.wrapLabel(\n      msg.message,\n      common_default.getMax(boundedWidth + 2 * conf.wrapPadding, conf.width),\n      messageFont(conf)\n    );\n  }\n  const msgDims = utils_default.calculateTextDimensions(msg.message, messageFont(conf));\n  return {\n    width: common_default.getMax(\n      msg.wrap ? 0 : msgDims.width + 2 * conf.wrapPadding,\n      boundedWidth + 2 * conf.wrapPadding,\n      conf.width\n    ),\n    height: 0,\n    startx,\n    stopx,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n    type: msg.type,\n    wrap: msg.wrap,\n    fromBounds: Math.min.apply(null, allBounds),\n    toBounds: Math.max.apply(null, allBounds)\n  };\n}, \"buildMessageModel\");\nvar calculateLoopBounds = /* @__PURE__ */ __name(async function(messages, actors, _maxWidthPerActor, diagObj) {\n  const loops = {};\n  const stack = [];\n  let current, noteModel, msgModel;\n  for (const msg of messages) {\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.LOOP_START:\n      case diagObj.db.LINETYPE.ALT_START:\n      case diagObj.db.LINETYPE.OPT_START:\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n      case diagObj.db.LINETYPE.CRITICAL_START:\n      case diagObj.db.LINETYPE.BREAK_START:\n        stack.push({\n          id: msg.id,\n          msg: msg.message,\n          from: Number.MAX_SAFE_INTEGER,\n          to: Number.MIN_SAFE_INTEGER,\n          width: 0\n        });\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n      case diagObj.db.LINETYPE.PAR_AND:\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        if (msg.message) {\n          current = stack.pop();\n          loops[current.id] = current;\n          loops[msg.id] = current;\n          stack.push(current);\n        }\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n      case diagObj.db.LINETYPE.ALT_END:\n      case diagObj.db.LINETYPE.OPT_END:\n      case diagObj.db.LINETYPE.PAR_END:\n      case diagObj.db.LINETYPE.CRITICAL_END:\n      case diagObj.db.LINETYPE.BREAK_END:\n        current = stack.pop();\n        loops[current.id] = current;\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        {\n          const actorRect = actors.get(msg.from ? msg.from : msg.to.actor);\n          const stackedSize = actorActivations(msg.from ? msg.from : msg.to.actor).length;\n          const x = actorRect.x + actorRect.width / 2 + (stackedSize - 1) * conf.activationWidth / 2;\n          const toAdd = {\n            startx: x,\n            stopx: x + conf.activationWidth,\n            actor: msg.from,\n            enabled: true\n          };\n          bounds.activations.push(toAdd);\n        }\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        {\n          const lastActorActivationIdx = bounds.activations.map((a) => a.actor).lastIndexOf(msg.from);\n          bounds.activations.splice(lastActorActivationIdx, 1).splice(0, 1);\n        }\n        break;\n    }\n    const isNote = msg.placement !== void 0;\n    if (isNote) {\n      noteModel = await buildNoteModel(msg, actors, diagObj);\n      msg.noteModel = noteModel;\n      stack.forEach((stk) => {\n        current = stk;\n        current.from = common_default.getMin(current.from, noteModel.startx);\n        current.to = common_default.getMax(current.to, noteModel.startx + noteModel.width);\n        current.width = common_default.getMax(current.width, Math.abs(current.from - current.to)) - conf.labelBoxWidth;\n      });\n    } else {\n      msgModel = buildMessageModel(msg, actors, diagObj);\n      msg.msgModel = msgModel;\n      if (msgModel.startx && msgModel.stopx && stack.length > 0) {\n        stack.forEach((stk) => {\n          current = stk;\n          if (msgModel.startx === msgModel.stopx) {\n            const from = actors.get(msg.from);\n            const to = actors.get(msg.to);\n            current.from = common_default.getMin(\n              from.x - msgModel.width / 2,\n              from.x - from.width / 2,\n              current.from\n            );\n            current.to = common_default.getMax(\n              to.x + msgModel.width / 2,\n              to.x + from.width / 2,\n              current.to\n            );\n            current.width = common_default.getMax(current.width, Math.abs(current.to - current.from)) - conf.labelBoxWidth;\n          } else {\n            current.from = common_default.getMin(msgModel.startx, current.from);\n            current.to = common_default.getMax(msgModel.stopx, current.to);\n            current.width = common_default.getMax(current.width, msgModel.width) - conf.labelBoxWidth;\n          }\n        });\n      }\n    }\n  }\n  bounds.activations = [];\n  log.debug(\"Loop type widths:\", loops);\n  return loops;\n}, \"calculateLoopBounds\");\nvar sequenceRenderer_default = {\n  bounds,\n  drawActors,\n  drawActorsPopup,\n  setConf,\n  draw\n};\n\n// src/diagrams/sequence/sequenceDiagram.ts\nvar diagram = {\n  parser: sequenceDiagram_default,\n  get db() {\n    return new SequenceDB();\n  },\n  renderer: sequenceRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.sequence) {\n      cnf.sequence = {};\n    }\n    if (cnf.wrap) {\n      cnf.sequence.wrap = cnf.wrap;\n      setConfig({ sequence: { wrap: cnf.wrap } });\n    }\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAqlDA,0BAA4B;AA9iD5B,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,EAAG;AACrD,WAAO;AAAA,EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG;AACnsC,MAAI,UAAU;AAAA,IACZ,OAAuB,OAAO,SAAS,QAAQ;AAAA,IAC/C,GAAG,OAAO;AAAA,IACV,IAAI,CAAC;AAAA,IACL,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,MAAM,GAAG,YAAY,GAAG,QAAQ,GAAG,aAAa,GAAG,eAAe,IAAI,YAAY,IAAI,yBAAyB,IAAI,UAAU,IAAI,OAAO,IAAI,cAAc,IAAI,OAAO,IAAI,UAAU,IAAI,cAAc,IAAI,OAAO,IAAI,OAAO,IAAI,YAAY,IAAI,SAAS,IAAI,cAAc,IAAI,kBAAkB,IAAI,mBAAmB,IAAI,kBAAkB,IAAI,wBAAwB,IAAI,qBAAqB,IAAI,SAAS,IAAI,gBAAgB,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,QAAQ,IAAI,QAAQ,IAAI,OAAO,IAAI,OAAO,IAAI,iBAAiB,IAAI,OAAO,IAAI,gBAAgB,IAAI,YAAY,IAAI,YAAY,IAAI,mBAAmB,IAAI,SAAS,IAAI,UAAU,IAAI,OAAO,IAAI,QAAQ,IAAI,eAAe,IAAI,MAAM,IAAI,qBAAqB,IAAI,WAAW,IAAI,QAAQ,IAAI,aAAa,IAAI,SAAS,IAAI,QAAQ,IAAI,cAAc,IAAI,SAAS,IAAI,QAAQ,IAAI,cAAc,IAAI,WAAW,IAAI,aAAa,IAAI,KAAK,IAAI,WAAW,IAAI,YAAY,IAAI,cAAc,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,IAAI,oBAAoB,IAAI,qBAAqB,IAAI,eAAe,IAAI,6BAA6B,IAAI,gBAAgB,IAAI,8BAA8B,IAAI,eAAe,IAAI,gBAAgB,IAAI,eAAe,IAAI,gBAAgB,IAAI,OAAO,IAAI,WAAW,GAAG,QAAQ,EAAE;AAAA,IAC91C,YAAY,EAAE,GAAG,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,MAAM,IAAI,UAAU,IAAI,OAAO,IAAI,cAAc,IAAI,OAAO,IAAI,cAAc,IAAI,OAAO,IAAI,OAAO,IAAI,YAAY,IAAI,cAAc,IAAI,SAAS,IAAI,gBAAgB,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,QAAQ,IAAI,QAAQ,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,YAAY,IAAI,YAAY,IAAI,SAAS,IAAI,UAAU,IAAI,OAAO,IAAI,QAAQ,IAAI,eAAe,IAAI,MAAM,IAAI,qBAAqB,IAAI,WAAW,IAAI,QAAQ,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,cAAc,IAAI,WAAW,IAAI,KAAK,IAAI,WAAW,IAAI,YAAY,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,IAAI,oBAAoB,IAAI,qBAAqB,IAAI,eAAe,IAAI,6BAA6B,IAAI,gBAAgB,IAAI,8BAA8B,IAAI,eAAe,IAAI,gBAAgB,IAAI,eAAe,IAAI,gBAAgB,IAAI,MAAM;AAAA,IAC36B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAA,IACnrB,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,aAAG,MAAM,GAAG,EAAE,CAAC;AACf,iBAAO,GAAG,EAAE;AACZ;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,CAAC;AACV;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;AACtB,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,CAAC;AACV;AAAA,QACF,KAAK;AACH,aAAG,EAAE,EAAE,OAAO;AACd,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,YAAY,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;AAC7E,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,UAAU,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC;AACvD,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,iBAAiB,eAAe,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,mBAAmB,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,iBAAiB,MAAM,YAAY,GAAG,SAAS,WAAW;AACtK;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,iBAAiB,eAAe,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,mBAAmB,GAAG,iBAAiB,MAAM,YAAY,GAAG,SAAS,WAAW;AACrJ;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,iBAAiB,iBAAiB,OAAO,YAAY,GAAG,SAAS,WAAW;AAC7F;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,iBAAiB,iBAAiB,MAAM,YAAY,GAAG,SAAS,WAAW;AAC5F;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,eAAe,YAAY,GAAG,SAAS,cAAc,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM;AAC9F;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,aAAa,YAAY,GAAG,SAAS,YAAY,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM;AAC1F;AAAA,QACF,KAAK;AACH,aAAG,gBAAgB,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE,EAAE,UAAU,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,aAAG,gBAAgB,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE,EAAE,UAAU,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,YAAY,KAAK,CAAC;AACrB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,kBAAkB,KAAK,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,aAAa,UAAU,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,WAAW,CAAC;AACnH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,WAAW,UAAU,GAAG,KAAK,CAAC,GAAG,YAAY,GAAG,SAAS,SAAS,CAAC;AAC3F,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,aAAa,OAAO,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,WAAW,CAAC;AAChH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,WAAW,OAAO,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,SAAS,CAAC;AACzG,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,YAAY,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAU,CAAC;AAChH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,UAAU,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,QAAQ,CAAC;AACzG,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,YAAY,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAU,CAAC;AAChH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,UAAU,YAAY,GAAG,SAAS,QAAQ,CAAC;AACnE,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,YAAY,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAU,CAAC;AAChH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,UAAU,YAAY,GAAG,SAAS,QAAQ,CAAC;AACnE,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,YAAY,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,eAAe,CAAC;AACrH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,UAAU,YAAY,GAAG,SAAS,QAAQ,CAAC;AACnE,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,iBAAiB,cAAc,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,eAAe,CAAC;AAC/H,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,eAAe,YAAY,GAAG,SAAS,aAAa,CAAC;AAC7E,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,QAAQ,EAAE,MAAM,cAAc,WAAW,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,YAAY,CAAC;AACtH,aAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,YAAY,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,UAAU,CAAC;AAC7G,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,UAAU,YAAY,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,gBAAgB,GAAG,GAAG,EAAE,CAAC,CAAC;AACzI;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,OAAO,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,QAAQ,GAAG,GAAG,EAAE,CAAC,CAAC;AAC3H;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,QAAQ,SAAS,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC,GAAG,YAAY,GAAG,SAAS,SAAS,GAAG,GAAG,EAAE,CAAC,CAAC;AAC7H;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,aAAG,KAAK,CAAC,EAAE,cAAc,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC;AACnD,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,aAAG,KAAK,CAAC,EAAE,cAAc,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC;AACnD,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,EAAE,OAAO;AAClB,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,WAAW,WAAW,GAAG,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,EAAE,CAAC;AACvG;AAAA,QACF,KAAK;AACH,aAAG,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC;AACzD,aAAG,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE;AAC9B,aAAG,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE;AAC9B,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,WAAW,WAAW,GAAG,UAAU,MAAM,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,EAAE,EAAE,CAAC;AACpH;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,YAAY,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,EAAE,CAAC;AACjF;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,YAAY,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,EAAE,CAAC;AACjF;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,iBAAiB,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,EAAE,CAAC;AACtF;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,cAAc,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,GAAG,EAAE,EAAE,CAAC;AACnF;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,UAAU;AACtB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,UAAU;AACtB;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AAAA,YACP,GAAG,KAAK,CAAC;AAAA,YACT,GAAG,KAAK,CAAC;AAAA,YACT,EAAE,MAAM,cAAc,MAAM,GAAG,KAAK,CAAC,EAAE,OAAO,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,YAAY,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,GAAG,UAAU,KAAK;AAAA,YACxH,EAAE,MAAM,eAAe,YAAY,GAAG,SAAS,cAAc,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM;AAAA,UACvF;AACA;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AAAA,YACP,GAAG,KAAK,CAAC;AAAA,YACT,GAAG,KAAK,CAAC;AAAA,YACT,EAAE,MAAM,cAAc,MAAM,GAAG,KAAK,CAAC,EAAE,OAAO,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,YAAY,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE;AAAA,YACxG,EAAE,MAAM,aAAa,YAAY,GAAG,SAAS,YAAY,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM;AAAA,UACnF;AACA;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,cAAc,MAAM,GAAG,KAAK,CAAC,EAAE,OAAO,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,YAAY,GAAG,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,CAAC;AAC3I;AAAA,QACF,KAAK;AACH,eAAK,IAAI,EAAE,MAAM,kBAAkB,OAAO,GAAG,EAAE,EAAE;AACjD;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,SAAS;AACrB;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,aAAa,GAAG,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACnD;AAAA,MACJ;AAAA,IACF,GAAG,WAAW;AAAA,IACd,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;AAAA,IAClhN,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE;AAAA,IACrQ,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;AAAA,MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF,GAAG,YAAY;AAAA,IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAClD,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;AAAA,MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACrB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAI;AAAA,UACf;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACrJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACtB,MAAM,OAAO;AAAA,YACb,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAM,OAAO;AAAA,YACb,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACpG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACjB,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;AACf,kBAAI,aAAa,GAAG;AAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,uBAAS;AACT,+BAAiB;AAAA,YACnB;AACA;AAAA,UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YACzC;AACA,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;AAAA,gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACnC;AAAA,YACF;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAClC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACF,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF,GAAG,YAAY;AAAA;AAAA,MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACZ,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;AAAA,QAC3B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACT,GAAG,UAAU;AAAA;AAAA,MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;AAAA,QACd,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;AAAA,QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;AAAA,QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;AAAA,QAC1L;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;AAAA,MACT,GAAG,MAAM;AAAA;AAAA,MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAChO,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,GAAG,QAAQ;AAAA;AAAA,MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAChC,GAAG,MAAM;AAAA;AAAA,MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAAG,WAAW;AAAA;AAAA,MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MACjF,GAAG,eAAe;AAAA;AAAA,MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACjD,GAAG,cAAc;AAAA;AAAA,MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACN,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC3B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;AAAA,QACzB;AACA,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QAC/I;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO;AACT,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,YAAY;AAAA;AAAA,MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;AAAA,cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACtH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,GAAG,MAAM;AAAA;AAAA,MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACL,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA;AAAA,MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;AAAA,MACpC,GAAG,OAAO;AAAA;AAAA,MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACpC;AAAA,MACF,GAAG,eAAe;AAAA;AAAA,MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;AAAA,MACtB,GAAG,WAAW;AAAA;AAAA,MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;AAAA,MAC7B,GAAG,gBAAgB;AAAA,MACnB,SAAS,EAAE,oBAAoB,KAAK;AAAA,MACpC,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AACrG,YAAI,UAAU;AACd,gBAAQ,2BAA2B;AAAA,UACjC,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,IAAI;AACf,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,IAAI;AACf,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,IAAI;AACf,mBAAO;AACP;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,KAAK;AAC7B,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,SAAS;AACd,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,IAAI;AACf,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,IAAI;AACf,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,IAAI,OAAO,KAAK;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,QACJ;AAAA,MACF,GAAG,WAAW;AAAA,MACd,OAAO,CAAC,eAAe,aAAa,qBAAqB,iBAAiB,uBAAuB,uBAAuB,0BAA0B,eAAe,uBAAuB,iBAAiB,kBAAkB,mBAAmB,uFAAuF,cAAc,cAAc,gBAAgB,gBAAgB,eAAe,eAAe,gBAAgB,eAAe,oBAAoB,eAAe,oBAAoB,kBAAkB,iBAAiB,sCAAsC,eAAe,mBAAmB,oBAAoB,iBAAiB,gBAAgB,sBAAsB,mBAAmB,gBAAgB,gBAAgB,oBAAoB,sBAAsB,yBAAyB,0BAA0B,yBAAyB,yBAAyB,yBAAyB,yBAAyB,0BAA0B,cAAc,gBAAgB,2BAA2B,sBAAsB,eAAe,WAAW,WAAW,2EAA2E,aAAa,eAAe,cAAc,gBAAgB,YAAY,aAAa,cAAc,eAAe,eAAe,gBAAgB,mCAAmC,YAAY,WAAW,WAAW,SAAS;AAAA,MACr1C,YAAY,EAAE,uBAAuB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,MAAM,EAAE,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,QAAQ,EAAE,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,aAAa,MAAM,GAAG,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAK,EAAE;AAAA,IAC3mB;AACA,WAAO;AAAA,EACT,EAAE;AACF,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAO;AACpB,EAAE;AACF,OAAO,SAAS;AAChB,IAAI,0BAA0B;AAG9B,IAAI,WAAW;AAAA,EACb,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,sBAAsB;AACxB;AACA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,MAAM;AACR;AACA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;AAt/BA;AAu/BA,IAAI,cAAa,WAAM;AAAA,EACrB,cAAc;AACZ,SAAK,QAAQ,IAAI,gBAAgB,OAAO;AAAA,MACtC,WAAW;AAAA,MACX,QAAwB,oBAAI,IAAI;AAAA,MAChC,eAA+B,oBAAI,IAAI;AAAA,MACvC,iBAAiC,oBAAI,IAAI;AAAA,MACzC,OAAO,CAAC;AAAA,MACR,UAAU,CAAC;AAAA,MACX,OAAO,CAAC;AAAA,MACR,wBAAwB;AAAA,MACxB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,eAAe;AAAA,IACjB,EAAE;AACF,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,MAAM;AACX,SAAK,QAAQ,WAAW,EAAE,IAAI;AAC9B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACnB;AAAA,EAIA,OAAO,MAAM;AACX,SAAK,MAAM,QAAQ,MAAM,KAAK;AAAA,MAC5B,MAAM,KAAK;AAAA,MACX,MAAM,KAAK,QAAQ,KAAK,SAAS;AAAA,MACjC,MAAM,KAAK;AAAA,MACX,WAAW,CAAC;AAAA,IACd,CAAC;AACD,SAAK,MAAM,QAAQ,aAAa,KAAK,MAAM,QAAQ,MAAM,MAAM,EAAE,EAAE,CAAC;AAAA,EACtE;AAAA,EACA,SAAS,IAAI,MAAM,aAAa,MAAM;AACpC,QAAI,cAAc,KAAK,MAAM,QAAQ;AACrC,UAAM,MAAM,KAAK,MAAM,QAAQ,OAAO,IAAI,EAAE;AAC5C,QAAI,KAAK;AACP,UAAI,KAAK,MAAM,QAAQ,cAAc,IAAI,OAAO,KAAK,MAAM,QAAQ,eAAe,IAAI,KAAK;AACzF,cAAM,IAAI;AAAA,UACR,yDAAyD,IAAI,IAAI,iBAAiB,IAAI,IAAI,IAAI,aAAa,KAAK,MAAM,QAAQ,WAAW,IAAI;AAAA,QAC/I;AAAA,MACF;AACA,oBAAc,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,QAAQ;AACrD,UAAI,MAAM;AACV,UAAI,OAAO,SAAS,IAAI,QAAQ,eAAe,MAAM;AACnD;AAAA,MACF;AAAA,IACF;AACA,SAAI,2CAAa,SAAQ,MAAM;AAC7B,oBAAc,EAAE,MAAM,MAAM,KAAK;AAAA,IACnC;AACA,QAAI,QAAQ,QAAQ,YAAY,QAAQ,MAAM;AAC5C,oBAAc,EAAE,MAAM,MAAM,KAAK;AAAA,IACnC;AACA,SAAK,MAAM,QAAQ,OAAO,IAAI,IAAI;AAAA,MAChC,KAAK;AAAA,MACL;AAAA,MACA,aAAa,YAAY;AAAA,MACzB,MAAM,YAAY,QAAQ,KAAK,SAAS;AAAA,MACxC,WAAW,KAAK,MAAM,QAAQ;AAAA,MAC9B,OAAO,CAAC;AAAA,MACR,YAAY,CAAC;AAAA,MACb,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM,QAAQ;AAAA,IAChB,CAAC;AACD,QAAI,KAAK,MAAM,QAAQ,WAAW;AAChC,YAAM,qBAAqB,KAAK,MAAM,QAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,SAAS;AACrF,UAAI,oBAAoB;AACtB,2BAAmB,YAAY;AAAA,MACjC;AAAA,IACF;AACA,QAAI,KAAK,MAAM,QAAQ,YAAY;AACjC,WAAK,MAAM,QAAQ,WAAW,UAAU,KAAK,EAAE;AAAA,IACjD;AACA,SAAK,MAAM,QAAQ,YAAY;AAAA,EACjC;AAAA,EACA,gBAAgB,MAAM;AACpB,QAAI;AACJ,QAAI,QAAQ;AACZ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,SAAK,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,SAAS,QAAQ,KAAK;AACvD,UAAI,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,KAAK,SAAS,gBAAgB,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,MAAM;AACtH;AAAA,MACF;AACA,UAAI,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,KAAK,SAAS,cAAc,KAAK,MAAM,QAAQ,SAAS,CAAC,EAAE,SAAS,MAAM;AACpH;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,QAAQ,MAAM,SAAS,QAAQ;AACxC,SAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,MAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ,QAAQ,KAAK,SAAS;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,QAAQ,MAAM,SAAS,aAAa,WAAW,OAAO;AAC9D,QAAI,gBAAgB,KAAK,SAAS,YAAY;AAC5C,YAAM,MAAM,KAAK,gBAAgB,UAAU,EAAE;AAC7C,UAAI,MAAM,GAAG;AACX,cAAM,QAAQ,IAAI,MAAM,mDAAmD,SAAS,GAAG;AACvF,cAAM,OAAO;AAAA,UACX,MAAM;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK,EAAE,YAAY,GAAG,WAAW,GAAG,cAAc,GAAG,aAAa,EAAE;AAAA,UACpE,UAAU,CAAC,sBAAsB;AAAA,QACnC;AACA,cAAM;AAAA,MACR;AAAA,IACF;AACA,SAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,MAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,UAAS,mCAAS,SAAQ;AAAA,MAC1B,OAAM,mCAAS,SAAQ,KAAK,SAAS;AAAA,MACrC,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,MAAM,QAAQ,MAAM,SAAS;AAAA,EAC3C;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,MAAM,EAAE,IAAI;AAAA,EACpD;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACA,YAAY;AACV,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACA,SAAS,IAAI;AACX,WAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,EAAE;AAAA,EACzC;AAAA,EACA,eAAe;AACb,WAAO,CAAC,GAAG,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAC;AAAA,EAC7C;AAAA,EACA,wBAAwB;AACtB,SAAK,MAAM,QAAQ,yBAAyB;AAAA,EAC9C;AAAA,EACA,yBAAyB;AACvB,SAAK,MAAM,QAAQ,yBAAyB;AAAA,EAC9C;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,MAAM,QAAQ;AAAA,EAC5B;AAAA,EACA,QAAQ,aAAa;AACnB,SAAK,MAAM,QAAQ,cAAc;AAAA,EACnC;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,SAAS,QAAQ;AACnB,aAAO,CAAC;AAAA,IACV;AACA,WAAO,KAAK,KAAK;AACjB,UAAM,OAAO,WAAW,KAAK,IAAI,MAAM,OAAO,OAAO,aAAa,KAAK,IAAI,MAAM,OAAO,QAAQ;AAChG,UAAM,eAAe,SAAS,SAAS,OAAO,KAAK,QAAQ,mBAAmB,EAAE,GAAG,KAAK;AACxF,WAAO,EAAE,aAAa,KAAK;AAAA,EAC7B;AAAA,EACA,WAAW;AAlrCb,QAAAC;AAmrCI,QAAI,KAAK,MAAM,QAAQ,gBAAgB,QAAQ;AAC7C,aAAO,KAAK,MAAM,QAAQ;AAAA,IAC5B;AACA,aAAOA,MAAA,WAAW,EAAE,aAAb,gBAAAA,IAAuB,SAAQ;AAAA,EACxC;AAAA,EACA,QAAQ;AACN,SAAK,MAAM,MAAM;AACjB,UAAM;AAAA,EACR;AAAA,EACA,aAAa,KAAK;AAChB,UAAM,aAAa,IAAI,KAAK;AAC5B,UAAM,EAAE,MAAM,YAAY,IAAI,KAAK,YAAY,UAAU;AACzD,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN;AAAA,IACF;AACA,QAAI,MAAM,iBAAiB,KAAK,UAAU,OAAO,CAAC,EAAE;AACpD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK;AAChB,UAAM,QAAQ,uCAAuC,KAAK,GAAG;AAC7D,QAAI,SAAQ,+BAAQ,MAAK,MAAM,CAAC,EAAE,KAAK,IAAI;AAC3C,QAAI,SAAQ,+BAAQ,MAAK,MAAM,CAAC,EAAE,KAAK,IAAI;AAC3C,QAAI,iCAAQ,KAAK;AACf,UAAI,CAAC,OAAO,IAAI,SAAS,SAAS,KAAK,GAAG;AACxC,gBAAQ;AACR,gBAAQ,IAAI,KAAK;AAAA,MACnB;AAAA,IACF,OAAO;AACL,YAAM,QAAQ,IAAI,OAAO,EAAE;AAC3B,YAAM,QAAQ;AACd,UAAI,MAAM,UAAU,OAAO;AACzB,gBAAQ;AACR,gBAAQ,IAAI,KAAK;AAAA,MACnB;AAAA,IACF;AACA,UAAM,EAAE,MAAM,YAAY,IAAI,KAAK,YAAY,KAAK;AACpD,WAAO;AAAA,MACL,MAAM,cAAc,aAAa,aAAa,WAAW,CAAC,IAAI;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,WAAW,SAAS;AACjC,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ,QAAQ,KAAK,SAAS;AAAA,IACtC;AACA,UAAM,SAAS,CAAC,EAAE,OAAO,OAAO,KAAK;AACrC,SAAK,MAAM,QAAQ,MAAM,KAAK,IAAI;AAClC,SAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,MAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,MAAM,OAAO,CAAC;AAAA,MACd,IAAI,OAAO,CAAC;AAAA,MACZ,SAAS,QAAQ;AAAA,MACjB,MAAM,QAAQ,QAAQ,KAAK,SAAS;AAAA,MACpC,MAAM,KAAK,SAAS;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,SAAS,MAAM;AACtB,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,QAAI;AACF,UAAI,gBAAgB,aAAa,KAAK,MAAM,WAAW,CAAC;AACxD,sBAAgB,cAAc,QAAQ,aAAa,GAAG;AACtD,sBAAgB,cAAc,QAAQ,UAAU,GAAG;AACnD,YAAM,QAAQ,KAAK,MAAM,aAAa;AACtC,WAAK,YAAY,OAAO,KAAK;AAAA,IAC/B,SAAS,GAAG;AACV,UAAI,MAAM,uCAAuC,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,SAAS,SAAS,MAAM;AACtB,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,QAAI;AACF,YAAM,QAAQ,CAAC;AACf,UAAI,gBAAgB,aAAa,KAAK,MAAM,WAAW,CAAC;AACxD,YAAM,MAAM,cAAc,QAAQ,GAAG;AACrC,sBAAgB,cAAc,QAAQ,aAAa,GAAG;AACtD,sBAAgB,cAAc,QAAQ,UAAU,GAAG;AACnD,YAAM,QAAQ,cAAc,MAAM,GAAG,MAAM,CAAC,EAAE,KAAK;AACnD,YAAM,OAAO,cAAc,MAAM,MAAM,CAAC,EAAE,KAAK;AAC/C,YAAM,KAAK,IAAI;AACf,WAAK,YAAY,OAAO,KAAK;AAAA,IAC/B,SAAS,GAAG;AACV,UAAI,MAAM,uCAAuC,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,QAAI,MAAM,SAAS,MAAM;AACvB,YAAM,QAAQ;AAAA,IAChB,OAAO;AACL,iBAAW,OAAO,OAAO;AACvB,cAAM,MAAM,GAAG,IAAI,MAAM,GAAG;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,SAAS,MAAM;AAC3B,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,QAAI;AACF,YAAM,gBAAgB,aAAa,KAAK,MAAM,WAAW,CAAC;AAC1D,YAAM,aAAa,KAAK,MAAM,aAAa;AAC3C,WAAK,iBAAiB,OAAO,UAAU;AAAA,IACzC,SAAS,GAAG;AACV,UAAI,MAAM,6CAA6C,CAAC;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,YAAY;AAClC,QAAI,MAAM,cAAc,MAAM;AAC5B,YAAM,aAAa;AAAA,IACrB,OAAO;AACL,iBAAW,OAAO,YAAY;AAC5B,cAAM,WAAW,GAAG,IAAI,WAAW,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,MAAM,QAAQ,aAAa;AAAA,EAClC;AAAA,EACA,WAAW,SAAS,MAAM;AACxB,UAAM,QAAQ,KAAK,SAAS,OAAO;AACnC,UAAM,OAAO,SAAS,eAAe,KAAK,IAAI;AAC9C,QAAI;AACF,YAAM,QAAQ,KAAK;AACnB,YAAM,UAAU,KAAK,MAAM,KAAK;AAChC,UAAI,QAAQ,YAAY;AACtB,aAAK,iBAAiB,OAAO,QAAQ,UAAU;AAAA,MACjD;AACA,UAAI,QAAQ,OAAO;AACjB,aAAK,YAAY,OAAO,QAAQ,KAAK;AAAA,MACvC;AAAA,IACF,SAAS,GAAG;AACV,UAAI,MAAM,0CAA0C,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO,KAAK;AAC3B,SAAI,+BAAO,gBAAe,QAAQ;AAChC,aAAO,MAAM,WAAW,GAAG;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,OAAO;AACX,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAM,QAAQ,CAAC,SAAS;AACtB,aAAK,MAAM,IAAI;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,eAAK,MAAM,QAAQ,SAAS,KAAK;AAAA,YAC/B,IAAI,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS;AAAA,YAChD,MAAM;AAAA,YACN,IAAI;AAAA,YACJ,SAAS;AAAA,cACP,OAAO,MAAM;AAAA,cACb,MAAM,MAAM;AAAA,cACZ,SAAS,MAAM;AAAA,YACjB;AAAA,YACA,MAAM;AAAA,YACN,MAAM,MAAM;AAAA,UACd,CAAC;AACD;AAAA,QACF,KAAK;AACH,eAAK,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM,aAAa,MAAM,IAAI;AACrE;AAAA,QACF,KAAK;AACH,cAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG;AAC9C,kBAAM,IAAI;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,eAAK,MAAM,QAAQ,cAAc,MAAM;AACvC,eAAK,SAAS,MAAM,OAAO,MAAM,OAAO,MAAM,aAAa,MAAM,IAAI;AACrE,eAAK,MAAM,QAAQ,cAAc,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,SAAS,MAAM;AACpF;AAAA,QACF,KAAK;AACH,eAAK,MAAM,QAAQ,gBAAgB,MAAM;AACzC,eAAK,MAAM,QAAQ,gBAAgB,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,SAAS,MAAM;AACtF;AAAA,QACF,KAAK;AACH,eAAK,UAAU,MAAM,OAAO,QAAQ,QAAQ,MAAM,UAAU;AAC5D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,MAAM,OAAO,QAAQ,QAAQ,MAAM,UAAU;AAC5D;AAAA,QACF,KAAK;AACH,eAAK,QAAQ,MAAM,OAAO,MAAM,WAAW,MAAM,IAAI;AACrD;AAAA,QACF,KAAK;AACH,eAAK,SAAS,MAAM,OAAO,MAAM,IAAI;AACrC;AAAA,QACF,KAAK;AACH,eAAK,SAAS,MAAM,OAAO,MAAM,IAAI;AACrC;AAAA,QACF,KAAK;AACH,eAAK,cAAc,MAAM,OAAO,MAAM,IAAI;AAC1C;AAAA,QACF,KAAK;AACH,eAAK,WAAW,MAAM,OAAO,MAAM,IAAI;AACvC;AAAA,QACF,KAAK;AACH,cAAI,KAAK,MAAM,QAAQ,aAAa;AAClC,gBAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,aAAa;AAC/C,oBAAM,IAAI;AAAA,gBACR,6BAA6B,KAAK,MAAM,QAAQ,YAAY,OAAO;AAAA,cACrE;AAAA,YACF,OAAO;AACL,mBAAK,MAAM,QAAQ,cAAc;AAAA,YACnC;AAAA,UACF,WAAW,KAAK,MAAM,QAAQ,eAAe;AAC3C,gBAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,iBAAiB,MAAM,SAAS,KAAK,MAAM,QAAQ,eAAe;AACpG,oBAAM,IAAI;AAAA,gBACR,+BAA+B,KAAK,MAAM,QAAQ,cAAc,OAAO;AAAA,cACzE;AAAA,YACF,OAAO;AACL,mBAAK,MAAM,QAAQ,gBAAgB;AAAA,YACrC;AAAA,UACF;AACA,eAAK,UAAU,MAAM,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,YAAY,MAAM,QAAQ;AAChF;AAAA,QACF,KAAK;AACH,eAAK,OAAO,MAAM,OAAO;AACzB;AAAA,QACF,KAAK;AACH,eAAK,OAAO;AACZ;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,UAAU,MAAM,UAAU;AAC/D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,OAAO,MAAM,UAAU;AAC5D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,SAAS,MAAM,UAAU;AAC9D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,SAAS,MAAM,UAAU;AAC9D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,SAAS,MAAM,UAAU;AAC9D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,QACF,KAAK;AACH,sBAAY,MAAM,IAAI;AACtB;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,SAAS,MAAM,UAAU;AAC9D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,SAAS,MAAM,UAAU;AAC9D;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,cAAc,MAAM,UAAU;AACnE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,YAAY,MAAM,UAAU;AACjE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,MAAM,WAAW,MAAM,UAAU;AAChE;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,QAAQ,QAAQ,MAAM,UAAU;AACvD;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AACV,WAAO,WAAW,EAAE;AAAA,EACtB;AACF,GAhcI,OAAO,IAAM,YAAY,GAhCZ;AAmejB,IAAI,YAA4B,OAAO,CAAC,YAAY;AAAA,cACtC,QAAQ,WAAW;AAAA,YACrB,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA,YAIhB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,cAKpB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMtB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMnB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,WAAW;AAAA,cACjB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA,YAI3B,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAInB,QAAQ,WAAW;AAAA,cACjB,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,YAIrB,QAAQ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,cAKrB,QAAQ,mBAAmB;AAAA,YAC7B,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA,YAIxB,QAAQ,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,YAKtB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOnB,QAAQ,mBAAmB;AAAA,YAC7B,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,cAKzB,QAAQ,eAAe;AAAA,YACzB,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA,YAIpB,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,YAKrB,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,YAI/B,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA,YAI/B,QAAQ,kBAAkB;AAAA,cACxB,QAAQ,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAS/B,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,cAKd,QAAQ,WAAW;AAAA,YACrB,QAAQ,QAAQ;AAAA;AAAA;AAAA,cAGd,QAAQ,WAAW;AAAA,YACrB,QAAQ,QAAQ;AAAA;AAAA;AAAA,GAGzB,WAAW;AACd,IAAI,iBAAiB;AAOrB,IAAI,mBAAmB,KAAK;AAC5B,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,yBAAyB;AAC7B,IAAI,YAA4B,OAAO,SAAS,MAAM,UAAU;AAC9D,SAAO,SAAS,MAAM,QAAQ;AAChC,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,SAAS,MAAM,OAAO,cAAc,WAAW,YAAY;AAChG,MAAI,MAAM,UAAU,UAAU,MAAM,UAAU,QAAQ,OAAO,KAAK,MAAM,KAAK,EAAE,WAAW,GAAG;AAC3F,WAAO,EAAE,QAAQ,GAAG,OAAO,EAAE;AAAA,EAC/B;AACA,QAAM,QAAQ,MAAM;AACpB,QAAM,YAAY,MAAM;AACxB,QAAM,WAAW,MAAM;AACvB,MAAI,eAAe;AACnB,MAAI,YAAY;AACd,mBAAe;AAAA,EACjB;AACA,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB,IAAE,KAAK,MAAM,UAAU,YAAY,QAAQ;AAC3C,IAAE,KAAK,SAAS,gBAAgB;AAChC,IAAE,KAAK,WAAW,YAAY;AAC9B,MAAI,aAAa;AACjB,MAAI,SAAS,UAAU,QAAQ;AAC7B,iBAAa,MAAM,SAAS;AAAA,EAC9B;AACA,MAAI,YAAY,SAAS,QAAQ,eAAe,SAAS,QAAQ;AACjE,QAAM,WAAW,EAAE,OAAO,MAAM;AAChC,WAAS,KAAK,SAAS,wBAAwB,UAAU;AACzD,WAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,WAAS,KAAK,KAAK,SAAS,MAAM;AAClC,WAAS,KAAK,QAAQ,SAAS,IAAI;AACnC,WAAS,KAAK,UAAU,SAAS,MAAM;AACvC,WAAS,KAAK,SAAS,SAAS;AAChC,WAAS,KAAK,UAAU,SAAS,MAAM;AACvC,WAAS,KAAK,MAAM,SAAS,EAAE;AAC/B,WAAS,KAAK,MAAM,SAAS,EAAE;AAC/B,MAAI,SAAS,MAAM;AACjB,QAAI,QAAQ;AACZ,aAAS,OAAO,OAAO;AACrB,UAAI,WAAW,EAAE,OAAO,GAAG;AAC3B,UAAI,oBAAgB,iCAAY,MAAM,GAAG,CAAC;AAC1C,eAAS,KAAK,cAAc,aAAa;AACzC,eAAS,KAAK,UAAU,QAAQ;AAChC,qCAA+B,SAAS;AAAA,QACtC;AAAA,QACA;AAAA,QACA,SAAS,IAAI;AAAA,QACb,SAAS,SAAS;AAAA,QAClB;AAAA,QACA;AAAA,QACA,EAAE,OAAO,QAAQ;AAAA,QACjB;AAAA,MACF;AACA,eAAS;AAAA,IACX;AAAA,EACF;AACA,WAAS,KAAK,UAAU,KAAK;AAC7B,SAAO,EAAE,QAAQ,SAAS,SAAS,OAAO,OAAO,UAAU;AAC7D,GAAG,WAAW;AACd,IAAI,kBAAkC,OAAO,SAAS,OAAO;AAC3D,SAAO,uCAAuC,QAAQ;AACxD,GAAG,iBAAiB;AACpB,IAAI,YAA4B,OAAO,eAAe,MAAM,UAAU,WAAW,MAAM;AACrF,MAAI,WAAW,KAAK,OAAO,eAAe;AAC1C,QAAM,QAAQ,MAAM,YAAY,SAAS,MAAM,UAAU,CAAC;AAC1D,QAAM,UAAU,SAAS,OAAO,WAAW,EAAE,KAAK,SAAS,qBAAqB,EAAE,KAAK,SAAS,8BAA8B,EAAE,KAAK,KAAK;AAC1I,QAAM,MAAM,QAAQ,KAAK,EAAE,sBAAsB;AACjD,WAAS,KAAK,UAAU,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC;AACnF,MAAI,SAAS,UAAU,YAAY;AACjC,UAAM,WAAW,KAAK,KAAK,EAAE;AAC7B,aAAS,aAAa,UAAU,IAAI,SAAS,IAAI,SAAS,UAAU;AACpE,UAAM,UAAU,SAAS,QAAQ;AACjC,aAAS,KAAK,KAAK,KAAK,MAAM,QAAQ,IAAI,QAAQ,QAAQ,IAAI,IAAI,QAAQ,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,MAAM,QAAQ,IAAI,QAAQ,SAAS,IAAI,IAAI,SAAS,CAAC,CAAC;AAAA,EACrJ,WAAW,UAAU;AACnB,QAAI,EAAE,QAAQ,OAAO,OAAO,IAAI;AAChC,QAAI,SAAS,OAAO;AAClB,YAAM,OAAO;AACb,eAAS;AACT,cAAQ;AAAA,IACV;AACA,aAAS,KAAK,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AACpF,QAAI,SAAS,UAAU,YAAY;AACjC,eAAS,KAAK,KAAK,KAAK,MAAM,MAAM,CAAC;AAAA,IACvC,OAAO;AACL,eAAS,KAAK,KAAK,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC;AAAA,IACpD;AAAA,EACF;AACA,SAAO,CAAC,QAAQ;AAClB,GAAG,WAAW;AACd,IAAI,WAA2B,OAAO,SAAS,MAAM,UAAU;AAC7D,MAAI,iBAAiB;AACrB,MAAI,aAAa;AACjB,QAAM,QAAQ,SAAS,KAAK,MAAM,eAAe,cAAc;AAC/D,QAAM,CAAC,eAAe,eAAe,IAAI,cAAc,SAAS,QAAQ;AACxE,MAAI,YAAY,CAAC;AACjB,MAAI,KAAK;AACT,MAAI,QAAwB,OAAO,MAAM,SAAS,GAAG,OAAO;AAC5D,MAAI,SAAS,WAAW,UAAU,SAAS,eAAe,UAAU,SAAS,aAAa,GAAG;AAC3F,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AAAA,MACL,KAAK;AACH,gBAAwB,OAAO,MAAM,KAAK,MAAM,SAAS,IAAI,SAAS,UAAU,GAAG,OAAO;AAC1F;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,gBAAwB,OAAO,MAAM,KAAK,MAAM,SAAS,KAAK,iBAAiB,aAAa,SAAS,cAAc,CAAC,GAAG,OAAO;AAC9H;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,gBAAwB,OAAO,MAAM,KAAK;AAAA,UACxC,SAAS,KAAK,iBAAiB,aAAa,IAAI,SAAS,cAAc,SAAS;AAAA,QAClF,GAAG,OAAO;AACV;AAAA,IACJ;AAAA,EACF;AACA,MAAI,SAAS,WAAW,UAAU,SAAS,eAAe,UAAU,SAAS,UAAU,QAAQ;AAC7F,YAAQ,SAAS,QAAQ;AAAA,MACvB,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,UAAU;AACxD,iBAAS,SAAS;AAClB,iBAAS,mBAAmB;AAC5B,iBAAS,oBAAoB;AAC7B;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,QAAQ,CAAC;AACvD,iBAAS,SAAS;AAClB,iBAAS,mBAAmB;AAC5B,iBAAS,oBAAoB;AAC7B;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,iBAAS,IAAI,KAAK,MAAM,SAAS,IAAI,SAAS,QAAQ,SAAS,UAAU;AACzE,iBAAS,SAAS;AAClB,iBAAS,mBAAmB;AAC5B,iBAAS,oBAAoB;AAC7B;AAAA,IACJ;AAAA,EACF;AACA,WAAS,CAAC,GAAG,IAAI,KAAK,MAAM,QAAQ,GAAG;AACrC,QAAI,SAAS,eAAe,UAAU,SAAS,eAAe,KAAK,kBAAkB,QAAQ;AAC3F,WAAK,IAAI;AAAA,IACX;AACA,UAAM,WAAW,KAAK,OAAO,MAAM;AACnC,aAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,aAAS,KAAK,KAAK,MAAM,CAAC;AAC1B,QAAI,SAAS,WAAW,QAAQ;AAC9B,eAAS,KAAK,eAAe,SAAS,MAAM,EAAE,KAAK,qBAAqB,SAAS,gBAAgB,EAAE,KAAK,sBAAsB,SAAS,iBAAiB;AAAA,IAC1J;AACA,QAAI,SAAS,eAAe,QAAQ;AAClC,eAAS,MAAM,eAAe,SAAS,UAAU;AAAA,IACnD;AACA,QAAI,oBAAoB,QAAQ;AAC9B,eAAS,MAAM,aAAa,eAAe;AAAA,IAC7C;AACA,QAAI,SAAS,eAAe,QAAQ;AAClC,eAAS,MAAM,eAAe,SAAS,UAAU;AAAA,IACnD;AACA,QAAI,SAAS,SAAS,QAAQ;AAC5B,eAAS,KAAK,QAAQ,SAAS,IAAI;AAAA,IACrC;AACA,QAAI,SAAS,UAAU,QAAQ;AAC7B,eAAS,KAAK,SAAS,SAAS,KAAK;AAAA,IACvC;AACA,QAAI,SAAS,OAAO,QAAQ;AAC1B,eAAS,KAAK,MAAM,SAAS,EAAE;AAAA,IACjC,WAAW,OAAO,GAAG;AACnB,eAAS,KAAK,MAAM,EAAE;AAAA,IACxB;AACA,UAAM,OAAO,QAAQ;AACrB,QAAI,SAAS,OAAO;AAClB,YAAM,OAAO,SAAS,OAAO,OAAO;AACpC,WAAK,KAAK,KAAK,SAAS,CAAC;AACzB,UAAI,SAAS,SAAS,QAAQ;AAC5B,aAAK,KAAK,QAAQ,SAAS,IAAI;AAAA,MACjC;AACA,WAAK,KAAK,IAAI;AAAA,IAChB,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,QAAI,SAAS,WAAW,UAAU,SAAS,eAAe,UAAU,SAAS,aAAa,GAAG;AAC3F,qBAAe,SAAS,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE;AAC7D,uBAAiB;AAAA,IACnB;AACA,cAAU,KAAK,QAAQ;AAAA,EACzB;AACA,SAAO;AACT,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,SAAS,MAAM,WAAW;AAC/D,WAAS,UAAU,GAAG,GAAG,OAAO,QAAQ,KAAK;AAC3C,WAAO,IAAI,MAAM,IAAI,OAAO,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,OAAO,IAAI,QAAQ,MAAM,OAAO,OAAO,IAAI,UAAU,MAAM,IAAI,OAAO,IAAI;AAAA,EAC9K;AACA,SAAO,WAAW,WAAW;AAC7B,QAAM,UAAU,KAAK,OAAO,SAAS;AACrC,UAAQ,KAAK,UAAU,UAAU,UAAU,GAAG,UAAU,GAAG,UAAU,OAAO,UAAU,QAAQ,CAAC,CAAC;AAChG,UAAQ,KAAK,SAAS,UAAU;AAChC,YAAU,IAAI,UAAU,IAAI,UAAU,SAAS;AAC/C,WAAS,MAAM,SAAS;AACxB,SAAO;AACT,GAAG,WAAW;AACd,IAAI,WAAW;AACf,IAAI,qBAAqC,OAAO,CAAC,UAAU,QAAQ,WAAW,UAAU;AACtF,MAAI,CAAC,SAAS,QAAQ;AACpB;AAAA,EACF;AACA,YAAU,QAAQ,CAAC,aAAa;AAC9B,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAM,WAAW,SAAS,OAAO,WAAW,MAAM,QAAQ;AAC1D,QAAI,CAAC,MAAM,gBAAgB,MAAM,OAAO;AACtC,eAAS,KAAK,MAAM,MAAM,QAAQ,MAAM,SAAS,CAAC;AAAA,IACpD,WAAW,MAAM,cAAc;AAC7B,eAAS,KAAK,MAAM,MAAM,KAAK;AAAA,IACjC;AAAA,EACF,CAAC;AACH,GAAG,oBAAoB;AACvB,IAAI,2BAA2C,OAAO,SAAS,MAAM,OAAO,OAAO,UAAU;AAhzD7F,MAAAA,KAAA;AAizDE,QAAM,SAAS,WAAW,MAAM,QAAQ,MAAM;AAC9C,QAAM,SAAS,MAAM,IAAI,MAAM,QAAQ;AACvC,QAAM,UAAU,SAAS,MAAM;AAC/B,QAAM,mBAAmB,KAAK,OAAO,GAAG,EAAE,MAAM;AAChD,MAAI,IAAI;AACR,MAAI,CAAC,UAAU;AACb;AACA,QAAI,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,YAAY;AAC9D,QAAE,KAAK,WAAW,gBAAgB,QAAQ,QAAQ,QAAQ,CAAC,EAAE,KAAK,UAAU,SAAS;AAAA,IACvF;AACA,MAAE,OAAO,MAAM,EAAE,KAAK,MAAM,UAAU,QAAQ,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,GAAG,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,gBAAgB,OAAO,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,QAAQ,MAAM,IAAI;AACtO,QAAI,iBAAiB,OAAO,GAAG;AAC/B,UAAM,WAAW;AACjB,QAAI,MAAM,SAAS,MAAM;AACvB,QAAE,KAAK,MAAM,UAAU,QAAQ;AAAA,IACjC;AAAA,EACF;AACA,QAAM,OAAO,YAAY;AACzB,MAAI,WAAW;AACf,OAAIA,MAAA,MAAM,eAAN,gBAAAA,IAAkB,OAAO;AAC3B,eAAW,MAAM,WAAW;AAAA,EAC9B,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AACA,MAAI,UAAU;AACZ,gBAAY,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,gBAAY,IAAI,eAAe;AAAA,EACjC;AACA,OAAK,IAAI,MAAM;AACf,OAAK,IAAI;AACT,OAAK,QAAQ,MAAM;AACnB,OAAK,SAAS,MAAM;AACpB,OAAK,QAAQ;AACb,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,OAAO,MAAM;AAClB,QAAM,WAAW,UAAU,GAAG,IAAI;AAClC,QAAM,WAAW;AACjB,OAAI,WAAM,eAAN,mBAAkB,MAAM;AAC1B,UAAM,UAAU,MAAM,WAAW,KAAK,KAAK;AAC3C,QAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC7B,wBAAkB,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,QAAQ,OAAO,CAAC,CAAC;AAAA,IAC/E,OAAO;AACL,gBAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,OAAO;AAAA,IAC7D;AAAA,EACF;AACA,yBAAuB,OAAO,SAAS,MAAM,WAAW,CAAC;AAAA,IACvD,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,SAAS,eAAe,GAAG;AAAA,IACpC;AAAA,EACF;AACA,MAAI,SAAS,MAAM;AACnB,MAAI,SAAS,MAAM;AACjB,UAAM,UAAU,SAAS,KAAK,EAAE,QAAQ;AACxC,UAAM,SAAS,QAAQ;AACvB,aAAS,QAAQ;AAAA,EACnB;AACA,SAAO;AACT,GAAG,0BAA0B;AAC7B,IAAI,qBAAqC,OAAO,SAAS,MAAM,OAAO,OAAO,UAAU;AACrF,QAAM,SAAS,WAAW,MAAM,QAAQ,MAAM;AAC9C,QAAM,SAAS,MAAM,IAAI,MAAM,QAAQ;AACvC,QAAM,UAAU,SAAS;AACzB,QAAM,OAAO,KAAK,OAAO,GAAG,EAAE,MAAM;AACpC,MAAI,CAAC,UAAU;AACb;AACA,SAAK,OAAO,MAAM,EAAE,KAAK,MAAM,UAAU,QAAQ,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,GAAG,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,gBAAgB,OAAO,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,QAAQ,MAAM,IAAI;AACzO,UAAM,WAAW;AAAA,EACnB;AACA,QAAM,UAAU,KAAK,OAAO,GAAG;AAC/B,MAAI,WAAW;AACf,MAAI,UAAU;AACZ,gBAAY,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,gBAAY,IAAI,eAAe;AAAA,EACjC;AACA,UAAQ,KAAK,SAAS,QAAQ;AAC9B,UAAQ,KAAK,QAAQ,MAAM,IAAI;AAC/B,QAAM,OAAO,YAAY;AACzB,OAAK,IAAI,MAAM;AACf,OAAK,IAAI;AACT,OAAK,OAAO;AACZ,OAAK,QAAQ,MAAM;AACnB,OAAK,SAAS,MAAM;AACpB,OAAK,QAAQ;AACb,OAAK,KAAK;AACV,OAAK,KAAK;AACV,UAAQ,OAAO,MAAM,EAAE,KAAK,MAAM,oBAAoB,QAAQ,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,SAAS,EAAE;AACpJ,UAAQ,OAAO,MAAM,EAAE,KAAK,MAAM,mBAAmB,QAAQ,EAAE,KAAK,MAAM,SAAS,mBAAmB,CAAC,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,MAAM,SAAS,mBAAmB,CAAC,EAAE,KAAK,MAAM,SAAS,EAAE;AACjM,UAAQ,OAAO,MAAM,EAAE,KAAK,MAAM,SAAS,mBAAmB,CAAC,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,SAAS,EAAE;AAClI,UAAQ,OAAO,MAAM,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,MAAM,SAAS,mBAAmB,IAAI,CAAC,EAAE,KAAK,MAAM,SAAS,EAAE;AACtI,QAAM,SAAS,QAAQ,OAAO,QAAQ;AACtC,SAAO,KAAK,MAAM,MAAM,IAAI,MAAM,QAAQ,CAAC;AAC3C,SAAO,KAAK,MAAM,SAAS,EAAE;AAC7B,SAAO,KAAK,KAAK,EAAE;AACnB,SAAO,KAAK,SAAS,MAAM,KAAK;AAChC,SAAO,KAAK,UAAU,MAAM,MAAM;AAClC,QAAM,UAAU,QAAQ,KAAK,EAAE,QAAQ;AACvC,QAAM,SAAS,QAAQ;AACvB,yBAAuB,OAAO,SAAS,MAAM,WAAW,CAAC;AAAA,IACvD,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,IACL,KAAK,IAAI;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,EAAE,OAAO,SAAS,sBAAsB,GAAG;AAAA,IAC3C;AAAA,EACF;AACA,SAAO,MAAM;AACf,GAAG,oBAAoB;AACvB,IAAI,YAA4B,OAAO,eAAe,MAAM,OAAO,OAAO,UAAU;AAClF,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK;AACH,aAAO,MAAM,mBAAmB,MAAM,OAAO,OAAO,QAAQ;AAAA,IAC9D,KAAK;AACH,aAAO,MAAM,yBAAyB,MAAM,OAAO,OAAO,QAAQ;AAAA,EACtE;AACF,GAAG,WAAW;AACd,IAAI,UAA0B,OAAO,SAAS,MAAM,KAAK,OAAO;AAC9D,QAAM,mBAAmB,KAAK,OAAO,GAAG;AACxC,QAAM,IAAI;AACV,sBAAoB,GAAG,GAAG;AAC1B,MAAI,IAAI,MAAM;AACZ,2BAAuB,KAAK;AAAA,MAC1B,IAAI;AAAA,MACJ;AAAA,MACA,IAAI;AAAA,MACJ,IAAI,KAAK,IAAI,iBAAiB,KAAK;AAAA,MACnC,IAAI;AAAA,MACJ;AAAA,MACA,EAAE,OAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,IAAE,MAAM;AACV,GAAG,SAAS;AACZ,IAAI,gBAAgC,OAAO,SAAS,MAAM;AACxD,SAAO,KAAK,OAAO,GAAG;AACxB,GAAG,eAAe;AAClB,IAAI,iBAAiC,OAAO,SAAS,MAAM,SAAS,aAAa,OAAO,mBAAmB;AACzG,QAAM,OAAO,YAAY;AACzB,QAAM,IAAI,QAAQ;AAClB,OAAK,IAAI,QAAQ;AACjB,OAAK,IAAI,QAAQ;AACjB,OAAK,QAAQ,eAAe,oBAAoB;AAChD,OAAK,QAAQ,QAAQ,QAAQ,QAAQ;AACrC,OAAK,SAAS,cAAc,QAAQ;AACpC,YAAU,GAAG,IAAI;AACnB,GAAG,gBAAgB;AACnB,IAAI,WAA2B,OAAO,eAAe,MAAM,WAAW,WAAW,OAAO;AACtF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,EACrB,IAAI;AACJ,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB,QAAM,eAA+B,OAAO,SAAS,QAAQ,QAAQ,OAAO,OAAO;AACjF,WAAO,EAAE,OAAO,MAAM,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,SAAS,UAAU;AAAA,EAC5H,GAAG,cAAc;AACjB,eAAa,UAAU,QAAQ,UAAU,QAAQ,UAAU,OAAO,UAAU,MAAM;AAClF,eAAa,UAAU,OAAO,UAAU,QAAQ,UAAU,OAAO,UAAU,KAAK;AAChF,eAAa,UAAU,QAAQ,UAAU,OAAO,UAAU,OAAO,UAAU,KAAK;AAChF,eAAa,UAAU,QAAQ,UAAU,QAAQ,UAAU,QAAQ,UAAU,KAAK;AAClF,MAAI,UAAU,aAAa,QAAQ;AACjC,cAAU,SAAS,QAAQ,SAAS,MAAM;AACxC,mBAAa,UAAU,QAAQ,KAAK,GAAG,UAAU,OAAO,KAAK,CAAC,EAAE;AAAA,QAC9D;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,MAAM,WAAW;AACrB,MAAI,OAAO;AACX,MAAI,IAAI,UAAU;AAClB,MAAI,IAAI,UAAU;AAClB,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,QAAQ,iBAAiB;AAC7B,MAAI,SAAS,kBAAkB;AAC/B,MAAI,aAAa;AACjB,MAAI,QAAQ;AACZ,YAAU,GAAG,GAAG;AAChB,QAAM,YAAY;AAClB,MAAI,OAAO,UAAU;AACrB,MAAI,IAAI,UAAU,SAAS,gBAAgB,KAAK,UAAU,QAAQ,UAAU,UAAU;AACtF,MAAI,IAAI,UAAU,SAAS,YAAY;AACvC,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,aAAa;AACjB,MAAI,QAAQ;AACZ,MAAI,aAAa;AACjB,MAAI,WAAW;AACf,MAAI,aAAa;AACjB,MAAI,OAAO;AACX,MAAI,WAAW,SAAS,IAAI,IAAI,IAAI,MAAM,UAAU,GAAG,KAAK,SAAS,IAAI,SAAS,GAAG,GAAG;AACxF,MAAI,UAAU,kBAAkB,QAAQ;AACtC,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,UAAU,aAAa,GAAG;AACjE,UAAI,KAAK,SAAS;AAChB,YAAI,OAAO,KAAK;AAChB,YAAI,IAAI,UAAU,UAAU,UAAU,QAAQ,UAAU,UAAU;AAClE,YAAI,IAAI,UAAU,SAAS,GAAG,EAAE,IAAI,YAAY;AAChD,YAAI,QAAQ;AACZ,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,OAAO,UAAU;AACrB,YAAI,SAAS,IAAI,IAAI,GAAG;AACtB,oBAAU,SAAS,UAAU,SAAS,GAAG,EAAE;AAC3C,gBAAM,UAAU,GAAG,KAAK,SAAS;AAAA,QACnC,OAAO;AACL,mBAAS,GAAG,GAAG;AAAA,QACjB;AACA,YAAI,gBAAgB,KAAK;AAAA,UACvB,SAAS,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,IAAI;AAAA,QAClG;AACA,kBAAU,SAAS,GAAG,EAAE,UAAU,iBAAiB,YAAY;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACA,YAAU,SAAS,KAAK,MAAM,UAAU,QAAQ,UAAU,MAAM;AAChE,SAAO;AACT,GAAG,UAAU;AACb,IAAI,sBAAsC,OAAO,SAAS,MAAM,SAAS;AACvE,qBAAmB,MAAM,OAAO;AAClC,GAAG,oBAAoB;AACvB,IAAI,qBAAqC,OAAO,SAAS,MAAM;AAC7D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,UAAU,EAAE,KAAK,aAAa,SAAS,EAAE,KAAK,aAAa,SAAS,EAAE,OAAO,MAAM,EAAE,KAAK,aAAa,WAAW,EAAE;AAAA,IAClK;AAAA,IACA;AAAA,EACF;AACF,GAAG,oBAAoB;AACvB,IAAI,qBAAqC,OAAO,SAAS,MAAM;AAC7D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,UAAU,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,aAAa,WAAW,EAAE;AAAA,IACjJ;AAAA,IACA;AAAA,EACF;AACF,GAAG,oBAAoB;AACvB,IAAI,kBAAkC,OAAO,SAAS,MAAM;AAC1D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,aAAa,WAAW,EAAE;AAAA,IAC9I;AAAA,IACA;AAAA,EACF;AACF,GAAG,iBAAiB;AACpB,IAAI,kBAAkC,OAAO,SAAS,MAAM;AAC1D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,WAAW,EAAE,KAAK,QAAQ,GAAG,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,oBAAoB,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,wBAAwB;AAC9Q,GAAG,iBAAiB;AACpB,IAAI,wBAAwC,OAAO,SAAS,MAAM;AAChE,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,aAAa,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B;AAChO,GAAG,uBAAuB;AAC1B,IAAI,uBAAuC,OAAO,SAAS,MAAM;AAC/D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,gBAAgB,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC;AACxO,GAAG,sBAAsB;AACzB,IAAI,uBAAuC,OAAO,SAAS,MAAM;AAC/D,QAAM,OAAO,KAAK,OAAO,MAAM;AAC/B,QAAM,SAAS,KAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,WAAW,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,CAAC,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG;AACpK,SAAO,OAAO,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,UAAU,SAAS,EAAE,MAAM,oBAAoB,MAAM,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,KAAK,yBAAyB;AACxK,GAAG,sBAAsB;AACzB,IAAI,cAA8B,OAAO,WAAW;AAClD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF,GAAG,YAAY;AACf,IAAI,eAA+B,OAAO,WAAW;AACnD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AACF,GAAG,aAAa;AAChB,IAAI,yBAAyC,WAAW;AACtD,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;AAC1D,UAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,OAAO;AAChI,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,QAAQ,QAAQ;AACvB,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AAClE,UAAM,EAAE,eAAe,iBAAiB,gBAAgB,IAAI;AAC5D,UAAM,CAAC,gBAAgB,gBAAgB,IAAI,cAAc,aAAa;AACtE,UAAM,QAAQ,QAAQ,MAAM,eAAe,cAAc;AACzD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,iBAAiB,kBAAkB,MAAM,SAAS,KAAK;AACtE,YAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,MAAM,eAAe,QAAQ,EAAE,MAAM,aAAa,gBAAgB,EAAE,MAAM,eAAe,eAAe,EAAE,MAAM,eAAe,eAAe;AAClN,WAAK,OAAO,OAAO,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,CAAC;AAC1E,WAAK,KAAK,KAAK,IAAI,SAAS,CAAC,EAAE,KAAK,qBAAqB,SAAS,EAAE,KAAK,sBAAsB,SAAS;AACxG,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AACA,SAAO,SAAS,SAAS;AACzB,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AAC/D,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EAAE,OAAO,eAAe,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AACxG,UAAM,OAAO,EAAE,OAAO,WAAW,EAAE,MAAM,WAAW,OAAO,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,SAAS,MAAM;AAC1G,SAAK,OAAO,KAAK,EAAE,MAAM,WAAW,YAAY,EAAE,MAAM,cAAc,QAAQ,EAAE,MAAM,kBAAkB,QAAQ,EAAE,KAAK,OAAO;AAC9H,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,KAAK;AACzD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,MAAM,MAAM;AACnB,iBAAe,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AACxE,UAAM,MAAM,MAAM,0BAA0B,SAAS,UAAU,CAAC;AAChE,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EAAE,OAAO,eAAe,EAAE,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,CAAC,EAAE,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,UAAU,IAAI,MAAM;AAC1K,UAAM,OAAO,EAAE,OAAO,WAAW,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,SAAS,MAAM;AAChF,SAAK,OAAO,KAAK,EAAE,MAAM,cAAc,QAAQ,EAAE,MAAM,kBAAkB,QAAQ,EAAE,KAAK,MAAM,YAAY,SAAS,UAAU,CAAC,CAAC;AAC/H,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,KAAK;AACzD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,SAAS,SAAS;AACzB,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,kBAAkB,eAAe,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,eAAe;AACrC,SAAO,SAAS,OAAO,YAAY,OAAO;AACxC,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AACA,WAAO,MAAM,kBAAkB,OAAO,OAAO,MAAM,kBAAkB,QAAQ,SAAS;AAAA,EACxF;AACF,EAAE;AACF,IAAI,iCAAiD,WAAW;AAC9D,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;AAC1D,UAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,MAAM,eAAe,OAAO,EAAE,KAAK,OAAO;AAClG,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,QAAQ,QAAQ;AACvB,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AAClE,UAAM,EAAE,eAAe,iBAAiB,gBAAgB,IAAI;AAC5D,UAAM,QAAQ,QAAQ,MAAM,eAAe,cAAc;AACzD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,gBAAgB,iBAAiB,MAAM,SAAS,KAAK;AACpE,YAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,MAAM,eAAe,OAAO,EAAE,MAAM,aAAa,aAAa,EAAE,MAAM,eAAe,eAAe,EAAE,MAAM,eAAe,eAAe;AAClM,WAAK,OAAO,OAAO,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,CAAC;AAC9D,WAAK,KAAK,KAAK,IAAI,SAAS,CAAC,EAAE,KAAK,qBAAqB,SAAS,EAAE,KAAK,sBAAsB,SAAS;AACxG,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AACA,SAAO,SAAS,SAAS;AACzB,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AAC/D,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EAAE,OAAO,eAAe,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AACxG,UAAM,OAAO,EAAE,OAAO,WAAW,EAAE,MAAM,WAAW,OAAO,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,SAAS,MAAM;AAC1G,SAAK,OAAO,KAAK,EAAE,MAAM,WAAW,YAAY,EAAE,MAAM,cAAc,QAAQ,EAAE,MAAM,kBAAkB,QAAQ,EAAE,KAAK,OAAO;AAC9H,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,KAAK;AACzD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,MAAM,MAAM;AACnB,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,kBAAkB,eAAe,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,eAAe;AACrC,SAAO,SAAS,OAAO;AACrB,WAAO,MAAM,kBAAkB,OAAO,OAAO,MAAM,kBAAkB,QAAQ,SAAS;AAAA,EACxF;AACF,EAAE;AACF,IAAI,kBAAkB;AAAA,EACpB,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,aAAa;AAAA,EACb;AAAA,EACA;AACF;AAGA,IAAI,OAAO,CAAC;AACZ,IAAI,SAAS;AAAA,EACX,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,eAAe,CAAC;AAAA,EAChB,aAAa,CAAC;AAAA,EACd,QAAQ;AAAA,IACN,WAA2B,OAAO,WAAW;AAC3C,aAAO,KAAK,IAAI;AAAA,QACd;AAAA,QACA,KAAK,OAAO,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,UAAU,CAAC;AAAA,MAC/E,KAAK,KAAK,MAAM,WAAW,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM,KAAK,SAAS,WAAW,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM,KAAK,MAAM,WAAW,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC;AAAA,IACtT,GAAG,WAAW;AAAA,IACd,OAAuB,OAAO,WAAW;AACvC,WAAK,SAAS,CAAC;AACf,WAAK,QAAQ,CAAC;AACd,WAAK,QAAQ,CAAC;AACd,WAAK,WAAW,CAAC;AACjB,WAAK,QAAQ,CAAC;AAAA,IAChB,GAAG,OAAO;AAAA,IACV,QAAwB,OAAO,SAAS,UAAU;AAChD,WAAK,MAAM,KAAK,QAAQ;AAAA,IAC1B,GAAG,QAAQ;AAAA,IACX,UAA0B,OAAO,SAAS,YAAY;AACpD,WAAK,OAAO,KAAK,UAAU;AAAA,IAC7B,GAAG,UAAU;AAAA,IACb,SAAyB,OAAO,SAAS,WAAW;AAClD,WAAK,MAAM,KAAK,SAAS;AAAA,IAC3B,GAAG,SAAS;AAAA,IACZ,YAA4B,OAAO,SAAS,UAAU;AACpD,WAAK,SAAS,KAAK,QAAQ;AAAA,IAC7B,GAAG,YAAY;AAAA,IACf,SAAyB,OAAO,SAAS,WAAW;AAClD,WAAK,MAAM,KAAK,SAAS;AAAA,IAC3B,GAAG,SAAS;AAAA,IACZ,WAA2B,OAAO,WAAW;AAC3C,aAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,IAC3C,GAAG,WAAW;AAAA,IACd,UAA0B,OAAO,WAAW;AAC1C,aAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,IACzC,GAAG,UAAU;AAAA,IACb,aAA6B,OAAO,WAAW;AAC7C,aAAO,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAAA,IAC/C,GAAG,aAAa;AAAA,IAChB,UAA0B,OAAO,WAAW;AAC1C,aAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,IACzC,GAAG,UAAU;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,IACR,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,EACV;AAAA,EACA,MAAsB,OAAO,WAAW;AACtC,SAAK,gBAAgB,CAAC;AACtB,SAAK,cAAc,CAAC;AACpB,SAAK,OAAO,MAAM;AAClB,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AACA,SAAK,cAAc;AACnB,YAAQ,WAAW,CAAC;AAAA,EACtB,GAAG,MAAM;AAAA,EACT,WAA2B,OAAO,SAAS,KAAK,KAAK,KAAK,KAAK;AAC7D,QAAI,IAAI,GAAG,MAAM,QAAQ;AACvB,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,EACF,GAAG,WAAW;AAAA,EACd,cAA8B,OAAO,SAAS,QAAQ,QAAQ,OAAO,OAAO;AAC1E,UAAM,QAAQ;AACd,QAAI,MAAM;AACV,aAAS,SAAS,MAAM;AACtB,aAAuB,OAAO,SAAS,iBAAiB,MAAM;AAC5D;AACA,cAAM,IAAI,MAAM,cAAc,SAAS,MAAM;AAC7C,cAAM,UAAU,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AACrE,cAAM,UAAU,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AACnE,cAAM,UAAU,OAAO,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AAC5E,cAAM,UAAU,OAAO,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AAC1E,YAAI,EAAE,SAAS,eAAe;AAC5B,gBAAM,UAAU,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AACrE,gBAAM,UAAU,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AACnE,gBAAM,UAAU,OAAO,MAAM,UAAU,SAAS,IAAI,KAAK,WAAW,KAAK,GAAG;AAC5E,gBAAM,UAAU,OAAO,MAAM,SAAS,QAAQ,IAAI,KAAK,WAAW,KAAK,GAAG;AAAA,QAC5E;AAAA,MACF,GAAG,kBAAkB;AAAA,IACvB;AACA,WAAO,UAAU,UAAU;AAC3B,SAAK,cAAc,QAAQ,SAAS,CAAC;AACrC,SAAK,YAAY,QAAQ,SAAS,YAAY,CAAC;AAAA,EACjD,GAAG,cAAc;AAAA,EACjB,QAAwB,OAAO,SAAS,QAAQ,QAAQ,OAAO,OAAO;AACpE,UAAM,UAAU,eAAe,OAAO,QAAQ,KAAK;AACnD,UAAM,SAAS,eAAe,OAAO,QAAQ,KAAK;AAClD,UAAM,UAAU,eAAe,OAAO,QAAQ,KAAK;AACnD,UAAM,SAAS,eAAe,OAAO,QAAQ,KAAK;AAClD,SAAK,UAAU,OAAO,MAAM,UAAU,SAAS,KAAK,GAAG;AACvD,SAAK,UAAU,OAAO,MAAM,UAAU,SAAS,KAAK,GAAG;AACvD,SAAK,UAAU,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AACrD,SAAK,UAAU,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG;AACrD,SAAK,aAAa,SAAS,SAAS,QAAQ,MAAM;AAAA,EACpD,GAAG,QAAQ;AAAA,EACX,eAA+B,OAAO,SAAS,SAAS,UAAU,QAAQ;AACxE,UAAM,YAAY,OAAO,IAAI,QAAQ,IAAI;AACzC,UAAM,cAAc,iBAAiB,QAAQ,IAAI,EAAE,UAAU;AAC7D,UAAM,IAAI,UAAU,IAAI,UAAU,QAAQ,KAAK,cAAc,KAAK,KAAK,kBAAkB;AACzF,SAAK,YAAY,KAAK;AAAA,MACpB,QAAQ;AAAA,MACR,QAAQ,KAAK,cAAc;AAAA,MAC3B,OAAO,IAAI,KAAK;AAAA,MAChB,OAAO;AAAA,MACP,OAAO,QAAQ;AAAA,MACf,UAAU,gBAAgB,cAAc,QAAQ;AAAA,IAClD,CAAC;AAAA,EACH,GAAG,eAAe;AAAA,EAClB,eAA+B,OAAO,SAAS,SAAS;AACtD,UAAM,yBAAyB,KAAK,YAAY,IAAI,SAAS,YAAY;AACvE,aAAO,WAAW;AAAA,IACpB,CAAC,EAAE,YAAY,QAAQ,IAAI;AAC3B,WAAO,KAAK,YAAY,OAAO,wBAAwB,CAAC,EAAE,CAAC;AAAA,EAC7D,GAAG,eAAe;AAAA,EAClB,YAA4B,OAAO,SAAS,QAAQ,EAAE,SAAS,QAAQ,MAAM,OAAO,OAAO,OAAO,GAAG,MAAM;AACzG,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ,KAAK;AAAA,MACb,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO,MAAM;AAAA,MACb,MAAM,MAAM;AAAA,MACZ,OAAO,MAAM;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,EACF,GAAG,YAAY;AAAA,EACf,SAAyB,OAAO,SAAS,QAAQ,EAAE,SAAS,QAAQ,MAAM,OAAO,OAAO,OAAO,GAAG,MAAM;AACtG,SAAK,cAAc,KAAK,KAAK,WAAW,OAAO,IAAI,CAAC;AAAA,EACtD,GAAG,SAAS;AAAA,EACZ,SAAyB,OAAO,WAAW;AACzC,WAAO,KAAK,cAAc,IAAI;AAAA,EAChC,GAAG,SAAS;AAAA,EACZ,eAA+B,OAAO,WAAW;AAC/C,WAAO,KAAK,cAAc,SAAS,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,EAAE,UAAU;AAAA,EACjG,GAAG,eAAe;AAAA,EAClB,kBAAkC,OAAO,SAAS,SAAS;AACzD,UAAM,OAAO,KAAK,cAAc,IAAI;AACpC,SAAK,WAAW,KAAK,YAAY,CAAC;AAClC,SAAK,gBAAgB,KAAK,iBAAiB,CAAC;AAC5C,SAAK,SAAS,KAAK,EAAE,GAAG,OAAO,eAAe,GAAG,QAAQ,EAAE,CAAC;AAC5D,SAAK,cAAc,KAAK,OAAO;AAC/B,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B,GAAG,kBAAkB;AAAA,EACrB,iBAAiC,OAAO,WAAW;AACjD,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,mBAAmB,KAAK;AAAA,IAC/B;AAAA,EACF,GAAG,iBAAiB;AAAA,EACpB,kBAAkC,OAAO,WAAW;AAClD,QAAI,KAAK,cAAc,GAAG;AACxB,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF,GAAG,kBAAkB;AAAA,EACrB,iBAAiC,OAAO,SAAS,MAAM;AACrD,SAAK,cAAc,KAAK,cAAc;AACtC,SAAK,KAAK,QAAQ,eAAe,OAAO,KAAK,KAAK,OAAO,KAAK,WAAW;AAAA,EAC3E,GAAG,iBAAiB;AAAA,EACpB,gBAAgC,OAAO,WAAW;AAChD,WAAO,KAAK;AAAA,EACd,GAAG,gBAAgB;AAAA,EACnB,WAA2B,OAAO,WAAW;AAC3C,WAAO,EAAE,QAAQ,KAAK,MAAM,QAAQ,KAAK,OAAO;AAAA,EAClD,GAAG,WAAW;AAChB;AACA,IAAI,WAA2B,OAAO,eAAe,MAAM,WAAW;AACpE,SAAO,gBAAgB,KAAK,SAAS;AACrC,YAAU,SAAS,KAAK;AACxB,YAAU,SAAS,OAAO,eAAe;AACzC,QAAM,OAAO,YAAY;AACzB,OAAK,IAAI,UAAU;AACnB,OAAK,IAAI,UAAU;AACnB,OAAK,QAAQ,UAAU,SAAS,KAAK;AACrC,OAAK,QAAQ;AACb,QAAM,IAAI,KAAK,OAAO,GAAG;AACzB,QAAM,WAAW,gBAAgB,SAAS,GAAG,IAAI;AACjD,QAAM,UAAU,WAAW;AAC3B,UAAQ,IAAI,UAAU;AACtB,UAAQ,IAAI,UAAU;AACtB,UAAQ,QAAQ,KAAK;AACrB,UAAQ,KAAK;AACb,UAAQ,OAAO,UAAU;AACzB,UAAQ,QAAQ;AAChB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,WAAW,KAAK;AACxB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,SAAS,KAAK;AACtB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,SAAS;AACjB,QAAM,WAAW,SAAS,QAAQ,IAAI,IAAI,MAAM,UAAU,GAAG,OAAO,IAAI,SAAS,GAAG,OAAO;AAC3F,QAAM,aAAa,KAAK;AAAA,IACtB,SAAS,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,IAAI;AAAA,EAClG;AACA,WAAS,KAAK,UAAU,aAAa,IAAI,KAAK,UAAU;AACxD,YAAU,UAAU,aAAa,IAAI,KAAK;AAC1C,SAAO,gBAAgB,aAAa,IAAI,KAAK,UAAU;AACvD,YAAU,QAAQ,UAAU,SAAS,aAAa,IAAI,KAAK;AAC3D,YAAU,QAAQ,UAAU,SAAS,KAAK;AAC1C,SAAO,OAAO,UAAU,QAAQ,UAAU,QAAQ,UAAU,OAAO,UAAU,KAAK;AAClF,SAAO,OAAO,QAAQ,SAAS;AACjC,GAAG,UAAU;AACb,IAAI,cAA8B,OAAO,CAAC,QAAQ;AAChD,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GAAG,aAAa;AAChB,IAAI,WAA2B,OAAO,CAAC,QAAQ;AAC7C,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,CAAC,QAAQ;AAC9C,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GAAG,WAAW;AACd,eAAe,aAAa,UAAU,UAAU;AAC9C,SAAO,gBAAgB,EAAE;AACzB,QAAM,EAAE,QAAQ,OAAO,QAAQ,IAAI;AACnC,QAAM,QAAQ,eAAe,YAAY,OAAO,EAAE;AAClD,QAAM,aAAa,SAAS,OAAO;AACnC,QAAM,WAAW,aAAa,MAAM,0BAA0B,SAAS,WAAW,CAAC,IAAI,cAAc,wBAAwB,SAAS,YAAY,IAAI,CAAC;AACvJ,MAAI,CAAC,YAAY;AACf,UAAM,aAAa,SAAS,SAAS;AACrC,aAAS,UAAU;AACnB,WAAO,gBAAgB,UAAU;AAAA,EACnC;AACA,MAAI;AACJ,MAAI,cAAc,SAAS,SAAS;AACpC,QAAM,YAAY,SAAS;AAC3B,MAAI,WAAW,OAAO;AACpB,iBAAa,OAAO,eAAe,IAAI;AACvC,QAAI,CAAC,KAAK,aAAa;AACrB,qBAAe,KAAK;AACpB,mBAAa,OAAO,eAAe,IAAI;AAAA,IACzC;AACA,mBAAe;AACf,UAAM,KAAK,eAAe,OAAO,YAAY,GAAG,KAAK,QAAQ,CAAC;AAC9D,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,eAAe,IAAI,KAAK;AAAA,MAC/B,QAAQ;AAAA,MACR,OAAO,eAAe,IAAI,KAAK;AAAA,IACjC;AAAA,EACF,OAAO;AACL,mBAAe,KAAK;AACpB,iBAAa,OAAO,eAAe,IAAI;AACvC,WAAO,OAAO,QAAQ,aAAa,IAAI,OAAO,UAAU;AAAA,EAC1D;AACA,SAAO,gBAAgB,WAAW;AAClC,WAAS,UAAU;AACnB,WAAS,QAAQ,SAAS,SAAS,SAAS;AAC5C,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,SAAS,UAAU,SAAS,KAAK;AACrF,SAAO;AACT;AACA,OAAO,cAAc,cAAc;AACnC,IAAI,cAA8B,OAAO,eAAe,UAAU,UAAU,YAAY,SAAS;AAC/F,QAAM,EAAE,QAAQ,OAAO,QAAQ,SAAS,MAAM,eAAe,gBAAgB,IAAI;AACjF,QAAM,WAAW,cAAc,wBAAwB,SAAS,YAAY,IAAI,CAAC;AACjF,QAAM,UAAU,WAAW;AAC3B,UAAQ,IAAI;AACZ,UAAQ,IAAI,SAAS;AACrB,UAAQ,QAAQ,QAAQ;AACxB,UAAQ,QAAQ;AAChB,UAAQ,KAAK;AACb,UAAQ,OAAO;AACf,UAAQ,aAAa,KAAK;AAC1B,UAAQ,WAAW,KAAK;AACxB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,SAAS,KAAK;AACtB,UAAQ,SAAS;AACjB,UAAQ,aAAa,KAAK;AAC1B,UAAQ,QAAQ;AAChB,MAAI,SAAS,QAAQ,IAAI,GAAG;AAC1B,UAAM,UAAU,UAAU,SAAS,EAAE,QAAQ,OAAO,QAAQ,WAAW,CAAC;AAAA,EAC1E,OAAO;AACL,aAAS,UAAU,OAAO;AAAA,EAC5B;AACA,QAAM,YAAY,SAAS;AAC3B,MAAI;AACJ,MAAI,WAAW,OAAO;AACpB,QAAI,KAAK,aAAa;AACpB,aAAO,SAAS,OAAO,MAAM,EAAE;AAAA,QAC7B;AAAA,QACA,MAAM,MAAM,IAAI,UAAU,MAAM,SAAS,eAAe,OAAO,KAAK,QAAQ,GAAG,YAAY,CAAC,CAAC,MAAM,aAAa,EAAE,MAAM,MAAM;AAAA,MAChI;AAAA,IACF,OAAO;AACL,aAAO,SAAS,OAAO,MAAM,EAAE;AAAA,QAC7B;AAAA,QACA,OAAO,SAAS,MAAM,aAAa,SAAS,SAAS,MAAM,OAAO,aAAa,MAAM,OAAO,SAAS,MAAM,OAAO,aAAa,MAAM,MAAM,SAAS,OAAO,aAAa;AAAA,MAC1K;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,SAAS,OAAO,MAAM;AAC7B,SAAK,KAAK,MAAM,MAAM;AACtB,SAAK,KAAK,MAAM,UAAU;AAC1B,SAAK,KAAK,MAAM,KAAK;AACrB,SAAK,KAAK,MAAM,UAAU;AAAA,EAC5B;AACA,MAAI,SAAS,QAAQ,GAAG,SAAS,UAAU,SAAS,QAAQ,GAAG,SAAS,gBAAgB,SAAS,QAAQ,GAAG,SAAS,gBAAgB,SAAS,QAAQ,GAAG,SAAS,eAAe,SAAS,QAAQ,GAAG,SAAS,sBAAsB;AAClO,SAAK,MAAM,oBAAoB,MAAM;AACrC,SAAK,KAAK,SAAS,cAAc;AAAA,EACnC,OAAO;AACL,SAAK,KAAK,SAAS,cAAc;AAAA,EACnC;AACA,MAAI,MAAM;AACV,MAAI,KAAK,qBAAqB;AAC5B,UAAM,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,OAAO,OAAO,SAAS,WAAW,OAAO,SAAS;AAC1G,UAAM,IAAI,QAAQ,OAAO,KAAK;AAC9B,UAAM,IAAI,QAAQ,OAAO,KAAK;AAAA,EAChC;AACA,OAAK,KAAK,gBAAgB,CAAC;AAC3B,OAAK,KAAK,UAAU,MAAM;AAC1B,OAAK,MAAM,QAAQ,MAAM;AACzB,MAAI,SAAS,QAAQ,GAAG,SAAS,SAAS,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAC7E,SAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,EACtD;AACA,MAAI,SAAS,QAAQ,GAAG,SAAS,uBAAuB,SAAS,QAAQ,GAAG,SAAS,sBAAsB;AACzG,SAAK,KAAK,gBAAgB,SAAS,MAAM,aAAa;AACtD,SAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,EACtD;AACA,MAAI,SAAS,QAAQ,GAAG,SAAS,eAAe,SAAS,QAAQ,GAAG,SAAS,cAAc;AACzF,SAAK,KAAK,cAAc,SAAS,MAAM,eAAe;AAAA,EACxD;AACA,MAAI,SAAS,QAAQ,GAAG,SAAS,eAAe,SAAS,QAAQ,GAAG,SAAS,cAAc;AACzF,SAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,EACtD;AACA,MAAI,mBAAmB,KAAK,qBAAqB;AAC/C,SAAK,KAAK,gBAAgB,SAAS,MAAM,kBAAkB;AAC3D,aAAS,OAAO,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,eAAe,YAAY,EAAE,KAAK,aAAa,MAAM,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,aAAa;AAAA,EAClN;AACF,GAAG,aAAa;AAChB,IAAI,wBAAwC,OAAO,SAAS,UAAU,QAAQ,eAAe,WAAW,aAAa,UAAU,UAAU;AACvI,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,aAAW,YAAY,WAAW;AAChC,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAM,MAAM,MAAM;AAClB,QAAI,WAAW,WAAW,KAAK;AAC7B,UAAI,CAAC,UAAU;AACb,eAAO,OAAO,OAAO,OAAO;AAAA,MAC9B;AACA,oBAAc,KAAK,YAAY,QAAQ;AAAA,IACzC;AACA,QAAI,OAAO,OAAO,SAAS;AACzB,UAAI,CAAC,UAAU;AACb,YAAI,IAAI,YAAY;AACpB,YAAI,IAAI;AAAA,MACV;AACA,oBAAc,IAAI;AAAA,IACpB;AACA,UAAM,QAAQ,MAAM,SAAS,KAAK;AAClC,UAAM,SAAS,eAAe,OAAO,MAAM,UAAU,KAAK,QAAQ,KAAK,MAAM;AAC7E,UAAM,SAAS,MAAM,UAAU,KAAK;AACpC,gBAAY,eAAe,OAAO,WAAW,MAAM,MAAM;AACzD,QAAI,cAAc,IAAI,MAAM,IAAI,GAAG;AACjC,oBAAc,MAAM,QAAQ;AAAA,IAC9B;AACA,UAAM,IAAI,YAAY;AACtB,UAAM,SAAS,OAAO,eAAe;AACrC,WAAO,OAAO,MAAM,GAAG,aAAa,MAAM,IAAI,MAAM,OAAO,MAAM,MAAM;AACvE,iBAAa,MAAM,QAAQ;AAC3B,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,QAAQ,YAAY,IAAI,SAAS,MAAM,IAAI;AAAA,IACvD;AACA,iBAAa,MAAM;AACnB,cAAU,MAAM;AAChB,WAAO,OAAO,SAAS,KAAK;AAAA,EAC9B;AACA,MAAI,WAAW,CAAC,UAAU;AACxB,WAAO,OAAO,OAAO,OAAO;AAAA,EAC9B;AACA,SAAO,gBAAgB,SAAS;AAClC,GAAG,uBAAuB;AAC1B,IAAI,aAA6B,OAAO,eAAe,UAAU,QAAQ,WAAW,UAAU;AAC5F,MAAI,CAAC,UAAU;AACb,eAAW,YAAY,WAAW;AAChC,YAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,YAAM,gBAAgB,UAAU,UAAU,OAAO,MAAM,KAAK;AAAA,IAC9D;AAAA,EACF,OAAO;AACL,QAAI,YAAY;AAChB,WAAO,gBAAgB,KAAK,YAAY,CAAC;AACzC,eAAW,YAAY,WAAW;AAChC,YAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAI,CAAC,MAAM,OAAO;AAChB,cAAM,QAAQ,OAAO,eAAe;AAAA,MACtC;AACA,YAAM,SAAS,MAAM,gBAAgB,UAAU,UAAU,OAAO,MAAM,IAAI;AAC1E,kBAAY,eAAe,OAAO,WAAW,MAAM;AAAA,IACrD;AACA,WAAO,gBAAgB,YAAY,KAAK,SAAS;AAAA,EACnD;AACF,GAAG,YAAY;AACf,IAAI,kBAAkC,OAAO,SAAS,UAAU,QAAQ,WAAW,KAAK;AACtF,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,aAAW,YAAY,WAAW;AAChC,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,UAAM,eAAe,sBAAsB,KAAK;AAChD,UAAM,iBAAiB,gBAAgB;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACF;AACA,QAAI,eAAe,SAAS,WAAW;AACrC,kBAAY,eAAe;AAAA,IAC7B;AACA,QAAI,eAAe,QAAQ,MAAM,IAAI,UAAU;AAC7C,iBAAW,eAAe,QAAQ,MAAM;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,EAAE,WAAW,SAAS;AAC/B,GAAG,iBAAiB;AACpB,IAAI,UAA0B,OAAO,SAAS,KAAK;AACjD,0BAAwB,MAAM,GAAG;AACjC,MAAI,IAAI,YAAY;AAClB,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,oBAAoB,IAAI;AAAA,EAC5E;AACA,MAAI,IAAI,UAAU;AAChB,SAAK,gBAAgB,KAAK,eAAe,KAAK,kBAAkB,IAAI;AAAA,EACtE;AACA,MAAI,IAAI,YAAY;AAClB,SAAK,kBAAkB,KAAK,iBAAiB,KAAK,oBAAoB,IAAI;AAAA,EAC5E;AACF,GAAG,SAAS;AACZ,IAAI,mBAAmC,OAAO,SAAS,OAAO;AAC5D,SAAO,OAAO,YAAY,OAAO,SAAS,YAAY;AACpD,WAAO,WAAW,UAAU;AAAA,EAC9B,CAAC;AACH,GAAG,kBAAkB;AACrB,IAAI,mBAAmC,OAAO,SAAS,OAAO,QAAQ;AACpE,QAAM,WAAW,OAAO,IAAI,KAAK;AACjC,QAAM,cAAc,iBAAiB,KAAK;AAC1C,QAAM,OAAO,YAAY;AAAA,IACvB,SAAS,KAAK,YAAY;AACxB,aAAO,eAAe,OAAO,KAAK,WAAW,MAAM;AAAA,IACrD;AAAA,IACA,SAAS,IAAI,SAAS,QAAQ,IAAI;AAAA,EACpC;AACA,QAAM,QAAQ,YAAY;AAAA,IACxB,SAAS,KAAK,YAAY;AACxB,aAAO,eAAe,OAAO,KAAK,WAAW,KAAK;AAAA,IACpD;AAAA,IACA,SAAS,IAAI,SAAS,QAAQ,IAAI;AAAA,EACpC;AACA,SAAO,CAAC,MAAM,KAAK;AACrB,GAAG,kBAAkB;AACrB,SAAS,wBAAwB,YAAY,KAAK,WAAW,YAAY,WAAW;AAClF,SAAO,gBAAgB,SAAS;AAChC,MAAI,eAAe;AACnB,MAAI,IAAI,MAAM,IAAI,WAAW,WAAW,IAAI,EAAE,GAAG;AAC/C,UAAM,YAAY,WAAW,IAAI,EAAE,EAAE;AACrC,UAAM,WAAW,YAAY,IAAI;AACjC,QAAI,UAAU,cAAc,UAAU,IAAI,IAAI,OAAO,KAAK,YAAY,IAAI,KAAK,aAAa,QAAQ;AACpG,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,UAAM,WAAW,cAAc,wBAAwB,IAAI,SAAS,QAAQ;AAC5E,UAAM,cAAc,eAAe,OAAO,SAAS,QAAQ,KAAK,cAAc;AAC9E,mBAAe,aAAa;AAC5B,QAAI,MAAM,GAAG,WAAW,MAAM,IAAI,OAAO,EAAE;AAAA,EAC7C;AACA,YAAU,GAAG;AACb,SAAO,gBAAgB,YAAY;AACrC;AACA,OAAO,yBAAyB,yBAAyB;AACzD,SAAS,2BAA2B,KAAK,UAAU,YAAY,OAAO,QAAQ,eAAe,iBAAiB;AAC5G,WAAS,mBAAmB,OAAO,YAAY;AAC7C,QAAI,MAAM,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,GAAG;AACpC,aAAO;AAAA,QACL,SAAS,QAAQ;AAAA,QACjB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,QAAQ,SAAS,QAAQ;AAAA,IACpC,OAAO;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ;AAAA,QACjB,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,QAAQ,SAAS,QAAQ;AAAA,IACpC;AAAA,EACF;AACA,SAAO,oBAAoB,oBAAoB;AAC/C,WAAS,iBAAiB,OAAO,YAAY;AAC3C,QAAI,MAAM,IAAI,OAAO,IAAI,IAAI,EAAE,EAAE,GAAG;AAClC,aAAO;AAAA,QACL,SAAS,SAAS;AAAA,QAClB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,SAAS,SAAS,SAAS;AAAA,IACtC,OAAO;AACL,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,SAAS;AAAA,QAClB,SAAS,QAAQ,MAAM,SAAS,IAAI,KAAK;AAAA,MAC3C;AACA,eAAS,SAAS,SAAS,SAAS;AAAA,IACtC;AAAA,EACF;AACA,SAAO,kBAAkB,kBAAkB;AAC3C,MAAI,cAAc,IAAI,IAAI,EAAE,KAAK,OAAO;AACtC,UAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAC/B,UAAM,aAAa,MAAM,QAAQ,UAAU,mBAAmB,IAAI,IAAI,MAAM,QAAQ,IAAI;AACxF,uBAAmB,OAAO,UAAU;AACpC,UAAM,SAAS,aAAa,MAAM,SAAS;AAC3C,WAAO,gBAAgB,MAAM,SAAS,CAAC;AAAA,EACzC,WAAW,gBAAgB,IAAI,IAAI,IAAI,KAAK,OAAO;AACjD,UAAM,QAAQ,OAAO,IAAI,IAAI,IAAI;AACjC,QAAI,KAAK,cAAc;AACrB,YAAM,aAAa,MAAM,QAAQ,UAAU,mBAAmB,IAAI,MAAM,QAAQ;AAChF,uBAAiB,OAAO,UAAU;AAAA,IACpC;AACA,UAAM,QAAQ,aAAa,MAAM,SAAS;AAC1C,WAAO,gBAAgB,MAAM,SAAS,CAAC;AAAA,EACzC,WAAW,gBAAgB,IAAI,IAAI,EAAE,KAAK,OAAO;AAC/C,UAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAC/B,QAAI,KAAK,cAAc;AACrB,YAAM,aAAa,MAAM,QAAQ,UAAU,mBAAmB,IAAI,IAAI,MAAM,QAAQ,IAAI;AACxF,yBAAmB,OAAO,UAAU;AAAA,IACtC;AACA,UAAM,QAAQ,aAAa,MAAM,SAAS;AAC1C,WAAO,gBAAgB,MAAM,SAAS,CAAC;AAAA,EACzC;AACF;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,IAAI,OAAuB,OAAO,eAAe,OAAO,IAAI,UAAU,SAAS;AAC7E,QAAM,EAAE,eAAe,SAAS,IAAI,WAAW;AAC/C,SAAO;AACP,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAO,MAAM;AACjH,QAAM,MAAM,kBAAkB,YAAY,eAAe,MAAM,EAAE,CAAC,EAAE,kBAAkB;AACtF,SAAO,KAAK;AACZ,MAAI,MAAM,QAAQ,EAAE;AACpB,QAAM,WAAW,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,eAAO,QAAQ,EAAE,IAAI;AAClG,QAAM,SAAS,QAAQ,GAAG,UAAU;AACpC,QAAM,gBAAgB,QAAQ,GAAG,iBAAiB;AAClD,QAAM,kBAAkB,QAAQ,GAAG,mBAAmB;AACtD,QAAM,QAAQ,QAAQ,GAAG,SAAS;AAClC,MAAI,YAAY,QAAQ,GAAG,aAAa;AACxC,QAAM,WAAW,QAAQ,GAAG,YAAY;AACxC,QAAM,QAAQ,QAAQ,GAAG,gBAAgB;AACzC,QAAM,WAAW,QAAQ,GAAG,iBAAiB;AAC7C,QAAM,eAAe,QAAQ,GAAG,0BAA0B;AAC1D,QAAM,0BAA0B,MAAM,2BAA2B,QAAQ,UAAU,OAAO;AAC1F,OAAK,SAAS,MAAM,sBAAsB,QAAQ,yBAAyB,KAAK;AAChF,kBAAgB,mBAAmB,QAAQ;AAC3C,kBAAgB,mBAAmB,QAAQ;AAC3C,kBAAgB,gBAAgB,QAAQ;AACxC,MAAI,UAAU;AACZ,WAAO,gBAAgB,KAAK,SAAS;AACrC,QAAI,cAAc;AAChB,aAAO,gBAAgB,MAAM,CAAC,EAAE,aAAa;AAAA,IAC/C;AAAA,EACF;AACA,MAAI,KAAK,2BAA2B,MAAM;AACxC,UAAM,YAA4B,oBAAI,IAAI;AAC1C,aAAS,QAAQ,CAAC,YAAY;AAC5B,gBAAU,IAAI,QAAQ,IAAI;AAC1B,gBAAU,IAAI,QAAQ,EAAE;AAAA,IAC1B,CAAC;AACD,gBAAY,UAAU,OAAO,CAAC,aAAa,UAAU,IAAI,QAAQ,CAAC;AAAA,EACpE;AACA,wBAAsB,UAAU,QAAQ,eAAe,WAAW,GAAG,UAAU,KAAK;AACpF,QAAM,aAAa,MAAM,oBAAoB,UAAU,QAAQ,yBAAyB,OAAO;AAC/F,kBAAgB,gBAAgB,QAAQ;AACxC,kBAAgB,qBAAqB,QAAQ;AAC7C,kBAAgB,sBAAsB,QAAQ;AAC9C,kBAAgB,qBAAqB,QAAQ;AAC7C,WAAS,UAAU,KAAK,aAAa;AACnC,UAAM,iBAAiB,OAAO,cAAc,GAAG;AAC/C,QAAI,eAAe,SAAS,KAAK,aAAa;AAC5C,qBAAe,SAAS,cAAc;AACtC,qBAAe;AAAA,IACjB;AACA,oBAAgB;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,IAAI,IAAI,EAAE;AAAA,IAC7B;AACA,WAAO,OAAO,eAAe,QAAQ,cAAc,IAAI,eAAe,OAAO,WAAW;AAAA,EAC1F;AACA,SAAO,WAAW,WAAW;AAC7B,MAAI,gBAAgB;AACpB,MAAI,oBAAoB;AACxB,QAAM,iBAAiB,CAAC;AACxB,QAAM,cAAc,CAAC;AACrB,MAAI,QAAQ;AACZ,aAAW,OAAO,UAAU;AAC1B,QAAI,WAAW,WAAW;AAC1B,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,QAAQ,GAAG,SAAS;AACvB,eAAO,iBAAiB;AACxB,oBAAY,IAAI;AAChB,cAAM,SAAS,UAAU,SAAS;AAClC;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,eAAO,cAAc,KAAK,UAAU,MAAM;AAC1C;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,kBAAU,KAAK,OAAO,eAAe,CAAC;AACtC;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAgB,SAAS,UAAU,WAAW,QAAQ,IAAI;AAChE,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,QAAQ,QAAQ,QAAQ,OAAO;AAAA,QACrD;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,oBAAY,KAAK,SAAS;AAC1B,eAAO,OAAO,QAAQ,SAAS;AAC/B,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAgB,SAAS,UAAU,WAAW,OAAO,IAAI;AAC/D,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK,YAAY,KAAK;AAAA,UACtB,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,iBAAiB,OAAO;AAAA,QAC9C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAgB,SAAS,UAAU,WAAW,OAAO,IAAI;AAC/D,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA,eAAO,gBAAgB;AACvB;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK,YAAY,KAAK;AAAA,UACtB,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,iBAAiB,OAAO;AAAA,QAC9C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAgB,SAAS,UAAU,WAAW,OAAO,IAAI;AAC/D,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,wBAAgB,IAAI,QAAQ,SAAS;AACrC,4BAAoB,IAAI,QAAQ,QAAQ;AACxC,YAAI,IAAI,QAAQ,SAAS;AACvB,kBAAQ,GAAG,sBAAsB;AAAA,QACnC,OAAO;AACL,kBAAQ,GAAG,uBAAuB;AAAA,QACpC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK,YAAY,KAAK;AAAA,UACtB,KAAK;AAAA,UACL,CAAC,YAAY,OAAO,iBAAiB,OAAO;AAAA,QAC9C;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAgB,SAAS,UAAU,WAAW,YAAY,IAAI;AACpE,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AAAA,UACE;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY,KAAK;AAAA,UACtB,CAAC,YAAY,OAAO,QAAQ,OAAO;AAAA,QACrC;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB,oBAAY,OAAO,QAAQ;AAC3B,cAAM,gBAAgB,SAAS,UAAU,WAAW,SAAS,IAAI;AACjE,eAAO,gBAAgB,UAAU,QAAQ,OAAO,eAAe,CAAC;AAChE,eAAO,OAAO,QAAQ,SAAS;AAC/B;AAAA,MACF;AACE,YAAI;AACF,qBAAW,IAAI;AACf,mBAAS,SAAS,OAAO,eAAe;AACxC,mBAAS,gBAAgB;AACzB,mBAAS,kBAAkB,QAAQ,GAAG,oBAAoB;AAC1D,gBAAM,aAAa,MAAM,aAAa,UAAU,QAAQ;AACxD;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,yBAAe,KAAK,EAAE,cAAc,UAAU,WAAW,CAAC;AAC1D,iBAAO,OAAO,WAAW,QAAQ;AAAA,QACnC,SAAS,GAAG;AACV,cAAI,MAAM,+BAA+B,CAAC;AAAA,QAC5C;AAAA,IACJ;AACA,QAAI;AAAA,MACF,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,MACpB,QAAQ,GAAG,SAAS;AAAA,IACtB,EAAE,SAAS,IAAI,IAAI,GAAG;AACpB,sBAAgB,gBAAgB;AAAA,IAClC;AACA;AAAA,EACF;AACA,MAAI,MAAM,iBAAiB,aAAa;AACxC,MAAI,MAAM,mBAAmB,eAAe;AAC5C,QAAM,WAAW,UAAU,QAAQ,WAAW,KAAK;AACnD,aAAW,KAAK,gBAAgB;AAC9B,UAAM,YAAY,UAAU,EAAE,cAAc,EAAE,YAAY,OAAO;AAAA,EACnE;AACA,MAAI,KAAK,cAAc;AACrB,UAAM,WAAW,UAAU,QAAQ,WAAW,IAAI;AAAA,EACpD;AACA,cAAY,QAAQ,CAAC,MAAM,gBAAgB,mBAAmB,UAAU,CAAC,CAAC;AAC1E,qBAAmB,UAAU,QAAQ,WAAW,IAAI;AACpD,aAAW,QAAQ,OAAO,OAAO,OAAO;AACtC,SAAK,SAAS,OAAO,eAAe,IAAI,KAAK;AAC7C,WAAO,OAAO,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM;AAC9D,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,KAAK;AACnB,SAAK,QAAQ,KAAK,SAAS,KAAK;AAChC,SAAK,QAAQ,KAAK,SAAS,KAAK;AAChC,SAAK,SAAS;AACd,oBAAgB,QAAQ,UAAU,MAAM,IAAI;AAAA,EAC9C;AACA,MAAI,UAAU;AACZ,WAAO,gBAAgB,KAAK,SAAS;AAAA,EACvC;AACA,QAAM,kBAAkB,gBAAgB,UAAU,QAAQ,WAAW,GAAG;AACxE,QAAM,EAAE,QAAQ,IAAI,IAAI,OAAO,UAAU;AACzC,MAAI,IAAI,WAAW,QAAQ;AACzB,QAAI,SAAS;AAAA,EACf;AACA,MAAI,IAAI,WAAW,QAAQ;AACzB,QAAI,SAAS;AAAA,EACf;AACA,MAAI,IAAI,UAAU,QAAQ;AACxB,QAAI,QAAQ;AAAA,EACd;AACA,MAAI,IAAI,UAAU,QAAQ;AACxB,QAAI,QAAQ;AAAA,EACd;AACA,MAAI,YAAY,IAAI,QAAQ,IAAI;AAChC,MAAI,YAAY,gBAAgB,WAAW;AACzC,gBAAY,gBAAgB;AAAA,EAC9B;AACA,MAAI,SAAS,YAAY,IAAI,KAAK;AAClC,MAAI,KAAK,cAAc;AACrB,aAAS,SAAS,KAAK,YAAY,KAAK;AAAA,EAC1C;AACA,MAAI,WAAW,IAAI,QAAQ,IAAI;AAC/B,MAAI,WAAW,gBAAgB,UAAU;AACvC,eAAW,gBAAgB;AAAA,EAC7B;AACA,QAAM,QAAQ,WAAW,IAAI,KAAK;AAClC,MAAI,OAAO;AACT,aAAS,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,IAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,KAAK,cAAc,EAAE,KAAK,KAAK,GAAG;AAAA,EACrH;AACA,mBAAiB,UAAU,QAAQ,OAAO,KAAK,WAAW;AAC1D,QAAM,oBAAoB,QAAQ,KAAK;AACvC,WAAS;AAAA,IACP;AAAA,IACA,IAAI,SAAS,KAAK,iBAAiB,QAAQ,KAAK,iBAAiB,qBAAqB,MAAM,QAAQ,OAAO,SAAS;AAAA,EACtH;AACA,MAAI,MAAM,WAAW,OAAO,MAAM;AACpC,GAAG,MAAM;AACT,eAAe,2BAA2B,QAAQ,UAAU,SAAS;AACnE,QAAM,0BAA0B,CAAC;AACjC,aAAW,OAAO,UAAU;AAC1B,QAAI,OAAO,IAAI,IAAI,EAAE,KAAK,OAAO,IAAI,IAAI,IAAI,GAAG;AAC9C,YAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAC/B,UAAI,IAAI,cAAc,QAAQ,GAAG,UAAU,UAAU,CAAC,MAAM,WAAW;AACrE;AAAA,MACF;AACA,UAAI,IAAI,cAAc,QAAQ,GAAG,UAAU,WAAW,CAAC,MAAM,WAAW;AACtE;AAAA,MACF;AACA,YAAM,SAAS,IAAI,cAAc;AACjC,YAAM,YAAY,CAAC;AACnB,YAAM,WAAW,SAAS,SAAS,IAAI,IAAI,YAAY,IAAI;AAC3D,YAAM,iBAAiB,IAAI,OAAO,cAAc,UAAU,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,IAAI;AAC1H,YAAM,oBAAoB,SAAS,cAAc,IAAI,MAAM,0BAA0B,IAAI,SAAS,WAAW,CAAC,IAAI,cAAc,wBAAwB,gBAAgB,QAAQ;AAChL,YAAM,eAAe,kBAAkB,QAAQ,IAAI,KAAK;AACxD,UAAI,aAAa,IAAI,SAAS,MAAM,WAAW;AAC7C,gCAAwB,IAAI,EAAE,IAAI,eAAe;AAAA,UAC/C,wBAAwB,IAAI,EAAE,KAAK;AAAA,UACnC;AAAA,QACF;AAAA,MACF,WAAW,aAAa,IAAI,SAAS,MAAM,WAAW;AACpD,gCAAwB,IAAI,IAAI,IAAI,eAAe;AAAA,UACjD,wBAAwB,IAAI,IAAI,KAAK;AAAA,UACrC;AAAA,QACF;AAAA,MACF,WAAW,aAAa,IAAI,SAAS,IAAI,IAAI;AAC3C,gCAAwB,IAAI,IAAI,IAAI,eAAe;AAAA,UACjD,wBAAwB,IAAI,IAAI,KAAK;AAAA,UACrC,eAAe;AAAA,QACjB;AACA,gCAAwB,IAAI,EAAE,IAAI,eAAe;AAAA,UAC/C,wBAAwB,IAAI,EAAE,KAAK;AAAA,UACnC,eAAe;AAAA,QACjB;AAAA,MACF,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,SAAS;AACzD,gCAAwB,IAAI,IAAI,IAAI,eAAe;AAAA,UACjD,wBAAwB,IAAI,IAAI,KAAK;AAAA,UACrC;AAAA,QACF;AAAA,MACF,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,QAAQ;AACxD,gCAAwB,MAAM,SAAS,IAAI,eAAe;AAAA,UACxD,wBAAwB,MAAM,SAAS,KAAK;AAAA,UAC5C;AAAA,QACF;AAAA,MACF,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,MAAM;AACtD,YAAI,MAAM,WAAW;AACnB,kCAAwB,MAAM,SAAS,IAAI,eAAe;AAAA,YACxD,wBAAwB,MAAM,SAAS,KAAK;AAAA,YAC5C,eAAe;AAAA,UACjB;AAAA,QACF;AACA,YAAI,MAAM,WAAW;AACnB,kCAAwB,IAAI,IAAI,IAAI,eAAe;AAAA,YACjD,wBAAwB,IAAI,IAAI,KAAK;AAAA,YACrC,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,4BAA4B,uBAAuB;AAC7D,SAAO;AACT;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,IAAI,wBAAwC,OAAO,SAAS,OAAO;AACjE,MAAI,qBAAqB;AACzB,QAAM,WAAW,UAAU,IAAI;AAC/B,aAAW,OAAO,MAAM,OAAO;AAC7B,UAAM,kBAAkB,cAAc,wBAAwB,KAAK,QAAQ;AAC3E,UAAM,aAAa,gBAAgB,QAAQ,IAAI,KAAK,cAAc,IAAI,KAAK;AAC3E,QAAI,qBAAqB,YAAY;AACnC,2BAAqB;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT,GAAG,uBAAuB;AAC1B,eAAe,sBAAsB,QAAQ,qBAAqB,OAAO;AACvE,MAAI,YAAY;AAChB,aAAW,QAAQ,OAAO,KAAK,GAAG;AAChC,UAAM,QAAQ,OAAO,IAAI,IAAI;AAC7B,QAAI,MAAM,MAAM;AACd,YAAM,cAAc,cAAc;AAAA,QAChC,MAAM;AAAA,QACN,KAAK,QAAQ,IAAI,KAAK;AAAA,QACtB,UAAU,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM,UAAU,SAAS,MAAM,WAAW,IAAI,MAAM,0BAA0B,MAAM,aAAa,WAAW,CAAC,IAAI,cAAc,wBAAwB,MAAM,aAAa,UAAU,IAAI,CAAC;AACzL,UAAM,QAAQ,MAAM,OAAO,KAAK,QAAQ,eAAe,OAAO,KAAK,OAAO,QAAQ,QAAQ,IAAI,KAAK,WAAW;AAC9G,UAAM,SAAS,MAAM,OAAO,eAAe,OAAO,QAAQ,QAAQ,KAAK,MAAM,IAAI,KAAK;AACtF,gBAAY,eAAe,OAAO,WAAW,MAAM,MAAM;AAAA,EAC3D;AACA,aAAW,YAAY,qBAAqB;AAC1C,UAAM,QAAQ,OAAO,IAAI,QAAQ;AACjC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,YAAY,OAAO,IAAI,MAAM,SAAS;AAC5C,QAAI,CAAC,WAAW;AACd,YAAM,gBAAgB,oBAAoB,QAAQ;AAClD,YAAM,cAAc,gBAAgB,KAAK,cAAc,MAAM,QAAQ;AACrE,YAAM,SAAS,eAAe,OAAO,aAAa,KAAK,WAAW;AAClE;AAAA,IACF;AACA,UAAM,eAAe,oBAAoB,QAAQ;AACjD,UAAM,aAAa,eAAe,KAAK,cAAc,MAAM,QAAQ,IAAI,UAAU,QAAQ;AACzF,UAAM,SAAS,eAAe,OAAO,YAAY,KAAK,WAAW;AAAA,EACnE;AACA,MAAI,eAAe;AACnB,QAAM,QAAQ,CAAC,QAAQ;AACrB,UAAM,WAAW,YAAY,IAAI;AACjC,QAAI,aAAa,IAAI,UAAU,OAAO,CAAC,OAAO,SAAS;AACrD,aAAO,SAAS,OAAO,IAAI,IAAI,EAAE,SAAS,OAAO,IAAI,IAAI,EAAE,UAAU;AAAA,IACvE,GAAG,CAAC;AACJ,kBAAc,IAAI,KAAK;AACvB,QAAI,IAAI,MAAM;AACZ,UAAI,OAAO,cAAc,UAAU,IAAI,MAAM,aAAa,IAAI,KAAK,aAAa,QAAQ;AAAA,IAC1F;AACA,UAAM,mBAAmB,cAAc,wBAAwB,IAAI,MAAM,QAAQ;AACjF,mBAAe,eAAe,OAAO,iBAAiB,QAAQ,YAAY;AAC1E,UAAM,WAAW,eAAe,OAAO,YAAY,iBAAiB,QAAQ,IAAI,KAAK,WAAW;AAChG,QAAI,SAAS,KAAK;AAClB,QAAI,aAAa,UAAU;AACzB,YAAM,WAAW,WAAW,cAAc;AAC1C,UAAI,UAAU;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,CAAC,QAAQ,IAAI,gBAAgB,YAAY;AACvD,SAAO,eAAe,OAAO,WAAW,KAAK,MAAM;AACrD;AACA,OAAO,uBAAuB,uBAAuB;AACrD,IAAI,iBAAiC,OAAO,eAAe,KAAK,QAAQ,SAAS;AAC/E,QAAM,YAAY,OAAO,IAAI,IAAI,IAAI;AACrC,QAAM,UAAU,OAAO,IAAI,IAAI,EAAE;AACjC,QAAM,SAAS,UAAU;AACzB,QAAM,QAAQ,QAAQ;AACtB,QAAM,aAAa,IAAI,QAAQ,IAAI;AACnC,MAAI,iBAAiB,SAAS,IAAI,OAAO,IAAI,MAAM,0BAA0B,IAAI,SAAS,WAAW,CAAC,IAAI,cAAc;AAAA,IACtH,aAAa,cAAc,UAAU,IAAI,SAAS,KAAK,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI;AAAA,IACpF,SAAS,IAAI;AAAA,EACf;AACA,QAAM,YAAY;AAAA,IAChB,OAAO,aAAa,KAAK,QAAQ,eAAe,OAAO,KAAK,OAAO,eAAe,QAAQ,IAAI,KAAK,UAAU;AAAA,IAC7G,QAAQ;AAAA,IACR,QAAQ,UAAU;AAAA,IAClB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS,IAAI;AAAA,EACf;AACA,MAAI,IAAI,cAAc,QAAQ,GAAG,UAAU,SAAS;AAClD,cAAU,QAAQ,aAAa,eAAe,OAAO,KAAK,OAAO,eAAe,KAAK,IAAI,eAAe;AAAA,MACtG,UAAU,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACtC,eAAe,QAAQ,IAAI,KAAK;AAAA,IAClC;AACA,cAAU,SAAS,UAAU,UAAU,QAAQ,KAAK,eAAe;AAAA,EACrE,WAAW,IAAI,cAAc,QAAQ,GAAG,UAAU,QAAQ;AACxD,cAAU,QAAQ,aAAa,eAAe,OAAO,KAAK,OAAO,eAAe,QAAQ,IAAI,KAAK,UAAU,IAAI,eAAe;AAAA,MAC5H,UAAU,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACtC,eAAe,QAAQ,IAAI,KAAK;AAAA,IAClC;AACA,cAAU,SAAS,SAAS,UAAU,SAAS,UAAU,QAAQ,KAAK,eAAe;AAAA,EACvF,WAAW,IAAI,OAAO,IAAI,MAAM;AAC9B,qBAAiB,cAAc;AAAA,MAC7B,aAAa,cAAc,UAAU,IAAI,SAAS,eAAe,OAAO,KAAK,OAAO,UAAU,KAAK,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI;AAAA,MAC5H,SAAS,IAAI;AAAA,IACf;AACA,cAAU,QAAQ,aAAa,eAAe,OAAO,KAAK,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,UAAU,OAAO,KAAK,OAAO,eAAe,QAAQ,IAAI,KAAK,UAAU;AACjL,cAAU,SAAS,UAAU,UAAU,QAAQ,UAAU,SAAS;AAAA,EACpE,OAAO;AACL,cAAU,QAAQ,KAAK,IAAI,SAAS,UAAU,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,EAAE,IAAI,KAAK;AAC9F,cAAU,SAAS,SAAS,QAAQ,SAAS,UAAU,QAAQ,IAAI,KAAK,cAAc,IAAI,QAAQ,QAAQ,QAAQ,IAAI,KAAK,cAAc;AAAA,EAC3I;AACA,MAAI,YAAY;AACd,cAAU,UAAU,cAAc;AAAA,MAChC,IAAI;AAAA,MACJ,UAAU,QAAQ,IAAI,KAAK;AAAA,MAC3B,SAAS,IAAI;AAAA,IACf;AAAA,EACF;AACA,MAAI;AAAA,IACF,OAAO,UAAU,MAAM,IAAI,UAAU,KAAK,IAAI,UAAU,MAAM,IAAI,UAAU,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU,MAAM,IAAI,IAAI,OAAO;AAAA,EACzI;AACA,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,oBAAoC,OAAO,SAAS,KAAK,QAAQ,SAAS;AAC5E,MAAI,CAAC;AAAA,IACH,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,IACpB,QAAQ,GAAG,SAAS;AAAA,EACtB,EAAE,SAAS,IAAI,IAAI,GAAG;AACpB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,CAAC,UAAU,SAAS,IAAI,iBAAiB,IAAI,MAAM,MAAM;AAC/D,QAAM,CAAC,QAAQ,OAAO,IAAI,iBAAiB,IAAI,IAAI,MAAM;AACzD,QAAM,iBAAiB,YAAY;AACnC,MAAI,SAAS,iBAAiB,YAAY;AAC1C,MAAI,QAAQ,iBAAiB,SAAS;AACtC,QAAM,sBAAsB,KAAK,IAAI,SAAS,OAAO,IAAI;AACzD,QAAM,cAA8B,OAAO,CAAC,UAAU;AACpD,WAAO,iBAAiB,CAAC,QAAQ;AAAA,EACnC,GAAG,aAAa;AAChB,MAAI,IAAI,SAAS,IAAI,IAAI;AACvB,YAAQ;AAAA,EACV,OAAO;AACL,QAAI,IAAI,YAAY,CAAC,qBAAqB;AACxC,eAAS,YAAY,KAAK,kBAAkB,IAAI,CAAC;AAAA,IACnD;AACA,QAAI,CAAC,CAAC,QAAQ,GAAG,SAAS,YAAY,QAAQ,GAAG,SAAS,WAAW,EAAE,SAAS,IAAI,IAAI,GAAG;AACzF,eAAS,YAAY,CAAC;AAAA,IACxB;AACA,QAAI,CAAC,QAAQ,GAAG,SAAS,qBAAqB,QAAQ,GAAG,SAAS,oBAAoB,EAAE;AAAA,MACtF,IAAI;AAAA,IACN,GAAG;AACD,gBAAU,YAAY,CAAC;AAAA,IACzB;AAAA,EACF;AACA,QAAM,YAAY,CAAC,UAAU,WAAW,QAAQ,OAAO;AACvD,QAAM,eAAe,KAAK,IAAI,SAAS,KAAK;AAC5C,MAAI,IAAI,QAAQ,IAAI,SAAS;AAC3B,QAAI,UAAU,cAAc;AAAA,MAC1B,IAAI;AAAA,MACJ,eAAe,OAAO,eAAe,IAAI,KAAK,aAAa,KAAK,KAAK;AAAA,MACrE,YAAY,IAAI;AAAA,IAClB;AAAA,EACF;AACA,QAAM,UAAU,cAAc,wBAAwB,IAAI,SAAS,YAAY,IAAI,CAAC;AACpF,SAAO;AAAA,IACL,OAAO,eAAe;AAAA,MACpB,IAAI,OAAO,IAAI,QAAQ,QAAQ,IAAI,KAAK;AAAA,MACxC,eAAe,IAAI,KAAK;AAAA,MACxB,KAAK;AAAA,IACP;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS,IAAI;AAAA,IACb,MAAM,IAAI;AAAA,IACV,MAAM,IAAI;AAAA,IACV,YAAY,KAAK,IAAI,MAAM,MAAM,SAAS;AAAA,IAC1C,UAAU,KAAK,IAAI,MAAM,MAAM,SAAS;AAAA,EAC1C;AACF,GAAG,mBAAmB;AACtB,IAAI,sBAAsC,OAAO,eAAe,UAAU,QAAQ,mBAAmB,SAAS;AAC5G,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,CAAC;AACf,MAAI,SAAS,WAAW;AACxB,aAAW,OAAO,UAAU;AAC1B,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB,cAAM,KAAK;AAAA,UACT,IAAI,IAAI;AAAA,UACR,KAAK,IAAI;AAAA,UACT,MAAM,OAAO;AAAA,UACb,IAAI,OAAO;AAAA,UACX,OAAO;AAAA,QACT,CAAC;AACD;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB,YAAI,IAAI,SAAS;AACf,oBAAU,MAAM,IAAI;AACpB,gBAAM,QAAQ,EAAE,IAAI;AACpB,gBAAM,IAAI,EAAE,IAAI;AAChB,gBAAM,KAAK,OAAO;AAAA,QACpB;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AAAA,MACzB,KAAK,QAAQ,GAAG,SAAS;AACvB,kBAAU,MAAM,IAAI;AACpB,cAAM,QAAQ,EAAE,IAAI;AACpB;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AACE,gBAAM,YAAY,OAAO,IAAI,IAAI,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;AAC/D,gBAAM,cAAc,iBAAiB,IAAI,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK,EAAE;AACzE,gBAAM,IAAI,UAAU,IAAI,UAAU,QAAQ,KAAK,cAAc,KAAK,KAAK,kBAAkB;AACzF,gBAAM,QAAQ;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO,IAAI,KAAK;AAAA,YAChB,OAAO,IAAI;AAAA,YACX,SAAS;AAAA,UACX;AACA,iBAAO,YAAY,KAAK,KAAK;AAAA,QAC/B;AACA;AAAA,MACF,KAAK,QAAQ,GAAG,SAAS;AACvB;AACE,gBAAM,yBAAyB,OAAO,YAAY,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,IAAI,IAAI;AAC1F,iBAAO,YAAY,OAAO,wBAAwB,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,QAClE;AACA;AAAA,IACJ;AACA,UAAM,SAAS,IAAI,cAAc;AACjC,QAAI,QAAQ;AACV,kBAAY,MAAM,eAAe,KAAK,QAAQ,OAAO;AACrD,UAAI,YAAY;AAChB,YAAM,QAAQ,CAAC,QAAQ;AACrB,kBAAU;AACV,gBAAQ,OAAO,eAAe,OAAO,QAAQ,MAAM,UAAU,MAAM;AACnE,gBAAQ,KAAK,eAAe,OAAO,QAAQ,IAAI,UAAU,SAAS,UAAU,KAAK;AACjF,gBAAQ,QAAQ,eAAe,OAAO,QAAQ,OAAO,KAAK,IAAI,QAAQ,OAAO,QAAQ,EAAE,CAAC,IAAI,KAAK;AAAA,MACnG,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,kBAAkB,KAAK,QAAQ,OAAO;AACjD,UAAI,WAAW;AACf,UAAI,SAAS,UAAU,SAAS,SAAS,MAAM,SAAS,GAAG;AACzD,cAAM,QAAQ,CAAC,QAAQ;AACrB,oBAAU;AACV,cAAI,SAAS,WAAW,SAAS,OAAO;AACtC,kBAAM,OAAO,OAAO,IAAI,IAAI,IAAI;AAChC,kBAAM,KAAK,OAAO,IAAI,IAAI,EAAE;AAC5B,oBAAQ,OAAO,eAAe;AAAA,cAC5B,KAAK,IAAI,SAAS,QAAQ;AAAA,cAC1B,KAAK,IAAI,KAAK,QAAQ;AAAA,cACtB,QAAQ;AAAA,YACV;AACA,oBAAQ,KAAK,eAAe;AAAA,cAC1B,GAAG,IAAI,SAAS,QAAQ;AAAA,cACxB,GAAG,IAAI,KAAK,QAAQ;AAAA,cACpB,QAAQ;AAAA,YACV;AACA,oBAAQ,QAAQ,eAAe,OAAO,QAAQ,OAAO,KAAK,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK;AAAA,UACnG,OAAO;AACL,oBAAQ,OAAO,eAAe,OAAO,SAAS,QAAQ,QAAQ,IAAI;AAClE,oBAAQ,KAAK,eAAe,OAAO,SAAS,OAAO,QAAQ,EAAE;AAC7D,oBAAQ,QAAQ,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,KAAK;AAAA,UAC9E;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO,cAAc,CAAC;AACtB,MAAI,MAAM,qBAAqB,KAAK;AACpC,SAAO;AACT,GAAG,qBAAqB;AACxB,IAAI,2BAA2B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,IAAI,KAAK;AACP,WAAO,IAAI,WAAW;AAAA,EACxB;AAAA,EACA,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAsB,OAAO,CAAC,QAAQ;AACpC,QAAI,CAAC,IAAI,UAAU;AACjB,UAAI,WAAW,CAAC;AAAA,IAClB;AACA,QAAI,IAAI,MAAM;AACZ,UAAI,SAAS,OAAO,IAAI;AACxB,iBAAU,EAAE,UAAU,EAAE,MAAM,IAAI,KAAK,EAAE,CAAC;AAAA,IAC5C;AAAA,EACF,GAAG,MAAM;AACX;", "names": ["import_dist", "_a"]}