{"name": "md", "type": "module", "version": "2.0.3", "private": false, "description": "WeChat Markdown Editor | 一款高度简洁的微信 Markdown 编辑器：支持 Markdown 语法、自定义主题样式、内容管理、多图床、AI 助手等特性", "homepage": "https://github.com/doocs/md", "repository": {"type": "git", "url": "https://github.com/doocs/md"}, "bugs": {"url": "https://github.com/doocs/md/issues"}, "scripts": {"start": "npm run dev", "dev": "vite --host", "build": "run-p type-check \"build:only {@}\" --", "build:only": "vite build", "build:h5-netlify": "run-p type-check \"build:h5-netlify:only {@}\" --", "build:h5-netlify:only": "cross-env SERVER_ENV=NETLIFY vite build", "build:cli": "npm run build && npx shx rm -rf md-cli/dist && npx shx rm -rf dist/**/*.map && npx shx cp -r dist md-cli/ && cd md-cli && npm pack", "build:analyze": "cross-env ANALYZE=true vite build", "compile:extension": "npm --prefix ./src/extension run compile", "preview": "npm run build && vite preview", "preview:pages": "cross-env CF_PAGES=1 npm run build:h5-netlify && wrangler pages dev ./dist", "release:cli": "node ./scripts/release.js", "ext:dev": "wxt", "ext:zip": "wxt zip", "firefox:dev": "wxt -b firefox", "firefox:zip": "wxt zip -b firefox", "lint": "eslint . --fix", "type-check": "vue-tsc --build --force", "postinstall": "simple-git-hooks && wxt prepare", "package:extension": "npm --prefix ./src/extension run package"}, "dependencies": {"@aws-sdk/client-s3": "^3.777.0", "@aws-sdk/s3-request-presigner": "^3.777.0", "@ssttevee/multipart-parser": "^0.1.9", "@vee-validate/yup": "^4.15.0", "@vueuse/core": "^12.5.0", "axios": "^1.8.4", "buffer-from": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^5.65.19", "core-js": "^3.40.0", "cos-js-sdk-v5": "^1.8.7", "crypto-js": "^4.2.0", "csstype": "^3.1.3", "es-toolkit": "^1.34.1", "form-data": "4.0.1", "front-matter": "^4.0.2", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "isomorphic-dompurify": "^2.25.0", "juice": "^11.0.0", "lucide-vue-next": "^0.473.0", "marked": "^15.0.6", "mermaid": "^11.6.0", "node-fetch": "^3.3.2", "nvm": "^0.0.4", "pinia": "^3.0.1", "qiniu-js": "^3.4.2", "radix-vue": "^1.9.12", "reading-time": "^1.5.0", "reka-ui": "^2.3.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tiny-oss": "^0.5.1", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-pick-colors": "^1.8.0", "vue-sonner": "^1.3.0", "yup": "^1.6.1"}, "devDependencies": {"@antfu/eslint-config": "3.11.0", "@cloudflare/workers-types": "^4.20250419.0", "@types/buffer-from": "^1.1.3", "@types/codemirror": "^5.60.15", "@types/crypto-js": "^4.2.2", "@types/mdast": "^4.0.4", "@types/node": "^22.13.14", "@types/uuid": "^10.0.0", "@unocss/eslint-plugin": "^0.64.1", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.23.0", "eslint-plugin-format": "^0.1.2", "less": "^4.2.2", "npm-run-all": "^4.1.5", "postcss": "^8.5.1", "prettier": "3.3.3", "rollup-plugin-visualizer": "^5.12.0", "shx": "^0.3.4", "simple-git-hooks": "^2.12.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "unocss": "^66.0.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.3.4", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-radar": "^0.10.0", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.0", "wrangler": "^4.12.0", "wxt": "^0.20.7"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": "npm run lint"}}