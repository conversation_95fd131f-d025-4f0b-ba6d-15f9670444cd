{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/sankeyDiagram-QLVOVGJD.mjs", "../../d3-sankey/src/index.js", "../../d3-sankey/src/sankey.js", "../../d3-sankey/node_modules/d3-array/src/index.js", "../../d3-sankey/node_modules/d3-array/src/bisect.js", "../../d3-sankey/node_modules/d3-array/src/ascending.js", "../../d3-sankey/node_modules/d3-array/src/bisector.js", "../../d3-sankey/node_modules/d3-array/src/number.js", "../../d3-sankey/node_modules/d3-array/src/count.js", "../../d3-sankey/node_modules/d3-array/src/cross.js", "../../d3-sankey/node_modules/d3-array/src/cumsum.js", "../../d3-sankey/node_modules/d3-array/src/descending.js", "../../d3-sankey/node_modules/d3-array/src/deviation.js", "../../d3-sankey/node_modules/d3-array/src/variance.js", "../../d3-sankey/node_modules/d3-array/src/extent.js", "../../d3-sankey/node_modules/d3-array/src/fsum.js", "../../d3-sankey/node_modules/d3-array/src/group.js", "../../d3-sankey/node_modules/internmap/src/index.js", "../../d3-sankey/node_modules/d3-array/src/identity.js", "../../d3-sankey/node_modules/d3-array/src/groupSort.js", "../../d3-sankey/node_modules/d3-array/src/sort.js", "../../d3-sankey/node_modules/d3-array/src/permute.js", "../../d3-sankey/node_modules/d3-array/src/bin.js", "../../d3-sankey/node_modules/d3-array/src/array.js", "../../d3-sankey/node_modules/d3-array/src/constant.js", "../../d3-sankey/node_modules/d3-array/src/nice.js", "../../d3-sankey/node_modules/d3-array/src/ticks.js", "../../d3-sankey/node_modules/d3-array/src/threshold/sturges.js", "../../d3-sankey/node_modules/d3-array/src/threshold/freedmanDiaconis.js", "../../d3-sankey/node_modules/d3-array/src/quantile.js", "../../d3-sankey/node_modules/d3-array/src/max.js", "../../d3-sankey/node_modules/d3-array/src/min.js", "../../d3-sankey/node_modules/d3-array/src/quickselect.js", "../../d3-sankey/node_modules/d3-array/src/threshold/scott.js", "../../d3-sankey/node_modules/d3-array/src/maxIndex.js", "../../d3-sankey/node_modules/d3-array/src/mean.js", "../../d3-sankey/node_modules/d3-array/src/median.js", "../../d3-sankey/node_modules/d3-array/src/merge.js", "../../d3-sankey/node_modules/d3-array/src/minIndex.js", "../../d3-sankey/node_modules/d3-array/src/pairs.js", "../../d3-sankey/node_modules/d3-array/src/range.js", "../../d3-sankey/node_modules/d3-array/src/least.js", "../../d3-sankey/node_modules/d3-array/src/leastIndex.js", "../../d3-sankey/node_modules/d3-array/src/greatest.js", "../../d3-sankey/node_modules/d3-array/src/greatestIndex.js", "../../d3-sankey/node_modules/d3-array/src/scan.js", "../../d3-sankey/node_modules/d3-array/src/shuffle.js", "../../d3-sankey/node_modules/d3-array/src/sum.js", "../../d3-sankey/node_modules/d3-array/src/transpose.js", "../../d3-sankey/node_modules/d3-array/src/zip.js", "../../d3-sankey/node_modules/d3-array/src/every.js", "../../d3-sankey/node_modules/d3-array/src/some.js", "../../d3-sankey/node_modules/d3-array/src/filter.js", "../../d3-sankey/node_modules/d3-array/src/map.js", "../../d3-sankey/node_modules/d3-array/src/reduce.js", "../../d3-sankey/node_modules/d3-array/src/reverse.js", "../../d3-sankey/node_modules/d3-array/src/difference.js", "../../d3-sankey/node_modules/d3-array/src/disjoint.js", "../../d3-sankey/node_modules/d3-array/src/intersection.js", "../../d3-sankey/node_modules/d3-array/src/set.js", "../../d3-sankey/node_modules/d3-array/src/subset.js", "../../d3-sankey/node_modules/d3-array/src/superset.js", "../../d3-sankey/node_modules/d3-array/src/union.js", "../../d3-sankey/src/align.js", "../../d3-sankey/src/constant.js", "../../d3-sankey/src/sankeyLinkHorizontal.js", "../../d3-sankey/node_modules/d3-shape/src/index.js", "../../d3-sankey/node_modules/d3-shape/src/arc.js", "../../d3-sankey/node_modules/d3-path/src/index.js", "../../d3-sankey/node_modules/d3-path/src/path.js", "../../d3-sankey/node_modules/d3-shape/src/constant.js", "../../d3-sankey/node_modules/d3-shape/src/math.js", "../../d3-sankey/node_modules/d3-shape/src/area.js", "../../d3-sankey/node_modules/d3-shape/src/curve/linear.js", "../../d3-sankey/node_modules/d3-shape/src/line.js", "../../d3-sankey/node_modules/d3-shape/src/point.js", "../../d3-sankey/node_modules/d3-shape/src/pie.js", "../../d3-sankey/node_modules/d3-shape/src/descending.js", "../../d3-sankey/node_modules/d3-shape/src/identity.js", "../../d3-sankey/node_modules/d3-shape/src/areaRadial.js", "../../d3-sankey/node_modules/d3-shape/src/curve/radial.js", "../../d3-sankey/node_modules/d3-shape/src/lineRadial.js", "../../d3-sankey/node_modules/d3-shape/src/pointRadial.js", "../../d3-sankey/node_modules/d3-shape/src/link/index.js", "../../d3-sankey/node_modules/d3-shape/src/array.js", "../../d3-sankey/node_modules/d3-shape/src/symbol.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/circle.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/cross.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/diamond.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/star.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/square.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/triangle.js", "../../d3-sankey/node_modules/d3-shape/src/symbol/wye.js", "../../d3-sankey/node_modules/d3-shape/src/curve/basisClosed.js", "../../d3-sankey/node_modules/d3-shape/src/noop.js", "../../d3-sankey/node_modules/d3-shape/src/curve/basis.js", "../../d3-sankey/node_modules/d3-shape/src/curve/basisOpen.js", "../../d3-sankey/node_modules/d3-shape/src/curve/bundle.js", "../../d3-sankey/node_modules/d3-shape/src/curve/cardinalClosed.js", "../../d3-sankey/node_modules/d3-shape/src/curve/cardinal.js", "../../d3-sankey/node_modules/d3-shape/src/curve/cardinalOpen.js", "../../d3-sankey/node_modules/d3-shape/src/curve/catmullRomClosed.js", "../../d3-sankey/node_modules/d3-shape/src/curve/catmullRom.js", "../../d3-sankey/node_modules/d3-shape/src/curve/catmullRomOpen.js", "../../d3-sankey/node_modules/d3-shape/src/curve/linearClosed.js", "../../d3-sankey/node_modules/d3-shape/src/curve/monotone.js", "../../d3-sankey/node_modules/d3-shape/src/curve/natural.js", "../../d3-sankey/node_modules/d3-shape/src/curve/step.js", "../../d3-sankey/node_modules/d3-shape/src/stack.js", "../../d3-sankey/node_modules/d3-shape/src/offset/none.js", "../../d3-sankey/node_modules/d3-shape/src/order/none.js", "../../d3-sankey/node_modules/d3-shape/src/offset/expand.js", "../../d3-sankey/node_modules/d3-shape/src/offset/diverging.js", "../../d3-sankey/node_modules/d3-shape/src/offset/silhouette.js", "../../d3-sankey/node_modules/d3-shape/src/offset/wiggle.js", "../../d3-sankey/node_modules/d3-shape/src/order/appearance.js", "../../d3-sankey/node_modules/d3-shape/src/order/ascending.js", "../../d3-sankey/node_modules/d3-shape/src/order/descending.js", "../../d3-sankey/node_modules/d3-shape/src/order/insideOut.js", "../../d3-sankey/node_modules/d3-shape/src/order/reverse.js"], "sourcesContent": ["import {\n  __name,\n  clear,\n  common_default,\n  defaultConfig2 as defaultConfig,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 9], $V1 = [1, 10], $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SANKEY\": 4, \"NEWLINE\": 5, \"csv\": 6, \"opt_eof\": 7, \"record\": 8, \"csv_tail\": 9, \"EOF\": 10, \"field[source]\": 11, \"COMMA\": 12, \"field[target]\": 13, \"field[value]\": 14, \"field\": 15, \"escaped\": 16, \"non_escaped\": 17, \"DQUOTE\": 18, \"ESCAPED_TEXT\": 19, \"NON_ESCAPED_TEXT\": 20, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SANKEY\", 5: \"NEWLINE\", 10: \"EOF\", 11: \"field[source]\", 12: \"COMMA\", 13: \"field[target]\", 14: \"field[value]\", 18: \"DQUOTE\", 19: \"ESCAPED_TEXT\", 20: \"NON_ESCAPED_TEXT\" },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, { 5: [1, 3] }, { 6: 4, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 1: [2, 6], 7: 11, 10: [1, 12] }, o($V1, [2, 4], { 9: 13, 5: [1, 14] }), { 12: [1, 15] }, o($V2, [2, 8]), o($V2, [2, 9]), { 19: [1, 16] }, o($V2, [2, 11]), { 1: [2, 1] }, { 1: [2, 5] }, o($V1, [2, 2]), { 6: 17, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 15: 18, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 18: [1, 19] }, o($V1, [2, 3]), { 12: [1, 20] }, o($V2, [2, 10]), { 15: 21, 16: 7, 17: 8, 18: $V0, 20: $V1 }, o([1, 5, 10], [2, 7])],\n    defaultActions: { 11: [2, 1], 12: [2, 5] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: { \"csv\": { \"rules\": [1, 2, 3, 4, 5, 6, 7], \"inclusive\": false }, \"escaped_text\": { \"rules\": [6, 7], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */ new Map();\nvar clear2 = /* @__PURE__ */ __name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */ new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */ __name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */ __name((ID) => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */ __name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */ __name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */ __name(() => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10\n} from \"d3\";\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify\n} from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId((d) => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([\n    [0, 0],\n    [width, height]\n  ]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", (d) => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function(d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", (d) => d.x0).attr(\"y\", (d) => d.y0).append(\"rect\").attr(\"height\", (d) => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", (d) => d.x1 - d.x0).attr(\"fill\", (d) => colorScheme(d.id));\n  const getText = /* @__PURE__ */ __name(({ id: id2, value }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", (d) => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", (d) => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", (d) => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", (d) => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", (d) => d.source.x1).attr(\"x2\", (d) => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", (d) => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", (d) => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */ __name((d) => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", (d) => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */ __name((text) => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n      font-family: ${options.fontFamily};\n    }`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = (text) => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  styles: styles_default,\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport {\n  diagram\n};\n", "export {default as sankey} from \"./sankey.js\";\nexport {center as sankey<PERSON><PERSON>, left as sankeyLeft, right as sankeyRight, justify as sankeyJustify} from \"./align.js\";\nexport {default as sankeyLinkHorizontal} from \"./sankeyLinkHorizontal.js\";\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "export {default as bisect, bisectRight, bisectLeft, bisectCenter} from \"./bisect.js\";\nexport {default as ascending} from \"./ascending.js\";\nexport {default as bisector} from \"./bisector.js\";\nexport {default as count} from \"./count.js\";\nexport {default as cross} from \"./cross.js\";\nexport {default as cumsum} from \"./cumsum.js\";\nexport {default as descending} from \"./descending.js\";\nexport {default as deviation} from \"./deviation.js\";\nexport {default as extent} from \"./extent.js\";\nexport {Adder, fsum, fcumsum} from \"./fsum.js\";\nexport {default as group, groups, index, indexes, rollup, rollups} from \"./group.js\";\nexport {default as groupSort} from \"./groupSort.js\";\nexport {default as bin, default as histogram} from \"./bin.js\"; // Deprecated; use bin.\nexport {default as thresholdFreedmanDiaconis} from \"./threshold/freedmanDiaconis.js\";\nexport {default as thresholdScott} from \"./threshold/scott.js\";\nexport {default as thresholdSturges} from \"./threshold/sturges.js\";\nexport {default as max} from \"./max.js\";\nexport {default as maxIndex} from \"./maxIndex.js\";\nexport {default as mean} from \"./mean.js\";\nexport {default as median} from \"./median.js\";\nexport {default as merge} from \"./merge.js\";\nexport {default as min} from \"./min.js\";\nexport {default as minIndex} from \"./minIndex.js\";\nexport {default as nice} from \"./nice.js\";\nexport {default as pairs} from \"./pairs.js\";\nexport {default as permute} from \"./permute.js\";\nexport {default as quantile, quantileSorted} from \"./quantile.js\";\nexport {default as quickselect} from \"./quickselect.js\";\nexport {default as range} from \"./range.js\";\nexport {default as least} from \"./least.js\";\nexport {default as leastIndex} from \"./leastIndex.js\";\nexport {default as greatest} from \"./greatest.js\";\nexport {default as greatestIndex} from \"./greatestIndex.js\";\nexport {default as scan} from \"./scan.js\"; // Deprecated; use leastIndex.\nexport {default as shuffle, shuffler} from \"./shuffle.js\";\nexport {default as sum} from \"./sum.js\";\nexport {default as ticks, tickIncrement, tickStep} from \"./ticks.js\";\nexport {default as transpose} from \"./transpose.js\";\nexport {default as variance} from \"./variance.js\";\nexport {default as zip} from \"./zip.js\";\nexport {default as every} from \"./every.js\";\nexport {default as some} from \"./some.js\";\nexport {default as filter} from \"./filter.js\";\nexport {default as map} from \"./map.js\";\nexport {default as reduce} from \"./reduce.js\";\nexport {default as reverse} from \"./reverse.js\";\nexport {default as sort} from \"./sort.js\";\nexport {default as difference} from \"./difference.js\";\nexport {default as disjoint} from \"./disjoint.js\";\nexport {default as intersection} from \"./intersection.js\";\nexport {default as subset} from \"./subset.js\";\nexport {default as superset} from \"./superset.js\";\nexport {default as union} from \"./union.js\";\nexport {InternMap, InternSet} from \"internmap\";\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "export default function(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function(f) {\n  let delta = f;\n  let compare = f;\n\n  if (f.length === 1) {\n    delta = (d, x) => f(d) - x;\n    compare = ascendingComparator(f);\n  }\n\n  function left(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) < 0) lo = mid + 1;\n      else hi = mid;\n    }\n    return lo;\n  }\n\n  function right(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) > 0) hi = mid;\n      else lo = mid + 1;\n    }\n    return lo;\n  }\n\n  function center(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction ascendingComparator(f) {\n  return (d, x) => ascending(f(d), x);\n}\n", "export default function(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n", "function length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n", "export default function cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}", "export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "import variance from \"./variance.js\";\n\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n", "export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n", "export default function(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n", "export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(value);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "export default function(x) {\n  return x;\n}\n", "import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length === 1\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n", "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f = ascending] = F;\n  if (f.length === 1 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascending(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascending(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(f);\n}\n", "export default function(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n", "import {slice} from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, {tickIncrement} from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\n\nexport default function() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    var m = tz.length;\n    while (tz[0] <= x0) tz.shift(), --m;\n    while (tz[m - 1] > x1) tz.pop(), --m;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    for (i = 0; i < n; ++i) {\n      x = values[i];\n      if (x0 <= x && x <= x1) {\n        bins[bisect(tz, x, 0, m)].push(data[i]);\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), histogram) : threshold;\n  };\n\n  return histogram;\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {tickIncrement} from \"./ticks.js\";\n\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n", "var e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nexport default function(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    let r0 = Math.round(start / step), r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step), r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0\n      ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)\n      : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nexport function tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;\n  else if (error >= e5) step1 *= 5;\n  else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}\n", "import count from \"../count.js\";\n\nexport default function(values) {\n  return Math.ceil(Math.log(count(values)) / Math.LN2) + 1;\n}\n", "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function(values, min, max) {\n  return Math.ceil((max - min) / (2 * (quantile(values, 0.75) - quantile(values, 0.25)) * Math.pow(count(values), -1 / 3)));\n}\n", "import max from \"./max.js\";\nimport min from \"./min.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = array.length - 1, compare = ascending) {\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n", "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function(values, min, max) {\n  return Math.ceil((max - min) / (3.5 * deviation(values) * Math.pow(count(values), -1 / 3)));\n}\n", "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n", "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n", "import quantile from \"./quantile.js\";\n\nexport default function(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "export default function minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n", "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n", "export default function(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n", "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n", "import leastIndex from \"./leastIndex.js\";\n\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n", "export default shuffler(Math.random);\n\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import min from \"./min.js\";\n\nexport default function(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n", "import transpose from \"./transpose.js\";\n\nexport default function() {\n  return transpose(arguments);\n}\n", "export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n", "export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n", "export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n", "export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n", "export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n", "export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n", "export default function difference(values, ...others) {\n  values = new Set(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n", "export default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new Set();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n", "import set from \"./set.js\";\n\nexport default function intersection(values, ...others) {\n  values = new Set(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n", "export default function set(values) {\n  return values instanceof Set ? values : new Set(values);\n}\n", "import superset from \"./superset.js\";\n\nexport default function subset(values, other) {\n  return superset(other, values);\n}\n", "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    if (set.has(o)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      set.add(value);\n      if (Object.is(o, value)) break;\n    }\n  }\n  return true;\n}\n", "export default function union(...others) {\n  const set = new Set();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "export {default as arc} from \"./arc.js\";\nexport {default as area} from \"./area.js\";\nexport {default as line} from \"./line.js\";\nexport {default as pie} from \"./pie.js\";\nexport {default as areaRadial, default as radialArea} from \"./areaRadial.js\"; // Note: radialArea is deprecated!\nexport {default as lineRadial, default as radialLine} from \"./lineRadial.js\"; // Note: radialLine is deprecated!\nexport {default as pointRadial} from \"./pointRadial.js\";\nexport {linkHorizontal, linkVertical, linkRadial} from \"./link/index.js\";\n\nexport {default as symbol, symbols} from \"./symbol.js\";\nexport {default as symbolCircle} from \"./symbol/circle.js\";\nexport {default as symbolCross} from \"./symbol/cross.js\";\nexport {default as symbolDiamond} from \"./symbol/diamond.js\";\nexport {default as symbolSquare} from \"./symbol/square.js\";\nexport {default as symbolStar} from \"./symbol/star.js\";\nexport {default as symbolTriangle} from \"./symbol/triangle.js\";\nexport {default as symbolWye} from \"./symbol/wye.js\";\n\nexport {default as curveBasisClosed} from \"./curve/basisClosed.js\";\nexport {default as curveBasisOpen} from \"./curve/basisOpen.js\";\nexport {default as curveBasis} from \"./curve/basis.js\";\nexport {default as curveBundle} from \"./curve/bundle.js\";\nexport {default as curveCardinalClosed} from \"./curve/cardinalClosed.js\";\nexport {default as curveCardinalOpen} from \"./curve/cardinalOpen.js\";\nexport {default as curveCardinal} from \"./curve/cardinal.js\";\nexport {default as curveCatmullRomClosed} from \"./curve/catmullRomClosed.js\";\nexport {default as curveCatmullRomOpen} from \"./curve/catmullRomOpen.js\";\nexport {default as curveCatmullRom} from \"./curve/catmullRom.js\";\nexport {default as curveLinearClosed} from \"./curve/linearClosed.js\";\nexport {default as curveLinear} from \"./curve/linear.js\";\nexport {monotoneX as curveMonotoneX, monotoneY as curveMonotoneY} from \"./curve/monotone.js\";\nexport {default as curveNatural} from \"./curve/natural.js\";\nexport {default as curveStep, stepAfter as curveStepAfter, stepBefore as curveStepBefore} from \"./curve/step.js\";\n\nexport {default as stack} from \"./stack.js\";\nexport {default as stackOffsetExpand} from \"./offset/expand.js\";\nexport {default as stackOffsetDiverging} from \"./offset/diverging.js\";\nexport {default as stackOffsetNone} from \"./offset/none.js\";\nexport {default as stackOffsetSilhouette} from \"./offset/silhouette.js\";\nexport {default as stackOffsetWiggle} from \"./offset/wiggle.js\";\nexport {default as stackOrderAppearance} from \"./order/appearance.js\";\nexport {default as stackOrderAscending} from \"./order/ascending.js\";\nexport {default as stackOrderDescending} from \"./order/descending.js\";\nexport {default as stackOrderInsideOut} from \"./order/insideOut.js\";\nexport {default as stackOrderNone} from \"./order/none.js\";\nexport {default as stackOrderReverse} from \"./order/reverse.js\";\n", "import {path} from \"d3-path\";\nimport constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null;\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle.\n        if (da < pi && (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10))) {\n          var ax = x01 - oc[0],\n              ay = y01 - oc[1],\n              bx = x11 - oc[0],\n              by = y11 - oc[1],\n              kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n              lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n          rc0 = min(rc, (r0 - lc) / (kc - 1));\n          rc1 = min(rc, (r1 - lc) / (kc + 1));\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n", "export {default as path} from \"./path.js\";\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export var abs = Math.abs;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var max = Math.max;\nexport var min = Math.min;\nexport var sin = Math.sin;\nexport var sqrt = Math.sqrt;\n\nexport var epsilon = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n", "import {path} from \"d3-path\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function() {\n  var x0 = pointX,\n      x1 = null,\n      y0 = constant(0),\n      y1 = pointY,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null;\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = data.length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n\n  area.x1 = function(_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n\n  area.y = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n\n  area.y1 = function(_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n\n  area.lineX0 =\n  area.lineY0 = function() {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function() {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function() {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n\n  area.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}\n", "function Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // proceed\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nexport default function(context) {\n  return new Linear(context);\n}\n", "import {path} from \"d3-path\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function() {\n  var x = pointX,\n      y = pointY,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null;\n\n  function line(data) {\n    var i,\n        n = data.length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x(d, i, data), +y(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), line) : x;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), line) : y;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport {tau} from \"./math.js\";\n\nexport default function() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = data.length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n", "export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "export default function(d) {\n  return d;\n}\n", "import curveRadial, {curveRadialLinear} from \"./curve/radial.js\";\nimport area from \"./area.js\";\nimport {lineRadial} from \"./lineRadial.js\";\n\nexport default function() {\n  var a = area().curve(curveRadialLinear),\n      c = a.curve,\n      x0 = a.lineX0,\n      x1 = a.lineX1,\n      y0 = a.lineY0,\n      y1 = a.lineY1;\n\n  a.angle = a.x, delete a.x;\n  a.startAngle = a.x0, delete a.x0;\n  a.endAngle = a.x1, delete a.x1;\n  a.radius = a.y, delete a.y;\n  a.innerRadius = a.y0, delete a.y0;\n  a.outerRadius = a.y1, delete a.y1;\n  a.lineStartAngle = function() { return lineRadial(x0()); }, delete a.lineX0;\n  a.lineEndAngle = function() { return lineRadial(x1()); }, delete a.lineX1;\n  a.lineInnerRadius = function() { return lineRadial(y0()); }, delete a.lineY0;\n  a.lineOuterRadius = function() { return lineRadial(y1()); }, delete a.lineY1;\n\n  a.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return a;\n}\n", "import curveLinear from \"./linear.js\";\n\nexport var curveRadialLinear = curveRadial(curveLinear);\n\nfunction Radial(curve) {\n  this._curve = curve;\n}\n\nRadial.prototype = {\n  areaStart: function() {\n    this._curve.areaStart();\n  },\n  areaEnd: function() {\n    this._curve.areaEnd();\n  },\n  lineStart: function() {\n    this._curve.lineStart();\n  },\n  lineEnd: function() {\n    this._curve.lineEnd();\n  },\n  point: function(a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\n\nexport default function curveRadial(curve) {\n\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n\n  radial._curve = curve;\n\n  return radial;\n}\n", "import curveRadial, {curveRadialLinear} from \"./curve/radial.js\";\nimport line from \"./line.js\";\n\nexport function lineRadial(l) {\n  var c = l.curve;\n\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n\n  l.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return l;\n}\n\nexport default function() {\n  return lineRadial(line().curve(curveRadialLinear));\n}\n", "export default function(x, y) {\n  return [(y = +y) * Math.cos(x -= Math.PI / 2), y * Math.sin(x)];\n}\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "export var slice = Array.prototype.slice;\n", "import {path} from \"d3-path\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport star from \"./symbol/star.js\";\nimport square from \"./symbol/square.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport wye from \"./symbol/wye.js\";\nimport constant from \"./constant.js\";\n\nexport var symbols = [\n  circle,\n  cross,\n  diamond,\n  square,\n  star,\n  triangle,\n  wye\n];\n\nexport default function() {\n  var type = constant(circle),\n      size = constant(64),\n      context = null;\n\n  function symbol() {\n    var buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  symbol.type = function(_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n\n  symbol.size = function(_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n\n  symbol.context = function(_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n\n  return symbol;\n}\n", "import {pi, tau} from \"../math.js\";\n\nexport default {\n  draw: function(context, size) {\n    var r = Math.sqrt(size / pi);\n    context.moveTo(r, 0);\n    context.arc(0, 0, r, 0, tau);\n  }\n};\n", "export default {\n  draw: function(context, size) {\n    var r = Math.sqrt(size / 5) / 2;\n    context.moveTo(-3 * r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, -3 * r);\n    context.lineTo(r, -3 * r);\n    context.lineTo(r, -r);\n    context.lineTo(3 * r, -r);\n    context.lineTo(3 * r, r);\n    context.lineTo(r, r);\n    context.lineTo(r, 3 * r);\n    context.lineTo(-r, 3 * r);\n    context.lineTo(-r, r);\n    context.lineTo(-3 * r, r);\n    context.closePath();\n  }\n};\n", "var tan30 = Math.sqrt(1 / 3),\n    tan30_2 = tan30 * 2;\n\nexport default {\n  draw: function(context, size) {\n    var y = Math.sqrt(size / tan30_2),\n        x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};\n", "import {pi, tau} from \"../math.js\";\n\nvar ka = 0.89081309152928522810,\n    kr = Math.sin(pi / 10) / Math.sin(7 * pi / 10),\n    kx = Math.sin(tau / 10) * kr,\n    ky = -Math.cos(tau / 10) * kr;\n\nexport default {\n  draw: function(context, size) {\n    var r = Math.sqrt(size * ka),\n        x = kx * r,\n        y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (var i = 1; i < 5; ++i) {\n      var a = tau * i / 5,\n          c = Math.cos(a),\n          s = Math.sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\n", "export default {\n  draw: function(context, size) {\n    var w = Math.sqrt(size),\n        x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\n", "var sqrt3 = Math.sqrt(3);\n\nexport default {\n  draw: function(context, size) {\n    var y = -Math.sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};\n", "var c = -0.5,\n    s = Math.sqrt(3) / 2,\n    k = 1 / Math.sqrt(12),\n    a = (k / 2 + 1) * 3;\n\nexport default {\n  draw: function(context, size) {\n    var r = Math.sqrt(size / a),\n        x0 = r / 2,\n        y0 = r * k,\n        x1 = x0,\n        y1 = r * k + r,\n        x2 = -x1,\n        y2 = y1;\n    context.moveTo(x0, y0);\n    context.lineTo(x1, y1);\n    context.lineTo(x2, y2);\n    context.lineTo(c * x0 - s * y0, s * x0 + c * y0);\n    context.lineTo(c * x1 - s * y1, s * x1 + c * y1);\n    context.lineTo(c * x2 - s * y2, s * x2 + c * y2);\n    context.lineTo(c * x0 + s * y0, c * y0 - s * x0);\n    context.lineTo(c * x1 + s * y1, c * y1 - s * x1);\n    context.lineTo(c * x2 + s * y2, c * y2 - s * x2);\n    context.closePath();\n  }\n};\n", "import noop from \"../noop.js\";\nimport {point} from \"./basis.js\";\n\nfunction BasisClosed(context) {\n  this._context = context;\n}\n\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x2, this._y2);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x2, this._y2);\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x2 = x, this._y2 = y; break;\n      case 1: this._point = 2; this._x3 = x, this._y3 = y; break;\n      case 2: this._point = 3; this._x4 = x, this._y4 = y; this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6); break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisClosed(context);\n}\n", "export default function() {}\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    (2 * that._x0 + that._x1) / 3,\n    (2 * that._y0 + that._y1) / 3,\n    (that._x0 + 2 * that._x1) / 3,\n    (that._y0 + 2 * that._y1) / 3,\n    (that._x0 + 4 * that._x1 + x) / 6,\n    (that._y0 + 4 * that._y1 + y) / 6\n  );\n}\n\nexport function Basis(context) {\n  this._context = context;\n}\n\nBasis.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 3: point(this, this._x1, this._y1); // proceed\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6); // proceed\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new Basis(context);\n}\n", "import {point} from \"./basis.js\";\n\nfunction BasisOpen(context) {\n  this._context = context;\n}\n\nBasisOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; var x0 = (this._x0 + 4 * this._x1 + x) / 6, y0 = (this._y0 + 4 * this._y1 + y) / 6; this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0); break;\n      case 3: this._point = 4; // proceed\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisOpen(context);\n}\n", "import {Basis} from \"./basis.js\";\n\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(\n          this._beta * x[i] + (1 - this._beta) * (x0 + t * dx),\n          this._beta * y[i] + (1 - this._beta) * (y0 + t * dy)\n        );\n      }\n    }\n\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\nexport default (function custom(beta) {\n\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function(beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n})(0.85);\n", "import noop from \"../noop.js\";\nimport {point} from \"./cardinal.js\";\n\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    that._x1 + that._k * (that._x2 - that._x0),\n    that._y1 + that._k * (that._y2 - that._y0),\n    that._x2 + that._k * (that._x1 - x),\n    that._y2 + that._k * (that._y1 - y),\n    that._x2,\n    that._y2\n  );\n}\n\nexport function Cardinal(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinal.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: point(this, this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; this._x1 = x, this._y1 = y; break;\n      case 2: this._point = 3; // proceed\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new Cardinal(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {point} from \"./cardinal.js\";\n\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // proceed\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {CardinalClosed} from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {epsilon} from \"../math.js\";\nimport {<PERSON>} from \"./cardinal.js\";\n\nexport function point(that, x, y) {\n  var x1 = that._x1,\n      y1 = that._y1,\n      x2 = that._x2,\n      y2 = that._y2;\n\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n        n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n        m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\n\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRom.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: this.point(this._x2, this._y2); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; // proceed\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {<PERSON><PERSON><PERSON>} from \"./cardinalOpen.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // proceed\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import noop from \"../noop.js\";\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._point) this._context.closePath();\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);\n    else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nexport default function(context) {\n  return new LinearClosed(context);\n}\n", "function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n      h1 = x2 - that._x1,\n      s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n      s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n      p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n      y0 = that._y0,\n      x1 = that._x1,\n      y1 = that._y1,\n      dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\n\nfunction MonotoneX(context) {\n  this._context = context;\n}\n\nMonotoneX.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 =\n    this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n      case 3: point(this, this._t0, slope2(this, this._t0)); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    var t1 = NaN;\n\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; point(this, slope2(this, t1 = slope3(this, x, y)), t1); break;\n      default: point(this, this._t0, t1 = slope3(this, x, y)); break;\n    }\n\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n}\n\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\n\nfunction ReflectContext(context) {\n  this._context = context;\n}\n\nReflectContext.prototype = {\n  moveTo: function(x, y) { this._context.moveTo(y, x); },\n  closePath: function() { this._context.closePath(); },\n  lineTo: function(x, y) { this._context.lineTo(y, x); },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }\n};\n\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\n\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}\n", "function Natural(context) {\n  this._context = context;\n}\n\nNatural.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        n = x.length;\n\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n            py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n\n    if (this._line || (this._line !== 0 && n === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n      n = x.length - 1,\n      m,\n      a = new Array(n),\n      b = new Array(n),\n      r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\n\nexport default function(context) {\n  return new Natural(context);\n}\n", "function Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\n\nStep.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // proceed\n      default: {\n        if (this._t <= 0) {\n          this._context.lineTo(this._x, y);\n          this._context.lineTo(x, y);\n        } else {\n          var x1 = this._x * (1 - this._t) + x * this._t;\n          this._context.lineTo(x1, this._y);\n          this._context.lineTo(x1, y);\n        }\n        break;\n      }\n    }\n    this._x = x, this._y = y;\n  }\n};\n\nexport default function(context) {\n  return new Step(context, 0.5);\n}\n\nexport function stepBefore(context) {\n  return new Step(context, 0);\n}\n\nexport function stepAfter(context) {\n  return new Step(context, 1);\n}\n", "import {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nexport default function() {\n  var keys = constant([]),\n      order = orderNone,\n      offset = offsetNone,\n      value = stackValue;\n\n  function stack(data) {\n    var kz = keys.apply(this, arguments),\n        i,\n        m = data.length,\n        n = kz.length,\n        sz = new Array(n),\n        oz;\n\n    for (i = 0; i < n; ++i) {\n      for (var ki = kz[i], si = sz[i] = new Array(m), j = 0, sij; j < m; ++j) {\n        si[j] = sij = [0, +value(data[j], ki, j, data)];\n        sij.data = data[j];\n      }\n      si.key = ki;\n    }\n\n    for (i = 0, oz = order(sz); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function(_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : keys;\n  };\n\n  stack.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n\n  stack.order = function(_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : order;\n  };\n\n  stack.offset = function(_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n\n  return stack;\n}\n", "export default function(series, order) {\n  if (!((n = series.length) > 1)) return;\n  for (var i = 1, j, s0, s1 = series[order[0]], n, m = s1.length; i < n; ++i) {\n    s0 = s1, s1 = series[order[i]];\n    for (j = 0; j < m; ++j) {\n      s1[j][1] += s1[j][0] = isNaN(s0[j][1]) ? s0[j][0] : s0[j][1];\n    }\n  }\n}\n", "export default function(series) {\n  var n = series.length, o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, n, j = 0, m = series[0].length, y; j < m; ++j) {\n    for (y = i = 0; i < n; ++i) y += series[i][j][1] || 0;\n    if (y) for (i = 0; i < n; ++i) series[i][j][1] /= y;\n  }\n  none(series, order);\n}\n", "export default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, j = 0, d, dy, yp, yn, n, m = series[order[0]].length; j < m; ++j) {\n    for (yp = yn = 0, i = 0; i < n; ++i) {\n      if ((dy = (d = series[order[i]][j])[1] - d[0]) > 0) {\n        d[0] = yp, d[1] = yp += dy;\n      } else if (dy < 0) {\n        d[1] = yn, d[0] = yn += dy;\n      } else {\n        d[0] = 0, d[1] = dy;\n      }\n    }\n  }\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  none(series, order);\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0) || !((m = (s0 = series[order[0]]).length) > 0)) return;\n  for (var y = 0, j = 1, s0, m, n; j < m; ++j) {\n    for (var i = 0, s1 = 0, s2 = 0; i < n; ++i) {\n      var si = series[order[i]],\n          sij0 = si[j][1] || 0,\n          sij1 = si[j - 1][1] || 0,\n          s3 = (sij0 - sij1) / 2;\n      for (var k = 0; k < i; ++k) {\n        var sk = series[order[k]],\n            skj0 = sk[j][1] || 0,\n            skj1 = sk[j - 1][1] || 0;\n        s3 += skj0 - skj1;\n      }\n      s1 += sij0, s2 += s3 * sij0;\n    }\n    s0[j - 1][1] += s0[j - 1][0] = y;\n    if (s1) y -= s2 / s1;\n  }\n  s0[j - 1][1] += s0[j - 1][0] = y;\n  none(series, order);\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  var peaks = series.map(peak);\n  return none(series).sort(function(a, b) { return peaks[a] - peaks[b]; });\n}\n\nfunction peak(series) {\n  var i = -1, j = 0, n = series.length, vi, vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  var sums = series.map(sum);\n  return none(series).sort(function(a, b) { return sums[a] - sums[b]; });\n}\n\nexport function sum(series) {\n  var s = 0, i = -1, n = series.length, v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function(series) {\n  return ascending(series).reverse();\n}\n", "import appearance from \"./appearance.js\";\nimport {sum} from \"./ascending.js\";\n\nexport default function(series) {\n  var n = series.length,\n      i,\n      j,\n      sums = series.map(sum),\n      order = appearance(series),\n      top = 0,\n      bottom = 0,\n      tops = [],\n      bottoms = [];\n\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n\n  return bottoms.reverse().concat(tops);\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  return none(series).reverse();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAe,SAAR,kBAAiBC,IAAG,GAAG;AAC5B,SAAOA,KAAI,IAAI,KAAKA,KAAI,IAAI,IAAIA,MAAK,IAAI,IAAI;AAC/C;;;ACFA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAEe,SAAR,iBAAiB,GAAG;AACzB,MAAI,QAAQ;AACZ,MAAI,UAAU;AAEd,MAAI,EAAE,WAAW,GAAG;AAClB,YAAQ,CAAC,GAAGC,OAAM,EAAE,CAAC,IAAIA;AACzB,cAAU,oBAAoB,CAAC;AAAA,EACjC;AAEA,WAASC,MAAKC,IAAGF,IAAG,IAAI,IAAI;AAC1B,QAAI,MAAM,KAAM,MAAK;AACrB,QAAI,MAAM,KAAM,MAAKE,GAAE;AACvB,WAAO,KAAK,IAAI;AACd,YAAM,MAAO,KAAK,OAAQ;AAC1B,UAAI,QAAQA,GAAE,GAAG,GAAGF,EAAC,IAAI,EAAG,MAAK,MAAM;AAAA,UAClC,MAAK;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAEA,WAASG,OAAMD,IAAGF,IAAG,IAAI,IAAI;AAC3B,QAAI,MAAM,KAAM,MAAK;AACrB,QAAI,MAAM,KAAM,MAAKE,GAAE;AACvB,WAAO,KAAK,IAAI;AACd,YAAM,MAAO,KAAK,OAAQ;AAC1B,UAAI,QAAQA,GAAE,GAAG,GAAGF,EAAC,IAAI,EAAG,MAAK;AAAA,UAC5B,MAAK,MAAM;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAEA,WAASI,QAAOF,IAAGF,IAAG,IAAI,IAAI;AAC5B,QAAI,MAAM,KAAM,MAAK;AACrB,QAAI,MAAM,KAAM,MAAKE,GAAE;AACvB,UAAM,IAAID,MAAKC,IAAGF,IAAG,IAAI,KAAK,CAAC;AAC/B,WAAO,IAAI,MAAM,MAAME,GAAE,IAAI,CAAC,GAAGF,EAAC,IAAI,CAAC,MAAME,GAAE,CAAC,GAAGF,EAAC,IAAI,IAAI,IAAI;AAAA,EAClE;AAEA,SAAO,EAAC,MAAAC,OAAM,QAAAG,SAAQ,OAAAD,OAAK;AAC7B;AAEA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,CAAC,GAAGH,OAAM,kBAAU,EAAE,CAAC,GAAGA,EAAC;AACpC;;;AC7CA,IAAAK,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAe,SAAR,eAAiBC,IAAG;AACzB,SAAOA,OAAM,OAAO,MAAM,CAACA;AAC7B;;;AHEA,IAAM,kBAAkB,iBAAS,iBAAS;AACnC,IAAM,cAAc,gBAAgB;AACpC,IAAM,aAAa,gBAAgB;AACnC,IAAM,eAAe,iBAAS,cAAM,EAAE;;;AIP7C,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAI,QAAQ,MAAM;AAEX,IAAI,QAAQ,MAAM;AAClB,IAAI,MAAM,MAAM;;;ACHvB,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAI,MAAM,KAAK,KAAK,EAAE;AAAtB,IACI,KAAK,KAAK,KAAK,EAAE;AADrB,IAEI,KAAK,KAAK,KAAK,CAAC;;;ACFpB,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SACLD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAASD,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAEC,QAAO,MAAM,MAAM,SACzCF,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACnBA,IAAAG,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAWC,UAAS,QAAQ;AAC1B,UAAIA,UAAS,SACLD,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAASD,UAAS,QAAQ;AACxB,WAAKA,SAAQ,QAAQA,QAAO,EAAEC,QAAO,MAAM,MAAM,SACzCF,OAAMC,UAAUD,SAAQ,UAAaC,UAASA,SAAS;AAC7D,QAAAD,OAAMC;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACnBA,IAAAG,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAO,kBAAQ,SAAS,KAAK,MAAM;AAE5B,SAAS,SAAS,QAAQ;AAC/B,SAAO,SAAS,QAAQC,QAAO,KAAK,GAAG,KAAKA,OAAM,QAAQ;AACxD,QAAI,IAAI,MAAM,KAAK,CAAC;AACpB,WAAO,GAAG;AACR,YAAM,IAAI,OAAO,IAAI,MAAM,GAAG,IAAIA,OAAM,IAAI,EAAE;AAC9C,MAAAA,OAAM,IAAI,EAAE,IAAIA,OAAM,IAAI,EAAE;AAC5B,MAAAA,OAAM,IAAI,EAAE,IAAI;AAAA,IAClB;AACA,WAAOA;AAAA,EACT;AACF;;;ACZA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAASC,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAACA,QAAO;AAClB,QAAAD,QAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAASD,UAAS,QAAQ;AACxB,UAAIA,SAAQ,CAAC,QAAQA,QAAO,EAAEC,QAAO,MAAM,GAAG;AAC5C,QAAAF,QAAOC;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACjBA,IAAAG,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,EAAE,OAAO;AAClB;AAEO,SAAS,KAAK,MAAM;AACzB,SAAO,KAAK;AACd;AAEO,SAAS,MAAM,MAAM,GAAG;AAC7B,SAAO,IAAI,IAAI,KAAK;AACtB;AAEO,SAAS,QAAQ,MAAM,GAAG;AAC/B,SAAO,KAAK,YAAY,SAAS,KAAK,QAAQ,IAAI;AACpD;AAEO,SAAS,OAAO,MAAM;AAC3B,SAAO,KAAK,YAAY,SAAS,KAAK,QAChC,KAAK,YAAY,SAAS,IAAI,KAAK,aAAa,WAAW,IAAI,IAC/D;AACR;;;ACtBA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,SAA0BC,IAAG;AAClC,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;;;A9DAA,SAAS,uBAAuBC,IAAG,GAAG;AACpC,SAAO,iBAAiBA,GAAE,QAAQ,EAAE,MAAM,KAAKA,GAAE,QAAQ,EAAE;AAC7D;AAEA,SAAS,uBAAuBA,IAAG,GAAG;AACpC,SAAO,iBAAiBA,GAAE,QAAQ,EAAE,MAAM,KAAKA,GAAE,QAAQ,EAAE;AAC7D;AAEA,SAAS,iBAAiBA,IAAG,GAAG;AAC9B,SAAOA,GAAE,KAAK,EAAE;AAClB;AAEA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE;AACX;AAEA,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE;AACX;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM;AACf;AAEA,SAAS,KAAK,UAAU,IAAI;AAC1B,QAAM,OAAO,SAAS,IAAI,EAAE;AAC5B,MAAI,CAAC,KAAM,OAAM,IAAI,MAAM,cAAc,EAAE;AAC3C,SAAO;AACT;AAEA,SAAS,oBAAoB,EAAC,OAAAC,OAAK,GAAG;AACpC,aAAW,QAAQA,QAAO;AACxB,QAAI,KAAK,KAAK;AACd,QAAI,KAAK;AACT,eAAWC,SAAQ,KAAK,aAAa;AACnC,MAAAA,MAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;AAAA,IACb;AACA,eAAWA,SAAQ,KAAK,aAAa;AACnC,MAAAA,MAAK,KAAK,KAAKA,MAAK,QAAQ;AAC5B,YAAMA,MAAK;AAAA,IACb;AAAA,EACF;AACF;AAEe,SAAR,SAA0B;AAC/B,MAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACjC,MAAI,KAAK;AACT,MAAI,KAAK,GAAG;AACZ,MAAI,KAAK;AACT,MAAI,QAAQ;AACZ,MAAIC;AACJ,MAAI;AACJ,MAAIF,SAAQ;AACZ,MAAIG,SAAQ;AACZ,MAAI,aAAa;AAEjB,WAAS,SAAS;AAChB,UAAM,QAAQ,EAAC,OAAOH,OAAM,MAAM,MAAM,SAAS,GAAG,OAAOG,OAAM,MAAM,MAAM,SAAS,EAAC;AACvF,qBAAiB,KAAK;AACtB,sBAAkB,KAAK;AACvB,sBAAkB,KAAK;AACvB,uBAAmB,KAAK;AACxB,wBAAoB,KAAK;AACzB,wBAAoB,KAAK;AACzB,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,SAAS,OAAO;AAC9B,wBAAoB,KAAK;AACzB,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,EACvF;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,EAC1F;AAEA,SAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAUD,QAAO,GAAG,UAAUA;AAAA,EACjD;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,KAAK,CAAC,GAAG,UAAU;AAAA,EAChD;AAEA,SAAO,cAAc,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU;AAAA,EACrD;AAEA,SAAO,QAAQ,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUF,SAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAUA;AAAA,EAC1F;AAEA,SAAO,QAAQ,SAAS,GAAG;AACzB,WAAO,UAAU,UAAUG,SAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAUA;AAAA,EAC1F;AAEA,SAAO,WAAW,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,WAAW,GAAG,UAAU;AAAA,EACrD;AAEA,SAAO,OAAO,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;AAAA,EAC7F;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,EACtH;AAEA,SAAO,aAAa,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,UAAU;AAAA,EACxD;AAEA,WAAS,iBAAiB,EAAC,OAAAH,QAAO,OAAAG,OAAK,GAAG;AACxC,eAAW,CAAC,GAAG,IAAI,KAAKH,OAAM,QAAQ,GAAG;AACvC,WAAK,QAAQ;AACb,WAAK,cAAc,CAAC;AACpB,WAAK,cAAc,CAAC;AAAA,IACtB;AACA,UAAM,WAAW,IAAI,IAAIA,OAAM,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAGA,MAAK,GAAG,CAAC,CAAC,CAAC;AAClE,eAAW,CAAC,GAAGC,KAAI,KAAKE,OAAM,QAAQ,GAAG;AACvC,MAAAF,MAAK,QAAQ;AACb,UAAI,EAAC,QAAQ,OAAM,IAAIA;AACvB,UAAI,OAAO,WAAW,SAAU,UAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,UAAI,OAAO,WAAW,SAAU,UAASA,MAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,aAAO,YAAY,KAAKA,KAAI;AAC5B,aAAO,YAAY,KAAKA,KAAI;AAAA,IAC9B;AACA,QAAI,YAAY,MAAM;AACpB,iBAAW,EAAC,aAAa,YAAW,KAAKD,QAAO;AAC9C,oBAAY,KAAK,QAAQ;AACzB,oBAAY,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAEA,WAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,eAAW,QAAQA,QAAO;AACxB,WAAK,QAAQ,KAAK,eAAe,SAC3B,KAAK,IAAI,IAAI,KAAK,aAAa,KAAK,GAAG,IAAI,KAAK,aAAa,KAAK,CAAC,IACnE,KAAK;AAAA,IACb;AAAA,EACF;AAEA,WAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI;AACf,QAAII,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,QAAQA;AACb,mBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AACA,UAAI,EAAEA,KAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI;AAAA,IACb;AAAA,EACF;AAEA,WAAS,mBAAmB,EAAC,OAAAJ,OAAK,GAAG;AACnC,UAAM,IAAIA,OAAM;AAChB,QAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,QAAI,OAAO,oBAAI;AACf,QAAII,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,iBAAW,QAAQ,SAAS;AAC1B,aAAK,SAASA;AACd,mBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AACA,UAAI,EAAEA,KAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,gBAAU;AACV,aAAO,oBAAI;AAAA,IACb;AAAA,EACF;AAEA,WAAS,kBAAkB,EAAC,OAAAJ,OAAK,GAAG;AAClC,UAAMI,KAAI,IAAIJ,QAAO,OAAK,EAAE,KAAK,IAAI;AACrC,UAAMK,OAAM,KAAK,KAAK,OAAOD,KAAI;AACjC,UAAM,UAAU,IAAI,MAAMA,EAAC;AAC3B,eAAW,QAAQJ,QAAO;AACxB,YAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAII,KAAI,GAAG,KAAK,MAAM,MAAM,KAAK,MAAM,MAAMA,EAAC,CAAC,CAAC,CAAC;AAC5E,WAAK,QAAQ;AACb,WAAK,KAAK,KAAK,IAAIC;AACnB,WAAK,KAAK,KAAK,KAAK;AACpB,UAAI,QAAQ,CAAC,EAAG,SAAQ,CAAC,EAAE,KAAK,IAAI;AAAA,UAC/B,SAAQ,CAAC,IAAI,CAAC,IAAI;AAAA,IACzB;AACA,QAAIH,MAAM,YAAW,UAAU,SAAS;AACtC,aAAO,KAAKA,KAAI;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,uBAAuB,SAAS;AACvC,UAAMI,MAAK,IAAI,SAAS,QAAM,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,CAAC;AAC5E,eAAWN,UAAS,SAAS;AAC3B,UAAIO,KAAI;AACR,iBAAW,QAAQP,QAAO;AACxB,aAAK,KAAKO;AACV,aAAK,KAAKA,KAAI,KAAK,QAAQD;AAC3B,QAAAC,KAAI,KAAK,KAAK;AACd,mBAAWN,SAAQ,KAAK,aAAa;AACnC,UAAAA,MAAK,QAAQA,MAAK,QAAQK;AAAA,QAC5B;AAAA,MACF;AACA,MAAAC,MAAK,KAAKA,KAAI,OAAOP,OAAM,SAAS;AACpC,eAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,GAAG;AACrC,cAAM,OAAOA,OAAM,CAAC;AACpB,aAAK,MAAMO,MAAK,IAAI;AACpB,aAAK,MAAMA,MAAK,IAAI;AAAA,MACtB;AACA,mBAAaP,MAAK;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,oBAAoB,OAAO;AAClC,UAAM,UAAU,kBAAkB,KAAK;AACvC,SAAK,KAAK,IAAI,KAAK,KAAK,OAAO,IAAI,SAAS,OAAK,EAAE,MAAM,IAAI,EAAE;AAC/D,2BAAuB,OAAO;AAC9B,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,YAAM,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC9B,YAAM,OAAO,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AACrD,uBAAiB,SAAS,OAAO,IAAI;AACrC,uBAAiB,SAAS,OAAO,IAAI;AAAA,IACvC;AAAA,EACF;AAGA,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAIO,KAAI;AACR,YAAI,IAAI;AACR,mBAAW,EAAC,QAAQ,OAAAC,OAAK,KAAK,OAAO,aAAa;AAChD,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,UAAAD,MAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;AAAA,QACP;AACA,YAAI,EAAE,IAAI,GAAI;AACd,YAAIE,OAAMF,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAME;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;AAAA,MACzB;AACA,UAAIP,UAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;AAAA,IAChC;AAAA,EACF;AAGA,WAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,aAAS,IAAI,QAAQ,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACnD,YAAM,SAAS,QAAQ,CAAC;AACxB,iBAAW,UAAU,QAAQ;AAC3B,YAAIK,KAAI;AACR,YAAI,IAAI;AACR,mBAAW,EAAC,QAAQ,OAAAC,OAAK,KAAK,OAAO,aAAa;AAChD,cAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,UAAAD,MAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,eAAK;AAAA,QACP;AACA,YAAI,EAAE,IAAI,GAAI;AACd,YAAIE,OAAMF,KAAI,IAAI,OAAO,MAAM;AAC/B,eAAO,MAAME;AACb,eAAO,MAAMA;AACb,yBAAiB,MAAM;AAAA,MACzB;AACA,UAAIP,UAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,wBAAkB,QAAQ,IAAI;AAAA,IAChC;AAAA,EACF;AAEA,WAAS,kBAAkBF,QAAO,OAAO;AACvC,UAAM,IAAIA,OAAM,UAAU;AAC1B,UAAM,UAAUA,OAAM,CAAC;AACvB,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,iCAA6BA,QAAO,IAAIA,OAAM,SAAS,GAAG,KAAK;AAC/D,iCAA6BA,QAAO,IAAI,GAAG,KAAK;AAAA,EAClD;AAGA,WAAS,6BAA6BA,QAAOO,IAAG,GAAG,OAAO;AACxD,WAAO,IAAIP,OAAM,QAAQ,EAAE,GAAG;AAC5B,YAAM,OAAOA,OAAM,CAAC;AACpB,YAAMS,OAAMF,KAAI,KAAK,MAAM;AAC3B,UAAIE,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,MAAAF,KAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AAGA,WAAS,6BAA6BP,QAAOO,IAAG,GAAG,OAAO;AACxD,WAAO,KAAK,GAAG,EAAE,GAAG;AAClB,YAAM,OAAOP,OAAM,CAAC;AACpB,YAAMS,OAAM,KAAK,KAAKF,MAAK;AAC3B,UAAIE,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,MAAAF,KAAI,KAAK,KAAK;AAAA,IAChB;AAAA,EACF;AAEA,WAAS,iBAAiB,EAAC,aAAa,YAAW,GAAG;AACpD,QAAI,aAAa,QAAW;AAC1B,iBAAW,EAAC,QAAQ,EAAC,aAAAG,aAAW,EAAC,KAAK,aAAa;AACjD,QAAAA,aAAY,KAAK,sBAAsB;AAAA,MACzC;AACA,iBAAW,EAAC,QAAQ,EAAC,aAAAC,aAAW,EAAC,KAAK,aAAa;AACjD,QAAAA,aAAY,KAAK,sBAAsB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAaX,QAAO;AAC3B,QAAI,aAAa,QAAW;AAC1B,iBAAW,EAAC,aAAa,YAAW,KAAKA,QAAO;AAC9C,oBAAY,KAAK,sBAAsB;AACvC,oBAAY,KAAK,sBAAsB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAGA,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIO,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK,QAAQ;AAAA,IACf;AACA,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK;AAAA,IACP;AACA,WAAOA;AAAA,EACT;AAGA,WAAS,UAAU,QAAQ,QAAQ;AACjC,QAAIA,KAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK,QAAQ;AAAA,IACf;AACA,eAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,UAAI,SAAS,OAAQ;AACrB,MAAAA,MAAK;AAAA,IACP;AACA,WAAOA;AAAA,EACT;AAEA,SAAO;AACT;;;A+DhXA,IAAAK,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAI,KAAK,KAAK;AAAd,IACI,MAAM,IAAI;AADd,IAEI,UAAU;AAFd,IAGI,aAAa,MAAM;AAEvB,SAAS,OAAO;AACd,OAAK,MAAM,KAAK;AAAA,EAChB,KAAK,MAAM,KAAK,MAAM;AACtB,OAAK,IAAI;AACX;AAEA,SAAS,OAAO;AACd,SAAO,IAAI;AACb;AAEA,KAAK,YAAY,KAAK,YAAY;AAAA,EAChC,aAAa;AAAA,EACb,QAAQ,SAASC,IAAGC,IAAG;AACrB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACC;AAAA,EAC7E;AAAA,EACA,WAAW,WAAW;AACpB,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,QAAQ,SAASD,IAAGC,IAAG;AACrB,SAAK,KAAK,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EACvD;AAAA,EACA,kBAAkB,SAAS,IAAI,IAAID,IAAGC,IAAG;AACvC,SAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EACnF;AAAA,EACA,eAAe,SAAS,IAAI,IAAI,IAAI,IAAID,IAAGC,IAAG;AAC5C,SAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,CAACC;AAAA,EAC/G;AAAA,EACA,OAAO,SAAS,IAAI,IAAI,IAAI,IAAI,GAAG;AACjC,SAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7C,QAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG9B,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IACtD,WAGS,EAAE,QAAQ,SAAS;AAAA,aAKnB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,WAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IACtD,OAGK;AACH,UAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGd,UAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,aAAK,KAAK,OAAO,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM;AAAA,MACvD;AAEA,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAW,EAAE,MAAM,MAAM,MAAM,OAAQ,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK,MAAM;AAAA,IACxI;AAAA,EACF;AAAA,EACA,KAAK,SAASD,IAAGC,IAAG,GAAG,IAAI,IAAI,KAAK;AAClC,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA,IAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC,QAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAKD,KAAI,IACT,KAAKC,KAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG9B,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IAC7B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,WAAK,KAAK,MAAM,KAAK,MAAM;AAAA,IAC7B;AAGA,QAAI,CAAC,EAAG;AAGR,QAAI,KAAK,EAAG,MAAK,KAAK,MAAM;AAG5B,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAOD,KAAI,MAAM,OAAOC,KAAI,MAAM,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,IAC9J,WAGS,KAAK,SAAS;AACrB,WAAK,KAAK,MAAM,IAAI,MAAM,IAAI,QAAS,EAAE,MAAM,MAAO,MAAM,KAAK,OAAO,KAAK,MAAMD,KAAI,IAAI,KAAK,IAAI,EAAE,KAAK,OAAO,KAAK,MAAMC,KAAI,IAAI,KAAK,IAAI,EAAE;AAAA,IAClJ;AAAA,EACF;AAAA,EACA,MAAM,SAASD,IAAGC,IAAG,GAAG,GAAG;AACzB,SAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACD,MAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAACC,MAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK;AAAA,EACzH;AAAA,EACA,UAAU,WAAW;AACnB,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,eAAQ;;;ACjIf,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAARC,kBAAiBC,IAAG;AACzB,SAAO,SAASC,YAAW;AACzB,WAAOD;AAAA,EACT;AACF;;;ACJA,IAAAE,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAQO,IAAIC,WAAU;AACd,IAAIC,MAAK,KAAK;AACd,IAAI,SAASA,MAAK;AAClB,IAAIC,OAAM,IAAID;;;ACXrB,IAAAE,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,SAAS,OAAO,SAAS;AACvB,OAAK,WAAW;AAClB;AAEA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,aAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,IACvC;AAAA,EACF;AACF;AAEe,SAAR,eAAiB,SAAS;AAC/B,SAAO,IAAI,OAAO,OAAO;AAC3B;;;AC9BA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;AAEO,SAAS,EAAE,GAAG;AACnB,SAAO,EAAE,CAAC;AACZ;;;ACNA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEO,IAAI,oBAAoB,YAAY,cAAW;AAEtD,SAAS,OAAO,OAAO;AACrB,OAAK,SAAS;AAChB;AAEA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAW;AACpB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,OAAO,SAASC,IAAG,GAAG;AACpB,SAAK,OAAO,MAAM,IAAI,KAAK,IAAIA,EAAC,GAAG,IAAI,CAAC,KAAK,IAAIA,EAAC,CAAC;AAAA,EACrD;AACF;AAEe,SAAR,YAA6B,OAAO;AAEzC,WAAS,OAAO,SAAS;AACvB,WAAO,IAAI,OAAO,MAAM,OAAO,CAAC;AAAA,EAClC;AAEA,SAAO,SAAS;AAEhB,SAAO;AACT;;;ACnCA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAO,IAAIC,SAAQ,MAAM,UAAU;;;ADMnC,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAEA,SAAS,WAAW,GAAG;AACrB,SAAO,EAAE;AACX;AAEA,SAAS,KAAK,OAAO;AACnB,MAAI,SAAS,YACT,SAAS,YACTC,KAAI,GACJC,KAAI,GACJ,UAAU;AAEd,WAASC,QAAO;AACd,QAAI,QAAQ,OAAOC,OAAM,KAAK,SAAS,GAAGC,KAAI,OAAO,MAAM,MAAM,IAAI,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI;AACnG,QAAI,CAAC,QAAS,WAAU,SAAS,aAAK;AACtC,UAAM,SAAS,CAACJ,GAAE,MAAM,OAAO,KAAK,CAAC,IAAII,IAAG,KAAK,GAAG,CAACH,GAAE,MAAM,MAAM,IAAI,GAAG,CAACD,GAAE,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAACC,GAAE,MAAM,MAAM,IAAI,CAAC;AACnI,QAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,EACpD;AAEA,EAAAC,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AAEA,EAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,EACjD;AAEA,EAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUF,KAAI,OAAO,MAAM,aAAa,IAAIK,kBAAS,CAAC,CAAC,GAAGH,SAAQF;AAAA,EACrF;AAEA,EAAAE,MAAK,IAAI,SAAS,GAAG;AACnB,WAAO,UAAU,UAAUD,KAAI,OAAO,MAAM,aAAa,IAAII,kBAAS,CAAC,CAAC,GAAGH,SAAQD;AAAA,EACrF;AAEA,EAAAC,MAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAIA,SAAQ;AAAA,EACvE;AAEA,SAAOA;AACT;AAEA,SAAS,gBAAgB,SAAS,IAAI,IAAI,IAAI,IAAI;AAChD,UAAQ,OAAO,IAAI,EAAE;AACrB,UAAQ,cAAc,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAC9D;AAgBO,SAAS,iBAAiB;AAC/B,SAAO,KAAK,eAAe;AAC7B;;;AExEA,IAAAI,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAI,QAAQ,KAAK,KAAK,IAAI,CAAC;AAA3B,IACI,UAAU,QAAQ;;;ACDtB,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,IACI,KAAK,KAAK,IAAIC,MAAK,EAAE,IAAI,KAAK,IAAI,IAAIA,MAAK,EAAE;AADjD,IAEI,KAAK,KAAK,IAAIC,OAAM,EAAE,IAAI;AAF9B,IAGI,KAAK,CAAC,KAAK,IAAIA,OAAM,EAAE,IAAI;;;ACL/B,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAI,QAAQ,KAAK,KAAK,CAAC;;;ACAvB,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IACI,IAAI,KAAK,KAAK,CAAC,IAAI;AADvB,IAEI,IAAI,IAAI,KAAK,KAAK,EAAE;AAFxB,IAGI,KAAK,IAAI,IAAI,KAAK;;;ACHtB,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAe,SAAR,eAAmB;AAAC;;;ACA3B,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAO,SAAS,MAAM,MAAMC,IAAGC,IAAG;AAChC,OAAK,SAAS;AAAA,KACX,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,KAC3B,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,KAC3B,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,KAC3B,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,KAC3B,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK;AAAA,KAC/B,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK;AAAA,EAClC;AACF;AAEO,SAAS,MAAM,SAAS;AAC7B,OAAK,WAAW;AAClB;AAEA,MAAM,YAAY;AAAA,EAChB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,cAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,MACtC,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,IACpD;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASD,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,SAAS,QAAQ,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA;AAAA,MAC1G;AAAS,cAAM,MAAMD,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;;;AF3CA,SAAS,YAAY,SAAS;AAC5B,OAAK,WAAW;AAClB;AAEA,YAAY,YAAY;AAAA,EACtB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MACjD,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACvD,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG,aAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK,CAAC;AAAG;AAAA,MACjJ;AAAS,cAAM,MAAMD,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;;;AG/CA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,YAAI,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMD,MAAK,GAAG,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMC,MAAK;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS,OAAO,IAAI,EAAE;AAAG;AAAA,MACvL,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,cAAM,MAAMD,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EAClC;AACF;;;AClCA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,SAAS,OAAO,SAAS,MAAM;AAC7B,OAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,OAAK,QAAQ;AACf;AAEA,OAAO,YAAY;AAAA,EACjB,WAAW,WAAW;AACpB,SAAK,KAAK,CAAC;AACX,SAAK,KAAK,CAAC;AACX,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA,EACA,SAAS,WAAW;AAClB,QAAIC,KAAI,KAAK,IACTC,KAAI,KAAK,IACT,IAAID,GAAE,SAAS;AAEnB,QAAI,IAAI,GAAG;AACT,UAAI,KAAKA,GAAE,CAAC,GACR,KAAKC,GAAE,CAAC,GACR,KAAKD,GAAE,CAAC,IAAI,IACZ,KAAKC,GAAE,CAAC,IAAI,IACZ,IAAI,IACJ;AAEJ,aAAO,EAAE,KAAK,GAAG;AACf,YAAI,IAAI;AACR,aAAK,OAAO;AAAA,UACV,KAAK,QAAQD,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,UACjD,KAAK,QAAQC,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAAA,EACA,OAAO,SAASD,IAAGC,IAAG;AACpB,SAAK,GAAG,KAAK,CAACD,EAAC;AACf,SAAK,GAAG,KAAK,CAACC,EAAC;AAAA,EACjB;AACF;AAEA,IAAO,iBAAS,SAAS,OAAO,MAAM;AAEpC,WAAS,OAAO,SAAS;AACvB,WAAO,SAAS,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,OAAO,SAAS,IAAI;AAAA,EACnE;AAEA,SAAO,OAAO,SAASC,OAAM;AAC3B,WAAO,OAAO,CAACA,KAAI;AAAA,EACrB;AAEA,SAAO;AACT,EAAG,IAAI;;;ACvDP,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAO,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,OAAK,SAAS;AAAA,IACZ,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA,IACtC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA,IACtC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMD;AAAA,IACjC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMC;AAAA,IACjC,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACF;AAEO,SAAS,SAAS,SAAS,SAAS;AACzC,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AAEA,SAAS,YAAY;AAAA,EACnB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAClD,KAAK;AAAG,QAAAF,OAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,IAC3C;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,mBAAS,SAASC,QAAO,SAAS;AAEvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,SAAS,SAAS,OAAO;AAAA,EACtC;AAEA,WAAS,UAAU,SAASC,UAAS;AACnC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AAEA,SAAO;AACT,EAAG,CAAC;;;ADzDG,SAAS,eAAe,SAAS,SAAS;AAC/C,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AAEA,eAAe,YAAY;AAAA,EACzB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAC5D,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,SAAS,OAAO,KAAK,MAAMD,IAAG,KAAK,MAAMC,EAAC;AAAG;AAAA,MAC3E,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,yBAAS,SAASE,QAAO,SAAS;AAEvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,eAAe,SAAS,OAAO;AAAA,EAC5C;AAEA,WAAS,UAAU,SAASC,UAAS;AACnC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AAEA,SAAO;AACT,EAAG,CAAC;;;AE5DJ,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEO,SAAS,aAAa,SAAS,SAAS;AAC7C,OAAK,WAAW;AAChB,OAAK,MAAM,IAAI,WAAW;AAC5B;AAEA,aAAa,YAAY;AAAA,EACvB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAC3H,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AACA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,uBAAS,SAASE,QAAO,SAAS;AAEvC,WAAS,SAAS,SAAS;AACzB,WAAO,IAAI,aAAa,SAAS,OAAO;AAAA,EAC1C;AAEA,WAAS,UAAU,SAASC,UAAS;AACnC,WAAOD,QAAO,CAACC,QAAO;AAAA,EACxB;AAEA,SAAO;AACT,EAAG,CAAC;;;AChDJ,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAGO,SAASC,OAAM,MAAMC,IAAGC,IAAG;AAChC,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACVC,MAAK,KAAK,KACVC,MAAK,KAAK;AAEd,MAAI,KAAK,SAASC,UAAS;AACzB,QAAIC,KAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC5D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC9C,UAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AACpE,UAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AAAA,EACtE;AAEA,MAAI,KAAK,SAASD,UAAS;AACzB,QAAI,IAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC5D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC9C,IAAAF,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUF,KAAI,KAAK,WAAW;AAC7D,IAAAG,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUF,KAAI,KAAK,WAAW;AAAA,EAC/D;AAEA,OAAK,SAAS,cAAc,IAAI,IAAIC,KAAIC,KAAI,KAAK,KAAK,KAAK,GAAG;AAChE;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AAEA,WAAW,YAAY;AAAA,EACrB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAClD,KAAK;AAAG,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,IAC1C;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASH,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AAEb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACjB,MAAM,KAAK,MAAMC;AACrB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AAEA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAF,OAAM,MAAMC,IAAGC,EAAC;AAAG;AAAA,IAC9B;AAEA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,qBAAS,SAASK,QAAO,OAAO;AAErC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,WAAW,SAAS,KAAK,IAAI,IAAI,SAAS,SAAS,CAAC;AAAA,EACzE;AAEA,aAAW,QAAQ,SAASC,QAAO;AACjC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AAEA,SAAO;AACT,EAAG,GAAG;;;ADnFN,SAAS,iBAAiB,SAAS,OAAO;AACxC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AAEA,iBAAiB,YAAY;AAAA,EAC3B,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAC5D,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,SAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,aAAK,SAAS,UAAU;AACxB;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AAEb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACjB,MAAM,KAAK,MAAMC;AACrB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AAEA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,SAAS,OAAO,KAAK,MAAMD,IAAG,KAAK,MAAMC,EAAC;AAAG;AAAA,MAC3E,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,MAAMD,IAAG,KAAK,MAAMC;AAAG;AAAA,MACrD;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AAEA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,2BAAS,SAASE,QAAO,OAAO;AAErC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,eAAe,SAAS,CAAC;AAAA,EACrF;AAEA,aAAW,QAAQ,SAASC,QAAO;AACjC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AAEA,SAAO;AACT,EAAG,GAAG;;;AEzEN,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAGA,SAAS,eAAe,SAAS,OAAO;AACtC,OAAK,WAAW;AAChB,OAAK,SAAS;AAChB;AAEA,eAAe,YAAY;AAAA,EACzB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,SAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AAEb,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAMD,IACjB,MAAM,KAAK,MAAMC;AACrB,WAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,IACrF;AAEA,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAC3H,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB;AAAS,QAAAC,OAAM,MAAMF,IAAGC,EAAC;AAAG;AAAA,IAC9B;AAEA,SAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,SAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMD;AACrD,SAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMC;AAAA,EACvD;AACF;AAEA,IAAO,yBAAS,SAASE,QAAO,OAAO;AAErC,WAAS,WAAW,SAAS;AAC3B,WAAO,QAAQ,IAAI,eAAe,SAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,EACjF;AAEA,aAAW,QAAQ,SAASC,QAAO;AACjC,WAAOD,QAAO,CAACC,MAAK;AAAA,EACtB;AAEA,SAAO;AACT,EAAG,GAAG;;;AC7DN,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,SAAS,aAAa,SAAS;AAC7B,OAAK,WAAW;AAClB;AAEA,aAAa,YAAY;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,OAAQ,MAAK,SAAS,UAAU;AAAA,EAC3C;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,QAAI,KAAK,OAAQ,MAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,QACrC,MAAK,SAAS,GAAG,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,EACjD;AACF;;;ACpBA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,SAAS,KAAKC,IAAG;AACf,SAAOA,KAAI,IAAI,KAAK;AACtB;AAMA,SAAS,OAAO,MAAM,IAAI,IAAI;AAC5B,MAAI,KAAK,KAAK,MAAM,KAAK,KACrB,KAAK,KAAK,KAAK,KACf,MAAM,KAAK,MAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,KAC9C,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,KACxC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK;AACpC,UAAQ,KAAK,EAAE,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,KAAK;AAC5F;AAGA,SAAS,OAAO,MAAM,GAAG;AACvB,MAAI,IAAI,KAAK,MAAM,KAAK;AACxB,SAAO,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI;AACvD;AAKA,SAASC,OAAM,MAAM,IAAI,IAAI;AAC3B,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,MAAM;AACrB,OAAK,SAAS,cAAc,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AAClF;AAEA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW;AAClB;AAEA,UAAU,YAAY;AAAA,EACpB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAChB,KAAK,MAAM;AACX,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,MAClD,KAAK;AAAG,QAAAA,OAAM,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG,CAAC;AAAG;AAAA,IACzD;AACA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,SAAK,QAAQ,IAAI,KAAK;AAAA,EACxB;AAAA,EACA,OAAO,SAASD,IAAGE,IAAG;AACpB,QAAI,KAAK;AAET,IAAAF,KAAI,CAACA,IAAGE,KAAI,CAACA;AACb,QAAIF,OAAM,KAAK,OAAOE,OAAM,KAAK,IAAK;AACtC,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOF,IAAGE,EAAC,IAAI,KAAK,SAAS,OAAOF,IAAGE,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAG;AAAA,MACzB,KAAK;AAAG,aAAK,SAAS;AAAG,QAAAD,OAAM,MAAM,OAAO,MAAM,KAAK,OAAO,MAAMD,IAAGE,EAAC,CAAC,GAAG,EAAE;AAAG;AAAA,MACjF;AAAS,QAAAD,OAAM,MAAM,KAAK,KAAK,KAAK,OAAO,MAAMD,IAAGE,EAAC,CAAC;AAAG;AAAA,IAC3D;AAEA,SAAK,MAAM,KAAK,KAAK,KAAK,MAAMF;AAChC,SAAK,MAAM,KAAK,KAAK,KAAK,MAAME;AAChC,SAAK,MAAM;AAAA,EACb;AACF;AAEA,SAAS,UAAU,SAAS;AAC1B,OAAK,WAAW,IAAI,eAAe,OAAO;AAC5C;AAAA,CAEC,UAAU,YAAY,OAAO,OAAO,UAAU,SAAS,GAAG,QAAQ,SAASF,IAAGE,IAAG;AAChF,YAAU,UAAU,MAAM,KAAK,MAAMA,IAAGF,EAAC;AAC3C;AAEA,SAAS,eAAe,SAAS;AAC/B,OAAK,WAAW;AAClB;AAEA,eAAe,YAAY;AAAA,EACzB,QAAQ,SAASA,IAAGE,IAAG;AAAE,SAAK,SAAS,OAAOA,IAAGF,EAAC;AAAA,EAAG;AAAA,EACrD,WAAW,WAAW;AAAE,SAAK,SAAS,UAAU;AAAA,EAAG;AAAA,EACnD,QAAQ,SAASA,IAAGE,IAAG;AAAE,SAAK,SAAS,OAAOA,IAAGF,EAAC;AAAA,EAAG;AAAA,EACrD,eAAe,SAAS,IAAI,IAAI,IAAI,IAAIA,IAAGE,IAAG;AAAE,SAAK,SAAS,cAAc,IAAI,IAAI,IAAI,IAAIA,IAAGF,EAAC;AAAA,EAAG;AACrG;;;AC/FA,IAAAG,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,SAAS,QAAQ,SAAS;AACxB,OAAK,WAAW;AAClB;AAEA,QAAQ,YAAY;AAAA,EAClB,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,KAAK,CAAC;AACX,SAAK,KAAK,CAAC;AAAA,EACb;AAAA,EACA,SAAS,WAAW;AAClB,QAAIC,KAAI,KAAK,IACTC,KAAI,KAAK,IACT,IAAID,GAAE;AAEV,QAAI,GAAG;AACL,WAAK,QAAQ,KAAK,SAAS,OAAOA,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC,IAAI,KAAK,SAAS,OAAOD,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAC/E,UAAI,MAAM,GAAG;AACX,aAAK,SAAS,OAAOD,GAAE,CAAC,GAAGC,GAAE,CAAC,CAAC;AAAA,MACjC,OAAO;AACL,YAAI,KAAK,cAAcD,EAAC,GACpB,KAAK,cAAcC,EAAC;AACxB,iBAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI;AAC3C,eAAK,SAAS,cAAc,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAGD,GAAE,EAAE,GAAGC,GAAE,EAAE,CAAC;AAAA,QACtF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,MAAM,EAAI,MAAK,SAAS,UAAU;AACzE,SAAK,QAAQ,IAAI,KAAK;AACtB,SAAK,KAAK,KAAK,KAAK;AAAA,EACtB;AAAA,EACA,OAAO,SAASD,IAAGC,IAAG;AACpB,SAAK,GAAG,KAAK,CAACD,EAAC;AACf,SAAK,GAAG,KAAK,CAACC,EAAC;AAAA,EACjB;AACF;AAGA,SAAS,cAAcD,IAAG;AACxB,MAAI,GACA,IAAIA,GAAE,SAAS,GACf,GACAE,KAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC;AACnB,EAAAA,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAIF,GAAE,CAAC,IAAI,IAAIA,GAAE,CAAC;AACzC,OAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,CAAAE,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAIF,GAAE,CAAC,IAAI,IAAIA,GAAE,IAAI,CAAC;AAC7E,EAAAE,GAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,IAAIF,GAAE,IAAI,CAAC,IAAIA,GAAE,CAAC;AACzD,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,KAAIE,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC;AAC3E,EAAAA,GAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAC7B,OAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,CAAAA,GAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,GAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3D,IAAE,IAAI,CAAC,KAAKF,GAAE,CAAC,IAAIE,GAAE,IAAI,CAAC,KAAK;AAC/B,OAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,IAAIF,GAAE,IAAI,CAAC,IAAIE,GAAE,IAAI,CAAC;AACzD,SAAO,CAACA,IAAG,CAAC;AACd;;;AC5DA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,SAAS,KAAK,SAAS,GAAG;AACxB,OAAK,WAAW;AAChB,OAAK,KAAK;AACZ;AAEA,KAAK,YAAY;AAAA,EACf,WAAW,WAAW;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,KAAK,KAAK,KAAK;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,OAAO,KAAK,IAAI,KAAK,EAAE;AAC1F,QAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,QAAI,KAAK,SAAS,EAAG,MAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,EACpE;AAAA,EACA,OAAO,SAASC,IAAGC,IAAG;AACpB,IAAAD,KAAI,CAACA,IAAGC,KAAI,CAACA;AACb,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAG,aAAK,SAAS;AAAG,aAAK,QAAQ,KAAK,SAAS,OAAOD,IAAGC,EAAC,IAAI,KAAK,SAAS,OAAOD,IAAGC,EAAC;AAAG;AAAA,MAC/F,KAAK;AAAG,aAAK,SAAS;AAAA;AAAA,MACtB,SAAS;AACP,YAAI,KAAK,MAAM,GAAG;AAChB,eAAK,SAAS,OAAO,KAAK,IAAIA,EAAC;AAC/B,eAAK,SAAS,OAAOD,IAAGC,EAAC;AAAA,QAC3B,OAAO;AACL,cAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAMD,KAAI,KAAK;AAC5C,eAAK,SAAS,OAAO,IAAI,KAAK,EAAE;AAChC,eAAK,SAAS,OAAO,IAAIC,EAAC;AAAA,QAC5B;AACA;AAAA,MACF;AAAA,IACF;AACA,SAAK,KAAKD,IAAG,KAAK,KAAKC;AAAA,EACzB;AACF;;;ACxCA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;AtDEA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AAEA,SAAS,iBAAiB,GAAG;AAC3B,SAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAC3B;AAEe,SAAR,+BAAmB;AACxB,SAAO,eAAe,EACjB,OAAO,gBAAgB,EACvB,OAAO,gBAAgB;AAC9B;;;AjEEA,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAASC,IAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAC,GAAG,IAAIA,GAAE,QAAQ,KAAK,GAAGA,GAAE,CAAC,CAAC,IAAI,EAAG;AACrD,WAAO;AAAA,EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE;AACzD,MAAI,UAAU;AAAA,IACZ,OAAuB,OAAO,SAAS,QAAQ;AAAA,IAC/C,GAAG,OAAO;AAAA,IACV,IAAI,CAAC;AAAA,IACL,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,OAAO,GAAG,WAAW,GAAG,UAAU,GAAG,YAAY,GAAG,OAAO,IAAI,iBAAiB,IAAI,SAAS,IAAI,iBAAiB,IAAI,gBAAgB,IAAI,SAAS,IAAI,WAAW,IAAI,eAAe,IAAI,UAAU,IAAI,gBAAgB,IAAI,oBAAoB,IAAI,WAAW,GAAG,QAAQ,EAAE;AAAA,IAC1U,YAAY,EAAE,GAAG,SAAS,GAAG,UAAU,GAAG,WAAW,IAAI,OAAO,IAAI,iBAAiB,IAAI,SAAS,IAAI,iBAAiB,IAAI,gBAAgB,IAAI,UAAU,IAAI,gBAAgB,IAAI,mBAAmB;AAAA,IACpM,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAA,IAC5G,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,MAAM,GAAG,CAAC;AAC1E,gBAAM,SAAS,GAAG,iBAAiB,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,MAAM,GAAG,CAAC;AAC1E,gBAAMC,SAAQ,WAAW,GAAG,EAAE,EAAE,KAAK,CAAC;AACtC,aAAG,QAAQ,QAAQ,QAAQA,MAAK;AAChC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,KAAK,CAAC;AAClB;AAAA,MACJ;AAAA,IACF,GAAG,WAAW;AAAA,IACd,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA,IACliB,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;AAAA,IACzC,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;AAAA,MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF,GAAG,YAAY;AAAA,IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAClD,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAASD,MAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAIA,EAAC,GAAG;AACpD,sBAAY,GAAGA,EAAC,IAAI,KAAK,GAAGA,EAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;AAAA,MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACrB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQE,IAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAI;AAAA,UACf;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACrJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACtB,MAAM,OAAO;AAAA,YACb,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAM,OAAO;AAAA,YACb,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACpG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACjB,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;AACf,kBAAI,aAAa,GAAG;AAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,uBAAS;AACT,+BAAiB;AAAA,YACnB;AACA;AAAA,UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YACzC;AACA,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;AAAA,gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACnC;AAAA,YACF;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAClC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACF,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF,GAAG,YAAY;AAAA;AAAA,MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACZ,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;AAAA,QAC3B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACT,GAAG,UAAU;AAAA;AAAA,MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;AAAA,QACd,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;AAAA,QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;AAAA,QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;AAAA,QAC1L;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;AAAA,MACT,GAAG,MAAM;AAAA;AAAA,MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAChO,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,GAAG,QAAQ;AAAA;AAAA,MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAChC,GAAG,MAAM;AAAA;AAAA,MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAAG,WAAW;AAAA;AAAA,MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MACjF,GAAG,eAAe;AAAA;AAAA,MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC1C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,IAAI;AAAA,MACjD,GAAG,cAAc;AAAA;AAAA,MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACN,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC3B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;AAAA,QACzB;AACA,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QAC/I;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO;AACT,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY;AAC1B,mBAASF,MAAK,QAAQ;AACpB,iBAAKA,EAAC,IAAI,OAAOA,EAAC;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,YAAY;AAAA;AAAA,MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO,OAAO,WAAWG;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,YAAAA,SAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;AAAA,cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAMA,MAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACtH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,GAAG,MAAM;AAAA;AAAA,MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACL,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA;AAAA,MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;AAAA,MACpC,GAAG,OAAO;AAAA;AAAA,MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACpC;AAAA,MACF,GAAG,eAAe;AAAA;AAAA,MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;AAAA,MACtB,GAAG,WAAW;AAAA;AAAA,MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;AAAA,MAC7B,GAAG,gBAAgB;AAAA,MACnB,SAAS,EAAE,oBAAoB,KAAK;AAAA,MACpC,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AACrG,YAAI,UAAU;AACd,gBAAQ,2BAA2B;AAAA,UACjC,KAAK;AACH,iBAAK,UAAU,KAAK;AACpB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,UAAU,cAAc;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS,cAAc;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,QACJ;AAAA,MACF,GAAG,WAAW;AAAA,MACd,OAAO,CAAC,uBAAuB,WAAW,mCAAmC,kBAAkB,kBAAkB,sDAAsD,8BAA8B,kGAAkG;AAAA,MACvS,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,MAAM,GAAG,gBAAgB,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,aAAa,MAAM,GAAG,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,KAAK,EAAE;AAAA,IAC5M;AACA,WAAO;AAAA,EACT,EAAE;AACF,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAO;AACpB,EAAE;AACF,OAAO,SAAS;AAChB,IAAI,iBAAiB;AAGrB,IAAI,QAAQ,CAAC;AACb,IAAI,QAAQ,CAAC;AACb,IAAI,WAA2B,oBAAI,IAAI;AACvC,IAAI,SAAyB,OAAO,MAAM;AACxC,UAAQ,CAAC;AACT,UAAQ,CAAC;AACT,aAA2B,oBAAI,IAAI;AACnC,QAAM;AACR,GAAG,OAAO;AA3hBV;AA4hBA,IAAI,cAAa,WAAM;AAAA,EACrB,YAAY,QAAQ,QAAQF,SAAQ,GAAG;AACrC,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQA;AAAA,EACf;AAIF,GAFI,OAAO,IAAM,YAAY,GAPZ;AAUjB,IAAI,UAA0B,OAAO,CAAC,QAAQ,QAAQA,WAAU;AAC9D,QAAM,KAAK,IAAI,WAAW,QAAQ,QAAQA,MAAK,CAAC;AAClD,GAAG,SAAS;AAxiBZ,IAAAG;AAyiBA,IAAI,cAAaA,MAAA,MAAM;AAAA,EACrB,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAIF,GAFI,OAAOA,KAAM,YAAY,GALZA;AAQjB,IAAI,mBAAmC,OAAO,CAAC,OAAO;AACpD,OAAK,eAAe,aAAa,IAAI,WAAU,CAAC;AAChD,MAAI,OAAO,SAAS,IAAI,EAAE;AAC1B,MAAI,SAAS,QAAQ;AACnB,WAAO,IAAI,WAAW,EAAE;AACxB,aAAS,IAAI,IAAI,IAAI;AACrB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT,GAAG,kBAAkB;AACrB,IAAI,WAA2B,OAAO,MAAM,OAAO,UAAU;AAC7D,IAAI,WAA2B,OAAO,MAAM,OAAO,UAAU;AAC7D,IAAI,WAA2B,OAAO,OAAO;AAAA,EAC3C,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,IAAI,KAAK,GAAG,EAAE;AAAA,EAC5C,OAAO,MAAM,IAAI,CAACC,WAAU;AAAA,IAC1B,QAAQA,MAAK,OAAO;AAAA,IACpB,QAAQA,MAAK,OAAO;AAAA,IACpB,OAAOA,MAAK;AAAA,EACd,EAAE;AACJ,IAAI,UAAU;AACd,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA,WAA2B,OAAO,MAAM,WAAU,EAAE,QAAQ,WAAW;AAAA,EACvE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AACT;AAplBA,IAAAD;AAsmBA,IAAI,OAAMA,MAAA,MAAW;AAAA,EAOnB,OAAO,KAAK,MAAM;AAChB,WAAO,IAAIA,IAAK,OAAO,EAAEA,IAAK,KAAK;AAAA,EACrC;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,OAAO,IAAI,EAAE;AAAA,EACpB;AAAA,EACA,WAAW;AACT,WAAO,SAAS,KAAK,OAAO;AAAA,EAC9B;AACF,GAfI,OAAOA,KAAM,KAAK,GAGlBA,IAAK,QAAQ,GALPA;AAoBV,IAAI,gBAAgB;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,OAAuB,OAAO,SAAS,MAAM,IAAI,UAAU,SAAS;AACtE,QAAM,EAAE,eAAe,QAAQ,KAAK,IAAI,WAAU;AAClD,QAAM,sBAAsB,eAAc;AAC1C,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAS,OAAO,EAAE;AAAA,EACrC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAS,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAS,MAAM;AACrH,QAAM,MAAM,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,eAAS,QAAQ,EAAE,IAAI;AAC/F,QAAM,SAAQ,6BAAM,UAAS,oBAAoB;AACjD,QAAM,UAAS,6BAAM,WAAU,oBAAoB;AACnD,QAAM,eAAc,6BAAM,gBAAe,oBAAoB;AAC7D,QAAM,iBAAgB,6BAAM,kBAAiB,oBAAoB;AACjE,QAAM,UAAS,6BAAM,WAAU,oBAAoB;AACnD,QAAM,UAAS,6BAAM,WAAU,oBAAoB;AACnD,QAAM,cAAa,6BAAM,eAAc,oBAAoB;AAC3D,QAAM,QAAQ,QAAQ,GAAG,SAAS;AAClC,QAAM,YAAY,cAAc,aAAa;AAC7C,QAAM,YAAY;AAClB,QAAM,SAAS,OAAS,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,UAAU,SAAS,EAAE,YAAY,MAAM,aAAa,KAAK,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO;AAAA,IACrI,CAAC,GAAG,CAAC;AAAA,IACL,CAAC,OAAO,MAAM;AAAA,EAChB,CAAC;AACD,SAAO,KAAK;AACZ,QAAM,cAAc,QAAe,iBAAiB;AACpD,MAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,SAAS,MAAM,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,KAAK,OAAO,GAAG,EAAE,EAAE,KAAK,aAAa,SAAS,GAAG;AAC1L,WAAO,eAAe,EAAE,KAAK,MAAM,EAAE,KAAK;AAAA,EAC5C,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,UAAU,CAAC,MAAM;AACpF,WAAO,EAAE,KAAK,EAAE;AAAA,EAClB,CAAC,EAAE,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,QAAQ,CAAC,MAAM,YAAY,EAAE,EAAE,CAAC;AAC1E,QAAM,UAA0B,OAAO,CAAC,EAAE,IAAI,KAAK,OAAAH,OAAM,MAAM;AAC7D,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,WAAO,GAAG,GAAG;AAAA,EACf,MAAM,GAAG,KAAK,MAAMA,SAAQ,GAAG,IAAI,GAAG,GAAG,MAAM;AAAA,EAC/C,GAAG,SAAS;AACZ,MAAI,OAAO,GAAG,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,aAAa,EAAE,EAAE,UAAU,MAAM,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,QAAQ,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,MAAM,GAAG,aAAa,MAAM,MAAM,IAAI,EAAE,KAAK,eAAe,CAAC,MAAM,EAAE,KAAK,QAAQ,IAAI,UAAU,KAAK,EAAE,KAAK,OAAO;AACzU,QAAMI,QAAO,IAAI,OAAO,GAAG,EAAE,KAAK,SAAS,OAAO,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,kBAAkB,GAAG,EAAE,UAAU,OAAO,EAAE,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,SAAS,MAAM,EAAE,MAAM,kBAAkB,UAAU;AAC5M,QAAM,aAAY,6BAAM,cAAa;AACrC,MAAI,cAAc,YAAY;AAC5B,UAAM,WAAWA,MAAK,OAAO,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,KAAK,iBAAiB,GAAG,EAAE,EAAE,KAAK,iBAAiB,gBAAgB,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;AAC/M,aAAS,OAAO,MAAM,EAAE,KAAK,UAAU,IAAI,EAAE,KAAK,cAAc,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,CAAC;AAC/F,aAAS,OAAO,MAAM,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,cAAc,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,CAAC;AAAA,EACnG;AACA,MAAI;AACJ,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,iBAA2B,OAAO,CAAC,MAAM,EAAE,KAAK,UAAU;AAC1D;AAAA,IACF,KAAK;AACH,iBAA2B,OAAO,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,GAAG,UAAU;AAC7E;AAAA,IACF,KAAK;AACH,iBAA2B,OAAO,CAAC,MAAM,YAAY,EAAE,OAAO,EAAE,GAAG,UAAU;AAC7E;AAAA,IACF;AACE,iBAAW;AAAA,EACf;AACA,EAAAA,MAAK,OAAO,MAAM,EAAE,KAAK,KAAK,6BAAuB,CAAC,EAAE,KAAK,UAAU,QAAQ,EAAE,KAAK,gBAAgB,CAAC,MAAM,KAAK,IAAI,GAAG,EAAE,KAAK,CAAC;AACjI,oBAAkB,QAAQ,KAAK,GAAG,WAAW;AAC/C,GAAG,MAAM;AACT,IAAI,yBAAyB;AAAA,EAC3B;AACF;AAGA,IAAI,wBAAwC,OAAO,CAAC,SAAS;AAC3D,QAAM,cAAc,KAAK,WAAW,4BAA4B,EAAE,EAAE,WAAW,cAAc,IAAI,EAAE,KAAK;AACxG,SAAO;AACT,GAAG,uBAAuB;AAG1B,IAAI,YAA4B,OAAO,CAAC,YAAY;AAAA,qBAC/B,QAAQ,UAAU;AAAA,QAC/B,WAAW;AACnB,IAAI,iBAAiB;AAGrB,IAAI,gBAAgB,eAAe,MAAM,KAAK,cAAc;AAC5D,eAAe,QAAQ,CAAC,SAAS,cAAc,sBAAsB,IAAI,CAAC;AAC1E,IAAI,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,UAAU;AACZ;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "a", "import_dist", "x", "left", "a", "right", "center", "import_dist", "x", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "max", "value", "index", "import_dist", "min", "value", "index", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "array", "import_dist", "sum", "value", "index", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "x", "a", "nodes", "link", "sort", "links", "x", "kx", "ky", "y", "value", "dy", "sourceLinks", "targetLinks", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "x", "y", "import_dist", "constant_default", "x", "constant", "import_dist", "epsilon", "pi", "tau", "import_dist", "import_dist", "x", "y", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "a", "import_dist", "import_dist", "import_dist", "import_dist", "slice", "x", "y", "link", "slice", "s", "constant_default", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "pi", "tau", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "x", "y", "x", "y", "import_dist", "x", "y", "import_dist", "x", "y", "beta", "import_dist", "import_dist", "point", "x", "y", "custom", "tension", "x", "y", "point", "custom", "tension", "import_dist", "x", "y", "point", "custom", "tension", "import_dist", "import_dist", "point", "x", "y", "x2", "y2", "epsilon", "a", "custom", "alpha", "x", "y", "point", "custom", "alpha", "import_dist", "x", "y", "point", "custom", "alpha", "import_dist", "x", "y", "import_dist", "x", "point", "y", "import_dist", "x", "y", "a", "import_dist", "x", "y", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "k", "value", "a", "index", "_a", "link"]}