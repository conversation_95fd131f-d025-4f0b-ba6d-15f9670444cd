{"version": 3, "sources": ["../../codemirror/addon/hint/css-hint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../../mode/css/css\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../../mode/css/css\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var pseudoClasses = {\"active\":1, \"after\":1, \"before\":1, \"checked\":1, \"default\":1,\n    \"disabled\":1, \"empty\":1, \"enabled\":1, \"first-child\":1, \"first-letter\":1,\n    \"first-line\":1, \"first-of-type\":1, \"focus\":1, \"hover\":1, \"in-range\":1,\n    \"indeterminate\":1, \"invalid\":1, \"lang\":1, \"last-child\":1, \"last-of-type\":1,\n    \"link\":1, \"not\":1, \"nth-child\":1, \"nth-last-child\":1, \"nth-last-of-type\":1,\n    \"nth-of-type\":1, \"only-of-type\":1, \"only-child\":1, \"optional\":1, \"out-of-range\":1,\n    \"placeholder\":1, \"read-only\":1, \"read-write\":1, \"required\":1, \"root\":1,\n    \"selection\":1, \"target\":1, \"valid\":1, \"visited\":1\n  };\n\n  CodeMirror.registerHelper(\"hint\", \"css\", function(cm) {\n    var cur = cm.getCursor(), token = cm.getTokenAt(cur);\n    var inner = CodeMirror.innerMode(cm.getMode(), token.state);\n    if (inner.mode.name != \"css\") return;\n\n    if (token.type == \"keyword\" && \"!important\".indexOf(token.string) == 0)\n      return {list: [\"!important\"], from: CodeMirror.Pos(cur.line, token.start),\n              to: CodeMirror.Pos(cur.line, token.end)};\n\n    var start = token.start, end = cur.ch, word = token.string.slice(0, end - start);\n    if (/[^\\w$_-]/.test(word)) {\n      word = \"\"; start = end = cur.ch;\n    }\n\n    var spec = CodeMirror.resolveMode(\"text/css\");\n\n    var result = [];\n    function add(keywords) {\n      for (var name in keywords)\n        if (!word || name.lastIndexOf(word, 0) == 0)\n          result.push(name);\n    }\n\n    var st = inner.state.state;\n    if (st == \"pseudo\" || token.type == \"variable-3\") {\n      add(pseudoClasses);\n    } else if (st == \"block\" || st == \"maybeprop\") {\n      add(spec.propertyKeywords);\n    } else if (st == \"prop\" || st == \"parens\" || st == \"at\" || st == \"params\") {\n      add(spec.valueKeywords);\n      add(spec.colorKeywords);\n    } else if (st == \"media\" || st == \"media_parens\") {\n      add(spec.mediaTypes);\n      add(spec.mediaFeatures);\n    }\n\n    if (result.length) return {\n      list: result,\n      from: CodeMirror.Pos(cur.line, start),\n      to: CodeMirror.Pos(cur.line, end)\n    };\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,aAA6B;AAAA,eAC3D,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,oBAAoB,GAAG,GAAG;AAAA;AAE1D,YAAI,UAAU;AAAA,IAClB,GAAG,SAASC,aAAY;AACtB;AAEA,UAAI,gBAAgB;AAAA,QAAC,UAAS;AAAA,QAAG,SAAQ;AAAA,QAAG,UAAS;AAAA,QAAG,WAAU;AAAA,QAAG,WAAU;AAAA,QAC7E,YAAW;AAAA,QAAG,SAAQ;AAAA,QAAG,WAAU;AAAA,QAAG,eAAc;AAAA,QAAG,gBAAe;AAAA,QACtE,cAAa;AAAA,QAAG,iBAAgB;AAAA,QAAG,SAAQ;AAAA,QAAG,SAAQ;AAAA,QAAG,YAAW;AAAA,QACpE,iBAAgB;AAAA,QAAG,WAAU;AAAA,QAAG,QAAO;AAAA,QAAG,cAAa;AAAA,QAAG,gBAAe;AAAA,QACzE,QAAO;AAAA,QAAG,OAAM;AAAA,QAAG,aAAY;AAAA,QAAG,kBAAiB;AAAA,QAAG,oBAAmB;AAAA,QACzE,eAAc;AAAA,QAAG,gBAAe;AAAA,QAAG,cAAa;AAAA,QAAG,YAAW;AAAA,QAAG,gBAAe;AAAA,QAChF,eAAc;AAAA,QAAG,aAAY;AAAA,QAAG,cAAa;AAAA,QAAG,YAAW;AAAA,QAAG,QAAO;AAAA,QACrE,aAAY;AAAA,QAAG,UAAS;AAAA,QAAG,SAAQ;AAAA,QAAG,WAAU;AAAA,MAClD;AAEA,MAAAA,YAAW,eAAe,QAAQ,OAAO,SAAS,IAAI;AACpD,YAAI,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,WAAW,GAAG;AACnD,YAAI,QAAQA,YAAW,UAAU,GAAG,QAAQ,GAAG,MAAM,KAAK;AAC1D,YAAI,MAAM,KAAK,QAAQ,MAAO;AAE9B,YAAI,MAAM,QAAQ,aAAa,aAAa,QAAQ,MAAM,MAAM,KAAK;AACnE,iBAAO;AAAA,YAAC,MAAM,CAAC,YAAY;AAAA,YAAG,MAAMA,YAAW,IAAI,IAAI,MAAM,MAAM,KAAK;AAAA,YAChE,IAAIA,YAAW,IAAI,IAAI,MAAM,MAAM,GAAG;AAAA,UAAC;AAEjD,YAAI,QAAQ,MAAM,OAAO,MAAM,IAAI,IAAI,OAAO,MAAM,OAAO,MAAM,GAAG,MAAM,KAAK;AAC/E,YAAI,WAAW,KAAK,IAAI,GAAG;AACzB,iBAAO;AAAI,kBAAQ,MAAM,IAAI;AAAA,QAC/B;AAEA,YAAI,OAAOA,YAAW,YAAY,UAAU;AAE5C,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,UAAU;AACrB,mBAAS,QAAQ;AACf,gBAAI,CAAC,QAAQ,KAAK,YAAY,MAAM,CAAC,KAAK;AACxC,qBAAO,KAAK,IAAI;AAAA,QACtB;AAEA,YAAI,KAAK,MAAM,MAAM;AACrB,YAAI,MAAM,YAAY,MAAM,QAAQ,cAAc;AAChD,cAAI,aAAa;AAAA,QACnB,WAAW,MAAM,WAAW,MAAM,aAAa;AAC7C,cAAI,KAAK,gBAAgB;AAAA,QAC3B,WAAW,MAAM,UAAU,MAAM,YAAY,MAAM,QAAQ,MAAM,UAAU;AACzE,cAAI,KAAK,aAAa;AACtB,cAAI,KAAK,aAAa;AAAA,QACxB,WAAW,MAAM,WAAW,MAAM,gBAAgB;AAChD,cAAI,KAAK,UAAU;AACnB,cAAI,KAAK,aAAa;AAAA,QACxB;AAEA,YAAI,OAAO,OAAQ,QAAO;AAAA,UACxB,MAAM;AAAA,UACN,MAAMA,YAAW,IAAI,IAAI,MAAM,KAAK;AAAA,UACpC,IAAIA,YAAW,IAAI,IAAI,MAAM,GAAG;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;", "names": ["import_dist", "CodeMirror"]}