{"version": 3, "sources": ["../../codemirror/addon/hint/show-hint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// declare global: DOMRect\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var HINT_ELEMENT_CLASS        = \"CodeMirror-hint\";\n  var ACTIVE_HINT_ELEMENT_CLASS = \"CodeMirror-hint-active\";\n\n  // This is the old interface, kept around for now to stay\n  // backwards-compatible.\n  CodeMirror.showHint = function(cm, getHints, options) {\n    if (!getHints) return cm.showHint(options);\n    if (options && options.async) getHints.async = true;\n    var newOpts = {hint: getHints};\n    if (options) for (var prop in options) newOpts[prop] = options[prop];\n    return cm.showHint(newOpts);\n  };\n\n  CodeMirror.defineExtension(\"showHint\", function(options) {\n    options = parseOptions(this, this.getCursor(\"start\"), options);\n    var selections = this.listSelections()\n    if (selections.length > 1) return;\n    // By default, don't allow completion when something is selected.\n    // A hint function can have a `supportsSelection` property to\n    // indicate that it can handle selections.\n    if (this.somethingSelected()) {\n      if (!options.hint.supportsSelection) return;\n      // Don't try with cross-line selections\n      for (var i = 0; i < selections.length; i++)\n        if (selections[i].head.line != selections[i].anchor.line) return;\n    }\n\n    if (this.state.completionActive) this.state.completionActive.close();\n    var completion = this.state.completionActive = new Completion(this, options);\n    if (!completion.options.hint) return;\n\n    CodeMirror.signal(this, \"startCompletion\", this);\n    completion.update(true);\n  });\n\n  CodeMirror.defineExtension(\"closeHint\", function() {\n    if (this.state.completionActive) this.state.completionActive.close()\n  })\n\n  function Completion(cm, options) {\n    this.cm = cm;\n    this.options = options;\n    this.widget = null;\n    this.debounce = 0;\n    this.tick = 0;\n    this.startPos = this.cm.getCursor(\"start\");\n    this.startLen = this.cm.getLine(this.startPos.line).length - this.cm.getSelection().length;\n\n    if (this.options.updateOnCursorActivity) {\n      var self = this;\n      cm.on(\"cursorActivity\", this.activityFunc = function() { self.cursorActivity(); });\n    }\n  }\n\n  var requestAnimationFrame = window.requestAnimationFrame || function(fn) {\n    return setTimeout(fn, 1000/60);\n  };\n  var cancelAnimationFrame = window.cancelAnimationFrame || clearTimeout;\n\n  Completion.prototype = {\n    close: function() {\n      if (!this.active()) return;\n      this.cm.state.completionActive = null;\n      this.tick = null;\n      if (this.options.updateOnCursorActivity) {\n        this.cm.off(\"cursorActivity\", this.activityFunc);\n      }\n\n      if (this.widget && this.data) CodeMirror.signal(this.data, \"close\");\n      if (this.widget) this.widget.close();\n      CodeMirror.signal(this.cm, \"endCompletion\", this.cm);\n    },\n\n    active: function() {\n      return this.cm.state.completionActive == this;\n    },\n\n    pick: function(data, i) {\n      var completion = data.list[i], self = this;\n      this.cm.operation(function() {\n        if (completion.hint)\n          completion.hint(self.cm, data, completion);\n        else\n          self.cm.replaceRange(getText(completion), completion.from || data.from,\n                               completion.to || data.to, \"complete\");\n        CodeMirror.signal(data, \"pick\", completion);\n        self.cm.scrollIntoView();\n      });\n      if (this.options.closeOnPick) {\n        this.close();\n      }\n    },\n\n    cursorActivity: function() {\n      if (this.debounce) {\n        cancelAnimationFrame(this.debounce);\n        this.debounce = 0;\n      }\n\n      var identStart = this.startPos;\n      if(this.data) {\n        identStart = this.data.from;\n      }\n\n      var pos = this.cm.getCursor(), line = this.cm.getLine(pos.line);\n      if (pos.line != this.startPos.line || line.length - pos.ch != this.startLen - this.startPos.ch ||\n          pos.ch < identStart.ch || this.cm.somethingSelected() ||\n          (!pos.ch || this.options.closeCharacters.test(line.charAt(pos.ch - 1)))) {\n        this.close();\n      } else {\n        var self = this;\n        this.debounce = requestAnimationFrame(function() {self.update();});\n        if (this.widget) this.widget.disable();\n      }\n    },\n\n    update: function(first) {\n      if (this.tick == null) return\n      var self = this, myTick = ++this.tick\n      fetchHints(this.options.hint, this.cm, this.options, function(data) {\n        if (self.tick == myTick) self.finishUpdate(data, first)\n      })\n    },\n\n    finishUpdate: function(data, first) {\n      if (this.data) CodeMirror.signal(this.data, \"update\");\n\n      var picked = (this.widget && this.widget.picked) || (first && this.options.completeSingle);\n      if (this.widget) this.widget.close();\n\n      this.data = data;\n\n      if (data && data.list.length) {\n        if (picked && data.list.length == 1) {\n          this.pick(data, 0);\n        } else {\n          this.widget = new Widget(this, data);\n          CodeMirror.signal(data, \"shown\");\n        }\n      }\n    }\n  };\n\n  function parseOptions(cm, pos, options) {\n    var editor = cm.options.hintOptions;\n    var out = {};\n    for (var prop in defaultOptions) out[prop] = defaultOptions[prop];\n    if (editor) for (var prop in editor)\n      if (editor[prop] !== undefined) out[prop] = editor[prop];\n    if (options) for (var prop in options)\n      if (options[prop] !== undefined) out[prop] = options[prop];\n    if (out.hint.resolve) out.hint = out.hint.resolve(cm, pos)\n    return out;\n  }\n\n  function getText(completion) {\n    if (typeof completion == \"string\") return completion;\n    else return completion.text;\n  }\n\n  function buildKeyMap(completion, handle) {\n    var baseMap = {\n      Up: function() {handle.moveFocus(-1);},\n      Down: function() {handle.moveFocus(1);},\n      PageUp: function() {handle.moveFocus(-handle.menuSize() + 1, true);},\n      PageDown: function() {handle.moveFocus(handle.menuSize() - 1, true);},\n      Home: function() {handle.setFocus(0);},\n      End: function() {handle.setFocus(handle.length - 1);},\n      Enter: handle.pick,\n      Tab: handle.pick,\n      Esc: handle.close\n    };\n\n    var mac = /Mac/.test(navigator.platform);\n\n    if (mac) {\n      baseMap[\"Ctrl-P\"] = function() {handle.moveFocus(-1);};\n      baseMap[\"Ctrl-N\"] = function() {handle.moveFocus(1);};\n    }\n\n    var custom = completion.options.customKeys;\n    var ourMap = custom ? {} : baseMap;\n    function addBinding(key, val) {\n      var bound;\n      if (typeof val != \"string\")\n        bound = function(cm) { return val(cm, handle); };\n      // This mechanism is deprecated\n      else if (baseMap.hasOwnProperty(val))\n        bound = baseMap[val];\n      else\n        bound = val;\n      ourMap[key] = bound;\n    }\n    if (custom)\n      for (var key in custom) if (custom.hasOwnProperty(key))\n        addBinding(key, custom[key]);\n    var extra = completion.options.extraKeys;\n    if (extra)\n      for (var key in extra) if (extra.hasOwnProperty(key))\n        addBinding(key, extra[key]);\n    return ourMap;\n  }\n\n  function getHintElement(hintsElement, el) {\n    while (el && el != hintsElement) {\n      if (el.nodeName.toUpperCase() === \"LI\" && el.parentNode == hintsElement) return el;\n      el = el.parentNode;\n    }\n  }\n\n  function Widget(completion, data) {\n    this.id = \"cm-complete-\" + Math.floor(Math.random(1e6))\n    this.completion = completion;\n    this.data = data;\n    this.picked = false;\n    var widget = this, cm = completion.cm;\n    var ownerDocument = cm.getInputField().ownerDocument;\n    var parentWindow = ownerDocument.defaultView || ownerDocument.parentWindow;\n\n    var hints = this.hints = ownerDocument.createElement(\"ul\");\n    hints.setAttribute(\"role\", \"listbox\")\n    hints.setAttribute(\"aria-expanded\", \"true\")\n    hints.id = this.id\n    var theme = completion.cm.options.theme;\n    hints.className = \"CodeMirror-hints \" + theme;\n    this.selectedHint = data.selectedHint || 0;\n\n    var completions = data.list;\n    for (var i = 0; i < completions.length; ++i) {\n      var elt = hints.appendChild(ownerDocument.createElement(\"li\")), cur = completions[i];\n      var className = HINT_ELEMENT_CLASS + (i != this.selectedHint ? \"\" : \" \" + ACTIVE_HINT_ELEMENT_CLASS);\n      if (cur.className != null) className = cur.className + \" \" + className;\n      elt.className = className;\n      if (i == this.selectedHint) elt.setAttribute(\"aria-selected\", \"true\")\n      elt.id = this.id + \"-\" + i\n      elt.setAttribute(\"role\", \"option\")\n      if (cur.render) cur.render(elt, data, cur);\n      else elt.appendChild(ownerDocument.createTextNode(cur.displayText || getText(cur)));\n      elt.hintId = i;\n    }\n\n    var container = completion.options.container || ownerDocument.body;\n    var pos = cm.cursorCoords(completion.options.alignWithWord ? data.from : null);\n    var left = pos.left, top = pos.bottom, below = true;\n    var offsetLeft = 0, offsetTop = 0;\n    if (container !== ownerDocument.body) {\n      // We offset the cursor position because left and top are relative to the offsetParent's top left corner.\n      var isContainerPositioned = ['absolute', 'relative', 'fixed'].indexOf(parentWindow.getComputedStyle(container).position) !== -1;\n      var offsetParent = isContainerPositioned ? container : container.offsetParent;\n      var offsetParentPosition = offsetParent.getBoundingClientRect();\n      var bodyPosition = ownerDocument.body.getBoundingClientRect();\n      offsetLeft = (offsetParentPosition.left - bodyPosition.left - offsetParent.scrollLeft);\n      offsetTop = (offsetParentPosition.top - bodyPosition.top - offsetParent.scrollTop);\n    }\n    hints.style.left = (left - offsetLeft) + \"px\";\n    hints.style.top = (top - offsetTop) + \"px\";\n\n    // If we're at the edge of the screen, then we want the menu to appear on the left of the cursor.\n    var winW = parentWindow.innerWidth || Math.max(ownerDocument.body.offsetWidth, ownerDocument.documentElement.offsetWidth);\n    var winH = parentWindow.innerHeight || Math.max(ownerDocument.body.offsetHeight, ownerDocument.documentElement.offsetHeight);\n    container.appendChild(hints);\n    cm.getInputField().setAttribute(\"aria-autocomplete\", \"list\")\n    cm.getInputField().setAttribute(\"aria-owns\", this.id)\n    cm.getInputField().setAttribute(\"aria-activedescendant\", this.id + \"-\" + this.selectedHint)\n\n    var box = completion.options.moveOnOverlap ? hints.getBoundingClientRect() : new DOMRect();\n    var scrolls = completion.options.paddingForScrollbar ? hints.scrollHeight > hints.clientHeight + 1 : false;\n\n    // Compute in the timeout to avoid reflow on init\n    var startScroll;\n    setTimeout(function() { startScroll = cm.getScrollInfo(); });\n\n    var overlapY = box.bottom - winH;\n    if (overlapY > 0) { // Does not fit below\n      var height = box.bottom - box.top, spaceAbove = box.top - (pos.bottom - pos.top) - 2\n      if (winH - box.top < spaceAbove) { // More room at the top\n        if (height > spaceAbove) hints.style.height = (height = spaceAbove) + \"px\";\n        hints.style.top = ((top = pos.top - height) + offsetTop) + \"px\";\n        below = false;\n      } else {\n        hints.style.height = (winH - box.top - 2) + \"px\";\n      }\n    }\n    var overlapX = box.right - winW;\n    if (scrolls) overlapX += cm.display.nativeBarWidth;\n    if (overlapX > 0) {\n      if (box.right - box.left > winW) {\n        hints.style.width = (winW - 5) + \"px\";\n        overlapX -= (box.right - box.left) - winW;\n      }\n      hints.style.left = (left = Math.max(pos.left - overlapX - offsetLeft, 0)) + \"px\";\n    }\n    if (scrolls) for (var node = hints.firstChild; node; node = node.nextSibling)\n      node.style.paddingRight = cm.display.nativeBarWidth + \"px\"\n\n    cm.addKeyMap(this.keyMap = buildKeyMap(completion, {\n      moveFocus: function(n, avoidWrap) { widget.changeActive(widget.selectedHint + n, avoidWrap); },\n      setFocus: function(n) { widget.changeActive(n); },\n      menuSize: function() { return widget.screenAmount(); },\n      length: completions.length,\n      close: function() { completion.close(); },\n      pick: function() { widget.pick(); },\n      data: data\n    }));\n\n    if (completion.options.closeOnUnfocus) {\n      var closingOnBlur;\n      cm.on(\"blur\", this.onBlur = function() { closingOnBlur = setTimeout(function() { completion.close(); }, 100); });\n      cm.on(\"focus\", this.onFocus = function() { clearTimeout(closingOnBlur); });\n    }\n\n    cm.on(\"scroll\", this.onScroll = function() {\n      var curScroll = cm.getScrollInfo(), editor = cm.getWrapperElement().getBoundingClientRect();\n      if (!startScroll) startScroll = cm.getScrollInfo();\n      var newTop = top + startScroll.top - curScroll.top;\n      var point = newTop - (parentWindow.pageYOffset || (ownerDocument.documentElement || ownerDocument.body).scrollTop);\n      if (!below) point += hints.offsetHeight;\n      if (point <= editor.top || point >= editor.bottom) return completion.close();\n      hints.style.top = newTop + \"px\";\n      hints.style.left = (left + startScroll.left - curScroll.left) + \"px\";\n    });\n\n    CodeMirror.on(hints, \"dblclick\", function(e) {\n      var t = getHintElement(hints, e.target || e.srcElement);\n      if (t && t.hintId != null) {widget.changeActive(t.hintId); widget.pick();}\n    });\n\n    CodeMirror.on(hints, \"click\", function(e) {\n      var t = getHintElement(hints, e.target || e.srcElement);\n      if (t && t.hintId != null) {\n        widget.changeActive(t.hintId);\n        if (completion.options.completeOnSingleClick) widget.pick();\n      }\n    });\n\n    CodeMirror.on(hints, \"mousedown\", function() {\n      setTimeout(function(){cm.focus();}, 20);\n    });\n\n    // The first hint doesn't need to be scrolled to on init\n    var selectedHintRange = this.getSelectedHintRange();\n    if (selectedHintRange.from !== 0 || selectedHintRange.to !== 0) {\n      this.scrollToActive();\n    }\n\n    CodeMirror.signal(data, \"select\", completions[this.selectedHint], hints.childNodes[this.selectedHint]);\n    return true;\n  }\n\n  Widget.prototype = {\n    close: function() {\n      if (this.completion.widget != this) return;\n      this.completion.widget = null;\n      if (this.hints.parentNode) this.hints.parentNode.removeChild(this.hints);\n      this.completion.cm.removeKeyMap(this.keyMap);\n      var input = this.completion.cm.getInputField()\n      input.removeAttribute(\"aria-activedescendant\")\n      input.removeAttribute(\"aria-owns\")\n\n      var cm = this.completion.cm;\n      if (this.completion.options.closeOnUnfocus) {\n        cm.off(\"blur\", this.onBlur);\n        cm.off(\"focus\", this.onFocus);\n      }\n      cm.off(\"scroll\", this.onScroll);\n    },\n\n    disable: function() {\n      this.completion.cm.removeKeyMap(this.keyMap);\n      var widget = this;\n      this.keyMap = {Enter: function() { widget.picked = true; }};\n      this.completion.cm.addKeyMap(this.keyMap);\n    },\n\n    pick: function() {\n      this.completion.pick(this.data, this.selectedHint);\n    },\n\n    changeActive: function(i, avoidWrap) {\n      if (i >= this.data.list.length)\n        i = avoidWrap ? this.data.list.length - 1 : 0;\n      else if (i < 0)\n        i = avoidWrap ? 0  : this.data.list.length - 1;\n      if (this.selectedHint == i) return;\n      var node = this.hints.childNodes[this.selectedHint];\n      if (node) {\n        node.className = node.className.replace(\" \" + ACTIVE_HINT_ELEMENT_CLASS, \"\");\n        node.removeAttribute(\"aria-selected\")\n      }\n      node = this.hints.childNodes[this.selectedHint = i];\n      node.className += \" \" + ACTIVE_HINT_ELEMENT_CLASS;\n      node.setAttribute(\"aria-selected\", \"true\")\n      this.completion.cm.getInputField().setAttribute(\"aria-activedescendant\", node.id)\n      this.scrollToActive()\n      CodeMirror.signal(this.data, \"select\", this.data.list[this.selectedHint], node);\n    },\n\n    scrollToActive: function() {\n      var selectedHintRange = this.getSelectedHintRange();\n      var node1 = this.hints.childNodes[selectedHintRange.from];\n      var node2 = this.hints.childNodes[selectedHintRange.to];\n      var firstNode = this.hints.firstChild;\n      if (node1.offsetTop < this.hints.scrollTop)\n        this.hints.scrollTop = node1.offsetTop - firstNode.offsetTop;\n      else if (node2.offsetTop + node2.offsetHeight > this.hints.scrollTop + this.hints.clientHeight)\n        this.hints.scrollTop = node2.offsetTop + node2.offsetHeight - this.hints.clientHeight + firstNode.offsetTop;\n    },\n\n    screenAmount: function() {\n      return Math.floor(this.hints.clientHeight / this.hints.firstChild.offsetHeight) || 1;\n    },\n\n    getSelectedHintRange: function() {\n      var margin = this.completion.options.scrollMargin || 0;\n      return {\n        from: Math.max(0, this.selectedHint - margin),\n        to: Math.min(this.data.list.length - 1, this.selectedHint + margin),\n      };\n    }\n  };\n\n  function applicableHelpers(cm, helpers) {\n    if (!cm.somethingSelected()) return helpers\n    var result = []\n    for (var i = 0; i < helpers.length; i++)\n      if (helpers[i].supportsSelection) result.push(helpers[i])\n    return result\n  }\n\n  function fetchHints(hint, cm, options, callback) {\n    if (hint.async) {\n      hint(cm, callback, options)\n    } else {\n      var result = hint(cm, options)\n      if (result && result.then) result.then(callback)\n      else callback(result)\n    }\n  }\n\n  function resolveAutoHints(cm, pos) {\n    var helpers = cm.getHelpers(pos, \"hint\"), words\n    if (helpers.length) {\n      var resolved = function(cm, callback, options) {\n        var app = applicableHelpers(cm, helpers);\n        function run(i) {\n          if (i == app.length) return callback(null)\n          fetchHints(app[i], cm, options, function(result) {\n            if (result && result.list.length > 0) callback(result)\n            else run(i + 1)\n          })\n        }\n        run(0)\n      }\n      resolved.async = true\n      resolved.supportsSelection = true\n      return resolved\n    } else if (words = cm.getHelper(cm.getCursor(), \"hintWords\")) {\n      return function(cm) { return CodeMirror.hint.fromList(cm, {words: words}) }\n    } else if (CodeMirror.hint.anyword) {\n      return function(cm, options) { return CodeMirror.hint.anyword(cm, options) }\n    } else {\n      return function() {}\n    }\n  }\n\n  CodeMirror.registerHelper(\"hint\", \"auto\", {\n    resolve: resolveAutoHints\n  });\n\n  CodeMirror.registerHelper(\"hint\", \"fromList\", function(cm, options) {\n    var cur = cm.getCursor(), token = cm.getTokenAt(cur)\n    var term, from = CodeMirror.Pos(cur.line, token.start), to = cur\n    if (token.start < cur.ch && /\\w/.test(token.string.charAt(cur.ch - token.start - 1))) {\n      term = token.string.substr(0, cur.ch - token.start)\n    } else {\n      term = \"\"\n      from = cur\n    }\n    var found = [];\n    for (var i = 0; i < options.words.length; i++) {\n      var word = options.words[i];\n      if (word.slice(0, term.length) == term)\n        found.push(word);\n    }\n\n    if (found.length) return {list: found, from: from, to: to};\n  });\n\n  CodeMirror.commands.autocomplete = CodeMirror.showHint;\n\n  var defaultOptions = {\n    hint: CodeMirror.hint.auto,\n    completeSingle: true,\n    alignWithWord: true,\n    closeCharacters: /[\\s()\\[\\]{};:>,]/,\n    closeOnPick: true,\n    closeOnUnfocus: true,\n    updateOnCursorActivity: true,\n    completeOnSingleClick: true,\n    container: null,\n    customKeys: null,\n    extraKeys: null,\n    paddingForScrollbar: true,\n    moveOnOverlap: true,\n  };\n\n  CodeMirror.defineOption(\"hintOptions\", null);\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AAKA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASC,aAAY;AACtB;AAEA,UAAI,qBAA4B;AAChC,UAAI,4BAA4B;AAIhC,MAAAA,YAAW,WAAW,SAAS,IAAI,UAAU,SAAS;AACpD,YAAI,CAAC,SAAU,QAAO,GAAG,SAAS,OAAO;AACzC,YAAI,WAAW,QAAQ,MAAO,UAAS,QAAQ;AAC/C,YAAI,UAAU,EAAC,MAAM,SAAQ;AAC7B,YAAI,QAAS,UAAS,QAAQ,QAAS,SAAQ,IAAI,IAAI,QAAQ,IAAI;AACnE,eAAO,GAAG,SAAS,OAAO;AAAA,MAC5B;AAEA,MAAAA,YAAW,gBAAgB,YAAY,SAAS,SAAS;AACvD,kBAAU,aAAa,MAAM,KAAK,UAAU,OAAO,GAAG,OAAO;AAC7D,YAAI,aAAa,KAAK,eAAe;AACrC,YAAI,WAAW,SAAS,EAAG;AAI3B,YAAI,KAAK,kBAAkB,GAAG;AAC5B,cAAI,CAAC,QAAQ,KAAK,kBAAmB;AAErC,mBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ;AACrC,gBAAI,WAAW,CAAC,EAAE,KAAK,QAAQ,WAAW,CAAC,EAAE,OAAO,KAAM;AAAA,QAC9D;AAEA,YAAI,KAAK,MAAM,iBAAkB,MAAK,MAAM,iBAAiB,MAAM;AACnE,YAAI,aAAa,KAAK,MAAM,mBAAmB,IAAI,WAAW,MAAM,OAAO;AAC3E,YAAI,CAAC,WAAW,QAAQ,KAAM;AAE9B,QAAAA,YAAW,OAAO,MAAM,mBAAmB,IAAI;AAC/C,mBAAW,OAAO,IAAI;AAAA,MACxB,CAAC;AAED,MAAAA,YAAW,gBAAgB,aAAa,WAAW;AACjD,YAAI,KAAK,MAAM,iBAAkB,MAAK,MAAM,iBAAiB,MAAM;AAAA,MACrE,CAAC;AAED,eAAS,WAAW,IAAI,SAAS;AAC/B,aAAK,KAAK;AACV,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,aAAK,OAAO;AACZ,aAAK,WAAW,KAAK,GAAG,UAAU,OAAO;AACzC,aAAK,WAAW,KAAK,GAAG,QAAQ,KAAK,SAAS,IAAI,EAAE,SAAS,KAAK,GAAG,aAAa,EAAE;AAEpF,YAAI,KAAK,QAAQ,wBAAwB;AACvC,cAAI,OAAO;AACX,aAAG,GAAG,kBAAkB,KAAK,eAAe,WAAW;AAAE,iBAAK,eAAe;AAAA,UAAG,CAAC;AAAA,QACnF;AAAA,MACF;AAEA,UAAI,wBAAwB,OAAO,yBAAyB,SAAS,IAAI;AACvE,eAAO,WAAW,IAAI,MAAK,EAAE;AAAA,MAC/B;AACA,UAAI,uBAAuB,OAAO,wBAAwB;AAE1D,iBAAW,YAAY;AAAA,QACrB,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,EAAG;AACpB,eAAK,GAAG,MAAM,mBAAmB;AACjC,eAAK,OAAO;AACZ,cAAI,KAAK,QAAQ,wBAAwB;AACvC,iBAAK,GAAG,IAAI,kBAAkB,KAAK,YAAY;AAAA,UACjD;AAEA,cAAI,KAAK,UAAU,KAAK,KAAM,CAAAA,YAAW,OAAO,KAAK,MAAM,OAAO;AAClE,cAAI,KAAK,OAAQ,MAAK,OAAO,MAAM;AACnC,UAAAA,YAAW,OAAO,KAAK,IAAI,iBAAiB,KAAK,EAAE;AAAA,QACrD;AAAA,QAEA,QAAQ,WAAW;AACjB,iBAAO,KAAK,GAAG,MAAM,oBAAoB;AAAA,QAC3C;AAAA,QAEA,MAAM,SAAS,MAAM,GAAG;AACtB,cAAI,aAAa,KAAK,KAAK,CAAC,GAAG,OAAO;AACtC,eAAK,GAAG,UAAU,WAAW;AAC3B,gBAAI,WAAW;AACb,yBAAW,KAAK,KAAK,IAAI,MAAM,UAAU;AAAA;AAEzC,mBAAK,GAAG;AAAA,gBAAa,QAAQ,UAAU;AAAA,gBAAG,WAAW,QAAQ,KAAK;AAAA,gBAC7C,WAAW,MAAM,KAAK;AAAA,gBAAI;AAAA,cAAU;AAC3D,YAAAA,YAAW,OAAO,MAAM,QAAQ,UAAU;AAC1C,iBAAK,GAAG,eAAe;AAAA,UACzB,CAAC;AACD,cAAI,KAAK,QAAQ,aAAa;AAC5B,iBAAK,MAAM;AAAA,UACb;AAAA,QACF;AAAA,QAEA,gBAAgB,WAAW;AACzB,cAAI,KAAK,UAAU;AACjB,iCAAqB,KAAK,QAAQ;AAClC,iBAAK,WAAW;AAAA,UAClB;AAEA,cAAI,aAAa,KAAK;AACtB,cAAG,KAAK,MAAM;AACZ,yBAAa,KAAK,KAAK;AAAA,UACzB;AAEA,cAAI,MAAM,KAAK,GAAG,UAAU,GAAG,OAAO,KAAK,GAAG,QAAQ,IAAI,IAAI;AAC9D,cAAI,IAAI,QAAQ,KAAK,SAAS,QAAQ,KAAK,SAAS,IAAI,MAAM,KAAK,WAAW,KAAK,SAAS,MACxF,IAAI,KAAK,WAAW,MAAM,KAAK,GAAG,kBAAkB,MACnD,CAAC,IAAI,MAAM,KAAK,QAAQ,gBAAgB,KAAK,KAAK,OAAO,IAAI,KAAK,CAAC,CAAC,IAAI;AAC3E,iBAAK,MAAM;AAAA,UACb,OAAO;AACL,gBAAI,OAAO;AACX,iBAAK,WAAW,sBAAsB,WAAW;AAAC,mBAAK,OAAO;AAAA,YAAE,CAAC;AACjE,gBAAI,KAAK,OAAQ,MAAK,OAAO,QAAQ;AAAA,UACvC;AAAA,QACF;AAAA,QAEA,QAAQ,SAAS,OAAO;AACtB,cAAI,KAAK,QAAQ,KAAM;AACvB,cAAI,OAAO,MAAM,SAAS,EAAE,KAAK;AACjC,qBAAW,KAAK,QAAQ,MAAM,KAAK,IAAI,KAAK,SAAS,SAAS,MAAM;AAClE,gBAAI,KAAK,QAAQ,OAAQ,MAAK,aAAa,MAAM,KAAK;AAAA,UACxD,CAAC;AAAA,QACH;AAAA,QAEA,cAAc,SAAS,MAAM,OAAO;AAClC,cAAI,KAAK,KAAM,CAAAA,YAAW,OAAO,KAAK,MAAM,QAAQ;AAEpD,cAAI,SAAU,KAAK,UAAU,KAAK,OAAO,UAAY,SAAS,KAAK,QAAQ;AAC3E,cAAI,KAAK,OAAQ,MAAK,OAAO,MAAM;AAEnC,eAAK,OAAO;AAEZ,cAAI,QAAQ,KAAK,KAAK,QAAQ;AAC5B,gBAAI,UAAU,KAAK,KAAK,UAAU,GAAG;AACnC,mBAAK,KAAK,MAAM,CAAC;AAAA,YACnB,OAAO;AACL,mBAAK,SAAS,IAAI,OAAO,MAAM,IAAI;AACnC,cAAAA,YAAW,OAAO,MAAM,OAAO;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,eAAS,aAAa,IAAI,KAAK,SAAS;AACtC,YAAI,SAAS,GAAG,QAAQ;AACxB,YAAI,MAAM,CAAC;AACX,iBAAS,QAAQ,eAAgB,KAAI,IAAI,IAAI,eAAe,IAAI;AAChE,YAAI;AAAQ,mBAAS,QAAQ;AAC3B,gBAAI,OAAO,IAAI,MAAM,OAAW,KAAI,IAAI,IAAI,OAAO,IAAI;AAAA;AACzD,YAAI;AAAS,mBAAS,QAAQ;AAC5B,gBAAI,QAAQ,IAAI,MAAM,OAAW,KAAI,IAAI,IAAI,QAAQ,IAAI;AAAA;AAC3D,YAAI,IAAI,KAAK,QAAS,KAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,GAAG;AACzD,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,YAAY;AAC3B,YAAI,OAAO,cAAc,SAAU,QAAO;AAAA,YACrC,QAAO,WAAW;AAAA,MACzB;AAEA,eAAS,YAAY,YAAY,QAAQ;AACvC,YAAI,UAAU;AAAA,UACZ,IAAI,WAAW;AAAC,mBAAO,UAAU,EAAE;AAAA,UAAE;AAAA,UACrC,MAAM,WAAW;AAAC,mBAAO,UAAU,CAAC;AAAA,UAAE;AAAA,UACtC,QAAQ,WAAW;AAAC,mBAAO,UAAU,CAAC,OAAO,SAAS,IAAI,GAAG,IAAI;AAAA,UAAE;AAAA,UACnE,UAAU,WAAW;AAAC,mBAAO,UAAU,OAAO,SAAS,IAAI,GAAG,IAAI;AAAA,UAAE;AAAA,UACpE,MAAM,WAAW;AAAC,mBAAO,SAAS,CAAC;AAAA,UAAE;AAAA,UACrC,KAAK,WAAW;AAAC,mBAAO,SAAS,OAAO,SAAS,CAAC;AAAA,UAAE;AAAA,UACpD,OAAO,OAAO;AAAA,UACd,KAAK,OAAO;AAAA,UACZ,KAAK,OAAO;AAAA,QACd;AAEA,YAAI,MAAM,MAAM,KAAK,UAAU,QAAQ;AAEvC,YAAI,KAAK;AACP,kBAAQ,QAAQ,IAAI,WAAW;AAAC,mBAAO,UAAU,EAAE;AAAA,UAAE;AACrD,kBAAQ,QAAQ,IAAI,WAAW;AAAC,mBAAO,UAAU,CAAC;AAAA,UAAE;AAAA,QACtD;AAEA,YAAI,SAAS,WAAW,QAAQ;AAChC,YAAI,SAAS,SAAS,CAAC,IAAI;AAC3B,iBAAS,WAAWC,MAAK,KAAK;AAC5B,cAAI;AACJ,cAAI,OAAO,OAAO;AAChB,oBAAQ,SAAS,IAAI;AAAE,qBAAO,IAAI,IAAI,MAAM;AAAA,YAAG;AAAA,mBAExC,QAAQ,eAAe,GAAG;AACjC,oBAAQ,QAAQ,GAAG;AAAA;AAEnB,oBAAQ;AACV,iBAAOA,IAAG,IAAI;AAAA,QAChB;AACA,YAAI;AACF,mBAAS,OAAO,OAAQ,KAAI,OAAO,eAAe,GAAG;AACnD,uBAAW,KAAK,OAAO,GAAG,CAAC;AAAA;AAC/B,YAAI,QAAQ,WAAW,QAAQ;AAC/B,YAAI;AACF,mBAAS,OAAO,MAAO,KAAI,MAAM,eAAe,GAAG;AACjD,uBAAW,KAAK,MAAM,GAAG,CAAC;AAAA;AAC9B,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,cAAc,IAAI;AACxC,eAAO,MAAM,MAAM,cAAc;AAC/B,cAAI,GAAG,SAAS,YAAY,MAAM,QAAQ,GAAG,cAAc,aAAc,QAAO;AAChF,eAAK,GAAG;AAAA,QACV;AAAA,MACF;AAEA,eAAS,OAAO,YAAY,MAAM;AAChC,aAAK,KAAK,iBAAiB,KAAK,MAAM,KAAK,OAAO,GAAG,CAAC;AACtD,aAAK,aAAa;AAClB,aAAK,OAAO;AACZ,aAAK,SAAS;AACd,YAAI,SAAS,MAAM,KAAK,WAAW;AACnC,YAAI,gBAAgB,GAAG,cAAc,EAAE;AACvC,YAAI,eAAe,cAAc,eAAe,cAAc;AAE9D,YAAI,QAAQ,KAAK,QAAQ,cAAc,cAAc,IAAI;AACzD,cAAM,aAAa,QAAQ,SAAS;AACpC,cAAM,aAAa,iBAAiB,MAAM;AAC1C,cAAM,KAAK,KAAK;AAChB,YAAI,QAAQ,WAAW,GAAG,QAAQ;AAClC,cAAM,YAAY,sBAAsB;AACxC,aAAK,eAAe,KAAK,gBAAgB;AAEzC,YAAI,cAAc,KAAK;AACvB,iBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,EAAE,GAAG;AAC3C,cAAI,MAAM,MAAM,YAAY,cAAc,cAAc,IAAI,CAAC,GAAG,MAAM,YAAY,CAAC;AACnF,cAAI,YAAY,sBAAsB,KAAK,KAAK,eAAe,KAAK,MAAM;AAC1E,cAAI,IAAI,aAAa,KAAM,aAAY,IAAI,YAAY,MAAM;AAC7D,cAAI,YAAY;AAChB,cAAI,KAAK,KAAK,aAAc,KAAI,aAAa,iBAAiB,MAAM;AACpE,cAAI,KAAK,KAAK,KAAK,MAAM;AACzB,cAAI,aAAa,QAAQ,QAAQ;AACjC,cAAI,IAAI,OAAQ,KAAI,OAAO,KAAK,MAAM,GAAG;AAAA,cACpC,KAAI,YAAY,cAAc,eAAe,IAAI,eAAe,QAAQ,GAAG,CAAC,CAAC;AAClF,cAAI,SAAS;AAAA,QACf;AAEA,YAAI,YAAY,WAAW,QAAQ,aAAa,cAAc;AAC9D,YAAI,MAAM,GAAG,aAAa,WAAW,QAAQ,gBAAgB,KAAK,OAAO,IAAI;AAC7E,YAAI,OAAO,IAAI,MAAM,MAAM,IAAI,QAAQ,QAAQ;AAC/C,YAAI,aAAa,GAAG,YAAY;AAChC,YAAI,cAAc,cAAc,MAAM;AAEpC,cAAI,wBAAwB,CAAC,YAAY,YAAY,OAAO,EAAE,QAAQ,aAAa,iBAAiB,SAAS,EAAE,QAAQ,MAAM;AAC7H,cAAI,eAAe,wBAAwB,YAAY,UAAU;AACjE,cAAI,uBAAuB,aAAa,sBAAsB;AAC9D,cAAI,eAAe,cAAc,KAAK,sBAAsB;AAC5D,uBAAc,qBAAqB,OAAO,aAAa,OAAO,aAAa;AAC3E,sBAAa,qBAAqB,MAAM,aAAa,MAAM,aAAa;AAAA,QAC1E;AACA,cAAM,MAAM,OAAQ,OAAO,aAAc;AACzC,cAAM,MAAM,MAAO,MAAM,YAAa;AAGtC,YAAI,OAAO,aAAa,cAAc,KAAK,IAAI,cAAc,KAAK,aAAa,cAAc,gBAAgB,WAAW;AACxH,YAAI,OAAO,aAAa,eAAe,KAAK,IAAI,cAAc,KAAK,cAAc,cAAc,gBAAgB,YAAY;AAC3H,kBAAU,YAAY,KAAK;AAC3B,WAAG,cAAc,EAAE,aAAa,qBAAqB,MAAM;AAC3D,WAAG,cAAc,EAAE,aAAa,aAAa,KAAK,EAAE;AACpD,WAAG,cAAc,EAAE,aAAa,yBAAyB,KAAK,KAAK,MAAM,KAAK,YAAY;AAE1F,YAAI,MAAM,WAAW,QAAQ,gBAAgB,MAAM,sBAAsB,IAAI,IAAI,QAAQ;AACzF,YAAI,UAAU,WAAW,QAAQ,sBAAsB,MAAM,eAAe,MAAM,eAAe,IAAI;AAGrG,YAAI;AACJ,mBAAW,WAAW;AAAE,wBAAc,GAAG,cAAc;AAAA,QAAG,CAAC;AAE3D,YAAI,WAAW,IAAI,SAAS;AAC5B,YAAI,WAAW,GAAG;AAChB,cAAI,SAAS,IAAI,SAAS,IAAI,KAAK,aAAa,IAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnF,cAAI,OAAO,IAAI,MAAM,YAAY;AAC/B,gBAAI,SAAS,WAAY,OAAM,MAAM,UAAU,SAAS,cAAc;AACtE,kBAAM,MAAM,OAAQ,MAAM,IAAI,MAAM,UAAU,YAAa;AAC3D,oBAAQ;AAAA,UACV,OAAO;AACL,kBAAM,MAAM,SAAU,OAAO,IAAI,MAAM,IAAK;AAAA,UAC9C;AAAA,QACF;AACA,YAAI,WAAW,IAAI,QAAQ;AAC3B,YAAI,QAAS,aAAY,GAAG,QAAQ;AACpC,YAAI,WAAW,GAAG;AAChB,cAAI,IAAI,QAAQ,IAAI,OAAO,MAAM;AAC/B,kBAAM,MAAM,QAAS,OAAO,IAAK;AACjC,wBAAa,IAAI,QAAQ,IAAI,OAAQ;AAAA,UACvC;AACA,gBAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,IAAI,OAAO,WAAW,YAAY,CAAC,KAAK;AAAA,QAC9E;AACA,YAAI,QAAS,UAAS,OAAO,MAAM,YAAY,MAAM,OAAO,KAAK;AAC/D,eAAK,MAAM,eAAe,GAAG,QAAQ,iBAAiB;AAExD,WAAG,UAAU,KAAK,SAAS,YAAY,YAAY;AAAA,UACjD,WAAW,SAAS,GAAG,WAAW;AAAE,mBAAO,aAAa,OAAO,eAAe,GAAG,SAAS;AAAA,UAAG;AAAA,UAC7F,UAAU,SAAS,GAAG;AAAE,mBAAO,aAAa,CAAC;AAAA,UAAG;AAAA,UAChD,UAAU,WAAW;AAAE,mBAAO,OAAO,aAAa;AAAA,UAAG;AAAA,UACrD,QAAQ,YAAY;AAAA,UACpB,OAAO,WAAW;AAAE,uBAAW,MAAM;AAAA,UAAG;AAAA,UACxC,MAAM,WAAW;AAAE,mBAAO,KAAK;AAAA,UAAG;AAAA,UAClC;AAAA,QACF,CAAC,CAAC;AAEF,YAAI,WAAW,QAAQ,gBAAgB;AACrC,cAAI;AACJ,aAAG,GAAG,QAAQ,KAAK,SAAS,WAAW;AAAE,4BAAgB,WAAW,WAAW;AAAE,yBAAW,MAAM;AAAA,YAAG,GAAG,GAAG;AAAA,UAAG,CAAC;AAC/G,aAAG,GAAG,SAAS,KAAK,UAAU,WAAW;AAAE,yBAAa,aAAa;AAAA,UAAG,CAAC;AAAA,QAC3E;AAEA,WAAG,GAAG,UAAU,KAAK,WAAW,WAAW;AACzC,cAAI,YAAY,GAAG,cAAc,GAAG,SAAS,GAAG,kBAAkB,EAAE,sBAAsB;AAC1F,cAAI,CAAC,YAAa,eAAc,GAAG,cAAc;AACjD,cAAI,SAAS,MAAM,YAAY,MAAM,UAAU;AAC/C,cAAI,QAAQ,UAAU,aAAa,gBAAgB,cAAc,mBAAmB,cAAc,MAAM;AACxG,cAAI,CAAC,MAAO,UAAS,MAAM;AAC3B,cAAI,SAAS,OAAO,OAAO,SAAS,OAAO,OAAQ,QAAO,WAAW,MAAM;AAC3E,gBAAM,MAAM,MAAM,SAAS;AAC3B,gBAAM,MAAM,OAAQ,OAAO,YAAY,OAAO,UAAU,OAAQ;AAAA,QAClE,CAAC;AAED,QAAAD,YAAW,GAAG,OAAO,YAAY,SAAS,GAAG;AAC3C,cAAI,IAAI,eAAe,OAAO,EAAE,UAAU,EAAE,UAAU;AACtD,cAAI,KAAK,EAAE,UAAU,MAAM;AAAC,mBAAO,aAAa,EAAE,MAAM;AAAG,mBAAO,KAAK;AAAA,UAAE;AAAA,QAC3E,CAAC;AAED,QAAAA,YAAW,GAAG,OAAO,SAAS,SAAS,GAAG;AACxC,cAAI,IAAI,eAAe,OAAO,EAAE,UAAU,EAAE,UAAU;AACtD,cAAI,KAAK,EAAE,UAAU,MAAM;AACzB,mBAAO,aAAa,EAAE,MAAM;AAC5B,gBAAI,WAAW,QAAQ,sBAAuB,QAAO,KAAK;AAAA,UAC5D;AAAA,QACF,CAAC;AAED,QAAAA,YAAW,GAAG,OAAO,aAAa,WAAW;AAC3C,qBAAW,WAAU;AAAC,eAAG,MAAM;AAAA,UAAE,GAAG,EAAE;AAAA,QACxC,CAAC;AAGD,YAAI,oBAAoB,KAAK,qBAAqB;AAClD,YAAI,kBAAkB,SAAS,KAAK,kBAAkB,OAAO,GAAG;AAC9D,eAAK,eAAe;AAAA,QACtB;AAEA,QAAAA,YAAW,OAAO,MAAM,UAAU,YAAY,KAAK,YAAY,GAAG,MAAM,WAAW,KAAK,YAAY,CAAC;AACrG,eAAO;AAAA,MACT;AAEA,aAAO,YAAY;AAAA,QACjB,OAAO,WAAW;AAChB,cAAI,KAAK,WAAW,UAAU,KAAM;AACpC,eAAK,WAAW,SAAS;AACzB,cAAI,KAAK,MAAM,WAAY,MAAK,MAAM,WAAW,YAAY,KAAK,KAAK;AACvE,eAAK,WAAW,GAAG,aAAa,KAAK,MAAM;AAC3C,cAAI,QAAQ,KAAK,WAAW,GAAG,cAAc;AAC7C,gBAAM,gBAAgB,uBAAuB;AAC7C,gBAAM,gBAAgB,WAAW;AAEjC,cAAI,KAAK,KAAK,WAAW;AACzB,cAAI,KAAK,WAAW,QAAQ,gBAAgB;AAC1C,eAAG,IAAI,QAAQ,KAAK,MAAM;AAC1B,eAAG,IAAI,SAAS,KAAK,OAAO;AAAA,UAC9B;AACA,aAAG,IAAI,UAAU,KAAK,QAAQ;AAAA,QAChC;AAAA,QAEA,SAAS,WAAW;AAClB,eAAK,WAAW,GAAG,aAAa,KAAK,MAAM;AAC3C,cAAI,SAAS;AACb,eAAK,SAAS,EAAC,OAAO,WAAW;AAAE,mBAAO,SAAS;AAAA,UAAM,EAAC;AAC1D,eAAK,WAAW,GAAG,UAAU,KAAK,MAAM;AAAA,QAC1C;AAAA,QAEA,MAAM,WAAW;AACf,eAAK,WAAW,KAAK,KAAK,MAAM,KAAK,YAAY;AAAA,QACnD;AAAA,QAEA,cAAc,SAAS,GAAG,WAAW;AACnC,cAAI,KAAK,KAAK,KAAK,KAAK;AACtB,gBAAI,YAAY,KAAK,KAAK,KAAK,SAAS,IAAI;AAAA,mBACrC,IAAI;AACX,gBAAI,YAAY,IAAK,KAAK,KAAK,KAAK,SAAS;AAC/C,cAAI,KAAK,gBAAgB,EAAG;AAC5B,cAAI,OAAO,KAAK,MAAM,WAAW,KAAK,YAAY;AAClD,cAAI,MAAM;AACR,iBAAK,YAAY,KAAK,UAAU,QAAQ,MAAM,2BAA2B,EAAE;AAC3E,iBAAK,gBAAgB,eAAe;AAAA,UACtC;AACA,iBAAO,KAAK,MAAM,WAAW,KAAK,eAAe,CAAC;AAClD,eAAK,aAAa,MAAM;AACxB,eAAK,aAAa,iBAAiB,MAAM;AACzC,eAAK,WAAW,GAAG,cAAc,EAAE,aAAa,yBAAyB,KAAK,EAAE;AAChF,eAAK,eAAe;AACpB,UAAAA,YAAW,OAAO,KAAK,MAAM,UAAU,KAAK,KAAK,KAAK,KAAK,YAAY,GAAG,IAAI;AAAA,QAChF;AAAA,QAEA,gBAAgB,WAAW;AACzB,cAAI,oBAAoB,KAAK,qBAAqB;AAClD,cAAI,QAAQ,KAAK,MAAM,WAAW,kBAAkB,IAAI;AACxD,cAAI,QAAQ,KAAK,MAAM,WAAW,kBAAkB,EAAE;AACtD,cAAI,YAAY,KAAK,MAAM;AAC3B,cAAI,MAAM,YAAY,KAAK,MAAM;AAC/B,iBAAK,MAAM,YAAY,MAAM,YAAY,UAAU;AAAA,mBAC5C,MAAM,YAAY,MAAM,eAAe,KAAK,MAAM,YAAY,KAAK,MAAM;AAChF,iBAAK,MAAM,YAAY,MAAM,YAAY,MAAM,eAAe,KAAK,MAAM,eAAe,UAAU;AAAA,QACtG;AAAA,QAEA,cAAc,WAAW;AACvB,iBAAO,KAAK,MAAM,KAAK,MAAM,eAAe,KAAK,MAAM,WAAW,YAAY,KAAK;AAAA,QACrF;AAAA,QAEA,sBAAsB,WAAW;AAC/B,cAAI,SAAS,KAAK,WAAW,QAAQ,gBAAgB;AACrD,iBAAO;AAAA,YACL,MAAM,KAAK,IAAI,GAAG,KAAK,eAAe,MAAM;AAAA,YAC5C,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,GAAG,KAAK,eAAe,MAAM;AAAA,UACpE;AAAA,QACF;AAAA,MACF;AAEA,eAAS,kBAAkB,IAAI,SAAS;AACtC,YAAI,CAAC,GAAG,kBAAkB,EAAG,QAAO;AACpC,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAClC,cAAI,QAAQ,CAAC,EAAE,kBAAmB,QAAO,KAAK,QAAQ,CAAC,CAAC;AAC1D,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,MAAM,IAAI,SAAS,UAAU;AAC/C,YAAI,KAAK,OAAO;AACd,eAAK,IAAI,UAAU,OAAO;AAAA,QAC5B,OAAO;AACL,cAAI,SAAS,KAAK,IAAI,OAAO;AAC7B,cAAI,UAAU,OAAO,KAAM,QAAO,KAAK,QAAQ;AAAA,cAC1C,UAAS,MAAM;AAAA,QACtB;AAAA,MACF;AAEA,eAAS,iBAAiB,IAAI,KAAK;AACjC,YAAI,UAAU,GAAG,WAAW,KAAK,MAAM,GAAG;AAC1C,YAAI,QAAQ,QAAQ;AAClB,cAAI,WAAW,SAASE,KAAI,UAAU,SAAS;AAC7C,gBAAI,MAAM,kBAAkBA,KAAI,OAAO;AACvC,qBAAS,IAAI,GAAG;AACd,kBAAI,KAAK,IAAI,OAAQ,QAAO,SAAS,IAAI;AACzC,yBAAW,IAAI,CAAC,GAAGA,KAAI,SAAS,SAAS,QAAQ;AAC/C,oBAAI,UAAU,OAAO,KAAK,SAAS,EAAG,UAAS,MAAM;AAAA,oBAChD,KAAI,IAAI,CAAC;AAAA,cAChB,CAAC;AAAA,YACH;AACA,gBAAI,CAAC;AAAA,UACP;AACA,mBAAS,QAAQ;AACjB,mBAAS,oBAAoB;AAC7B,iBAAO;AAAA,QACT,WAAW,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,WAAW,GAAG;AAC5D,iBAAO,SAASA,KAAI;AAAE,mBAAOF,YAAW,KAAK,SAASE,KAAI,EAAC,MAAY,CAAC;AAAA,UAAE;AAAA,QAC5E,WAAWF,YAAW,KAAK,SAAS;AAClC,iBAAO,SAASE,KAAI,SAAS;AAAE,mBAAOF,YAAW,KAAK,QAAQE,KAAI,OAAO;AAAA,UAAE;AAAA,QAC7E,OAAO;AACL,iBAAO,WAAW;AAAA,UAAC;AAAA,QACrB;AAAA,MACF;AAEA,MAAAF,YAAW,eAAe,QAAQ,QAAQ;AAAA,QACxC,SAAS;AAAA,MACX,CAAC;AAED,MAAAA,YAAW,eAAe,QAAQ,YAAY,SAAS,IAAI,SAAS;AAClE,YAAI,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,WAAW,GAAG;AACnD,YAAI,MAAM,OAAOA,YAAW,IAAI,IAAI,MAAM,MAAM,KAAK,GAAG,KAAK;AAC7D,YAAI,MAAM,QAAQ,IAAI,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AACpF,iBAAO,MAAM,OAAO,OAAO,GAAG,IAAI,KAAK,MAAM,KAAK;AAAA,QACpD,OAAO;AACL,iBAAO;AACP,iBAAO;AAAA,QACT;AACA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC7C,cAAI,OAAO,QAAQ,MAAM,CAAC;AAC1B,cAAI,KAAK,MAAM,GAAG,KAAK,MAAM,KAAK;AAChC,kBAAM,KAAK,IAAI;AAAA,QACnB;AAEA,YAAI,MAAM,OAAQ,QAAO,EAAC,MAAM,OAAO,MAAY,GAAM;AAAA,MAC3D,CAAC;AAED,MAAAA,YAAW,SAAS,eAAeA,YAAW;AAE9C,UAAI,iBAAiB;AAAA,QACnB,MAAMA,YAAW,KAAK;AAAA,QACtB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,eAAe;AAAA,MACjB;AAEA,MAAAA,YAAW,aAAa,eAAe,IAAI;AAAA,IAC7C,CAAC;AAAA;AAAA;", "names": ["import_dist", "CodeMirror", "key", "cm"]}