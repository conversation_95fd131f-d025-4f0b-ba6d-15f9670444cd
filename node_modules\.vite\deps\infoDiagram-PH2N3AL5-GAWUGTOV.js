import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  parse
} from "./chunk-CJJ6QZ3B.js";
import "./chunk-SOEBE2BH.js";
import "./chunk-X4OOHYWR.js";
import "./chunk-ETHDTUWD.js";
import "./chunk-KPUJJMWY.js";
import "./chunk-NZKR3EU3.js";
import "./chunk-NPC5QHFT.js";
import "./chunk-H4LMIB3O.js";
import "./chunk-IKTTXOWS.js";
import {
  package_default
} from "./chunk-JOGQGSMD.js";
import {
  selectSvgElement
} from "./chunk-R6BC42JI.js";
import {
  __name,
  configureSvgSize,
  log
} from "./chunk-C5K7Y3YN.js";
import "./chunk-OSPUQ53P.js";
import "./chunk-XNDO3ZN6.js";
import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-PH2N3AL5.mjs
var import_dist = __toESM(require_dist(), 1);
var import_dist2 = __toESM(require_dist2(), 1);
var import_dist3 = __toESM(require_dist3(), 1);
var parser = {
  parse: __name(async (input) => {
    const ast = await parse("info", input);
    log.debug(ast);
  }, "parse")
};
var DEFAULT_INFO_DB = { version: package_default.version };
var getVersion = __name(() => DEFAULT_INFO_DB.version, "getVersion");
var db = {
  getVersion
};
var draw = __name((text, id, version) => {
  log.debug("rendering info diagram\n" + text);
  const svg = selectSvgElement(id);
  configureSvgSize(svg, 100, 400, true);
  const group = svg.append("g");
  group.append("text").attr("x", 100).attr("y", 40).attr("class", "version").attr("font-size", 32).style("text-anchor", "middle").text(`v${version}`);
}, "draw");
var renderer = { draw };
var diagram = {
  parser,
  db,
  renderer
};
export {
  diagram
};
//# sourceMappingURL=infoDiagram-PH2N3AL5-GAWUGTOV.js.map
