{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "strict": true, "skipLibCheck": true, "paths": {"@": ["../src"], "@/*": ["../src/*"], "~": ["../src"], "~/*": ["../src/*"], "@@": [".."], "@@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "/src/main.ts": ["../src/main.ts"], "/src/main.ts/*": ["../src/main.ts/*"], "/src/sidepanel.ts": ["../src/sidepanel.ts"], "/src/sidepanel.ts/*": ["../src/sidepanel.ts/*"]}}, "include": ["../**/*", "./wxt.d.ts"], "exclude": ["../.output"]}