import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  Fragment,
  Teleport,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  inject,
  nextTick,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  openBlock,
  provide,
  ref,
  renderList,
  resolveComponent,
  toDisplayString,
  toRaw,
  unref,
  watch,
  withCtx,
  withKeys,
  withModifiers
} from "./chunk-CM5VDFBH.js";
import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/vue-pick-colors/dist/index.esm.js
var import_dist172 = __toESM(require_dist());
var import_dist173 = __toESM(require_dist2());
var import_dist174 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/index.js
var import_dist169 = __toESM(require_dist());
var import_dist170 = __toESM(require_dist2());
var import_dist171 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/enums.js
var import_dist = __toESM(require_dist());
var import_dist2 = __toESM(require_dist2());
var import_dist3 = __toESM(require_dist3());
var top = "top";
var bottom = "bottom";
var right = "right";
var left = "left";
var auto = "auto";
var basePlacements = [top, bottom, right, left];
var start = "start";
var end = "end";
var clippingParents = "clippingParents";
var viewport = "viewport";
var popper = "popper";
var reference = "reference";
var variationPlacements = basePlacements.reduce(function(acc, placement) {
  return acc.concat([placement + "-" + start, placement + "-" + end]);
}, []);
var placements = [].concat(basePlacements, [auto]).reduce(function(acc, placement) {
  return acc.concat([placement, placement + "-" + start, placement + "-" + end]);
}, []);
var beforeRead = "beforeRead";
var read = "read";
var afterRead = "afterRead";
var beforeMain = "beforeMain";
var main = "main";
var afterMain = "afterMain";
var beforeWrite = "beforeWrite";
var write = "write";
var afterWrite = "afterWrite";
var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];

// node_modules/@popperjs/core/lib/modifiers/index.js
var import_dist139 = __toESM(require_dist());
var import_dist140 = __toESM(require_dist2());
var import_dist141 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/modifiers/applyStyles.js
var import_dist13 = __toESM(require_dist());
var import_dist14 = __toESM(require_dist2());
var import_dist15 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getNodeName.js
var import_dist4 = __toESM(require_dist());
var import_dist5 = __toESM(require_dist2());
var import_dist6 = __toESM(require_dist3());
function getNodeName(element) {
  return element ? (element.nodeName || "").toLowerCase() : null;
}

// node_modules/@popperjs/core/lib/dom-utils/instanceOf.js
var import_dist10 = __toESM(require_dist());
var import_dist11 = __toESM(require_dist2());
var import_dist12 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getWindow.js
var import_dist7 = __toESM(require_dist());
var import_dist8 = __toESM(require_dist2());
var import_dist9 = __toESM(require_dist3());
function getWindow(node) {
  if (node == null) {
    return window;
  }
  if (node.toString() !== "[object Window]") {
    var ownerDocument = node.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView || window : window;
  }
  return node;
}

// node_modules/@popperjs/core/lib/dom-utils/instanceOf.js
function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
  if (typeof ShadowRoot === "undefined") {
    return false;
  }
  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}

// node_modules/@popperjs/core/lib/modifiers/applyStyles.js
function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function(name) {
    var style = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name];
    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    }
    Object.assign(element.style, style);
    Object.keys(attributes).forEach(function(name2) {
      var value = attributes[name2];
      if (value === false) {
        element.removeAttribute(name2);
      } else {
        element.setAttribute(name2, value === true ? "" : value);
      }
    });
  });
}
function effect(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;
  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }
  return function() {
    Object.keys(state.elements).forEach(function(name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]);
      var style = styleProperties.reduce(function(style2, property) {
        style2[property] = "";
        return style2;
      }, {});
      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }
      Object.assign(element.style, style);
      Object.keys(attributes).forEach(function(attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
}
var applyStyles_default = {
  name: "applyStyles",
  enabled: true,
  phase: "write",
  fn: applyStyles,
  effect,
  requires: ["computeStyles"]
};

// node_modules/@popperjs/core/lib/modifiers/arrow.js
var import_dist67 = __toESM(require_dist());
var import_dist68 = __toESM(require_dist2());
var import_dist69 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/getBasePlacement.js
var import_dist16 = __toESM(require_dist());
var import_dist17 = __toESM(require_dist2());
var import_dist18 = __toESM(require_dist3());
function getBasePlacement(placement) {
  return placement.split("-")[0];
}

// node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js
var import_dist31 = __toESM(require_dist());
var import_dist32 = __toESM(require_dist2());
var import_dist33 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js
var import_dist28 = __toESM(require_dist());
var import_dist29 = __toESM(require_dist2());
var import_dist30 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/math.js
var import_dist19 = __toESM(require_dist());
var import_dist20 = __toESM(require_dist2());
var import_dist21 = __toESM(require_dist3());
var max = Math.max;
var min = Math.min;
var round = Math.round;

// node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js
var import_dist25 = __toESM(require_dist());
var import_dist26 = __toESM(require_dist2());
var import_dist27 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/userAgent.js
var import_dist22 = __toESM(require_dist());
var import_dist23 = __toESM(require_dist2());
var import_dist24 = __toESM(require_dist3());
function getUAString() {
  var uaData = navigator.userAgentData;
  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {
    return uaData.brands.map(function(item) {
      return item.brand + "/" + item.version;
    }).join(" ");
  }
  return navigator.userAgent;
}

// node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js
function isLayoutViewport() {
  return !/^((?!chrome|android).)*safari/i.test(getUAString());
}

// node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js
function getBoundingClientRect(element, includeScale, isFixedStrategy) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  var clientRect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;
  if (includeScale && isHTMLElement(element)) {
    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
  }
  var _ref = isElement(element) ? getWindow(element) : window, visualViewport = _ref.visualViewport;
  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
  var width = clientRect.width / scaleX;
  var height = clientRect.height / scaleY;
  return {
    width,
    height,
    top: y,
    right: x + width,
    bottom: y + height,
    left: x,
    x,
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js
function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element);
  var width = element.offsetWidth;
  var height = element.offsetHeight;
  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }
  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }
  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width,
    height
  };
}

// node_modules/@popperjs/core/lib/dom-utils/contains.js
var import_dist34 = __toESM(require_dist());
var import_dist35 = __toESM(require_dist2());
var import_dist36 = __toESM(require_dist3());
function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode();
  if (parent.contains(child)) {
    return true;
  } else if (rootNode && isShadowRoot(rootNode)) {
    var next = child;
    do {
      if (next && parent.isSameNode(next)) {
        return true;
      }
      next = next.parentNode || next.host;
    } while (next);
  }
  return false;
}

// node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js
var import_dist49 = __toESM(require_dist());
var import_dist50 = __toESM(require_dist2());
var import_dist51 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js
var import_dist37 = __toESM(require_dist());
var import_dist38 = __toESM(require_dist2());
var import_dist39 = __toESM(require_dist3());
function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}

// node_modules/@popperjs/core/lib/dom-utils/isTableElement.js
var import_dist40 = __toESM(require_dist());
var import_dist41 = __toESM(require_dist2());
var import_dist42 = __toESM(require_dist3());
function isTableElement(element) {
  return ["table", "td", "th"].indexOf(getNodeName(element)) >= 0;
}

// node_modules/@popperjs/core/lib/dom-utils/getParentNode.js
var import_dist46 = __toESM(require_dist());
var import_dist47 = __toESM(require_dist2());
var import_dist48 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js
var import_dist43 = __toESM(require_dist());
var import_dist44 = __toESM(require_dist2());
var import_dist45 = __toESM(require_dist3());
function getDocumentElement(element) {
  return ((isElement(element) ? element.ownerDocument : (
    // $FlowFixMe[prop-missing]
    element.document
  )) || window.document).documentElement;
}

// node_modules/@popperjs/core/lib/dom-utils/getParentNode.js
function getParentNode(element) {
  if (getNodeName(element) === "html") {
    return element;
  }
  return (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || // DOM Element detected
    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element)
  );
}

// node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js
function getTrueOffsetParent(element) {
  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
  getComputedStyle(element).position === "fixed") {
    return null;
  }
  return element.offsetParent;
}
function getContainingBlock(element) {
  var isFirefox = /firefox/i.test(getUAString());
  var isIE = /Trident/i.test(getUAString());
  if (isIE && isHTMLElement(element)) {
    var elementCss = getComputedStyle(element);
    if (elementCss.position === "fixed") {
      return null;
    }
  }
  var currentNode = getParentNode(element);
  if (isShadowRoot(currentNode)) {
    currentNode = currentNode.host;
  }
  while (isHTMLElement(currentNode) && ["html", "body"].indexOf(getNodeName(currentNode)) < 0) {
    var css = getComputedStyle(currentNode);
    if (css.transform !== "none" || css.perspective !== "none" || css.contain === "paint" || ["transform", "perspective"].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === "filter" || isFirefox && css.filter && css.filter !== "none") {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }
  return null;
}
function getOffsetParent(element) {
  var window2 = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);
  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === "static") {
    offsetParent = getTrueOffsetParent(offsetParent);
  }
  if (offsetParent && (getNodeName(offsetParent) === "html" || getNodeName(offsetParent) === "body" && getComputedStyle(offsetParent).position === "static")) {
    return window2;
  }
  return offsetParent || getContainingBlock(element) || window2;
}

// node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js
var import_dist52 = __toESM(require_dist());
var import_dist53 = __toESM(require_dist2());
var import_dist54 = __toESM(require_dist3());
function getMainAxisFromPlacement(placement) {
  return ["top", "bottom"].indexOf(placement) >= 0 ? "x" : "y";
}

// node_modules/@popperjs/core/lib/utils/within.js
var import_dist55 = __toESM(require_dist());
var import_dist56 = __toESM(require_dist2());
var import_dist57 = __toESM(require_dist3());
function within(min2, value, max2) {
  return max(min2, min(value, max2));
}
function withinMaxClamp(min2, value, max2) {
  var v = within(min2, value, max2);
  return v > max2 ? max2 : v;
}

// node_modules/@popperjs/core/lib/utils/mergePaddingObject.js
var import_dist61 = __toESM(require_dist());
var import_dist62 = __toESM(require_dist2());
var import_dist63 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/getFreshSideObject.js
var import_dist58 = __toESM(require_dist());
var import_dist59 = __toESM(require_dist2());
var import_dist60 = __toESM(require_dist3());
function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}

// node_modules/@popperjs/core/lib/utils/mergePaddingObject.js
function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}

// node_modules/@popperjs/core/lib/utils/expandToHashMap.js
var import_dist64 = __toESM(require_dist());
var import_dist65 = __toESM(require_dist2());
var import_dist66 = __toESM(require_dist3());
function expandToHashMap(value, keys) {
  return keys.reduce(function(hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}

// node_modules/@popperjs/core/lib/modifiers/arrow.js
var toPaddingObject = function toPaddingObject2(padding, state) {
  padding = typeof padding === "function" ? padding(Object.assign({}, state.rects, {
    placement: state.placement
  })) : padding;
  return mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
};
function arrow(_ref) {
  var _state$modifiersData$;
  var state = _ref.state, name = _ref.name, options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? "height" : "width";
  if (!arrowElement || !popperOffsets2) {
    return;
  }
  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === "y" ? top : left;
  var maxProp = axis === "y" ? bottom : right;
  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets2[axis] - state.rects.popper[len];
  var startDiff = popperOffsets2[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent ? axis === "y" ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
  var centerToReference = endDiff / 2 - startDiff / 2;
  var min2 = paddingObject[minProp];
  var max2 = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset2 = within(min2, center, max2);
  var axisProp = axis;
  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset2, _state$modifiersData$.centerOffset = offset2 - center, _state$modifiersData$);
}
function effect2(_ref2) {
  var state = _ref2.state, options = _ref2.options;
  var _options$element = options.element, arrowElement = _options$element === void 0 ? "[data-popper-arrow]" : _options$element;
  if (arrowElement == null) {
    return;
  }
  if (typeof arrowElement === "string") {
    arrowElement = state.elements.popper.querySelector(arrowElement);
    if (!arrowElement) {
      return;
    }
  }
  if (!contains(state.elements.popper, arrowElement)) {
    return;
  }
  state.elements.arrow = arrowElement;
}
var arrow_default = {
  name: "arrow",
  enabled: true,
  phase: "main",
  fn: arrow,
  effect: effect2,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};

// node_modules/@popperjs/core/lib/modifiers/computeStyles.js
var import_dist73 = __toESM(require_dist());
var import_dist74 = __toESM(require_dist2());
var import_dist75 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/getVariation.js
var import_dist70 = __toESM(require_dist());
var import_dist71 = __toESM(require_dist2());
var import_dist72 = __toESM(require_dist3());
function getVariation(placement) {
  return placement.split("-")[1];
}

// node_modules/@popperjs/core/lib/modifiers/computeStyles.js
var unsetSides = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function roundOffsetsByDPR(_ref, win) {
  var x = _ref.x, y = _ref.y;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(x * dpr) / dpr || 0,
    y: round(y * dpr) / dpr || 0
  };
}
function mapToStyles(_ref2) {
  var _Object$assign2;
  var popper2 = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;
  var _offsets$x = offsets.x, x = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y = _offsets$y === void 0 ? 0 : _offsets$y;
  var _ref3 = typeof roundOffsets === "function" ? roundOffsets({
    x,
    y
  }) : {
    x,
    y
  };
  x = _ref3.x;
  y = _ref3.y;
  var hasX = offsets.hasOwnProperty("x");
  var hasY = offsets.hasOwnProperty("y");
  var sideX = left;
  var sideY = top;
  var win = window;
  if (adaptive) {
    var offsetParent = getOffsetParent(popper2);
    var heightProp = "clientHeight";
    var widthProp = "clientWidth";
    if (offsetParent === getWindow(popper2)) {
      offsetParent = getDocumentElement(popper2);
      if (getComputedStyle(offsetParent).position !== "static" && position === "absolute") {
        heightProp = "scrollHeight";
        widthProp = "scrollWidth";
      }
    }
    offsetParent = offsetParent;
    if (placement === top || (placement === left || placement === right) && variation === end) {
      sideY = bottom;
      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : (
        // $FlowFixMe[prop-missing]
        offsetParent[heightProp]
      );
      y -= offsetY - popperRect.height;
      y *= gpuAcceleration ? 1 : -1;
    }
    if (placement === left || (placement === top || placement === bottom) && variation === end) {
      sideX = right;
      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : (
        // $FlowFixMe[prop-missing]
        offsetParent[widthProp]
      );
      x -= offsetX - popperRect.width;
      x *= gpuAcceleration ? 1 : -1;
    }
  }
  var commonStyles = Object.assign({
    position
  }, adaptive && unsetSides);
  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
    x,
    y
  }, getWindow(popper2)) : {
    x,
    y
  };
  x = _ref4.x;
  y = _ref4.y;
  if (gpuAcceleration) {
    var _Object$assign;
    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? "0" : "", _Object$assign[sideX] = hasX ? "0" : "", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x + "px, " + y + "px)" : "translate3d(" + x + "px, " + y + "px, 0)", _Object$assign));
  }
  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + "px" : "", _Object$assign2[sideX] = hasX ? x + "px" : "", _Object$assign2.transform = "", _Object$assign2));
}
function computeStyles(_ref5) {
  var state = _ref5.state, options = _ref5.options;
  var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;
  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration,
    isFixed: state.options.strategy === "fixed"
  };
  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.popperOffsets,
      position: state.options.strategy,
      adaptive,
      roundOffsets
    })));
  }
  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.arrow,
      position: "absolute",
      adaptive: false,
      roundOffsets
    })));
  }
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-placement": state.placement
  });
}
var computeStyles_default = {
  name: "computeStyles",
  enabled: true,
  phase: "beforeWrite",
  fn: computeStyles,
  data: {}
};

// node_modules/@popperjs/core/lib/modifiers/eventListeners.js
var import_dist76 = __toESM(require_dist());
var import_dist77 = __toESM(require_dist2());
var import_dist78 = __toESM(require_dist3());
var passive = {
  passive: true
};
function effect3(_ref) {
  var state = _ref.state, instance = _ref.instance, options = _ref.options;
  var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;
  var window2 = getWindow(state.elements.popper);
  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);
  if (scroll) {
    scrollParents.forEach(function(scrollParent) {
      scrollParent.addEventListener("scroll", instance.update, passive);
    });
  }
  if (resize) {
    window2.addEventListener("resize", instance.update, passive);
  }
  return function() {
    if (scroll) {
      scrollParents.forEach(function(scrollParent) {
        scrollParent.removeEventListener("scroll", instance.update, passive);
      });
    }
    if (resize) {
      window2.removeEventListener("resize", instance.update, passive);
    }
  };
}
var eventListeners_default = {
  name: "eventListeners",
  enabled: true,
  phase: "write",
  fn: function fn() {
  },
  effect: effect3,
  data: {}
};

// node_modules/@popperjs/core/lib/modifiers/flip.js
var import_dist121 = __toESM(require_dist());
var import_dist122 = __toESM(require_dist2());
var import_dist123 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/getOppositePlacement.js
var import_dist79 = __toESM(require_dist());
var import_dist80 = __toESM(require_dist2());
var import_dist81 = __toESM(require_dist3());
var hash = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function(matched) {
    return hash[matched];
  });
}

// node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js
var import_dist82 = __toESM(require_dist());
var import_dist83 = __toESM(require_dist2());
var import_dist84 = __toESM(require_dist3());
var hash2 = {
  start: "end",
  end: "start"
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function(matched) {
    return hash2[matched];
  });
}

// node_modules/@popperjs/core/lib/utils/detectOverflow.js
var import_dist115 = __toESM(require_dist());
var import_dist116 = __toESM(require_dist2());
var import_dist117 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js
var import_dist109 = __toESM(require_dist());
var import_dist110 = __toESM(require_dist2());
var import_dist111 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js
var import_dist91 = __toESM(require_dist());
var import_dist92 = __toESM(require_dist2());
var import_dist93 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js
var import_dist88 = __toESM(require_dist());
var import_dist89 = __toESM(require_dist2());
var import_dist90 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js
var import_dist85 = __toESM(require_dist());
var import_dist86 = __toESM(require_dist2());
var import_dist87 = __toESM(require_dist3());
function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft,
    scrollTop
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js
function getWindowScrollBarX(element) {
  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}

// node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js
function getViewportRect(element, strategy) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x = 0;
  var y = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    var layoutViewport = isLayoutViewport();
    if (layoutViewport || !layoutViewport && strategy === "fixed") {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x + getWindowScrollBarX(element),
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js
var import_dist94 = __toESM(require_dist());
var import_dist95 = __toESM(require_dist2());
var import_dist96 = __toESM(require_dist3());
function getDocumentRect(element) {
  var _element$ownerDocumen;
  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y = -winScroll.scrollTop;
  if (getComputedStyle(body || html).direction === "rtl") {
    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }
  return {
    width,
    height,
    x,
    y
  };
}

// node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js
var import_dist103 = __toESM(require_dist());
var import_dist104 = __toESM(require_dist2());
var import_dist105 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js
var import_dist100 = __toESM(require_dist());
var import_dist101 = __toESM(require_dist2());
var import_dist102 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js
var import_dist97 = __toESM(require_dist());
var import_dist98 = __toESM(require_dist2());
var import_dist99 = __toESM(require_dist3());
function isScrollParent(element) {
  var _getComputedStyle = getComputedStyle(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;
  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}

// node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js
function getScrollParent(node) {
  if (["html", "body", "#document"].indexOf(getNodeName(node)) >= 0) {
    return node.ownerDocument.body;
  }
  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }
  return getScrollParent(getParentNode(node));
}

// node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js
function listScrollParents(element, list) {
  var _element$ownerDocumen;
  if (list === void 0) {
    list = [];
  }
  var scrollParent = getScrollParent(element);
  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
  var updatedList = list.concat(target);
  return isBody ? updatedList : (
    // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
    updatedList.concat(listScrollParents(getParentNode(target)))
  );
}

// node_modules/@popperjs/core/lib/utils/rectToClientRect.js
var import_dist106 = __toESM(require_dist());
var import_dist107 = __toESM(require_dist2());
var import_dist108 = __toESM(require_dist3());
function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js
function getInnerBoundingClientRect(element, strategy) {
  var rect = getBoundingClientRect(element, false, strategy === "fixed");
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}
function getClientRectFromMixedType(element, clippingParent, strategy) {
  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
}
function getClippingParents(element) {
  var clippingParents2 = listScrollParents(getParentNode(element));
  var canEscapeClipping = ["absolute", "fixed"].indexOf(getComputedStyle(element).position) >= 0;
  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;
  if (!isElement(clipperElement)) {
    return [];
  }
  return clippingParents2.filter(function(clippingParent) {
    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== "body";
  });
}
function getClippingRect(element, boundary, rootBoundary, strategy) {
  var mainClippingParents = boundary === "clippingParents" ? getClippingParents(element) : [].concat(boundary);
  var clippingParents2 = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents2[0];
  var clippingRect = clippingParents2.reduce(function(accRect, clippingParent) {
    var rect = getClientRectFromMixedType(element, clippingParent, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromMixedType(element, firstClippingParent, strategy));
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}

// node_modules/@popperjs/core/lib/utils/computeOffsets.js
var import_dist112 = __toESM(require_dist());
var import_dist113 = __toESM(require_dist2());
var import_dist114 = __toESM(require_dist3());
function computeOffsets(_ref) {
  var reference2 = _ref.reference, element = _ref.element, placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference2.x + reference2.width / 2 - element.width / 2;
  var commonY = reference2.y + reference2.height / 2 - element.height / 2;
  var offsets;
  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference2.y - element.height
      };
      break;
    case bottom:
      offsets = {
        x: commonX,
        y: reference2.y + reference2.height
      };
      break;
    case right:
      offsets = {
        x: reference2.x + reference2.width,
        y: commonY
      };
      break;
    case left:
      offsets = {
        x: reference2.x - element.width,
        y: commonY
      };
      break;
    default:
      offsets = {
        x: reference2.x,
        y: reference2.y
      };
  }
  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
  if (mainAxis != null) {
    var len = mainAxis === "y" ? "height" : "width";
    switch (variation) {
      case start:
        offsets[mainAxis] = offsets[mainAxis] - (reference2[len] / 2 - element[len] / 2);
        break;
      case end:
        offsets[mainAxis] = offsets[mainAxis] + (reference2[len] / 2 - element[len] / 2);
        break;
      default:
    }
  }
  return offsets;
}

// node_modules/@popperjs/core/lib/utils/detectOverflow.js
function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(typeof padding !== "number" ? padding : expandToHashMap(padding, basePlacements));
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets2 = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: "absolute",
    placement
  });
  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets2));
  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect;
  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right: elementClientRect.right - clippingClientRect.right + paddingObject.right
  };
  var offsetData = state.modifiersData.offset;
  if (elementContext === popper && offsetData) {
    var offset2 = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function(key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? "y" : "x";
      overflowOffsets[key] += offset2[axis] * multiply;
    });
  }
  return overflowOffsets;
}

// node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js
var import_dist118 = __toESM(require_dist());
var import_dist119 = __toESM(require_dist2());
var import_dist120 = __toESM(require_dist3());
function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements2 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function(placement2) {
    return getVariation(placement2) === variation;
  }) : basePlacements;
  var allowedPlacements = placements2.filter(function(placement2) {
    return allowedAutoPlacements.indexOf(placement2) >= 0;
  });
  if (allowedPlacements.length === 0) {
    allowedPlacements = placements2;
  }
  var overflows = allowedPlacements.reduce(function(acc, placement2) {
    acc[placement2] = detectOverflow(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding
    })[getBasePlacement(placement2)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function(a, b) {
    return overflows[a] - overflows[b];
  });
}

// node_modules/@popperjs/core/lib/modifiers/flip.js
function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }
  var oppositePlacement = getOppositePlacement(placement);
  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];
}
function flip(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  if (state.modifiersData[name]._skip) {
    return;
  }
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));
  var placements2 = [preferredPlacement].concat(fallbackPlacements).reduce(function(acc, placement2) {
    return acc.concat(getBasePlacement(placement2) === auto ? computeAutoPlacement(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding,
      flipVariations,
      allowedAutoPlacements
    }) : placement2);
  }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = /* @__PURE__ */ new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements2[0];
  for (var i = 0; i < placements2.length; i++) {
    var placement = placements2[i];
    var _basePlacement = getBasePlacement(placement);
    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? "width" : "height";
    var overflow = detectOverflow(state, {
      placement,
      boundary,
      rootBoundary,
      altBoundary,
      padding
    });
    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;
    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }
    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];
    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }
    if (checkAltAxis) {
      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
    }
    if (checks.every(function(check) {
      return check;
    })) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }
    checksMap.set(placement, checks);
  }
  if (makeFallbackChecks) {
    var numberOfChecks = flipVariations ? 3 : 1;
    var _loop = function _loop2(_i2) {
      var fittingPlacement = placements2.find(function(placement2) {
        var checks2 = checksMap.get(placement2);
        if (checks2) {
          return checks2.slice(0, _i2).every(function(check) {
            return check;
          });
        }
      });
      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return "break";
      }
    };
    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);
      if (_ret === "break") break;
    }
  }
  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
}
var flip_default = {
  name: "flip",
  enabled: true,
  phase: "main",
  fn: flip,
  requiresIfExists: ["offset"],
  data: {
    _skip: false
  }
};

// node_modules/@popperjs/core/lib/modifiers/hide.js
var import_dist124 = __toESM(require_dist());
var import_dist125 = __toESM(require_dist2());
var import_dist126 = __toESM(require_dist3());
function getSideOffsets(overflow, rect, preventedOffsets) {
  if (preventedOffsets === void 0) {
    preventedOffsets = {
      x: 0,
      y: 0
    };
  }
  return {
    top: overflow.top - rect.height - preventedOffsets.y,
    right: overflow.right - rect.width + preventedOffsets.x,
    bottom: overflow.bottom - rect.height + preventedOffsets.y,
    left: overflow.left - rect.width - preventedOffsets.x
  };
}
function isAnySideFullyClipped(overflow) {
  return [top, right, bottom, left].some(function(side) {
    return overflow[side] >= 0;
  });
}
function hide(_ref) {
  var state = _ref.state, name = _ref.name;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var preventedOffsets = state.modifiersData.preventOverflow;
  var referenceOverflow = detectOverflow(state, {
    elementContext: "reference"
  });
  var popperAltOverflow = detectOverflow(state, {
    altBoundary: true
  });
  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);
  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);
  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
  state.modifiersData[name] = {
    referenceClippingOffsets,
    popperEscapeOffsets,
    isReferenceHidden,
    hasPopperEscaped
  };
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    "data-popper-reference-hidden": isReferenceHidden,
    "data-popper-escaped": hasPopperEscaped
  });
}
var hide_default = {
  name: "hide",
  enabled: true,
  phase: "main",
  requiresIfExists: ["preventOverflow"],
  fn: hide
};

// node_modules/@popperjs/core/lib/modifiers/offset.js
var import_dist127 = __toESM(require_dist());
var import_dist128 = __toESM(require_dist2());
var import_dist129 = __toESM(require_dist3());
function distanceAndSkiddingToXY(placement, rects, offset2) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;
  var _ref = typeof offset2 === "function" ? offset2(Object.assign({}, rects, {
    placement
  })) : offset2, skidding = _ref[0], distance = _ref[1];
  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0 ? {
    x: distance,
    y: skidding
  } : {
    x: skidding,
    y: distance
  };
}
function offset(_ref2) {
  var state = _ref2.state, options = _ref2.options, name = _ref2.name;
  var _options$offset = options.offset, offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function(acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement], x = _data$state$placement.x, y = _data$state$placement.y;
  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x;
    state.modifiersData.popperOffsets.y += y;
  }
  state.modifiersData[name] = data;
}
var offset_default = {
  name: "offset",
  enabled: true,
  phase: "main",
  requires: ["popperOffsets"],
  fn: offset
};

// node_modules/@popperjs/core/lib/modifiers/popperOffsets.js
var import_dist130 = __toESM(require_dist());
var import_dist131 = __toESM(require_dist2());
var import_dist132 = __toESM(require_dist3());
function popperOffsets(_ref) {
  var state = _ref.state, name = _ref.name;
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: "absolute",
    placement: state.placement
  });
}
var popperOffsets_default = {
  name: "popperOffsets",
  enabled: true,
  phase: "read",
  fn: popperOffsets,
  data: {}
};

// node_modules/@popperjs/core/lib/modifiers/preventOverflow.js
var import_dist136 = __toESM(require_dist());
var import_dist137 = __toESM(require_dist2());
var import_dist138 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/utils/getAltAxis.js
var import_dist133 = __toESM(require_dist());
var import_dist134 = __toESM(require_dist2());
var import_dist135 = __toESM(require_dist3());
function getAltAxis(axis) {
  return axis === "x" ? "y" : "x";
}

// node_modules/@popperjs/core/lib/modifiers/preventOverflow.js
function preventOverflow(_ref) {
  var state = _ref.state, options = _ref.options, name = _ref.name;
  var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary,
    rootBoundary,
    padding,
    altBoundary
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue = typeof tetherOffset === "function" ? tetherOffset(Object.assign({}, state.rects, {
    placement: state.placement
  })) : tetherOffset;
  var normalizedTetherOffsetValue = typeof tetherOffsetValue === "number" ? {
    mainAxis: tetherOffsetValue,
    altAxis: tetherOffsetValue
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, tetherOffsetValue);
  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
  var data = {
    x: 0,
    y: 0
  };
  if (!popperOffsets2) {
    return;
  }
  if (checkMainAxis) {
    var _offsetModifierState$;
    var mainSide = mainAxis === "y" ? top : left;
    var altSide = mainAxis === "y" ? bottom : right;
    var len = mainAxis === "y" ? "height" : "width";
    var offset2 = popperOffsets2[mainAxis];
    var min2 = offset2 + overflow[mainSide];
    var max2 = offset2 - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len];
    var arrowElement = state.elements.arrow;
    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
      width: 0,
      height: 0
    };
    var arrowPaddingObject = state.modifiersData["arrow#persistent"] ? state.modifiersData["arrow#persistent"].padding : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide];
    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent ? mainAxis === "y" ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
    var tetherMin = offset2 + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = offset2 + maxOffset - offsetModifierValue;
    var preventedOffset = within(tether ? min(min2, tetherMin) : min2, offset2, tether ? max(max2, tetherMax) : max2);
    popperOffsets2[mainAxis] = preventedOffset;
    data[mainAxis] = preventedOffset - offset2;
  }
  if (checkAltAxis) {
    var _offsetModifierState$2;
    var _mainSide = mainAxis === "x" ? top : left;
    var _altSide = mainAxis === "x" ? bottom : right;
    var _offset = popperOffsets2[altAxis];
    var _len = altAxis === "y" ? "height" : "width";
    var _min = _offset + overflow[_mainSide];
    var _max = _offset - overflow[_altSide];
    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;
    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;
    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;
    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;
    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);
    popperOffsets2[altAxis] = _preventedOffset;
    data[altAxis] = _preventedOffset - _offset;
  }
  state.modifiersData[name] = data;
}
var preventOverflow_default = {
  name: "preventOverflow",
  enabled: true,
  phase: "main",
  fn: preventOverflow,
  requiresIfExists: ["offset"]
};

// node_modules/@popperjs/core/lib/createPopper.js
var import_dist160 = __toESM(require_dist());
var import_dist161 = __toESM(require_dist2());
var import_dist162 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js
var import_dist148 = __toESM(require_dist());
var import_dist149 = __toESM(require_dist2());
var import_dist150 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js
var import_dist145 = __toESM(require_dist());
var import_dist146 = __toESM(require_dist2());
var import_dist147 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js
var import_dist142 = __toESM(require_dist());
var import_dist143 = __toESM(require_dist2());
var import_dist144 = __toESM(require_dist3());
function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js
function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}

// node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js
function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = round(rect.width) / element.offsetWidth || 1;
  var scaleY = round(rect.height) / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
}
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  var offsets = {
    x: 0,
    y: 0
  };
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || // https://github.com/popperjs/popper-core/issues/1078
    isScrollParent(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height
  };
}

// node_modules/@popperjs/core/lib/utils/orderModifiers.js
var import_dist151 = __toESM(require_dist());
var import_dist152 = __toESM(require_dist2());
var import_dist153 = __toESM(require_dist3());
function order(modifiers) {
  var map = /* @__PURE__ */ new Map();
  var visited = /* @__PURE__ */ new Set();
  var result = [];
  modifiers.forEach(function(modifier) {
    map.set(modifier.name, modifier);
  });
  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
    requires.forEach(function(dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);
        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }
  modifiers.forEach(function(modifier) {
    if (!visited.has(modifier.name)) {
      sort(modifier);
    }
  });
  return result;
}
function orderModifiers(modifiers) {
  var orderedModifiers = order(modifiers);
  return modifierPhases.reduce(function(acc, phase) {
    return acc.concat(orderedModifiers.filter(function(modifier) {
      return modifier.phase === phase;
    }));
  }, []);
}

// node_modules/@popperjs/core/lib/utils/debounce.js
var import_dist154 = __toESM(require_dist());
var import_dist155 = __toESM(require_dist2());
var import_dist156 = __toESM(require_dist3());
function debounce(fn2) {
  var pending;
  return function() {
    if (!pending) {
      pending = new Promise(function(resolve) {
        Promise.resolve().then(function() {
          pending = void 0;
          resolve(fn2());
        });
      });
    }
    return pending;
  };
}

// node_modules/@popperjs/core/lib/utils/mergeByName.js
var import_dist157 = __toESM(require_dist());
var import_dist158 = __toESM(require_dist2());
var import_dist159 = __toESM(require_dist3());
function mergeByName(modifiers) {
  var merged = modifiers.reduce(function(merged2, current) {
    var existing = merged2[current.name];
    merged2[current.name] = existing ? Object.assign({}, existing, current, {
      options: Object.assign({}, existing.options, current.options),
      data: Object.assign({}, existing.data, current.data)
    }) : current;
    return merged2;
  }, {});
  return Object.keys(merged).map(function(key) {
    return merged[key];
  });
}

// node_modules/@popperjs/core/lib/createPopper.js
var DEFAULT_OPTIONS = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function areValidElements() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return !args.some(function(element) {
    return !(element && typeof element.getBoundingClientRect === "function");
  });
}
function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }
  var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers3 = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
  return function createPopper4(reference2, popper2, options) {
    if (options === void 0) {
      options = defaultOptions;
    }
    var state = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference2,
        popper: popper2
      },
      attributes: {},
      styles: {}
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state,
      setOptions: function setOptions(setOptionsAction) {
        var options2 = typeof setOptionsAction === "function" ? setOptionsAction(state.options) : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign({}, defaultOptions, state.options, options2);
        state.scrollParents = {
          reference: isElement(reference2) ? listScrollParents(reference2) : reference2.contextElement ? listScrollParents(reference2.contextElement) : [],
          popper: listScrollParents(popper2)
        };
        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers3, state.options.modifiers)));
        state.orderedModifiers = orderedModifiers.filter(function(m) {
          return m.enabled;
        });
        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }
        var _state$elements = state.elements, reference3 = _state$elements.reference, popper3 = _state$elements.popper;
        if (!areValidElements(reference3, popper3)) {
          return;
        }
        state.rects = {
          reference: getCompositeRect(reference3, getOffsetParent(popper3), state.options.strategy === "fixed"),
          popper: getLayoutRect(popper3)
        };
        state.reset = false;
        state.placement = state.options.placement;
        state.orderedModifiers.forEach(function(modifier) {
          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
        });
        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }
          var _state$orderedModifie = state.orderedModifiers[index], fn2 = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;
          if (typeof fn2 === "function") {
            state = fn2({
              state,
              options: _options,
              name,
              instance
            }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function() {
        return new Promise(function(resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      }
    };
    if (!areValidElements(reference2, popper2)) {
      return instance;
    }
    instance.setOptions(options).then(function(state2) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state2);
      }
    });
    function runModifierEffects() {
      state.orderedModifiers.forEach(function(_ref) {
        var name = _ref.name, _ref$options = _ref.options, options2 = _ref$options === void 0 ? {} : _ref$options, effect4 = _ref.effect;
        if (typeof effect4 === "function") {
          var cleanupFn = effect4({
            state,
            name,
            instance,
            options: options2
          });
          var noopFn = function noopFn2() {
          };
          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }
    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function(fn2) {
        return fn2();
      });
      effectCleanupFns = [];
    }
    return instance;
  };
}
var createPopper = popperGenerator();

// node_modules/@popperjs/core/lib/popper.js
var import_dist166 = __toESM(require_dist());
var import_dist167 = __toESM(require_dist2());
var import_dist168 = __toESM(require_dist3());

// node_modules/@popperjs/core/lib/popper-lite.js
var import_dist163 = __toESM(require_dist());
var import_dist164 = __toESM(require_dist2());
var import_dist165 = __toESM(require_dist3());
var defaultModifiers = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default];
var createPopper2 = popperGenerator({
  defaultModifiers
});

// node_modules/@popperjs/core/lib/popper.js
var defaultModifiers2 = [eventListeners_default, popperOffsets_default, computeStyles_default, applyStyles_default, offset_default, flip_default, preventOverflow_default, arrow_default, hide_default];
var createPopper3 = popperGenerator({
  defaultModifiers: defaultModifiers2
});

// node_modules/vue-pick-colors/dist/index.esm.js
function I(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
  return n;
}
function F(e, t) {
  if (e) {
    if ("string" == typeof e) return I(e, t);
    var r = {}.toString.call(e).slice(8, -1);
    return "Object" === r && e.constructor && (r = e.constructor.name), "Map" === r || "Set" === r ? Array.from(e) : "Arguments" === r || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r) ? I(e, t) : void 0;
  }
}
function _(e) {
  return function(e2) {
    if (Array.isArray(e2)) return I(e2);
  }(e) || function(e2) {
    if ("undefined" != typeof Symbol && null != e2[Symbol.iterator] || null != e2["@@iterator"]) return Array.from(e2);
  }(e) || F(e) || function() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function L(e) {
  return L = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e2) {
    return typeof e2;
  } : function(e2) {
    return e2 && "function" == typeof Symbol && e2.constructor === Symbol && e2 !== Symbol.prototype ? "symbol" : typeof e2;
  }, L(e);
}
function P(e, t, r, n, o, a, i) {
  try {
    var c = e[a](i), l = c.value;
  } catch (e2) {
    return void r(e2);
  }
  c.done ? t(l) : Promise.resolve(l).then(n, o);
}
function j(e) {
  return function() {
    var t = this, r = arguments;
    return new Promise(function(n, o) {
      var a = e.apply(t, r);
      function i(e2) {
        P(a, n, o, i, c, "next", e2);
      }
      function c(e2) {
        P(a, n, o, i, c, "throw", e2);
      }
      i(void 0);
    });
  };
}
var M = { exports: {} };
var B = { exports: {} };
!function(e) {
  function t(r) {
    return e.exports = t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e2) {
      return typeof e2;
    } : function(e2) {
      return e2 && "function" == typeof Symbol && e2.constructor === Symbol && e2 !== Symbol.prototype ? "symbol" : typeof e2;
    }, e.exports.__esModule = true, e.exports.default = e.exports, t(r);
  }
  e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
}(B), function(e) {
  var t = B.exports.default;
  function r() {
    e.exports = r = function() {
      return o;
    }, e.exports.__esModule = true, e.exports.default = e.exports;
    var n, o = {}, a = Object.prototype, i = a.hasOwnProperty, c = Object.defineProperty || function(e2, t2, r2) {
      e2[t2] = r2.value;
    }, l = "function" == typeof Symbol ? Symbol : {}, u = l.iterator || "@@iterator", s = l.asyncIterator || "@@asyncIterator", f = l.toStringTag || "@@toStringTag";
    function d(e2, t2, r2) {
      return Object.defineProperty(e2, t2, { value: r2, enumerable: true, configurable: true, writable: true }), e2[t2];
    }
    try {
      d({}, "");
    } catch (n2) {
      d = function(e2, t2, r2) {
        return e2[t2] = r2;
      };
    }
    function p(e2, t2, r2, n2) {
      var o2 = t2 && t2.prototype instanceof x ? t2 : x, a2 = Object.create(o2.prototype), i2 = new P2(n2 || []);
      return c(a2, "_invoke", { value: I2(e2, r2, i2) }), a2;
    }
    function v(e2, t2, r2) {
      try {
        return { type: "normal", arg: e2.call(t2, r2) };
      } catch (e3) {
        return { type: "throw", arg: e3 };
      }
    }
    o.wrap = p;
    var h = "suspendedStart", m = "suspendedYield", g = "executing", y = "completed", b = {};
    function x() {
    }
    function w() {
    }
    function S() {
    }
    var k = {};
    d(k, u, function() {
      return this;
    });
    var C = Object.getPrototypeOf, O = C && C(C(j2([])));
    O && O !== a && i.call(O, u) && (k = O);
    var E = S.prototype = x.prototype = Object.create(k);
    function A(e2) {
      ["next", "throw", "return"].forEach(function(t2) {
        d(e2, t2, function(e3) {
          return this._invoke(t2, e3);
        });
      });
    }
    function N(e2, r2) {
      function n2(o3, a2, c2, l2) {
        var u2 = v(e2[o3], e2, a2);
        if ("throw" !== u2.type) {
          var s2 = u2.arg, f2 = s2.value;
          return f2 && "object" == t(f2) && i.call(f2, "__await") ? r2.resolve(f2.__await).then(function(e3) {
            n2("next", e3, c2, l2);
          }, function(e3) {
            n2("throw", e3, c2, l2);
          }) : r2.resolve(f2).then(function(e3) {
            s2.value = e3, c2(s2);
          }, function(e3) {
            return n2("throw", e3, c2, l2);
          });
        }
        l2(u2.arg);
      }
      var o2;
      c(this, "_invoke", { value: function(e3, t2) {
        function a2() {
          return new r2(function(r3, o3) {
            n2(e3, t2, r3, o3);
          });
        }
        return o2 = o2 ? o2.then(a2, a2) : a2();
      } });
    }
    function I2(e2, t2, r2) {
      var o2 = h;
      return function(a2, i2) {
        if (o2 === g) throw Error("Generator is already running");
        if (o2 === y) {
          if ("throw" === a2) throw i2;
          return { value: n, done: true };
        }
        for (r2.method = a2, r2.arg = i2; ; ) {
          var c2 = r2.delegate;
          if (c2) {
            var l2 = F2(c2, r2);
            if (l2) {
              if (l2 === b) continue;
              return l2;
            }
          }
          if ("next" === r2.method) r2.sent = r2._sent = r2.arg;
          else if ("throw" === r2.method) {
            if (o2 === h) throw o2 = y, r2.arg;
            r2.dispatchException(r2.arg);
          } else "return" === r2.method && r2.abrupt("return", r2.arg);
          o2 = g;
          var u2 = v(e2, t2, r2);
          if ("normal" === u2.type) {
            if (o2 = r2.done ? y : m, u2.arg === b) continue;
            return { value: u2.arg, done: r2.done };
          }
          "throw" === u2.type && (o2 = y, r2.method = "throw", r2.arg = u2.arg);
        }
      };
    }
    function F2(e2, t2) {
      var r2 = t2.method, o2 = e2.iterator[r2];
      if (o2 === n) return t2.delegate = null, "throw" === r2 && e2.iterator.return && (t2.method = "return", t2.arg = n, F2(e2, t2), "throw" === t2.method) || "return" !== r2 && (t2.method = "throw", t2.arg = new TypeError("The iterator does not provide a '" + r2 + "' method")), b;
      var a2 = v(o2, e2.iterator, t2.arg);
      if ("throw" === a2.type) return t2.method = "throw", t2.arg = a2.arg, t2.delegate = null, b;
      var i2 = a2.arg;
      return i2 ? i2.done ? (t2[e2.resultName] = i2.value, t2.next = e2.nextLoc, "return" !== t2.method && (t2.method = "next", t2.arg = n), t2.delegate = null, b) : i2 : (t2.method = "throw", t2.arg = new TypeError("iterator result is not an object"), t2.delegate = null, b);
    }
    function _2(e2) {
      var t2 = { tryLoc: e2[0] };
      1 in e2 && (t2.catchLoc = e2[1]), 2 in e2 && (t2.finallyLoc = e2[2], t2.afterLoc = e2[3]), this.tryEntries.push(t2);
    }
    function L2(e2) {
      var t2 = e2.completion || {};
      t2.type = "normal", delete t2.arg, e2.completion = t2;
    }
    function P2(e2) {
      this.tryEntries = [{ tryLoc: "root" }], e2.forEach(_2, this), this.reset(true);
    }
    function j2(e2) {
      if (e2 || "" === e2) {
        var r2 = e2[u];
        if (r2) return r2.call(e2);
        if ("function" == typeof e2.next) return e2;
        if (!isNaN(e2.length)) {
          var o2 = -1, a2 = function t2() {
            for (; ++o2 < e2.length; ) if (i.call(e2, o2)) return t2.value = e2[o2], t2.done = false, t2;
            return t2.value = n, t2.done = true, t2;
          };
          return a2.next = a2;
        }
      }
      throw new TypeError(t(e2) + " is not iterable");
    }
    return w.prototype = S, c(E, "constructor", { value: S, configurable: true }), c(S, "constructor", { value: w, configurable: true }), w.displayName = d(S, f, "GeneratorFunction"), o.isGeneratorFunction = function(e2) {
      var t2 = "function" == typeof e2 && e2.constructor;
      return !!t2 && (t2 === w || "GeneratorFunction" === (t2.displayName || t2.name));
    }, o.mark = function(e2) {
      return Object.setPrototypeOf ? Object.setPrototypeOf(e2, S) : (e2.__proto__ = S, d(e2, f, "GeneratorFunction")), e2.prototype = Object.create(E), e2;
    }, o.awrap = function(e2) {
      return { __await: e2 };
    }, A(N.prototype), d(N.prototype, s, function() {
      return this;
    }), o.AsyncIterator = N, o.async = function(e2, t2, r2, n2, a2) {
      void 0 === a2 && (a2 = Promise);
      var i2 = new N(p(e2, t2, r2, n2), a2);
      return o.isGeneratorFunction(t2) ? i2 : i2.next().then(function(e3) {
        return e3.done ? e3.value : i2.next();
      });
    }, A(E), d(E, f, "Generator"), d(E, u, function() {
      return this;
    }), d(E, "toString", function() {
      return "[object Generator]";
    }), o.keys = function(e2) {
      var t2 = Object(e2), r2 = [];
      for (var n2 in t2) r2.push(n2);
      return r2.reverse(), function e3() {
        for (; r2.length; ) {
          var n3 = r2.pop();
          if (n3 in t2) return e3.value = n3, e3.done = false, e3;
        }
        return e3.done = true, e3;
      };
    }, o.values = j2, P2.prototype = { constructor: P2, reset: function(e2) {
      if (this.prev = 0, this.next = 0, this.sent = this._sent = n, this.done = false, this.delegate = null, this.method = "next", this.arg = n, this.tryEntries.forEach(L2), !e2) for (var t2 in this) "t" === t2.charAt(0) && i.call(this, t2) && !isNaN(+t2.slice(1)) && (this[t2] = n);
    }, stop: function() {
      this.done = true;
      var e2 = this.tryEntries[0].completion;
      if ("throw" === e2.type) throw e2.arg;
      return this.rval;
    }, dispatchException: function(e2) {
      if (this.done) throw e2;
      var t2 = this;
      function r2(r3, o3) {
        return c2.type = "throw", c2.arg = e2, t2.next = r3, o3 && (t2.method = "next", t2.arg = n), !!o3;
      }
      for (var o2 = this.tryEntries.length - 1; o2 >= 0; --o2) {
        var a2 = this.tryEntries[o2], c2 = a2.completion;
        if ("root" === a2.tryLoc) return r2("end");
        if (a2.tryLoc <= this.prev) {
          var l2 = i.call(a2, "catchLoc"), u2 = i.call(a2, "finallyLoc");
          if (l2 && u2) {
            if (this.prev < a2.catchLoc) return r2(a2.catchLoc, true);
            if (this.prev < a2.finallyLoc) return r2(a2.finallyLoc);
          } else if (l2) {
            if (this.prev < a2.catchLoc) return r2(a2.catchLoc, true);
          } else {
            if (!u2) throw Error("try statement without catch or finally");
            if (this.prev < a2.finallyLoc) return r2(a2.finallyLoc);
          }
        }
      }
    }, abrupt: function(e2, t2) {
      for (var r2 = this.tryEntries.length - 1; r2 >= 0; --r2) {
        var n2 = this.tryEntries[r2];
        if (n2.tryLoc <= this.prev && i.call(n2, "finallyLoc") && this.prev < n2.finallyLoc) {
          var o2 = n2;
          break;
        }
      }
      o2 && ("break" === e2 || "continue" === e2) && o2.tryLoc <= t2 && t2 <= o2.finallyLoc && (o2 = null);
      var a2 = o2 ? o2.completion : {};
      return a2.type = e2, a2.arg = t2, o2 ? (this.method = "next", this.next = o2.finallyLoc, b) : this.complete(a2);
    }, complete: function(e2, t2) {
      if ("throw" === e2.type) throw e2.arg;
      return "break" === e2.type || "continue" === e2.type ? this.next = e2.arg : "return" === e2.type ? (this.rval = this.arg = e2.arg, this.method = "return", this.next = "end") : "normal" === e2.type && t2 && (this.next = t2), b;
    }, finish: function(e2) {
      for (var t2 = this.tryEntries.length - 1; t2 >= 0; --t2) {
        var r2 = this.tryEntries[t2];
        if (r2.finallyLoc === e2) return this.complete(r2.completion, r2.afterLoc), L2(r2), b;
      }
    }, catch: function(e2) {
      for (var t2 = this.tryEntries.length - 1; t2 >= 0; --t2) {
        var r2 = this.tryEntries[t2];
        if (r2.tryLoc === e2) {
          var n2 = r2.completion;
          if ("throw" === n2.type) {
            var o2 = n2.arg;
            L2(r2);
          }
          return o2;
        }
      }
      throw Error("illegal catch attempt");
    }, delegateYield: function(e2, t2, r2) {
      return this.delegate = { iterator: j2(e2), resultName: t2, nextLoc: r2 }, "next" === this.method && (this.arg = n), b;
    } }, o;
  }
  e.exports = r, e.exports.__esModule = true, e.exports.default = e.exports;
}(M);
var R = M.exports();
var z = R;
try {
  regeneratorRuntime = R;
} catch (e) {
  "object" == typeof globalThis ? globalThis.regeneratorRuntime = R : Function("r", "regeneratorRuntime = r")(R);
}
function T(e, t) {
  return function(e2) {
    if (Array.isArray(e2)) return e2;
  }(e) || function(e2, t2) {
    var r = null == e2 ? null : "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
    if (null != r) {
      var n, o, a, i, c = [], l = true, u = false;
      try {
        if (a = (r = r.call(e2)).next, 0 === t2) {
          if (Object(r) !== r) return;
          l = false;
        } else for (; !(l = (n = a.call(r)).done) && (c.push(n.value), c.length !== t2); l = true) ;
      } catch (e3) {
        u = true, o = e3;
      } finally {
        try {
          if (!l && null != r.return && (i = r.return(), Object(i) !== i)) return;
        } finally {
          if (u) throw o;
        }
      }
      return c;
    }
  }(e, t) || F(e, t) || function() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function D(e) {
  var t = function(e2, t2) {
    if ("object" != L(e2) || !e2) return e2;
    var r = e2[Symbol.toPrimitive];
    if (void 0 !== r) {
      var n = r.call(e2, t2 || "default");
      if ("object" != L(n)) return n;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === t2 ? String : Number)(e2);
  }(e, "string");
  return "symbol" == L(t) ? t : t + "";
}
function H(e, t, r) {
  return (t = D(t)) in e ? Object.defineProperty(e, t, { value: r, enumerable: true, configurable: true, writable: true }) : e[t] = r, e;
}
var G = defineComponent({ name: "Saturation", props: { size: { type: Number, default: 160 }, hue: { type: Number, default: 0 }, saturation: { type: Number, default: 0 }, value: { type: Number, default: 0 } }, emits: ["change"], setup: function(e, r) {
  var n = r.emit, o = computed(function() {
    return { width: "".concat(e.size, "px"), height: "".concat(e.size, "px"), background: "hsl(".concat(e.hue, ", 100%, 50%)") };
  });
  return { saturationStyle: o, sliderStyle: computed(function() {
    return { top: "".concat((100 - e.value) / 100 * e.size - 5, "px"), left: "".concat(e.saturation * e.size / 100 - 5, "px"), width: "".concat(10, "px"), height: "".concat(10, "px") };
  }), onSelect: function(t) {
    var r2 = t.target.getBoundingClientRect(), o2 = r2.left, a = r2.top, i = function(t2) {
      var r3, i2;
      t2 instanceof MouseEvent ? (r3 = t2.clientX, i2 = t2.clientY) : t2 instanceof TouchEvent && (r3 = t2.touches[0].clientX, i2 = t2.touches[0].clientY);
      var c2 = r3 - o2, l = i2 - a;
      c2 < 0 && (c2 = 0), l < 0 && (l = 0), c2 > e.size && (c2 = e.size), l > e.size && (l = e.size);
      var u = c2 / e.size * 100, s = 100 - l / e.size * 100;
      n("change", u, s);
    };
    i(t);
    var c = function() {
      document.removeEventListener("mousemove", i), document.removeEventListener("mouseup", c), document.removeEventListener("touchmove", i), document.removeEventListener("touchend", c);
    };
    i(t), t instanceof MouseEvent && (document.addEventListener("mousemove", i), document.addEventListener("mouseup", c)), t instanceof TouchEvent && (t.preventDefault(), document.addEventListener("touchmove", i, { passive: false }), document.addEventListener("touchend", c));
  } };
} });
function V(e, t) {
  void 0 === t && (t = {});
  var r = t.insertAt;
  if (e && "undefined" != typeof document) {
    var n = document.head || document.getElementsByTagName("head")[0], o = document.createElement("style");
    o.type = "text/css", "top" === r && n.firstChild ? n.insertBefore(o, n.firstChild) : n.appendChild(o), o.styleSheet ? o.styleSheet.cssText = e : o.appendChild(document.createTextNode(e));
  }
}
V(".saturation[data-v-24517fec]{position:relative}.saturation-black[data-v-24517fec],.saturation-white[data-v-24517fec]{cursor:pointer;height:100%;left:0;position:absolute;top:0;width:100%}.saturation-white[data-v-24517fec]{background:linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.saturation-black[data-v-24517fec]{background:linear-gradient(0deg,#000,transparent)}.slider[data-v-24517fec]{border:1px solid #fff;border-radius:50%;box-shadow:0 0 1px 1px rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;top:0;z-index:1}"), G.render = function(e, t, c, l, u, s) {
  return openBlock(), createElementBlock("div", { class: "saturation", style: normalizeStyle(e.saturationStyle), onMousedown: t[0] || (t[0] = withModifiers(function() {
    return e.onSelect && e.onSelect.apply(e, arguments);
  }, ["prevent", "stop"])), onTouchstart: t[1] || (t[1] = withModifiers(function() {
    return e.onSelect && e.onSelect.apply(e, arguments);
  }, ["prevent", "stop"])) }, [t[2] || (t[2] = createBaseVNode("div", { class: "saturation-white" }, null, -1)), t[3] || (t[3] = createBaseVNode("div", { class: "saturation-black" }, null, -1)), createBaseVNode("div", { class: "slider", style: normalizeStyle(e.sliderStyle) }, null, 4)], 36);
}, G.__scopeId = "data-v-24517fec", G.__file = "src/picker/Saturation.vue";
var Y = defineComponent({ name: "Hue", props: { width: { type: Number, default: 15 }, height: { type: Number, default: 160 }, hue: { type: Number, default: 0 } }, emits: ["change"], setup: function(e, r) {
  var n = r.emit, o = computed(function() {
    return { top: "".concat((1 - e.hue / 360) * e.height - 2, "px"), height: "".concat(4, "px") };
  }), a = ref();
  onMounted(function() {
    !function() {
      if (a.value) {
        a.value.width = e.width, a.value.height = e.height;
        var t = a.value.getContext("2d");
        if (t) {
          var r2 = t.createLinearGradient(0, 0, 0, e.height);
          r2.addColorStop(0, "#FF0000"), r2.addColorStop(0.17, "#FF00FF"), r2.addColorStop(0.34, "#0000FF"), r2.addColorStop(0.51, "#00FFFF"), r2.addColorStop(0.68, "#00FF00"), r2.addColorStop(0.17 * 5, "#FFFF00"), r2.addColorStop(1, "#FF0000"), t.fillStyle = r2, t.fillRect(0, 0, e.width, e.height);
        }
      }
    }();
  });
  return { canvas: a, sliderStyle: o, onSelect: function(t) {
    var r2 = t.target.getBoundingClientRect().top, o2 = function(t2) {
      var o3;
      t2 instanceof MouseEvent ? o3 = t2.clientY : t2 instanceof TouchEvent && (o3 = t2.touches[0].clientY);
      var a3 = o3 - r2;
      a3 < 0 && (a3 = 0), a3 > e.height && (a3 = e.height);
      var i = -100 * a3 / e.height + 100;
      n("change", 360 * i / 100);
    }, a2 = function() {
      document.removeEventListener("mousemove", o2), document.removeEventListener("mouseup", a2), document.removeEventListener("touchmove", o2), document.removeEventListener("touchend", a2);
    };
    o2(t), t instanceof MouseEvent && (document.addEventListener("mousemove", o2), document.addEventListener("mouseup", a2)), t instanceof TouchEvent && (t.preventDefault(), document.addEventListener("touchmove", o2, { passive: false }), document.addEventListener("touchend", a2));
  } };
} });
var $ = { ref: "canvas" };
V(".hue[data-v-78b9f4f0]{position:relative;touch-action:none}.slider[data-v-78b9f4f0]{background:#fff;box-shadow:0 0 1px 0 rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;width:100%;z-index:1}"), Y.render = function(e, t, c, l, u, s) {
  return openBlock(), createElementBlock("div", { class: "hue", onMousedown: t[0] || (t[0] = withModifiers(function() {
    return e.onSelect && e.onSelect.apply(e, arguments);
  }, ["prevent", "stop"])), onTouchstart: t[1] || (t[1] = withModifiers(function() {
    return e.onSelect && e.onSelect.apply(e, arguments);
  }, ["prevent", "stop"])) }, [createBaseVNode("canvas", $, null, 512), createBaseVNode("div", { class: "slider", style: normalizeStyle(e.sliderStyle) }, null, 4)], 32);
}, Y.__scopeId = "data-v-78b9f4f0", Y.__file = "src/picker/Hue.vue";
var X = defineComponent({ name: "Alpha", props: { width: { type: Number, default: 15 }, height: { type: Number, default: 160 }, color: { type: String, default: "#000000" }, alpha: { type: Number, default: 1 } }, setup: function(e, r) {
  var n = r.emit, o = computed(function() {
    return { top: "".concat(e.alpha * e.height - 2, "px"), height: "".concat(4, "px") };
  }), a = ref(), i = function() {
    var t = a.value.getContext("2d");
    a.value.width = e.width, a.value.height = e.height;
    var r2 = function(e2) {
      var t2 = document.createElement("canvas"), r3 = t2.getContext("2d"), n3 = 2 * e2;
      return t2.width = n3, t2.height = n3, r3.fillStyle = "#ffffff", r3.fillRect(0, 0, n3, n3), r3.fillStyle = "#ccd5db", r3.fillRect(0, 0, e2, e2), r3.fillRect(e2, e2, e2, e2), t2;
    }(5);
    t.fillStyle = t.createPattern(r2, "repeat"), t.fillRect(0, 0, e.width, e.height);
    var n2 = t.createLinearGradient(0, 0, 0, e.height);
    n2.addColorStop(0.01, "rgba(255,255,255,0)"), n2.addColorStop(0.99, e.color), t.fillStyle = n2, t.fillRect(0, 0, e.width, e.height);
  };
  watch(function() {
    return e.color;
  }, function() {
    i();
  }), onMounted(function() {
    i();
  });
  return { canvas: a, sliderStyle: o, onSelect: function(t) {
    var r2 = t.target.getBoundingClientRect().top, o2 = function(t2) {
      var o3;
      t2 instanceof MouseEvent ? o3 = t2.clientY : t2 instanceof TouchEvent && (o3 = t2.touches[0].clientY);
      var a3 = o3 - r2;
      a3 < 0 && (a3 = 0), a3 > e.height && (a3 = e.height);
      var i2 = parseFloat((a3 / e.height).toFixed(2));
      n("change", i2);
    }, a2 = function() {
      document.removeEventListener("mousemove", o2), document.removeEventListener("mouseup", a2), document.removeEventListener("touchmove", o2), document.removeEventListener("touchend", a2);
    };
    o2(t), t instanceof MouseEvent && (document.addEventListener("mousemove", o2), document.addEventListener("mouseup", a2)), t instanceof TouchEvent && (t.preventDefault(), document.addEventListener("touchmove", o2, { passive: false }), document.addEventListener("touchend", a2));
  } };
} });
var J = { ref: "canvas" };
V(".alpha[data-v-24dc9656]{position:relative;touch-action:none}.slider[data-v-24dc9656]{background:#fff;box-shadow:0 0 1px 0 rgba(0,0,0,.3);left:0;pointer-events:none;position:absolute;width:100%;z-index:1}"), X.render = function(e, t, c, l, u, s) {
  return openBlock(), createElementBlock("div", { class: "alpha", onMousedown: t[0] || (t[0] = withModifiers(function() {
    return e.onSelect && e.onSelect.apply(e, arguments);
  }, ["prevent", "stop"])), onTouchstart: t[1] || (t[1] = withModifiers(function() {
    return e.onSelect && e.onSelect.apply(e, arguments);
  }, ["prevent", "stop"])) }, [createBaseVNode("canvas", J, null, 512), createBaseVNode("div", { class: "slider", style: normalizeStyle(e.sliderStyle) }, null, 4)], 32);
}, X.__scopeId = "data-v-24dc9656", X.__file = "src/picker/Alpha.vue";
var q = { rgb: "RGBA", hex: "HEX", hsl: "HSLA", hsv: "HSVA" };
var U = { rgb: "RGB", hex: "HEX", hsl: "HSL", hsv: "HSV" };
var W = { RGB: "rgb", RGBA: "rgb", HEX: "hex", HSL: "hsl", HSLA: "hsl", HSV: "hsv", HSVA: "hsv" };
function K(e, t) {
  var r = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var n = Object.getOwnPropertySymbols(e);
    t && (n = n.filter(function(t2) {
      return Object.getOwnPropertyDescriptor(e, t2).enumerable;
    })), r.push.apply(r, n);
  }
  return r;
}
function Q(e) {
  for (var t = 1; t < arguments.length; t++) {
    var r = null != arguments[t] ? arguments[t] : {};
    t % 2 ? K(Object(r), true).forEach(function(t2) {
      H(e, t2, r[t2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : K(Object(r)).forEach(function(t2) {
      Object.defineProperty(e, t2, Object.getOwnPropertyDescriptor(r, t2));
    });
  }
  return e;
}
var Z = null;
var ee = function(e, t, r) {
  var n = ref({}), o = r || {}, a = o.placement, i = o.defaultStyle, l = { strategy: o.strategy || "absolute", placement: a || "auto", onFirstUpdate: function() {
    Z.update();
  }, modifiers: [{ name: "offset", options: { offset: [0, 5] } }, { name: "computeStyles", options: { gpuAcceleration: false, adaptive: true } }, { name: "flip", options: { allowedAutoPlacements: ["top", "bottom"] } }, { name: "applyStyles", enabled: false }, { name: "updateState", enabled: true, phase: "write", requires: ["computeStyles"], fn: function(e2) {
    var t2 = e2.state, r2 = t2.styles, o2 = t2.placement, a2 = r2.popper;
    n.value = Q(Q(Q({}, a2), i), {}, { transformOrigin: "top" === o2 ? "center bottom" : "center top" });
  } }] };
  return watch(function() {
    return [unref(e), unref(t)];
  }, function(e2, t2) {
    var r2, n2 = T(e2, 2), o2 = n2[0], a2 = n2[1], i2 = T(t2, 2), c = i2[0], u = i2[1];
    if (o2 && a2 && (c !== o2 || u !== c)) {
      null === (r2 = Z) || void 0 === r2 || r2.destroy();
      var s = o2.$el || o2, d = a2.$el || a2;
      nextTick(function() {
        Z = createPopper3(s, d, l);
      });
    }
  }), onBeforeUnmount(function() {
    var e2;
    Z && (null === (e2 = Z) || void 0 === e2 || e2.destroy(), Z = null);
  }), { instance: Z, style: n };
};
var te = defineComponent({ props: { value: { type: String, default: "RGBA" }, showAlpha: { type: Boolean }, options: { type: [Boolean, Array] } }, emits: ["change"], setup: function(e, r) {
  var n = r.emit, o = ref(null), a = ref(null), i = ref(false), u = ee(o, a, { strategy: "fixed", defaultStyle: { zIndex: 2 } }).style, f = computed(function() {
    return Array.isArray(e.options) && e.options.length > 1;
  }), d = computed(function() {
    var t = e.options, r2 = e.showAlpha, n2 = e.value;
    return Array.isArray(t) ? r2 ? t.map(function(e2) {
      return q[e2];
    }).filter(function(e2) {
      return !e2.includes(n2);
    }) : t.map(function(e2) {
      return U[e2];
    }).filter(function(e2) {
      return !e2.includes(n2);
    }) : [];
  }), v = function(e2) {
    var t, r2, n2 = e2.target;
    !(null !== (t = unref(o)) && void 0 !== t && t.isEqualNode(n2)) && (null === (r2 = unref(o)) || void 0 === r2 ? void 0 : r2.contains(n2)) || (i.value = false);
  };
  return onMounted(function() {
    document.addEventListener("mouseup", v, false);
  }), onUnmounted(function() {
    document.removeEventListener("mouseup", v, false);
  }), { targetRef: o, selectorRef: a, selectorStyle: u, isShowSelector: i, isNeedSelect: f, formatOptions: d, onShow: function() {
    f.value && (i.value = true);
  }, onFormatChange: function(e2) {
    n("change", W[e2]);
  } };
} });
var re = { class: "format" };
var ne = { key: 0, class: "arrow" };
var oe = ["onClick"];
V(".format[data-v-5f6e8f5e]{position:relative}.label[data-v-5f6e8f5e]{align-items:center;background:#e7e8e9;color:#999;display:flex;float:left;font-weight:500;height:30px;justify-content:center;position:relative;width:60px}[pick-colors-theme=dark] .label[data-v-5f6e8f5e]{background:#252930;color:#999}.arrow[data-v-5f6e8f5e]{height:6px;margin-bottom:4px;margin-left:5px;transform:rotate(135deg);width:6px}.arrow[data-v-5f6e8f5e],[pick-colors-theme=dark] .arrow[data-v-5f6e8f5e]{border-right:1px solid #999;border-top:1px solid #999}.selector[data-v-5f6e8f5e]{align-items:center;background:#f7f8f9;border-radius:5px;box-shadow:0 0 16px 0 rgba(0,0,0,.16);cursor:pointer;display:flex;flex-direction:column;font-weight:400;justify-content:center;padding:4px}[pick-colors-theme=dark] .selector[data-v-5f6e8f5e]{background:#252930;color:#999}.selector-item[data-v-5f6e8f5e]{align-items:center;display:flex;height:30px;justify-content:center;width:60px}.selector-item[data-v-5f6e8f5e]:hover{background:#e1f2fe}[pick-colors-theme=dark] .selector-item[data-v-5f6e8f5e]{color:#fff}[pick-colors-theme=dark] .selector-item[data-v-5f6e8f5e]:hover{background:#0087fa}.active-selector-item[data-v-5f6e8f5e]{background:#e1f2fe}[pick-colors-theme=dark] .active-selector-item[data-v-5f6e8f5e]{background:#0087fa}.v-enter-active[data-v-5f6e8f5e],.v-leave-active[data-v-5f6e8f5e]{opacity:1;transform:scaleY(1);transform-origin:center top;transition:opacity .2s ease-in-out,transform .2s ease-in-out}.v-enter-from[data-v-5f6e8f5e],.v-leave-to[data-v-5f6e8f5e]{opacity:0;transform:scaleY(0)}"), te.render = function(e, t, a, c, l, u) {
  return openBlock(), createElementBlock("div", re, [createBaseVNode("div", { class: "label", ref: "targetRef", onClick: t[0] || (t[0] = function() {
    return e.onShow && e.onShow.apply(e, arguments);
  }) }, [createBaseVNode("span", null, toDisplayString(e.value), 1), e.isNeedSelect ? (openBlock(), createElementBlock("div", ne)) : createCommentVNode("v-if", true)], 512), createVNode(Transition, null, { default: withCtx(function() {
    return [e.isShowSelector ? (openBlock(), createElementBlock("div", { key: 0, class: "selector", ref: "selectorRef", style: normalizeStyle(e.selectorStyle) }, [(openBlock(true), createElementBlock(Fragment, null, renderList(e.formatOptions, function(t2) {
      return openBlock(), createElementBlock("div", { class: "selector-item", key: t2, onClick: function(r) {
        return e.onFormatChange(t2);
      } }, toDisplayString(t2), 9, oe);
    }), 128))], 4)) : createCommentVNode("v-if", true)];
  }), _: 1 })]);
}, te.__scopeId = "data-v-5f6e8f5e", te.__file = "src/picker/input-value/FormatValue.vue";
var ae = defineComponent({ name: "Input", components: { FormatValue: te }, props: { format: { type: String, default: "RGBA" }, value: { type: String, default: "" }, width: { type: Number }, showAlpha: { type: Boolean }, formatOptions: { type: [Boolean, Array] } }, emits: ["change", "focus", "blur", "enter", "formatChange"], setup: function(e, r) {
  var n = r.emit;
  return { onInput: function(e2) {
    var t;
    n("change", null === (t = e2.target) || void 0 === t ? void 0 : t.value);
  }, valueStyle: computed(function() {
    return { minWidth: "".concat(e.width, "px"), maxWidth: "".concat(e.width, "px"), width: "".concat(e.width, "px") };
  }), onFocus: function() {
    n("focus");
  }, onBlur: function() {
    n("blur");
  }, onEnter: function() {
    n("enter");
  }, onFormatChange: function(e2) {
    n("formatChange", e2);
  } };
} });
var ie = { class: "input" };
var ce = [".value"];
function le(e, t) {
  var r = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var n = Object.getOwnPropertySymbols(e);
    t && (n = n.filter(function(t2) {
      return Object.getOwnPropertyDescriptor(e, t2).enumerable;
    })), r.push.apply(r, n);
  }
  return r;
}
function ue(e) {
  for (var t = 1; t < arguments.length; t++) {
    var r = null != arguments[t] ? arguments[t] : {};
    t % 2 ? le(Object(r), true).forEach(function(t2) {
      H(e, t2, r[t2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : le(Object(r)).forEach(function(t2) {
      Object.defineProperty(e, t2, Object.getOwnPropertyDescriptor(r, t2));
    });
  }
  return e;
}
V(".input[data-v-2c454c00]{display:flex;font-size:12px}.value[data-v-2c454c00]{background:#eceef0;border:1px solid #eceef0;box-sizing:border-box;color:#666;flex:1;height:30px;text-align:center}[pick-colors-theme=dark] .value[data-v-2c454c00]{background:#2e333a;border:1px solid #2e333a;color:#fff}.value[data-v-2c454c00]:focus{border:1px solid #1890ff;outline:none}"), ae.render = function(e, t, a, c, l, u) {
  var s = resolveComponent("FormatValue");
  return openBlock(), createElementBlock("div", ie, [createVNode(s, { value: e.format, showAlpha: e.showAlpha, options: e.formatOptions, onChange: e.onFormatChange }, null, 8, ["value", "showAlpha", "options", "onChange"]), createBaseVNode("input", { class: "value", style: normalizeStyle(e.valueStyle), ".value": e.value, onFocus: t[0] || (t[0] = function() {
    return e.onFocus && e.onFocus.apply(e, arguments);
  }), onInput: t[1] || (t[1] = function() {
    return e.onInput && e.onInput.apply(e, arguments);
  }), onBlur: t[2] || (t[2] = function() {
    return e.onBlur && e.onBlur.apply(e, arguments);
  }), onKeydown: t[3] || (t[3] = withKeys(function() {
    return e.onEnter && e.onEnter.apply(e, arguments);
  }, ["enter"])) }, null, 44, ce)]);
}, ae.__scopeId = "data-v-2c454c00", ae.__file = "src/picker/input-value/InputValue.vue";
var se = function(e, t, r) {
  return [e, t * r / ((e = (2 - t) * r) < 1 ? e : 2 - e) || 0, e / 2];
};
var fe = { 10: "A", 11: "B", 12: "C", 13: "D", 14: "E", 15: "F" };
var de = function(e) {
  e = Math.min(Math.round(e), 255);
  var t = Math.floor(e / 16), r = e % 16;
  return "".concat(fe[t] || t).concat(fe[r] || r);
};
var pe = function(e) {
  var t = e.r, r = e.g, n = e.b;
  return isNaN(t) || isNaN(r) || isNaN(n) ? "" : "#".concat(de(t)).concat(de(r)).concat(de(n));
};
var ve = function(e, t) {
  var r;
  "string" == typeof (r = e) && -1 !== r.indexOf(".") && 1 === parseFloat(r) && (e = "100%");
  var n = function(e2) {
    return "string" == typeof e2 && -1 !== e2.indexOf("%");
  }(e);
  return e = Math.min(t, Math.max(0, parseFloat("".concat(e)))), n && (e = parseInt("".concat(e * t), 10) / 100), Math.abs(e - t) < 1e-6 ? 1 : e % t / parseFloat(t);
};
var he = function(e, t, r) {
  e = 6 * ve(e, 360), t = ve(t, 100), r = ve(r, 100);
  var n = Math.floor(e), o = e - n, a = r * (1 - t), i = r * (1 - o * t), c = r * (1 - (1 - o) * t), l = n % 6, u = [r, i, a, a, c, r][l], s = [c, r, r, i, a, a][l], f = [a, a, c, r, r, i][l];
  return { r: Math.round(255 * u), g: Math.round(255 * s), b: Math.round(255 * f) };
};
var me = function(e, t, r) {
  var n, o, a = e.h, i = e.s, c = e.v, l = e.a;
  if (r) switch (["hsl", "hsv", "rga"].includes(t) && (l = (o = (n = l).toString().match(/\.(\d{1,2})(\d*)/)) && o[2].length > 0 ? parseFloat(n.toFixed(2)) : n), t) {
    case "hsl":
      var u = se(a, i / 100, c / 100);
      return "hsla(".concat(a.toFixed(0), ", ").concat(Math.round(100 * u[1]), "%, ").concat(Math.round(100 * u[2]), "%, ").concat(l, ")");
    case "hsv":
      return "hsva(".concat(a.toFixed(0), ", ").concat(Math.round(i), "%, ").concat(Math.round(c), "%, ").concat(l, ")");
    case "rgb":
      var s = he(a, i, c), f = s.r, d = s.g, p = s.b;
      return "rgba(".concat(f, ", ").concat(d, ", ").concat(p, ", ").concat(l, ")");
    default:
      return "".concat(pe(he(a, i, c))).concat(de(255 * l));
  }
  else switch (t) {
    case "hsl":
      var v = se(a, i / 100, c / 100);
      return "hsl(".concat(a.toFixed(0), ", ").concat(Math.round(100 * v[1]), "%, ").concat(Math.round(100 * v[2]), "%)");
    case "hsv":
      return "hsv(".concat(a.toFixed(0), ", ").concat(Math.round(i), "%, ").concat(Math.round(c), "%)");
    case "rgb":
      var h = he(a, i, c), m = h.r, g = h.g, y = h.b;
      return "rgb(".concat(m, ", ").concat(g, ", ").concat(y, ")");
    default:
      return pe(he(a, i, c));
  }
};
var ge = function(e) {
  var t = e.r, r = e.g, n = e.b;
  t = ve(t, 255), r = ve(r, 255), n = ve(n, 255);
  var o, a = Math.max(t, r, n), i = Math.min(t, r, n), c = a, l = a - i, u = 0 === a ? 0 : l / a;
  if (a === i) o = 0;
  else {
    switch (a) {
      case t:
        o = (r - n) / l + (r < n ? 6 : 0);
        break;
      case r:
        o = (n - t) / l + 2;
        break;
      case n:
        o = (t - r) / l + 4;
    }
    o /= 6;
  }
  return { h: 360 * o, s: 100 * u, v: 100 * c };
};
var ye = function(e) {
  var t = e.h, r = e.s, n = e.l;
  n /= 100;
  var o = r /= 100, a = Math.max(n, 0.01);
  return r *= (n *= 2) <= 1 ? n : 2 - n, o *= a <= 1 ? a : 2 - a, { h: +t, s: 100 * (0 === n ? 2 * o / (a + o) : 2 * r / (n + r)), v: 100 * ((n + r) / 2) };
};
var be = function(e) {
  var t = [];
  if (e.match(/^#([0-9a-fA-f]{3,4})$/g)) for (var r = 1; r < e.length; r++) t.push(parseInt("0x" + e[r].repeat(2)));
  else if (e.match(/^#([0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/g)) for (var n = 1; n < e.length; n += 2) t.push(parseInt("0x" + e.slice(n, n + 2)));
  return { r: t[0], g: t[1], b: t[2], a: t[3] };
};
var xe = function(e, t, r) {
  if ("string" == typeof e && "" !== e) {
    var n = Ce(e, Oe(e), r), o = Ee(n);
    return null == o ? "" : me(o, t, r);
  }
  return "";
};
var we = function(e) {
  var t = T(e.match(/(\d(\.\d+)?)+/g), 4);
  return { r: t[0], g: t[1], b: t[2], a: t[3] };
};
var Se = function(e) {
  var t = T(e.match(/(\d(\.\d+)?)+/g), 4), r = t[0], n = t[1], o = t[2], a = t[3];
  return { h: r, s: parseFloat(n), l: parseFloat(o), a };
};
var ke = function(e) {
  var t = T(e.match(/(\d(\.\d+)?)+/g), 4), r = t[0], n = t[1], o = t[2], a = t[3];
  return { h: parseFloat(r), s: parseFloat(n), v: parseFloat(o), a: parseFloat(a) };
};
var Ce = function(e, t) {
  if (!(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2]) switch (t) {
    case "rgb":
      var r = we(e), n = r.r, o = r.g, a = r.b, i = r.a;
      return ue(ue({}, ge({ r: n, g: o, b: a })), {}, { a: +i });
    case "hsv":
      var c = ke(e);
      return { h: c.h, s: c.s, v: c.v, a: c.a };
    case "hsl":
      var l = Se(e), u = l.h, s = l.s, f = l.l, d = l.a;
      return ue(ue({}, ye({ h: u, s, l: f })), {}, { a: +d });
    default:
      var p = be(e), v = p.r, h = p.g, m = p.b, g = p.a;
      return ue(ue({}, ge({ r: v, g: h, b: m })), {}, { a: g / 255 });
  }
  else {
    switch (t) {
      case "rgb":
        return ue(ue({}, ge(we(e))), {}, { a: 1 });
      case "hsv":
        var y = ke(e);
        return { h: y.h, s: y.s, v: y.v, a: 1 };
      case "hsl":
        return ue(ue({}, ye(Se(e))), {}, { a: 1 });
      default:
        return ue(ue({}, ge(be(e))), {}, { a: 1 });
    }
  }
};
var Oe = function(e) {
  return e.match(/^#/) ? "hex" : e.match(/^rgb/) ? "rgb" : e.match(/^hsl/) ? "hsl" : e.match(/^hsv/) ? "hsv" : "hex";
};
var Ee = function(e) {
  var t = e.h, r = e.s, n = e.v, o = e.a;
  return isNaN(t) && isNaN(r) && isNaN(n) ? null : (isNaN(t) && (t = 0), isNaN(r) && (r = 0), isNaN(n) && (n = 0), isNaN(o) && (o = 1), { h: t, s: r, v: n, a: o });
};
var Ae = defineComponent({ name: "ColorItem", props: { size: { type: [Number, String], default: 20 }, width: { type: [Number, String] }, height: { type: [Number, String] }, value: { type: String, default: "" }, border: { type: Boolean, default: true }, borderRadius: { type: Number, default: 5 }, selected: { type: Boolean, default: false }, draggable: { type: Boolean, default: false } }, emits: ["select"], setup: function(e) {
  var r = ref(), n = inject("theme", { theme: "light" }).theme, o = computed(function() {
    return parseFloat((e.width || e.size) + "");
  }), a = computed(function() {
    return parseFloat((e.height || e.size) + "");
  }), i = computed(function() {
    return { width: "".concat(unref(o), "px"), height: "".concat(unref(a), "px"), border: e.border ? "1px solid ".concat("dark" === unref(n) ? "#434345" : "#d9d9d9") : "", borderRadius: "".concat(e.borderRadius, "px"), boxShadow: e.selected ? "0 0 3px 2px ".concat("dark" === unref(n) ? "#2681ff" : "#1890ff") : "" };
  }), f = function() {
    var t = r.value.getContext("2d");
    r.value.width = unref(o), r.value.height = unref(a);
    var n2 = function(e2) {
      var t2 = document.createElement("canvas"), r2 = t2.getContext("2d"), n3 = 2 * e2;
      return t2.width = n3, t2.height = n3, r2.fillStyle = "#ffffff", r2.fillRect(0, 0, n3, n3), r2.fillStyle = "#ccd5db", r2.fillRect(0, 0, e2, e2), r2.fillRect(e2, e2, e2, e2), t2;
    }(5);
    t.fillStyle = t.createPattern(n2, "repeat"), t.fillRect(0, 0, unref(o), unref(a)), t.fillStyle = e.value, t.fillRect(0, 0, unref(o), unref(a));
  };
  return watch(function() {
    return e.value;
  }, function() {
    f();
  }), onMounted(function() {
    f();
  }), { canvas: r, colorItemStyle: i };
} });
var Ne = ["draggable"];
V(".color-item[data-v-02da71fd]{display:inline-block;vertical-align:top}"), Ae.render = function(e, t, a, i, c, l) {
  return openBlock(), createElementBlock("canvas", { class: "color-item", style: normalizeStyle(e.colorItemStyle), ref: "canvas", draggable: e.draggable }, null, 12, Ne);
}, Ae.__scopeId = "data-v-02da71fd", Ae.__file = "src/color-item/ColorItem.vue";
var Ie = defineComponent({ name: "Colors", components: { ColorItem: Ae }, props: { colors: { type: Array, default: function() {
  return [];
} }, selectedIndex: { type: Number, default: -1 } }, emits: ["change"], setup: function(e, r) {
  var n = r.emit;
  return { onSelectColor: function(e2, t) {
    n("change", e2, t);
  }, useColors: computed(function() {
    return e.colors.map(function(e2) {
      return xe(e2, "hex", true);
    });
  }) };
} });
var Fe = { class: "colors" };
function _e(e, t) {
  var r = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var n = Object.getOwnPropertySymbols(e);
    t && (n = n.filter(function(t2) {
      return Object.getOwnPropertyDescriptor(e, t2).enumerable;
    })), r.push.apply(r, n);
  }
  return r;
}
function Le(e) {
  for (var t = 1; t < arguments.length; t++) {
    var r = null != arguments[t] ? arguments[t] : {};
    t % 2 ? _e(Object(r), true).forEach(function(t2) {
      H(e, t2, r[t2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : _e(Object(r)).forEach(function(t2) {
      Object.defineProperty(e, t2, Object.getOwnPropertyDescriptor(r, t2));
    });
  }
  return e;
}
V(".colors[data-v-0f8b52a8]{align-items:center;display:flex;flex-wrap:wrap;margin-top:5px}.color-item[data-v-0f8b52a8]{margin:5px}"), Ie.render = function(e, t, o, i, c, l) {
  var u = resolveComponent("color-item");
  return openBlock(), createElementBlock("div", Fe, [(openBlock(true), createElementBlock(Fragment, null, renderList(e.useColors, function(t2, n) {
    return openBlock(), createBlock(u, { key: n, class: "color-item", size: 16, value: t2, border: false, "border-radius": 3, selected: e.selectedIndex === n, onClick: withModifiers(function(r) {
      return e.onSelectColor(t2, +n);
    }, ["stop", "prevent"]) }, null, 8, ["value", "selected", "onClick"]);
  }), 128))]);
}, Ie.__scopeId = "data-v-0f8b52a8", Ie.__file = "src/picker/Colors.vue";
var Pe = defineComponent({ name: "Picker", components: { Colors: Ie, Saturation: G, Hue: Y, Alpha: X, InputValue: ae }, props: { format: { type: String, default: "hex" }, showAlpha: { type: Boolean, default: false }, value: { type: String, default: "" }, colors: { type: Array, default: function() {
  return [];
} }, style: { type: Object, default: function() {
  return {};
} }, formatOptions: { type: [Boolean, Array] } }, emits: ["change", "formatChange"], setup: function(e, r) {
  var n = r.emit, o = computed(function() {
    return Le(Le({}, e.style), {}, { width: e.showAlpha ? "230px" : "205px" });
  }), a = ref(), i = ref(), l = ref(), f = function() {
    var t, r2 = null === (t = e.value) || void 0 === t ? void 0 : t.trim();
    if (null != r2 && "" !== r2) {
      var n2 = Oe(r2);
      return Ee(Ce(r2, n2, e.showAlpha));
    }
    return null;
  };
  watch(function() {
    return e.value;
  }, function() {
    e.value !== unref(l) && (a.value = f());
  }, { immediate: true }), watch(function() {
    return [unref(a), e.format];
  }, function(t) {
    var r2 = T(t, 2), o2 = r2[0], a2 = r2[1], c = "";
    null != o2 ? (c = me(Le({}, o2), e.format, e.showAlpha), i.value = Le({}, o2)) : i.value = null, l.value = c;
    var u = f();
    JSON.stringify(o2) !== JSON.stringify(u) && JSON.stringify(o2) !== JSON.stringify(a2) && n("change", c);
  }, { immediate: true });
  var d = computed(function() {
    var e2;
    return (null === (e2 = unref(a)) || void 0 === e2 ? void 0 : e2.h) || 0;
  }), p = computed(function() {
    var e2;
    return (null === (e2 = unref(a)) || void 0 === e2 ? void 0 : e2.s) || 0;
  }), v = computed(function() {
    var e2;
    return (null === (e2 = unref(a)) || void 0 === e2 ? void 0 : e2.v) || 0;
  }), h = computed(function() {
    var e2, t;
    return null != (null === (e2 = unref(a)) || void 0 === e2 ? void 0 : e2.a) ? null === (t = unref(a)) || void 0 === t ? void 0 : t.a : 1;
  }), m = computed(function() {
    return he(unref(d), unref(p), unref(v));
  }), g = computed(function() {
    return "rgb(".concat(unref(m).r, ", ").concat(unref(m).g, ", ").concat(unref(m).b, ")");
  }), y = ref(-1);
  return { h: d, s: p, v, a: h, rgbStr: g, onSelectSaturation: function(e2, t) {
    y.value = -1, null == unref(a) ? a.value = { h: unref(d), s: e2, v: t, a: unref(h) } : a.value = Le(Le({}, unref(a)), {}, { s: e2, v: t });
  }, onSelectHue: function(e2) {
    y.value = -1, null == unref(a) && (a.value = { h: e2, s: unref(p), v: unref(v), a: unref(h) }), a.value = Le(Le({}, unref(a)), {}, { h: e2 });
  }, onSelectAlpha: function(e2) {
    y.value = -1, null == unref(a) ? a.value = { h: unref(d), s: unref(p), v: unref(v), a: e2 } : a.value = Le(Le({}, unref(a)), {}, { a: e2 });
  }, handleInputFormat: function(t) {
    return e.showAlpha ? q[t] : U[t];
  }, colorValue: l, pickerStyle: o, onInputChange: function(e2) {
    y.value = -1, l.value = e2;
  }, handleChange: function() {
    var t, r2 = null === (t = unref(l)) || void 0 === t ? void 0 : t.trim();
    if ("" !== r2) {
      var n2 = e.showAlpha, o2 = Oe(r2), c = Ee(Ce(r2, o2, n2)), u = function(e2) {
        if (!e2) return false;
        var t2 = e2.h, r3 = e2.s, n3 = e2.v, o3 = e2.a;
        return !(isNaN(t2) || isNaN(r3) || isNaN(n3) || isNaN(o3));
      }(c);
      u ? a.value = c : null != unref(i) ? a.value = unref(i) : l.value = "";
    } else a.value = null;
  }, selectColorIndex: y, onSelectColor: function(e2, t) {
    if (y.value = t, "" === (e2 = e2.trim())) a.value = null;
    else {
      var r2 = Oe(e2);
      r2 ? function(e3, t2, r3) {
        if (0 !== e3.length) {
          var n2 = Ce(e3, t2, r3), o2 = n2.h, i2 = n2.s, c = n2.v;
          if (!(isNaN(o2) || isNaN(i2) || isNaN(c))) {
            var l2 = 1, u = n2.a;
            isNaN(u) || (l2 = u), a.value = { h: o2, s: i2, v: c, a: l2 };
          }
        }
      }(e2, r2, true) : a.value = null;
    }
  }, onFormatChange: function(e2) {
    n("formatChange", e2);
  } };
} });
var je = { class: "picker-inner" };
var Me = { class: "picker-header" };
V(".picker[data-v-6ceadec6]{background:#f7f8f9;border-radius:4px;box-shadow:0 0 16px 0 rgba(0,0,0,.16)}.picker-inner[data-v-6ceadec6]{padding:10px}[pick-colors-theme=dark] .picker-inner[data-v-6ceadec6]{background:#1d2024;box-shadow:0 0 16px 0 rgba(0,0,0,.16)}.picker-header[data-v-6ceadec6]{display:flex;margin-bottom:5px}.alpha[data-v-6ceadec6],.hue[data-v-6ceadec6]{margin-left:10px}.colors[data-v-6ceadec6]{margin-top:5px}"), Pe.render = function(e, t, a, c, l, u) {
  var s = resolveComponent("saturation"), f = resolveComponent("hue"), d = resolveComponent("alpha"), p = resolveComponent("input-value"), v = resolveComponent("Colors");
  return openBlock(), createElementBlock("div", { class: "picker", style: normalizeStyle(e.pickerStyle) }, [createBaseVNode("div", je, [createBaseVNode("div", Me, [createVNode(s, { class: "saturation", hue: e.h, saturation: e.s, value: e.v, onChange: e.onSelectSaturation }, null, 8, ["hue", "saturation", "value", "onChange"]), createVNode(f, { class: "hue", hue: e.h, onChange: e.onSelectHue }, null, 8, ["hue", "onChange"]), e.showAlpha ? (openBlock(), createBlock(d, { key: 0, class: "alpha", alpha: e.a, color: e.rgbStr, onChange: e.onSelectAlpha }, null, 8, ["alpha", "color", "onChange"])) : createCommentVNode("v-if", true)]), createVNode(p, { format: e.handleInputFormat(e.format), value: e.colorValue, showAlpha: e.showAlpha, width: e.showAlpha ? 150 : 125, formatOptions: e.formatOptions, onChange: e.onInputChange, onBlur: e.handleChange, onEnter: e.handleChange, onFormatChange: e.onFormatChange }, null, 8, ["format", "value", "showAlpha", "width", "formatOptions", "onChange", "onBlur", "onEnter", "onFormatChange"]), e.colors.length > 0 ? (openBlock(), createBlock(v, { key: 0, class: "colors", colors: e.colors, "selected-index": e.selectColorIndex, onChange: e.onSelectColor }, null, 8, ["colors", "selected-index", "onChange"])) : createCommentVNode("v-if", true)])], 4);
}, Pe.__scopeId = "data-v-6ceadec6", Pe.__file = "src/picker/Picker.vue";
var Be = defineComponent({ name: "AddColorItem", props: { size: { type: Number, default: 20 }, selected: { type: Boolean, default: false } }, setup: function(e) {
  var r = inject("theme", { theme: "light" }).theme;
  return { addColorItemStyle: computed(function() {
    return { width: "".concat(e.size, "px"), height: "".concat(e.size, "px"), lineHeight: "".concat(e.size, "px"), boxShadow: e.selected ? "0 0 3px 2px ".concat("dark" === unref(r) ? "#2681ff" : "#1890ff") : "" };
  }) };
} });
V(".add-color-item[data-v-ceb1719c]{background:#fff;border:1px solid #d9d9d9;border-radius:5px;vertical-align:top}.container[data-v-ceb1719c]{pointer-events:none;transform:scale(.9);transform-origin:center}[pick-colors-theme=dark] .add-color-item[data-v-ceb1719c]{background:#141414;border:1px solid #434343}path[data-v-ceb1719c]{fill:#000}[pick-colors-theme=dark] path[data-v-ceb1719c]{fill:#fff}"), Be.render = function(e, t, a, c, l, u) {
  return openBlock(), createElementBlock("svg", { class: "add-color-item", style: normalizeStyle(e.addColorItemStyle), viewBox: "0 0 1024 1024", xmlns: "http://www.w3.org/2000/svg" }, t[0] || (t[0] = [createBaseVNode("g", { class: "container" }, [createBaseVNode("path", { d: "M544 464V160h-80v304H160v80h304v304h80V544h304v-80z" })], -1)]), 4);
}, Be.__scopeId = "data-v-ceb1719c", Be.__file = "src/add-color-item/AddColorItem.vue";
var Re = defineComponent({ name: "ColorPicker", components: { ColorItem: Ae, Picker: Pe, AddColorItem: Be }, props: { value: { type: [String, Array] }, theme: { type: String, default: "light" }, size: { type: [Number, String], default: 20 }, width: { type: [Number, String] }, height: { type: [Number, String] }, format: { type: String }, showPicker: { type: Boolean, default: void 0 }, showAlpha: { type: Boolean, default: false }, addColor: { type: Boolean, default: false }, deleteColor: { type: Boolean, default: true }, max: { type: Number, default: 13 }, popupContainer: { type: [String, Object, Boolean], default: "body" }, zIndex: { type: Number, default: 1e3 }, colors: { type: Array, default: function() {
  return ["#ff4500", "#ff8c00", "#ffd700", "#90ee90", "#00ced1", "#1e90ff", "#c71585", "#ff4500", "#ff7d4d", "#00babd", "#1f93ff", "#fa64c3"];
} }, position: { type: String }, placement: { type: String }, formatOptions: { type: [Boolean, Array], default: false } }, emits: ["change", "update:value", "update:showPicker", "overflowMax", "closePicker", "formatChange"], setup: function(e, r) {
  var n = r.emit, o = ref([]), a = computed(function() {
    return unref(o).map(function(t) {
      return xe(t, "hex", e.showAlpha);
    });
  }), i = ref("hex");
  watch(function() {
    return e.format;
  }, function() {
    i.value = e.format;
  }, { immediate: true });
  watch(function() {
    return e.value;
  }, function() {
    var t = e.value || "", r2 = Array.isArray(t) ? t : [t];
    o.value = r2.map(function(t2) {
      return xe(t2, unref(i), e.showAlpha);
    });
  }, { immediate: true });
  var d = ref(void 0), v = computed(function() {
    return unref(o)[unref(d)];
  }), h = ref(false);
  watch(function() {
    return e.showPicker;
  }, function() {
    h.value = e.showPicker;
  }, { immediate: true });
  var m, g, y = ref(null), b = ref(null), x = ee(y, b, { defaultStyle: { zIndex: e.zIndex }, strategy: e.position, placement: e.placement }).style, w = function() {
    null == unref(y) && (y.value = unref(B2)[0]), null == unref(d) && (d.value = 0), void 0 === e.showPicker ? h.value = true : n("update:showPicker", true);
  }, S = function() {
    d.value = void 0, void 0 === e.showPicker ? h.value = false : n("update:showPicker", false), n("closePicker", toRaw(Array.isArray(e.value) || e.addColor ? unref(o) : unref(o)[0]));
  }, k = ref(), C = function() {
    var e2 = j(z.mark(function e3(t) {
      var r2, n2, o2, a2;
      return z.wrap(function(e4) {
        for (; ; ) switch (e4.prev = e4.next) {
          case 0:
            if (n2 = t.target, null != (o2 = null === (r2 = n2.dataset) || void 0 === r2 ? void 0 : r2.index) && "" !== o2) {
              e4.next = 4;
              break;
            }
            return e4.abrupt("return");
          case 4:
            if (a2 = +o2, unref(d) !== a2) {
              e4.next = 7;
              break;
            }
            return e4.abrupt("return");
          case 7:
            null != unref(d) && unref(d) !== a2 ? (S(), m && clearTimeout(m), m = setTimeout(function() {
              w(), clearTimeout(m);
            }, 100)) : w(), d.value = a2, y.value = n2;
          case 10:
          case "end":
            return e4.stop();
        }
      }, e3);
    }));
    return function(t) {
      return e2.apply(this, arguments);
    };
  }(), A = function() {
    var e2 = j(z.mark(function e3(t) {
      var r2, n2, o2, a2, i2;
      return z.wrap(function(e4) {
        for (; ; ) switch (e4.prev = e4.next) {
          case 0:
            if (a2 = t.target, !(!(null !== (r2 = unref(k)) && void 0 !== r2 && r2.isEqualNode(a2)) && (null === (n2 = unref(k)) || void 0 === n2 ? void 0 : n2.contains(a2)))) {
              e4.next = 4;
              break;
            }
            return e4.abrupt("return");
          case 4:
            if (i2 = null === (o2 = unref(b)) || void 0 === o2 ? void 0 : o2.$el, !((null == i2 ? void 0 : i2.contains(a2)) || false)) {
              e4.next = 8;
              break;
            }
            return e4.abrupt("return");
          case 8:
            g && clearTimeout(g), g = setTimeout(function() {
              unref(h) && S();
            }, 0);
          case 10:
          case "end":
            return e4.stop();
        }
      }, e3);
    }));
    return function(t) {
      return e2.apply(this, arguments);
    };
  }();
  watch(h, function() {
    unref(h) && (w(), clearTimeout(g));
  });
  var N, I2 = ref(e.max > unref(o).length), F2 = computed(function() {
    return "string" == typeof e.popupContainer || "object" === L(e.popupContainer) && null != e.popupContainer ? e.popupContainer : "body";
  }), P2 = computed(function() {
    return "boolean" == typeof e.popupContainer && false === e.popupContainer;
  }), M2 = computed(function() {
    return e.theme;
  });
  watch(function() {
    return [e.theme, unref(b)];
  }, function() {
    nextTick(function() {
      var e2, t;
      null === (e2 = unref(k)) || void 0 === e2 || e2.setAttribute("pick-colors-theme", unref(M2)), null === (t = unref(b)) || void 0 === t || null === (t = t.$el) || void 0 === t || t.setAttribute("pick-colors-theme", unref(M2));
    });
  }, { immediate: true }), provide("theme", { theme: M2 });
  var B2 = ref([]);
  return onMounted(function() {
    document.addEventListener("mouseup", A, false), e.showPicker && w();
  }), onUnmounted(function() {
    document.removeEventListener("mouseup", A, false), m && (clearTimeout(m), m = null), g && (clearTimeout(g), g = null);
  }), { valueList: o, colorItemSelected: function(t) {
    return (e.addColor ? unref(o).length > 0 : unref(o).length > 1) && unref(d) === t;
  }, selectedColor: v, selectedIndex: d, isShowPicker: h, addColorItemShow: I2, onPickerChange: function(t) {
    var r2 = unref(d), a2 = unref(o).slice(), i2 = unref(o).length;
    if (null != r2) {
      r2 >= 0 ? a2[r2] = t : (d.value = i2, a2.push(t));
      var c = "";
      c = Array.isArray(e.value) || e.addColor ? a2 : t, o.value = Array.isArray(c) ? c : [c], n("update:value", c), n("change", c, t, r2), e.addColor && i2 >= e.max && (I2.value = false, n("overflowMax"));
    }
  }, colorPicker: k, onColorClick: C, pickerRef: b, onColorItemDragStart: function(e2) {
    e2.dataTransfer.effectAllowed = "move";
    var t = e2.target;
    N = +t.dataset.index;
  }, onColorItemDragOver: function(e2) {
  }, onColorItemDrop: function(e2) {
    var t = +e2.target.dataset.index, r2 = _(unref(o)), a2 = r2[N];
    r2.splice(N, 1);
    var i2 = r2.slice(0, t), c = r2.splice(t), l = i2.concat([a2]).concat(c);
    n("update:value", l), n("change", l, l[N], N);
  }, colorItemsRef: B2, pickerStyle: x, values: a, teleportDisabled: P2, toPopupContainer: F2, formatValue: i, onFormatChange: function(e2) {
    i.value = e2, n("formatChange", e2);
  } };
} });
V(".color-picker[data-v-3c43ade8]{display:inline-block}.color-item[data-v-3c43ade8]{margin:5px}.add-color-item[data-v-3c43ade8]{display:inline-block;margin:5px}.picker[data-v-3c43ade8]{overflow:hidden}.v-enter-active[data-v-3c43ade8],.v-leave-active[data-v-3c43ade8]{opacity:1;transform:scaleY(1);transform-origin:center top;transition:opacity .2s ease-in-out,transform .2s ease-in-out}.v-enter-from[data-v-3c43ade8],.v-leave-to[data-v-3c43ade8]{opacity:0;transform:scaleY(0)}"), Re.render = function(e, t, i, c, l, u) {
  var s = resolveComponent("color-item"), f = resolveComponent("add-color-item"), d = resolveComponent("picker");
  return openBlock(), createElementBlock("div", { class: "color-picker", ref: "colorPicker", onDragstart: t[0] || (t[0] = withModifiers(function() {
    return e.onColorItemDragStart && e.onColorItemDragStart.apply(e, arguments);
  }, ["stop"])), onDragover: t[1] || (t[1] = withModifiers(function() {
    return e.onColorItemDragOver && e.onColorItemDragOver.apply(e, arguments);
  }, ["prevent", "stop"])), onDrop: t[2] || (t[2] = withModifiers(function() {
    return e.onColorItemDrop && e.onColorItemDrop.apply(e, arguments);
  }, ["prevent", "stop"])), onClick: t[3] || (t[3] = withModifiers(function() {
    return e.onColorClick && e.onColorClick.apply(e, arguments);
  }, ["stop"])) }, [(openBlock(true), createElementBlock(Fragment, null, renderList(e.values, function(t2, n) {
    return openBlock(), createBlock(s, { class: "color-item", key: n, ref_for: true, ref: function(t3) {
      return e.colorItemsRef[n] = t3;
    }, size: e.size, width: e.width, height: e.height, value: t2, selected: e.colorItemSelected(n), "data-index": n, draggable: e.valueList.length > 1, format: e.formatValue }, null, 8, ["size", "width", "height", "value", "selected", "data-index", "draggable", "format"]);
  }), 128)), e.addColor && e.addColorItemShow ? (openBlock(), createBlock(f, { key: 0, class: "add-color-item", ref: "addColorItem", selected: e.colorItemSelected(-1), "data-index": -1 }, null, 8, ["selected"])) : createCommentVNode("v-if", true), (openBlock(), createBlock(Teleport, { to: e.toPopupContainer, disabled: e.teleportDisabled }, [createVNode(Transition, null, { default: withCtx(function() {
    return [e.isShowPicker ? (openBlock(), createBlock(d, { key: 0, class: "picker", style: normalizeStyle(e.pickerStyle), ref: "pickerRef", value: e.selectedColor, format: e.formatValue, "show-alpha": e.showAlpha, colors: e.colors, formatOptions: e.formatOptions, onChange: e.onPickerChange, onFormatChange: e.onFormatChange }, null, 8, ["style", "value", "format", "show-alpha", "colors", "formatOptions", "onChange", "onFormatChange"])) : createCommentVNode("v-if", true)];
  }), _: 1 })], 8, ["to", "disabled"]))], 544);
}, Re.__scopeId = "data-v-3c43ade8", Re.__file = "src/ColorPicker.vue";
export {
  q as ALPHA_FORMAT_MAP,
  U as FORMAT_MAP,
  W as FORMAT_VALUE_MAP,
  Re as default
};
//# sourceMappingURL=vue-pick-colors.js.map
