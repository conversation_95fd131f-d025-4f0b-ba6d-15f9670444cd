/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AboutDialog: typeof import('./src/components/CodemirrorEditor/EditorHeader/AboutDialog.vue')['default']
    AIAssistantPanel: typeof import('./src/components/ai/AIAssistantPanel.vue')['default']
    AIConfig: typeof import('./src/components/ai/AIConfig.vue')['default']
    AIFixedBtn: typeof import('./src/components/ai/AIFixedBtn.vue')['default']
    AIPolishButton: typeof import('./src/components/AIPolish/AIPolishButton.vue')['default']
    AIPolishPopover: typeof import('./src/components/AIPolish/AIPolishPopover.vue')['default']
    Alert: typeof import('./src/components/ui/alert/Alert.vue')['default']
    AlertDescription: typeof import('./src/components/ui/alert/AlertDescription.vue')['default']
    AlertDialog: typeof import('./src/components/ui/alert-dialog/AlertDialog.vue')['default']
    AlertDialogAction: typeof import('./src/components/ui/alert-dialog/AlertDialogAction.vue')['default']
    AlertDialogCancel: typeof import('./src/components/ui/alert-dialog/AlertDialogCancel.vue')['default']
    AlertDialogContent: typeof import('./src/components/ui/alert-dialog/AlertDialogContent.vue')['default']
    AlertDialogDescription: typeof import('./src/components/ui/alert-dialog/AlertDialogDescription.vue')['default']
    AlertDialogFooter: typeof import('./src/components/ui/alert-dialog/AlertDialogFooter.vue')['default']
    AlertDialogHeader: typeof import('./src/components/ui/alert-dialog/AlertDialogHeader.vue')['default']
    AlertDialogTitle: typeof import('./src/components/ui/alert-dialog/AlertDialogTitle.vue')['default']
    AlertDialogTrigger: typeof import('./src/components/ui/alert-dialog/AlertDialogTrigger.vue')['default']
    AlertTitle: typeof import('./src/components/ui/alert/AlertTitle.vue')['default']
    BackTop: typeof import('./src/components/ui/back-top/BackTop.vue')['default']
    Button: typeof import('./src/components/ui/button/Button.vue')['default']
    ContextMenu: typeof import('./src/components/ui/context-menu/ContextMenu.vue')['default']
    ContextMenuCheckboxItem: typeof import('./src/components/ui/context-menu/ContextMenuCheckboxItem.vue')['default']
    ContextMenuContent: typeof import('./src/components/ui/context-menu/ContextMenuContent.vue')['default']
    ContextMenuGroup: typeof import('./src/components/ui/context-menu/ContextMenuGroup.vue')['default']
    ContextMenuItem: typeof import('./src/components/ui/context-menu/ContextMenuItem.vue')['default']
    ContextMenuLabel: typeof import('./src/components/ui/context-menu/ContextMenuLabel.vue')['default']
    ContextMenuPortal: typeof import('./src/components/ui/context-menu/ContextMenuPortal.vue')['default']
    ContextMenuRadioGroup: typeof import('./src/components/ui/context-menu/ContextMenuRadioGroup.vue')['default']
    ContextMenuRadioItem: typeof import('./src/components/ui/context-menu/ContextMenuRadioItem.vue')['default']
    ContextMenuSeparator: typeof import('./src/components/ui/context-menu/ContextMenuSeparator.vue')['default']
    ContextMenuShortcut: typeof import('./src/components/ui/context-menu/ContextMenuShortcut.vue')['default']
    ContextMenuSub: typeof import('./src/components/ui/context-menu/ContextMenuSub.vue')['default']
    ContextMenuSubContent: typeof import('./src/components/ui/context-menu/ContextMenuSubContent.vue')['default']
    ContextMenuSubTrigger: typeof import('./src/components/ui/context-menu/ContextMenuSubTrigger.vue')['default']
    ContextMenuTrigger: typeof import('./src/components/ui/context-menu/ContextMenuTrigger.vue')['default']
    CssEditor: typeof import('./src/components/CodemirrorEditor/CssEditor.vue')['default']
    CustomUploadForm: typeof import('./src/components/CustomUploadForm.vue')['default']
    Dialog: typeof import('./src/components/ui/dialog/Dialog.vue')['default']
    DialogClose: typeof import('./src/components/ui/dialog/DialogClose.vue')['default']
    DialogContent: typeof import('./src/components/ui/dialog/DialogContent.vue')['default']
    DialogDescription: typeof import('./src/components/ui/dialog/DialogDescription.vue')['default']
    DialogFooter: typeof import('./src/components/ui/dialog/DialogFooter.vue')['default']
    DialogHeader: typeof import('./src/components/ui/dialog/DialogHeader.vue')['default']
    DialogScrollContent: typeof import('./src/components/ui/dialog/DialogScrollContent.vue')['default']
    DialogTitle: typeof import('./src/components/ui/dialog/DialogTitle.vue')['default']
    DialogTrigger: typeof import('./src/components/ui/dialog/DialogTrigger.vue')['default']
    DropdownMenu: typeof import('./src/components/ui/dropdown-menu/DropdownMenu.vue')['default']
    DropdownMenuCheckboxItem: typeof import('./src/components/ui/dropdown-menu/DropdownMenuCheckboxItem.vue')['default']
    DropdownMenuContent: typeof import('./src/components/ui/dropdown-menu/DropdownMenuContent.vue')['default']
    DropdownMenuGroup: typeof import('./src/components/ui/dropdown-menu/DropdownMenuGroup.vue')['default']
    DropdownMenuItem: typeof import('./src/components/ui/dropdown-menu/DropdownMenuItem.vue')['default']
    DropdownMenuLabel: typeof import('./src/components/ui/dropdown-menu/DropdownMenuLabel.vue')['default']
    DropdownMenuRadioGroup: typeof import('./src/components/ui/dropdown-menu/DropdownMenuRadioGroup.vue')['default']
    DropdownMenuRadioItem: typeof import('./src/components/ui/dropdown-menu/DropdownMenuRadioItem.vue')['default']
    DropdownMenuSeparator: typeof import('./src/components/ui/dropdown-menu/DropdownMenuSeparator.vue')['default']
    DropdownMenuShortcut: typeof import('./src/components/ui/dropdown-menu/DropdownMenuShortcut.vue')['default']
    DropdownMenuSub: typeof import('./src/components/ui/dropdown-menu/DropdownMenuSub.vue')['default']
    DropdownMenuSubContent: typeof import('./src/components/ui/dropdown-menu/DropdownMenuSubContent.vue')['default']
    DropdownMenuSubTrigger: typeof import('./src/components/ui/dropdown-menu/DropdownMenuSubTrigger.vue')['default']
    DropdownMenuTrigger: typeof import('./src/components/ui/dropdown-menu/DropdownMenuTrigger.vue')['default']
    EditDropdown: typeof import('./src/components/CodemirrorEditor/EditorHeader/EditDropdown.vue')['default']
    EditorHeader: typeof import('./src/components/CodemirrorEditor/EditorHeader/index.vue')['default']
    EditorStateDialog: typeof import('./src/components/CodemirrorEditor/EditorStateDialog.vue')['default']
    FileDropdown: typeof import('./src/components/CodemirrorEditor/EditorHeader/FileDropdown.vue')['default']
    FormItem: typeof import('./src/components/FormItem.vue')['default']
    FundDialog: typeof import('./src/components/CodemirrorEditor/EditorHeader/FundDialog.vue')['default']
    HelpDropdown: typeof import('./src/components/CodemirrorEditor/EditorHeader/HelpDropdown.vue')['default']
    HoverCard: typeof import('./src/components/ui/hover-card/HoverCard.vue')['default']
    HoverCardContent: typeof import('./src/components/ui/hover-card/HoverCardContent.vue')['default']
    HoverCardTrigger: typeof import('./src/components/ui/hover-card/HoverCardTrigger.vue')['default']
    Input: typeof import('./src/components/ui/input/Input.vue')['default']
    InsertFormDialog: typeof import('./src/components/CodemirrorEditor/InsertFormDialog.vue')['default']
    InsertMpCardDialog: typeof import('./src/components/CodemirrorEditor/InsertMpCardDialog.vue')['default']
    Label: typeof import('./src/components/ui/label/Label.vue')['default']
    Menubar: typeof import('./src/components/ui/menubar/Menubar.vue')['default']
    MenubarCheckboxItem: typeof import('./src/components/ui/menubar/MenubarCheckboxItem.vue')['default']
    MenubarContent: typeof import('./src/components/ui/menubar/MenubarContent.vue')['default']
    MenubarGroup: typeof import('./src/components/ui/menubar/MenubarGroup.vue')['default']
    MenubarItem: typeof import('./src/components/ui/menubar/MenubarItem.vue')['default']
    MenubarLabel: typeof import('./src/components/ui/menubar/MenubarLabel.vue')['default']
    MenubarMenu: typeof import('./src/components/ui/menubar/MenubarMenu.vue')['default']
    MenubarRadioGroup: typeof import('./src/components/ui/menubar/MenubarRadioGroup.vue')['default']
    MenubarRadioItem: typeof import('./src/components/ui/menubar/MenubarRadioItem.vue')['default']
    MenubarSeparator: typeof import('./src/components/ui/menubar/MenubarSeparator.vue')['default']
    MenubarShortcut: typeof import('./src/components/ui/menubar/MenubarShortcut.vue')['default']
    MenubarSub: typeof import('./src/components/ui/menubar/MenubarSub.vue')['default']
    MenubarSubContent: typeof import('./src/components/ui/menubar/MenubarSubContent.vue')['default']
    MenubarSubTrigger: typeof import('./src/components/ui/menubar/MenubarSubTrigger.vue')['default']
    MenubarTrigger: typeof import('./src/components/ui/menubar/MenubarTrigger.vue')['default']
    NumberField: typeof import('./src/components/ui/number-field/NumberField.vue')['default']
    NumberFieldContent: typeof import('./src/components/ui/number-field/NumberFieldContent.vue')['default']
    NumberFieldDecrement: typeof import('./src/components/ui/number-field/NumberFieldDecrement.vue')['default']
    NumberFieldIncrement: typeof import('./src/components/ui/number-field/NumberFieldIncrement.vue')['default']
    NumberFieldInput: typeof import('./src/components/ui/number-field/NumberFieldInput.vue')['default']
    Popover: typeof import('./src/components/ui/popover/Popover.vue')['default']
    PopoverContent: typeof import('./src/components/ui/popover/PopoverContent.vue')['default']
    PopoverTrigger: typeof import('./src/components/ui/popover/PopoverTrigger.vue')['default']
    PostInfo: typeof import('./src/components/CodemirrorEditor/EditorHeader/PostInfo.vue')['default']
    PostItem: typeof import('./src/components/CodemirrorEditor/PostItem.vue')['default']
    PostSlider: typeof import('./src/components/CodemirrorEditor/PostSlider.vue')['default']
    PostTaskDialog: typeof import('./src/components/CodemirrorEditor/EditorHeader/PostTaskDialog.vue')['default']
    QuickCommandManager: typeof import('./src/components/ai/QuickCommandManager.vue')['default']
    ResizableHandle: typeof import('./src/components/ui/resizable/ResizableHandle.vue')['default']
    ResizablePanelGroup: typeof import('./src/components/ui/resizable/ResizablePanelGroup.vue')['default']
    RightSlider: typeof import('./src/components/CodemirrorEditor/RightSlider.vue')['default']
    RunLoading: typeof import('./src/components/RunLoading.vue')['default']
    SearchTab: typeof import('./src/components/ui/search-tab/SearchTab.vue')['default']
    Select: typeof import('./src/components/ui/select/Select.vue')['default']
    SelectContent: typeof import('./src/components/ui/select/SelectContent.vue')['default']
    SelectGroup: typeof import('./src/components/ui/select/SelectGroup.vue')['default']
    SelectItem: typeof import('./src/components/ui/select/SelectItem.vue')['default']
    SelectItemText: typeof import('./src/components/ui/select/SelectItemText.vue')['default']
    SelectLabel: typeof import('./src/components/ui/select/SelectLabel.vue')['default']
    SelectScrollDownButton: typeof import('./src/components/ui/select/SelectScrollDownButton.vue')['default']
    SelectScrollUpButton: typeof import('./src/components/ui/select/SelectScrollUpButton.vue')['default']
    SelectSeparator: typeof import('./src/components/ui/select/SelectSeparator.vue')['default']
    SelectTrigger: typeof import('./src/components/ui/select/SelectTrigger.vue')['default']
    SelectValue: typeof import('./src/components/ui/select/SelectValue.vue')['default']
    Separator: typeof import('./src/components/ui/separator/Separator.vue')['default']
    Sonner: typeof import('./src/components/ui/sonner/Sonner.vue')['default']
    StyleDropdown: typeof import('./src/components/CodemirrorEditor/EditorHeader/StyleDropdown.vue')['default']
    StyleOptionMenu: typeof import('./src/components/CodemirrorEditor/EditorHeader/StyleOptionMenu.vue')['default']
    Switch: typeof import('./src/components/ui/switch/Switch.vue')['default']
    Tabs: typeof import('./src/components/ui/tabs/Tabs.vue')['default']
    TabsContent: typeof import('./src/components/ui/tabs/TabsContent.vue')['default']
    TabsList: typeof import('./src/components/ui/tabs/TabsList.vue')['default']
    TabsTrigger: typeof import('./src/components/ui/tabs/TabsTrigger.vue')['default']
    Textarea: typeof import('./src/components/ui/textarea/Textarea.vue')['default']
    Tooltip: typeof import('./src/components/ui/tooltip/Tooltip.vue')['default']
    TooltipContent: typeof import('./src/components/ui/tooltip/TooltipContent.vue')['default']
    TooltipProvider: typeof import('./src/components/ui/tooltip/TooltipProvider.vue')['default']
    TooltipTrigger: typeof import('./src/components/ui/tooltip/TooltipTrigger.vue')['default']
    UploadImgDialog: typeof import('./src/components/CodemirrorEditor/UploadImgDialog.vue')['default']
  }
}
