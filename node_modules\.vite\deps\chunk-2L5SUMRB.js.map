{"version": 3, "sources": ["../../ts-dedent/src/index.ts", "../../mermaid/dist/chunks/mermaid.core/chunk-C3MQ5ANM.mjs"], "sourcesContent": ["export function dedent(\n  templ: TemplateStringsArray | string,\n  ...values: unknown[]\n): string {\n  let strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n\n  // 1. Remove trailing whitespace.\n  strings[strings.length - 1] = strings[strings.length - 1].replace(\n    /\\r?\\n([\\t ]*)$/,\n    '',\n  );\n\n  // 2. Find all line breaks to determine the highest common indentation level.\n  const indentLengths = strings.reduce((arr, str) => {\n    const matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n    if (matches) {\n      return arr.concat(\n        matches.map((match) => match.match(/[\\t ]/g)?.length ?? 0),\n      );\n    }\n    return arr;\n  }, <number[]>[]);\n\n  // 3. Remove the common indentation from all strings.\n  if (indentLengths.length) {\n    const pattern = new RegExp(`\\n[\\t ]{${Math.min(...indentLengths)}}`, 'g');\n\n    strings = strings.map((str) => str.replace(pattern, '\\n'));\n  }\n\n  // 4. Remove leading whitespace.\n  strings[0] = strings[0].replace(/^\\r?\\n/, '');\n\n  // 5. Perform interpolation.\n  let string = strings[0];\n\n  values.forEach((value, i) => {\n    // 5.1 Read current indentation level\n    const endentations = string.match(/(?:^|\\n)( *)$/)\n    const endentation = endentations ? endentations[1] : ''\n    let indentedValue = value\n    // 5.2 Add indentation to values with multiline strings\n    if (typeof value === 'string' && value.includes('\\n')) {\n      indentedValue = String(value)\n        .split('\\n')\n        .map((str, i) => {\n          return i === 0 ? str : `${endentation}${str}`\n        })\n        .join('\\n');\n    }\n\n    string += indentedValue + strings[i + 1];\n  });\n\n  return string;\n}\n\nexport default dedent;\n", "import {\n  decodeEntities\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  common_default,\n  getConfig2 as getConfig,\n  hasKatex,\n  log,\n  renderKatex\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/createText.ts\nimport { select } from \"d3\";\n\n// src/rendering-util/handle-markdown-text.ts\nimport { marked } from \"marked\";\nimport { dedent } from \"ts-dedent\";\nfunction preprocessMarkdown(markdown, { markdownAutoWrap }) {\n  const withoutBR = markdown.replace(/<br\\/>/g, \"\\n\");\n  const withoutMultipleNewlines = withoutBR.replace(/\\n{2,}/g, \"\\n\");\n  const withoutExtraSpaces = dedent(withoutMultipleNewlines);\n  if (markdownAutoWrap === false) {\n    return withoutExtraSpaces.replace(/ /g, \"&nbsp;\");\n  }\n  return withoutExtraSpaces;\n}\n__name(preprocessMarkdown, \"preprocessMarkdown\");\nfunction markdownToLines(markdown, config = {}) {\n  const preprocessedMarkdown = preprocessMarkdown(markdown, config);\n  const nodes = marked.lexer(preprocessedMarkdown);\n  const lines = [[]];\n  let currentLine = 0;\n  function processNode(node, parentType = \"normal\") {\n    if (node.type === \"text\") {\n      const textLines = node.text.split(\"\\n\");\n      textLines.forEach((textLine, index) => {\n        if (index !== 0) {\n          currentLine++;\n          lines.push([]);\n        }\n        textLine.split(\" \").forEach((word) => {\n          word = word.replace(/&#39;/g, `'`);\n          if (word) {\n            lines[currentLine].push({ content: word, type: parentType });\n          }\n        });\n      });\n    } else if (node.type === \"strong\" || node.type === \"em\") {\n      node.tokens.forEach((contentNode) => {\n        processNode(contentNode, node.type);\n      });\n    } else if (node.type === \"html\") {\n      lines[currentLine].push({ content: node.text, type: \"normal\" });\n    }\n  }\n  __name(processNode, \"processNode\");\n  nodes.forEach((treeNode) => {\n    if (treeNode.type === \"paragraph\") {\n      treeNode.tokens?.forEach((contentNode) => {\n        processNode(contentNode);\n      });\n    } else if (treeNode.type === \"html\") {\n      lines[currentLine].push({ content: treeNode.text, type: \"normal\" });\n    }\n  });\n  return lines;\n}\n__name(markdownToLines, \"markdownToLines\");\nfunction markdownToHTML(markdown, { markdownAutoWrap } = {}) {\n  const nodes = marked.lexer(markdown);\n  function output(node) {\n    if (node.type === \"text\") {\n      if (markdownAutoWrap === false) {\n        return node.text.replace(/\\n */g, \"<br/>\").replace(/ /g, \"&nbsp;\");\n      }\n      return node.text.replace(/\\n */g, \"<br/>\");\n    } else if (node.type === \"strong\") {\n      return `<strong>${node.tokens?.map(output).join(\"\")}</strong>`;\n    } else if (node.type === \"em\") {\n      return `<em>${node.tokens?.map(output).join(\"\")}</em>`;\n    } else if (node.type === \"paragraph\") {\n      return `<p>${node.tokens?.map(output).join(\"\")}</p>`;\n    } else if (node.type === \"space\") {\n      return \"\";\n    } else if (node.type === \"html\") {\n      return `${node.text}`;\n    } else if (node.type === \"escape\") {\n      return node.text;\n    }\n    return `Unsupported markdown: ${node.type}`;\n  }\n  __name(output, \"output\");\n  return nodes.map(output).join(\"\");\n}\n__name(markdownToHTML, \"markdownToHTML\");\n\n// src/rendering-util/splitText.ts\nfunction splitTextToChars(text) {\n  if (Intl.Segmenter) {\n    return [...new Intl.Segmenter().segment(text)].map((s) => s.segment);\n  }\n  return [...text];\n}\n__name(splitTextToChars, \"splitTextToChars\");\nfunction splitWordToFitWidth(checkFit, word) {\n  const characters = splitTextToChars(word.content);\n  return splitWordToFitWidthRecursion(checkFit, [], characters, word.type);\n}\n__name(splitWordToFitWidth, \"splitWordToFitWidth\");\nfunction splitWordToFitWidthRecursion(checkFit, usedChars, remainingChars, type) {\n  if (remainingChars.length === 0) {\n    return [\n      { content: usedChars.join(\"\"), type },\n      { content: \"\", type }\n    ];\n  }\n  const [nextChar, ...rest] = remainingChars;\n  const newWord = [...usedChars, nextChar];\n  if (checkFit([{ content: newWord.join(\"\"), type }])) {\n    return splitWordToFitWidthRecursion(checkFit, newWord, rest, type);\n  }\n  if (usedChars.length === 0 && nextChar) {\n    usedChars.push(nextChar);\n    remainingChars.shift();\n  }\n  return [\n    { content: usedChars.join(\"\"), type },\n    { content: remainingChars.join(\"\"), type }\n  ];\n}\n__name(splitWordToFitWidthRecursion, \"splitWordToFitWidthRecursion\");\nfunction splitLineToFitWidth(line, checkFit) {\n  if (line.some(({ content }) => content.includes(\"\\n\"))) {\n    throw new Error(\"splitLineToFitWidth does not support newlines in the line\");\n  }\n  return splitLineToFitWidthRecursion(line, checkFit);\n}\n__name(splitLineToFitWidth, \"splitLineToFitWidth\");\nfunction splitLineToFitWidthRecursion(words, checkFit, lines = [], newLine = []) {\n  if (words.length === 0) {\n    if (newLine.length > 0) {\n      lines.push(newLine);\n    }\n    return lines.length > 0 ? lines : [];\n  }\n  let joiner = \"\";\n  if (words[0].content === \" \") {\n    joiner = \" \";\n    words.shift();\n  }\n  const nextWord = words.shift() ?? { content: \" \", type: \"normal\" };\n  const lineWithNextWord = [...newLine];\n  if (joiner !== \"\") {\n    lineWithNextWord.push({ content: joiner, type: \"normal\" });\n  }\n  lineWithNextWord.push(nextWord);\n  if (checkFit(lineWithNextWord)) {\n    return splitLineToFitWidthRecursion(words, checkFit, lines, lineWithNextWord);\n  }\n  if (newLine.length > 0) {\n    lines.push(newLine);\n    words.unshift(nextWord);\n  } else if (nextWord.content) {\n    const [line, rest] = splitWordToFitWidth(checkFit, nextWord);\n    lines.push([line]);\n    if (rest.content) {\n      words.unshift(rest);\n    }\n  }\n  return splitLineToFitWidthRecursion(words, checkFit, lines);\n}\n__name(splitLineToFitWidthRecursion, \"splitLineToFitWidthRecursion\");\n\n// src/rendering-util/createText.ts\nfunction applyStyle(dom, styleFn) {\n  if (styleFn) {\n    dom.attr(\"style\", styleFn);\n  }\n}\n__name(applyStyle, \"applyStyle\");\nasync function addHtmlSpan(element, node, width, classes, addBackground = false) {\n  const fo = element.append(\"foreignObject\");\n  fo.attr(\"width\", `${10 * width}px`);\n  fo.attr(\"height\", `${10 * width}px`);\n  const div = fo.append(\"xhtml:div\");\n  let label = node.label;\n  if (node.label && hasKatex(node.label)) {\n    label = await renderKatex(node.label.replace(common_default.lineBreakRegex, \"\\n\"), getConfig());\n  }\n  const labelClass = node.isNode ? \"nodeLabel\" : \"edgeLabel\";\n  const span = div.append(\"span\");\n  span.html(label);\n  applyStyle(span, node.labelStyle);\n  span.attr(\"class\", `${labelClass} ${classes}`);\n  applyStyle(div, node.labelStyle);\n  div.style(\"display\", \"table-cell\");\n  div.style(\"white-space\", \"nowrap\");\n  div.style(\"line-height\", \"1.5\");\n  div.style(\"max-width\", width + \"px\");\n  div.style(\"text-align\", \"center\");\n  div.attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\");\n  if (addBackground) {\n    div.attr(\"class\", \"labelBkg\");\n  }\n  let bbox = div.node().getBoundingClientRect();\n  if (bbox.width === width) {\n    div.style(\"display\", \"table\");\n    div.style(\"white-space\", \"break-spaces\");\n    div.style(\"width\", width + \"px\");\n    bbox = div.node().getBoundingClientRect();\n  }\n  return fo.node();\n}\n__name(addHtmlSpan, \"addHtmlSpan\");\nfunction createTspan(textElement, lineIndex, lineHeight) {\n  return textElement.append(\"tspan\").attr(\"class\", \"text-outer-tspan\").attr(\"x\", 0).attr(\"y\", lineIndex * lineHeight - 0.1 + \"em\").attr(\"dy\", lineHeight + \"em\");\n}\n__name(createTspan, \"createTspan\");\nfunction computeWidthOfText(parentNode, lineHeight, line) {\n  const testElement = parentNode.append(\"text\");\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, line);\n  const textLength = testSpan.node().getComputedTextLength();\n  testElement.remove();\n  return textLength;\n}\n__name(computeWidthOfText, \"computeWidthOfText\");\nfunction computeDimensionOfText(parentNode, lineHeight, text) {\n  const testElement = parentNode.append(\"text\");\n  const testSpan = createTspan(testElement, 1, lineHeight);\n  updateTextContentAndStyles(testSpan, [{ content: text, type: \"normal\" }]);\n  const textDimension = testSpan.node()?.getBoundingClientRect();\n  if (textDimension) {\n    testElement.remove();\n  }\n  return textDimension;\n}\n__name(computeDimensionOfText, \"computeDimensionOfText\");\nfunction createFormattedText(width, g, structuredText, addBackground = false) {\n  const lineHeight = 1.1;\n  const labelGroup = g.append(\"g\");\n  const bkg = labelGroup.insert(\"rect\").attr(\"class\", \"background\").attr(\"style\", \"stroke: none\");\n  const textElement = labelGroup.append(\"text\").attr(\"y\", \"-10.1\");\n  let lineIndex = 0;\n  for (const line of structuredText) {\n    const checkWidth = /* @__PURE__ */ __name((line2) => computeWidthOfText(labelGroup, lineHeight, line2) <= width, \"checkWidth\");\n    const linesUnderWidth = checkWidth(line) ? [line] : splitLineToFitWidth(line, checkWidth);\n    for (const preparedLine of linesUnderWidth) {\n      const tspan = createTspan(textElement, lineIndex, lineHeight);\n      updateTextContentAndStyles(tspan, preparedLine);\n      lineIndex++;\n    }\n  }\n  if (addBackground) {\n    const bbox = textElement.node().getBBox();\n    const padding = 2;\n    bkg.attr(\"x\", bbox.x - padding).attr(\"y\", bbox.y - padding).attr(\"width\", bbox.width + 2 * padding).attr(\"height\", bbox.height + 2 * padding);\n    return labelGroup.node();\n  } else {\n    return textElement.node();\n  }\n}\n__name(createFormattedText, \"createFormattedText\");\nfunction updateTextContentAndStyles(tspan, wrappedLine) {\n  tspan.text(\"\");\n  wrappedLine.forEach((word, index) => {\n    const innerTspan = tspan.append(\"tspan\").attr(\"font-style\", word.type === \"em\" ? \"italic\" : \"normal\").attr(\"class\", \"text-inner-tspan\").attr(\"font-weight\", word.type === \"strong\" ? \"bold\" : \"normal\");\n    if (index === 0) {\n      innerTspan.text(word.content);\n    } else {\n      innerTspan.text(\" \" + word.content);\n    }\n  });\n}\n__name(updateTextContentAndStyles, \"updateTextContentAndStyles\");\nfunction replaceIconSubstring(text) {\n  return text.replace(\n    /fa[bklrs]?:fa-[\\w-]+/g,\n    // cspell: disable-line\n    (s) => `<i class='${s.replace(\":\", \" \")}'></i>`\n  );\n}\n__name(replaceIconSubstring, \"replaceIconSubstring\");\nvar createText = /* @__PURE__ */ __name(async (el, text = \"\", {\n  style = \"\",\n  isTitle = false,\n  classes = \"\",\n  useHtmlLabels = true,\n  isNode = true,\n  width = 200,\n  addSvgBackground = false\n} = {}, config) => {\n  log.debug(\n    \"XYZ createText\",\n    text,\n    style,\n    isTitle,\n    classes,\n    useHtmlLabels,\n    isNode,\n    \"addSvgBackground: \",\n    addSvgBackground\n  );\n  if (useHtmlLabels) {\n    const htmlText = markdownToHTML(text, config);\n    const decodedReplacedText = replaceIconSubstring(decodeEntities(htmlText));\n    const inputForKatex = text.replace(/\\\\\\\\/g, \"\\\\\");\n    const node = {\n      isNode,\n      label: hasKatex(text) ? inputForKatex : decodedReplacedText,\n      labelStyle: style.replace(\"fill:\", \"color:\")\n    };\n    const vertexNode = await addHtmlSpan(el, node, width, classes, addSvgBackground);\n    return vertexNode;\n  } else {\n    const sanitizeBR = text.replace(/<br\\s*\\/?>/g, \"<br/>\");\n    const structuredText = markdownToLines(sanitizeBR.replace(\"<br>\", \"<br/>\"), config);\n    const svgLabel = createFormattedText(\n      width,\n      el,\n      structuredText,\n      text ? addSvgBackground : false\n    );\n    if (isNode) {\n      if (/stroke:/.exec(style)) {\n        style = style.replace(\"stroke:\", \"lineColor:\");\n      }\n      const nodeLabelTextStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/color:/g, \"fill:\");\n      select(svgLabel).attr(\"style\", nodeLabelTextStyle);\n    } else {\n      const edgeLabelRectStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/background:/g, \"fill:\");\n      select(svgLabel).select(\"rect\").attr(\"style\", edgeLabelRectStyle.replace(/background:/g, \"fill:\"));\n      const edgeLabelTextStyle = style.replace(/stroke:[^;]+;?/g, \"\").replace(/stroke-width:[^;]+;?/g, \"\").replace(/fill:[^;]+;?/g, \"\").replace(/color:/g, \"fill:\");\n      select(svgLabel).select(\"text\").attr(\"style\", edgeLabelTextStyle);\n    }\n    return svgLabel;\n  }\n}, \"createText\");\n\nexport {\n  computeDimensionOfText,\n  replaceIconSubstring,\n  createText\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;IAAAA,eAAA;IAAAA,eAAA;AAAM,SAAU,OACd,OAAoC;AACpC,MAAA,SAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAoB;AAApB,WAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAEA,MAAI,UAAU,MAAM,KAAK,OAAO,UAAU,WAAW,CAAC,KAAK,IAAI,KAAK;AAGpE,UAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,QAAQ,SAAS,CAAC,EAAE,QACxD,kBACA,EAAE;AAIJ,MAAM,gBAAgB,QAAQ,OAAO,SAAC,KAAK,KAAG;AAC5C,QAAM,UAAU,IAAI,MAAM,qBAAqB;AAC/C,QAAI,SAAS;AACX,aAAO,IAAI,OACT,QAAQ,IAAI,SAAC,OAAK;AAAA,YAAA,IAAA;AAAK,gBAAA,MAAA,KAAA,MAAM,MAAM,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,KAAI;MAAC,CAAA,CAAC;;AAG9D,WAAO;EACT,GAAa,CAAA,CAAE;AAGf,MAAI,cAAc,QAAQ;AACxB,QAAM,YAAU,IAAI,OAAO,YAAW,KAAK,IAAG,MAAR,MAAY,aAAa,IAAA,KAAM,GAAG;AAExE,cAAU,QAAQ,IAAI,SAAC,KAAG;AAAK,aAAA,IAAI,QAAQ,WAAS,IAAI;IAAzB,CAA0B;;AAI3D,UAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,QAAQ,UAAU,EAAE;AAG5C,MAAI,SAAS,QAAQ,CAAC;AAEtB,SAAO,QAAQ,SAAC,OAAO,GAAC;AAEtB,QAAM,eAAe,OAAO,MAAM,eAAe;AACjD,QAAM,cAAc,eAAe,aAAa,CAAC,IAAI;AACrD,QAAI,gBAAgB;AAEpB,QAAI,OAAO,UAAU,YAAY,MAAM,SAAS,IAAI,GAAG;AACrD,sBAAgB,OAAO,KAAK,EACzB,MAAM,IAAI,EACV,IAAI,SAAC,KAAKC,IAAC;AACV,eAAOA,OAAM,IAAI,MAAM,KAAG,cAAc;MAC1C,CAAC,EACA,KAAK,IAAI;;AAGd,cAAU,gBAAgB,QAAQ,IAAI,CAAC;EACzC,CAAC;AAED,SAAO;AACT;;;ACvDA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAkBA,SAAS,mBAAmB,UAAU,EAAE,iBAAiB,GAAG;AAC1D,QAAM,YAAY,SAAS,QAAQ,WAAW,IAAI;AAClD,QAAM,0BAA0B,UAAU,QAAQ,WAAW,IAAI;AACjE,QAAM,qBAAqB,OAAO,uBAAuB;AACzD,MAAI,qBAAqB,OAAO;AAC9B,WAAO,mBAAmB,QAAQ,MAAM,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,gBAAgB,UAAU,SAAS,CAAC,GAAG;AAC9C,QAAM,uBAAuB,mBAAmB,UAAU,MAAM;AAChE,QAAM,QAAQ,OAAO,MAAM,oBAAoB;AAC/C,QAAM,QAAQ,CAAC,CAAC,CAAC;AACjB,MAAI,cAAc;AAClB,WAAS,YAAY,MAAM,aAAa,UAAU;AAChD,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,YAAY,KAAK,KAAK,MAAM,IAAI;AACtC,gBAAU,QAAQ,CAAC,UAAU,UAAU;AACrC,YAAI,UAAU,GAAG;AACf;AACA,gBAAM,KAAK,CAAC,CAAC;AAAA,QACf;AACA,iBAAS,MAAM,GAAG,EAAE,QAAQ,CAAC,SAAS;AACpC,iBAAO,KAAK,QAAQ,UAAU,GAAG;AACjC,cAAI,MAAM;AACR,kBAAM,WAAW,EAAE,KAAK,EAAE,SAAS,MAAM,MAAM,WAAW,CAAC;AAAA,UAC7D;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,YAAY,KAAK,SAAS,MAAM;AACvD,WAAK,OAAO,QAAQ,CAAC,gBAAgB;AACnC,oBAAY,aAAa,KAAK,IAAI;AAAA,MACpC,CAAC;AAAA,IACH,WAAW,KAAK,SAAS,QAAQ;AAC/B,YAAM,WAAW,EAAE,KAAK,EAAE,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,IAChE;AAAA,EACF;AACA,SAAO,aAAa,aAAa;AACjC,QAAM,QAAQ,CAAC,aAAa;AAzD9B;AA0DI,QAAI,SAAS,SAAS,aAAa;AACjC,qBAAS,WAAT,mBAAiB,QAAQ,CAAC,gBAAgB;AACxC,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF,WAAW,SAAS,SAAS,QAAQ;AACnC,YAAM,WAAW,EAAE,KAAK,EAAE,SAAS,SAAS,MAAM,MAAM,SAAS,CAAC;AAAA,IACpE;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,OAAO,iBAAiB,iBAAiB;AACzC,SAAS,eAAe,UAAU,EAAE,iBAAiB,IAAI,CAAC,GAAG;AAC3D,QAAM,QAAQ,OAAO,MAAM,QAAQ;AACnC,WAAS,OAAO,MAAM;AAvExB;AAwEI,QAAI,KAAK,SAAS,QAAQ;AACxB,UAAI,qBAAqB,OAAO;AAC9B,eAAO,KAAK,KAAK,QAAQ,SAAS,OAAO,EAAE,QAAQ,MAAM,QAAQ;AAAA,MACnE;AACA,aAAO,KAAK,KAAK,QAAQ,SAAS,OAAO;AAAA,IAC3C,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,YAAW,UAAK,WAAL,mBAAa,IAAI,QAAQ,KAAK,GAAG;AAAA,IACrD,WAAW,KAAK,SAAS,MAAM;AAC7B,aAAO,QAAO,UAAK,WAAL,mBAAa,IAAI,QAAQ,KAAK,GAAG;AAAA,IACjD,WAAW,KAAK,SAAS,aAAa;AACpC,aAAO,OAAM,UAAK,WAAL,mBAAa,IAAI,QAAQ,KAAK,GAAG;AAAA,IAChD,WAAW,KAAK,SAAS,SAAS;AAChC,aAAO;AAAA,IACT,WAAW,KAAK,SAAS,QAAQ;AAC/B,aAAO,GAAG,KAAK,IAAI;AAAA,IACrB,WAAW,KAAK,SAAS,UAAU;AACjC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,yBAAyB,KAAK,IAAI;AAAA,EAC3C;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,MAAM,IAAI,MAAM,EAAE,KAAK,EAAE;AAClC;AACA,OAAO,gBAAgB,gBAAgB;AAGvC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,KAAK,WAAW;AAClB,WAAO,CAAC,GAAG,IAAI,KAAK,UAAU,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;AAAA,EACrE;AACA,SAAO,CAAC,GAAG,IAAI;AACjB;AACA,OAAO,kBAAkB,kBAAkB;AAC3C,SAAS,oBAAoB,UAAU,MAAM;AAC3C,QAAM,aAAa,iBAAiB,KAAK,OAAO;AAChD,SAAO,6BAA6B,UAAU,CAAC,GAAG,YAAY,KAAK,IAAI;AACzE;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,6BAA6B,UAAU,WAAW,gBAAgB,MAAM;AAC/E,MAAI,eAAe,WAAW,GAAG;AAC/B,WAAO;AAAA,MACL,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK;AAAA,MACpC,EAAE,SAAS,IAAI,KAAK;AAAA,IACtB;AAAA,EACF;AACA,QAAM,CAAC,UAAU,GAAG,IAAI,IAAI;AAC5B,QAAM,UAAU,CAAC,GAAG,WAAW,QAAQ;AACvC,MAAI,SAAS,CAAC,EAAE,SAAS,QAAQ,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC,GAAG;AACnD,WAAO,6BAA6B,UAAU,SAAS,MAAM,IAAI;AAAA,EACnE;AACA,MAAI,UAAU,WAAW,KAAK,UAAU;AACtC,cAAU,KAAK,QAAQ;AACvB,mBAAe,MAAM;AAAA,EACvB;AACA,SAAO;AAAA,IACL,EAAE,SAAS,UAAU,KAAK,EAAE,GAAG,KAAK;AAAA,IACpC,EAAE,SAAS,eAAe,KAAK,EAAE,GAAG,KAAK;AAAA,EAC3C;AACF;AACA,OAAO,8BAA8B,8BAA8B;AACnE,SAAS,oBAAoB,MAAM,UAAU;AAC3C,MAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,MAAM,QAAQ,SAAS,IAAI,CAAC,GAAG;AACtD,UAAM,IAAI,MAAM,2DAA2D;AAAA,EAC7E;AACA,SAAO,6BAA6B,MAAM,QAAQ;AACpD;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,6BAA6B,OAAO,UAAU,QAAQ,CAAC,GAAG,UAAU,CAAC,GAAG;AAC/E,MAAI,MAAM,WAAW,GAAG;AACtB,QAAI,QAAQ,SAAS,GAAG;AACtB,YAAM,KAAK,OAAO;AAAA,IACpB;AACA,WAAO,MAAM,SAAS,IAAI,QAAQ,CAAC;AAAA,EACrC;AACA,MAAI,SAAS;AACb,MAAI,MAAM,CAAC,EAAE,YAAY,KAAK;AAC5B,aAAS;AACT,UAAM,MAAM;AAAA,EACd;AACA,QAAM,WAAW,MAAM,MAAM,KAAK,EAAE,SAAS,KAAK,MAAM,SAAS;AACjE,QAAM,mBAAmB,CAAC,GAAG,OAAO;AACpC,MAAI,WAAW,IAAI;AACjB,qBAAiB,KAAK,EAAE,SAAS,QAAQ,MAAM,SAAS,CAAC;AAAA,EAC3D;AACA,mBAAiB,KAAK,QAAQ;AAC9B,MAAI,SAAS,gBAAgB,GAAG;AAC9B,WAAO,6BAA6B,OAAO,UAAU,OAAO,gBAAgB;AAAA,EAC9E;AACA,MAAI,QAAQ,SAAS,GAAG;AACtB,UAAM,KAAK,OAAO;AAClB,UAAM,QAAQ,QAAQ;AAAA,EACxB,WAAW,SAAS,SAAS;AAC3B,UAAM,CAAC,MAAM,IAAI,IAAI,oBAAoB,UAAU,QAAQ;AAC3D,UAAM,KAAK,CAAC,IAAI,CAAC;AACjB,QAAI,KAAK,SAAS;AAChB,YAAM,QAAQ,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO,6BAA6B,OAAO,UAAU,KAAK;AAC5D;AACA,OAAO,8BAA8B,8BAA8B;AAGnE,SAAS,WAAW,KAAK,SAAS;AAChC,MAAI,SAAS;AACX,QAAI,KAAK,SAAS,OAAO;AAAA,EAC3B;AACF;AACA,OAAO,YAAY,YAAY;AAC/B,eAAe,YAAY,SAAS,MAAM,OAAO,SAAS,gBAAgB,OAAO;AAC/E,QAAM,KAAK,QAAQ,OAAO,eAAe;AACzC,KAAG,KAAK,SAAS,GAAG,KAAK,KAAK,IAAI;AAClC,KAAG,KAAK,UAAU,GAAG,KAAK,KAAK,IAAI;AACnC,QAAM,MAAM,GAAG,OAAO,WAAW;AACjC,MAAI,QAAQ,KAAK;AACjB,MAAI,KAAK,SAAS,SAAS,KAAK,KAAK,GAAG;AACtC,YAAQ,MAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,gBAAgB,IAAI,GAAG,WAAU,CAAC;AAAA,EAChG;AACA,QAAM,aAAa,KAAK,SAAS,cAAc;AAC/C,QAAM,OAAO,IAAI,OAAO,MAAM;AAC9B,OAAK,KAAK,KAAK;AACf,aAAW,MAAM,KAAK,UAAU;AAChC,OAAK,KAAK,SAAS,GAAG,UAAU,IAAI,OAAO,EAAE;AAC7C,aAAW,KAAK,KAAK,UAAU;AAC/B,MAAI,MAAM,WAAW,YAAY;AACjC,MAAI,MAAM,eAAe,QAAQ;AACjC,MAAI,MAAM,eAAe,KAAK;AAC9B,MAAI,MAAM,aAAa,QAAQ,IAAI;AACnC,MAAI,MAAM,cAAc,QAAQ;AAChC,MAAI,KAAK,SAAS,8BAA8B;AAChD,MAAI,eAAe;AACjB,QAAI,KAAK,SAAS,UAAU;AAAA,EAC9B;AACA,MAAI,OAAO,IAAI,KAAK,EAAE,sBAAsB;AAC5C,MAAI,KAAK,UAAU,OAAO;AACxB,QAAI,MAAM,WAAW,OAAO;AAC5B,QAAI,MAAM,eAAe,cAAc;AACvC,QAAI,MAAM,SAAS,QAAQ,IAAI;AAC/B,WAAO,IAAI,KAAK,EAAE,sBAAsB;AAAA,EAC1C;AACA,SAAO,GAAG,KAAK;AACjB;AACA,OAAO,aAAa,aAAa;AACjC,SAAS,YAAY,aAAa,WAAW,YAAY;AACvD,SAAO,YAAY,OAAO,OAAO,EAAE,KAAK,SAAS,kBAAkB,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,YAAY,aAAa,MAAM,IAAI,EAAE,KAAK,MAAM,aAAa,IAAI;AAC/J;AACA,OAAO,aAAa,aAAa;AACjC,SAAS,mBAAmB,YAAY,YAAY,MAAM;AACxD,QAAM,cAAc,WAAW,OAAO,MAAM;AAC5C,QAAM,WAAW,YAAY,aAAa,GAAG,UAAU;AACvD,6BAA2B,UAAU,IAAI;AACzC,QAAM,aAAa,SAAS,KAAK,EAAE,sBAAsB;AACzD,cAAY,OAAO;AACnB,SAAO;AACT;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,SAAS,uBAAuB,YAAY,YAAY,MAAM;AApO9D;AAqOE,QAAM,cAAc,WAAW,OAAO,MAAM;AAC5C,QAAM,WAAW,YAAY,aAAa,GAAG,UAAU;AACvD,6BAA2B,UAAU,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,CAAC;AACxE,QAAM,iBAAgB,cAAS,KAAK,MAAd,mBAAiB;AACvC,MAAI,eAAe;AACjB,gBAAY,OAAO;AAAA,EACrB;AACA,SAAO;AACT;AACA,OAAO,wBAAwB,wBAAwB;AACvD,SAAS,oBAAoB,OAAO,GAAG,gBAAgB,gBAAgB,OAAO;AAC5E,QAAM,aAAa;AACnB,QAAM,aAAa,EAAE,OAAO,GAAG;AAC/B,QAAM,MAAM,WAAW,OAAO,MAAM,EAAE,KAAK,SAAS,YAAY,EAAE,KAAK,SAAS,cAAc;AAC9F,QAAM,cAAc,WAAW,OAAO,MAAM,EAAE,KAAK,KAAK,OAAO;AAC/D,MAAI,YAAY;AAChB,aAAW,QAAQ,gBAAgB;AACjC,UAAM,aAA6B,OAAO,CAAC,UAAU,mBAAmB,YAAY,YAAY,KAAK,KAAK,OAAO,YAAY;AAC7H,UAAM,kBAAkB,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,oBAAoB,MAAM,UAAU;AACxF,eAAW,gBAAgB,iBAAiB;AAC1C,YAAM,QAAQ,YAAY,aAAa,WAAW,UAAU;AAC5D,iCAA2B,OAAO,YAAY;AAC9C;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe;AACjB,UAAM,OAAO,YAAY,KAAK,EAAE,QAAQ;AACxC,UAAM,UAAU;AAChB,QAAI,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,OAAO,EAAE,KAAK,SAAS,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,UAAU,KAAK,SAAS,IAAI,OAAO;AAC5I,WAAO,WAAW,KAAK;AAAA,EACzB,OAAO;AACL,WAAO,YAAY,KAAK;AAAA,EAC1B;AACF;AACA,OAAO,qBAAqB,qBAAqB;AACjD,SAAS,2BAA2B,OAAO,aAAa;AACtD,QAAM,KAAK,EAAE;AACb,cAAY,QAAQ,CAAC,MAAM,UAAU;AACnC,UAAM,aAAa,MAAM,OAAO,OAAO,EAAE,KAAK,cAAc,KAAK,SAAS,OAAO,WAAW,QAAQ,EAAE,KAAK,SAAS,kBAAkB,EAAE,KAAK,eAAe,KAAK,SAAS,WAAW,SAAS,QAAQ;AACtM,QAAI,UAAU,GAAG;AACf,iBAAW,KAAK,KAAK,OAAO;AAAA,IAC9B,OAAO;AACL,iBAAW,KAAK,MAAM,KAAK,OAAO;AAAA,IACpC;AAAA,EACF,CAAC;AACH;AACA,OAAO,4BAA4B,4BAA4B;AAC/D,SAAS,qBAAqB,MAAM;AAClC,SAAO,KAAK;AAAA,IACV;AAAA;AAAA,IAEA,CAAC,MAAM,aAAa,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,EACzC;AACF;AACA,OAAO,sBAAsB,sBAAsB;AACnD,IAAI,aAA6B,OAAO,OAAO,IAAI,OAAO,IAAI;AAAA,EAC5D,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,mBAAmB;AACrB,IAAI,CAAC,GAAG,WAAW;AACjB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,eAAe;AACjB,UAAM,WAAW,eAAe,MAAM,MAAM;AAC5C,UAAM,sBAAsB,qBAAqB,eAAe,QAAQ,CAAC;AACzE,UAAM,gBAAgB,KAAK,QAAQ,SAAS,IAAI;AAChD,UAAM,OAAO;AAAA,MACX;AAAA,MACA,OAAO,SAAS,IAAI,IAAI,gBAAgB;AAAA,MACxC,YAAY,MAAM,QAAQ,SAAS,QAAQ;AAAA,IAC7C;AACA,UAAM,aAAa,MAAM,YAAY,IAAI,MAAM,OAAO,SAAS,gBAAgB;AAC/E,WAAO;AAAA,EACT,OAAO;AACL,UAAM,aAAa,KAAK,QAAQ,eAAe,OAAO;AACtD,UAAM,iBAAiB,gBAAgB,WAAW,QAAQ,QAAQ,OAAO,GAAG,MAAM;AAClF,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,mBAAmB;AAAA,IAC5B;AACA,QAAI,QAAQ;AACV,UAAI,UAAU,KAAK,KAAK,GAAG;AACzB,gBAAQ,MAAM,QAAQ,WAAW,YAAY;AAAA,MAC/C;AACA,YAAM,qBAAqB,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,yBAAyB,EAAE,EAAE,QAAQ,iBAAiB,EAAE,EAAE,QAAQ,WAAW,OAAO;AAC5J,qBAAO,QAAQ,EAAE,KAAK,SAAS,kBAAkB;AAAA,IACnD,OAAO;AACL,YAAM,qBAAqB,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,yBAAyB,EAAE,EAAE,QAAQ,iBAAiB,EAAE,EAAE,QAAQ,gBAAgB,OAAO;AACjK,qBAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,mBAAmB,QAAQ,gBAAgB,OAAO,CAAC;AACjG,YAAM,qBAAqB,MAAM,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,yBAAyB,EAAE,EAAE,QAAQ,iBAAiB,EAAE,EAAE,QAAQ,WAAW,OAAO;AAC5J,qBAAO,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,kBAAkB;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AACF,GAAG,YAAY;", "names": ["import_dist", "i", "import_dist"]}