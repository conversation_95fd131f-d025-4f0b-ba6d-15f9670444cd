"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _nodePath = _interopRequireDefault(await jitiImport("node:path"));
var _nodeProcess = _interopRequireDefault(await jitiImport("node:process"));

var _pluginVue = _interopRequireDefault(await jitiImport("@vitejs/plugin-vue"));
var _rollupPluginVisualizer = await jitiImport("rollup-plugin-visualizer");
var _vite = _interopRequireDefault(await jitiImport("unocss/vite"));
var _vite2 = _interopRequireDefault(await jitiImport("unplugin-auto-import/vite"));
var _vite3 = _interopRequireDefault(await jitiImport("unplugin-vue-components/vite"));
var _vite4 = await jitiImport("vite");
var _vitePluginNodePolyfills = await jitiImport("vite-plugin-node-polyfills");
var _vitePluginRadar = await jitiImport("vite-plugin-radar");
var _vitePluginVueDevtools = _interopRequireDefault(await jitiImport("vite-plugin-vue-devtools"));function _interopRequireDefault(e) {return e && e.__esModule ? e : { default: e };}var _default = exports.default =

(0, _vite4.defineConfig)({
  base: _nodeProcess.default.env.SERVER_ENV === `NETLIFY` ? `/` : `/md/`,
  define: { process: _nodeProcess.default },
  envPrefix: [`VITE_`, `CF_`],
  plugins: [
  (0, _pluginVue.default)(),
  (0, _vite.default)(),
  (0, _vitePluginVueDevtools.default)(),
  (0, _vitePluginNodePolyfills.nodePolyfills)({
    include: [`path`, `util`, `timers`, `stream`, `fs`],
    overrides: {


      // Since `fs` is not supported in browsers, we can use the `memfs` package to polyfill it.
      // fs: 'memfs',
    } }), (0, _vitePluginRadar.VitePluginRadar)({
    analytics: { id: `G-7NZL3PZ0NK` }
  }),
  _nodeProcess.default.env.ANALYZE === `true` &&
  (0, _rollupPluginVisualizer.visualizer)({ emitFile: true, filename: `stats.html` }),
  (0, _vite2.default)({
    imports: [`vue`, `pinia`, `@vueuse/core`],
    dirs: [`./src/stores`, `./src/utils/toast`]
  }),
  (0, _vite3.default)({
    resolvers: []
  })],

  resolve: {
    alias: { '@': _nodePath.default.resolve(__dirname, `./src`) }
  },
  css: { devSourcemap: true },
  build: {
    rollupOptions: {
      output: {
        chunkFileNames: `static/js/md-[name]-[hash].js`,
        entryFileNames: `static/js/md-[name]-[hash].js`,
        assetFileNames: `static/[ext]/md-[name]-[hash].[ext]`,
        manualChunks(id) {
          if (id.includes(`node_modules`)) {
            if (id.includes(`katex`))
            return `katex`;
            if (id.includes(`mermaid`))
            return `mermaid`;
            if (id.includes(`cytoscape`))
            return `cytoscape`;
            if (id.includes(`highlight.js`))
            return `hljs`;
            const pkg = id.
            split(`node_modules/`)[1].
            split(`/`)[0].
            replace(`@`, `npm_`);
            return `vendor_${pkg}`;
          }
        }
      }
    }
  }
}); /* v9-772cf3f069ae68e0 */
