{"version": 3, "sources": ["../../@babel/runtime/helpers/interopRequireDefault.js", "../../object-assign/index.js", "../../tiny-oss/lib/utils/ajax.js", "../../crypt/crypt.js", "../../charenc/charenc.js", "../../is-buffer/index.js", "../../md5/md5.js", "../../tiny-oss/vendor/digest.js", "../../tiny-oss/lib/utils/index.js", "../../tiny-oss/lib/TinyOSS.js", "../../tiny-oss/lib/index.js"], "sourcesContent": ["function _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    \"default\": e\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "\"use strict\";\n\nexports.__esModule = true;\nexports[\"default\"] = ajax;\n\nfunction ajax(url, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  return new Promise(function (resolve, reject) {\n    var _options = options,\n        _options$async = _options.async,\n        async = _options$async === void 0 ? true : _options$async,\n        _options$data = _options.data,\n        data = _options$data === void 0 ? null : _options$data,\n        _options$headers = _options.headers,\n        headers = _options$headers === void 0 ? {} : _options$headers,\n        _options$method = _options.method,\n        method = _options$method === void 0 ? 'get' : _options$method,\n        _options$timeout = _options.timeout,\n        timeout = _options$timeout === void 0 ? 0 : _options$timeout,\n        onprogress = _options.onprogress;\n    var xhr = new XMLHttpRequest();\n    var timerId;\n\n    if (timeout) {\n      timerId = setTimeout(function () {\n        reject(new Error(\"the request timeout \" + timeout + \"ms\"));\n      }, timeout);\n    }\n\n    xhr.onerror = function () {\n      reject(new Error('unknown error'));\n    };\n\n    if (xhr.upload) {\n      // Note: the progress event must be located before the xhr.open method\n      xhr.upload.onprogress = onprogress;\n    }\n\n    xhr.onreadystatechange = function () {\n      if (xhr.readyState === 4) {\n        if (timeout) clearTimeout(timerId);\n\n        if (xhr.status >= 200 && xhr.status < 300) {\n          resolve(xhr.response, xhr);\n        } else {\n          var err = new Error('the request is error');\n          reject(err);\n        }\n      }\n    };\n\n    xhr.open(method, url, async);\n    Object.keys(headers).forEach(function (key) {\n      xhr.setRequestHeader(key, headers[key]);\n    });\n\n    try {\n      xhr.send(data);\n    } catch (err) {\n      reject(err);\n    }\n  });\n}", "(function() {\n  var base64map\n      = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n\n  crypt = {\n    // Bit-wise rotation left\n    rotl: function(n, b) {\n      return (n << b) | (n >>> (32 - b));\n    },\n\n    // Bit-wise rotation right\n    rotr: function(n, b) {\n      return (n << (32 - b)) | (n >>> b);\n    },\n\n    // Swap big-endian to little-endian and vice versa\n    endian: function(n) {\n      // If number given, swap endian\n      if (n.constructor == Number) {\n        return crypt.rotl(n, 8) & 0x00FF00FF | crypt.rotl(n, 24) & 0xFF00FF00;\n      }\n\n      // Else, assume array and swap all items\n      for (var i = 0; i < n.length; i++)\n        n[i] = crypt.endian(n[i]);\n      return n;\n    },\n\n    // Generate an array of any length of random bytes\n    randomBytes: function(n) {\n      for (var bytes = []; n > 0; n--)\n        bytes.push(Math.floor(Math.random() * 256));\n      return bytes;\n    },\n\n    // Convert a byte array to big-endian 32-bit words\n    bytesToWords: function(bytes) {\n      for (var words = [], i = 0, b = 0; i < bytes.length; i++, b += 8)\n        words[b >>> 5] |= bytes[i] << (24 - b % 32);\n      return words;\n    },\n\n    // Convert big-endian 32-bit words to a byte array\n    wordsToBytes: function(words) {\n      for (var bytes = [], b = 0; b < words.length * 32; b += 8)\n        bytes.push((words[b >>> 5] >>> (24 - b % 32)) & 0xFF);\n      return bytes;\n    },\n\n    // Convert a byte array to a hex string\n    bytesToHex: function(bytes) {\n      for (var hex = [], i = 0; i < bytes.length; i++) {\n        hex.push((bytes[i] >>> 4).toString(16));\n        hex.push((bytes[i] & 0xF).toString(16));\n      }\n      return hex.join('');\n    },\n\n    // Convert a hex string to a byte array\n    hexToBytes: function(hex) {\n      for (var bytes = [], c = 0; c < hex.length; c += 2)\n        bytes.push(parseInt(hex.substr(c, 2), 16));\n      return bytes;\n    },\n\n    // Convert a byte array to a base-64 string\n    bytesToBase64: function(bytes) {\n      for (var base64 = [], i = 0; i < bytes.length; i += 3) {\n        var triplet = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2];\n        for (var j = 0; j < 4; j++)\n          if (i * 8 + j * 6 <= bytes.length * 8)\n            base64.push(base64map.charAt((triplet >>> 6 * (3 - j)) & 0x3F));\n          else\n            base64.push('=');\n      }\n      return base64.join('');\n    },\n\n    // Convert a base-64 string to a byte array\n    base64ToBytes: function(base64) {\n      // Remove non-base-64 characters\n      base64 = base64.replace(/[^A-Z0-9+\\/]/ig, '');\n\n      for (var bytes = [], i = 0, imod4 = 0; i < base64.length;\n          imod4 = ++i % 4) {\n        if (imod4 == 0) continue;\n        bytes.push(((base64map.indexOf(base64.charAt(i - 1))\n            & (Math.pow(2, -2 * imod4 + 8) - 1)) << (imod4 * 2))\n            | (base64map.indexOf(base64.charAt(i)) >>> (6 - imod4 * 2)));\n      }\n      return bytes;\n    }\n  };\n\n  module.exports = crypt;\n})();\n", "var charenc = {\n  // UTF-8 encoding\n  utf8: {\n    // Convert a string to a byte array\n    stringToBytes: function(str) {\n      return charenc.bin.stringToBytes(unescape(encodeURIComponent(str)));\n    },\n\n    // Convert a byte array to a string\n    bytesToString: function(bytes) {\n      return decodeURIComponent(escape(charenc.bin.bytesToString(bytes)));\n    }\n  },\n\n  // Binary encoding\n  bin: {\n    // Convert a string to a byte array\n    stringToBytes: function(str) {\n      for (var bytes = [], i = 0; i < str.length; i++)\n        bytes.push(str.charCodeAt(i) & 0xFF);\n      return bytes;\n    },\n\n    // Convert a byte array to a string\n    bytesToString: function(bytes) {\n      for (var str = [], i = 0; i < bytes.length; i++)\n        str.push(String.fromCharCode(bytes[i]));\n      return str.join('');\n    }\n  }\n};\n\nmodule.exports = charenc;\n", "/*!\n * Determine if an object is a Buffer\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n\n// The _isBuffer check is for Safari 5-7 support, because it's missing\n// Object.prototype.constructor. Remove this eventually\nmodule.exports = function (obj) {\n  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)\n}\n\nfunction isBuffer (obj) {\n  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n\n// For Node v0.10 support. Remove this eventually.\nfunction isSlowBuffer (obj) {\n  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))\n}\n", "(function(){\r\n  var crypt = require('crypt'),\r\n      utf8 = require('charenc').utf8,\r\n      isBuffer = require('is-buffer'),\r\n      bin = require('charenc').bin,\r\n\r\n  // The core\r\n  md5 = function (message, options) {\r\n    // Convert to byte array\r\n    if (message.constructor == String)\r\n      if (options && options.encoding === 'binary')\r\n        message = bin.stringToBytes(message);\r\n      else\r\n        message = utf8.stringToBytes(message);\r\n    else if (isBuffer(message))\r\n      message = Array.prototype.slice.call(message, 0);\r\n    else if (!Array.isArray(message) && message.constructor !== Uint8Array)\r\n      message = message.toString();\r\n    // else, assume byte array already\r\n\r\n    var m = crypt.bytesToWords(message),\r\n        l = message.length * 8,\r\n        a =  1732584193,\r\n        b = -271733879,\r\n        c = -1732584194,\r\n        d =  271733878;\r\n\r\n    // Swap endian\r\n    for (var i = 0; i < m.length; i++) {\r\n      m[i] = ((m[i] <<  8) | (m[i] >>> 24)) & 0x00FF00FF |\r\n             ((m[i] << 24) | (m[i] >>>  8)) & 0xFF00FF00;\r\n    }\r\n\r\n    // Padding\r\n    m[l >>> 5] |= 0x80 << (l % 32);\r\n    m[(((l + 64) >>> 9) << 4) + 14] = l;\r\n\r\n    // Method shortcuts\r\n    var FF = md5._ff,\r\n        GG = md5._gg,\r\n        HH = md5._hh,\r\n        II = md5._ii;\r\n\r\n    for (var i = 0; i < m.length; i += 16) {\r\n\r\n      var aa = a,\r\n          bb = b,\r\n          cc = c,\r\n          dd = d;\r\n\r\n      a = FF(a, b, c, d, m[i+ 0],  7, -680876936);\r\n      d = FF(d, a, b, c, m[i+ 1], 12, -389564586);\r\n      c = FF(c, d, a, b, m[i+ 2], 17,  606105819);\r\n      b = FF(b, c, d, a, m[i+ 3], 22, -1044525330);\r\n      a = FF(a, b, c, d, m[i+ 4],  7, -176418897);\r\n      d = FF(d, a, b, c, m[i+ 5], 12,  1200080426);\r\n      c = FF(c, d, a, b, m[i+ 6], 17, -1473231341);\r\n      b = FF(b, c, d, a, m[i+ 7], 22, -45705983);\r\n      a = FF(a, b, c, d, m[i+ 8],  7,  1770035416);\r\n      d = FF(d, a, b, c, m[i+ 9], 12, -1958414417);\r\n      c = FF(c, d, a, b, m[i+10], 17, -42063);\r\n      b = FF(b, c, d, a, m[i+11], 22, -1990404162);\r\n      a = FF(a, b, c, d, m[i+12],  7,  1804603682);\r\n      d = FF(d, a, b, c, m[i+13], 12, -40341101);\r\n      c = FF(c, d, a, b, m[i+14], 17, -1502002290);\r\n      b = FF(b, c, d, a, m[i+15], 22,  1236535329);\r\n\r\n      a = GG(a, b, c, d, m[i+ 1],  5, -165796510);\r\n      d = GG(d, a, b, c, m[i+ 6],  9, -1069501632);\r\n      c = GG(c, d, a, b, m[i+11], 14,  643717713);\r\n      b = GG(b, c, d, a, m[i+ 0], 20, -373897302);\r\n      a = GG(a, b, c, d, m[i+ 5],  5, -701558691);\r\n      d = GG(d, a, b, c, m[i+10],  9,  38016083);\r\n      c = GG(c, d, a, b, m[i+15], 14, -660478335);\r\n      b = GG(b, c, d, a, m[i+ 4], 20, -405537848);\r\n      a = GG(a, b, c, d, m[i+ 9],  5,  568446438);\r\n      d = GG(d, a, b, c, m[i+14],  9, -1019803690);\r\n      c = GG(c, d, a, b, m[i+ 3], 14, -187363961);\r\n      b = GG(b, c, d, a, m[i+ 8], 20,  1163531501);\r\n      a = GG(a, b, c, d, m[i+13],  5, -1444681467);\r\n      d = GG(d, a, b, c, m[i+ 2],  9, -51403784);\r\n      c = GG(c, d, a, b, m[i+ 7], 14,  1735328473);\r\n      b = GG(b, c, d, a, m[i+12], 20, -1926607734);\r\n\r\n      a = HH(a, b, c, d, m[i+ 5],  4, -378558);\r\n      d = HH(d, a, b, c, m[i+ 8], 11, -2022574463);\r\n      c = HH(c, d, a, b, m[i+11], 16,  1839030562);\r\n      b = HH(b, c, d, a, m[i+14], 23, -35309556);\r\n      a = HH(a, b, c, d, m[i+ 1],  4, -1530992060);\r\n      d = HH(d, a, b, c, m[i+ 4], 11,  1272893353);\r\n      c = HH(c, d, a, b, m[i+ 7], 16, -155497632);\r\n      b = HH(b, c, d, a, m[i+10], 23, -1094730640);\r\n      a = HH(a, b, c, d, m[i+13],  4,  681279174);\r\n      d = HH(d, a, b, c, m[i+ 0], 11, -358537222);\r\n      c = HH(c, d, a, b, m[i+ 3], 16, -722521979);\r\n      b = HH(b, c, d, a, m[i+ 6], 23,  76029189);\r\n      a = HH(a, b, c, d, m[i+ 9],  4, -640364487);\r\n      d = HH(d, a, b, c, m[i+12], 11, -421815835);\r\n      c = HH(c, d, a, b, m[i+15], 16,  530742520);\r\n      b = HH(b, c, d, a, m[i+ 2], 23, -995338651);\r\n\r\n      a = II(a, b, c, d, m[i+ 0],  6, -198630844);\r\n      d = II(d, a, b, c, m[i+ 7], 10,  1126891415);\r\n      c = II(c, d, a, b, m[i+14], 15, -1416354905);\r\n      b = II(b, c, d, a, m[i+ 5], 21, -57434055);\r\n      a = II(a, b, c, d, m[i+12],  6,  1700485571);\r\n      d = II(d, a, b, c, m[i+ 3], 10, -1894986606);\r\n      c = II(c, d, a, b, m[i+10], 15, -1051523);\r\n      b = II(b, c, d, a, m[i+ 1], 21, -2054922799);\r\n      a = II(a, b, c, d, m[i+ 8],  6,  1873313359);\r\n      d = II(d, a, b, c, m[i+15], 10, -30611744);\r\n      c = II(c, d, a, b, m[i+ 6], 15, -1560198380);\r\n      b = II(b, c, d, a, m[i+13], 21,  1309151649);\r\n      a = II(a, b, c, d, m[i+ 4],  6, -145523070);\r\n      d = II(d, a, b, c, m[i+11], 10, -1120210379);\r\n      c = II(c, d, a, b, m[i+ 2], 15,  718787259);\r\n      b = II(b, c, d, a, m[i+ 9], 21, -343485551);\r\n\r\n      a = (a + aa) >>> 0;\r\n      b = (b + bb) >>> 0;\r\n      c = (c + cc) >>> 0;\r\n      d = (d + dd) >>> 0;\r\n    }\r\n\r\n    return crypt.endian([a, b, c, d]);\r\n  };\r\n\r\n  // Auxiliary functions\r\n  md5._ff  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (b & c | ~b & d) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n  md5._gg  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (b & d | c & ~d) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n  md5._hh  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (b ^ c ^ d) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n  md5._ii  = function (a, b, c, d, x, s, t) {\r\n    var n = a + (c ^ (b | ~d)) + (x >>> 0) + t;\r\n    return ((n << s) | (n >>> (32 - s))) + b;\r\n  };\r\n\r\n  // Package private blocksize\r\n  md5._blocksize = 16;\r\n  md5._digestsize = 16;\r\n\r\n  module.exports = function (message, options) {\r\n    if (message === undefined || message === null)\r\n      throw new Error('Illegal argument ' + message);\r\n\r\n    var digestbytes = crypt.wordsToBytes(md5(message, options));\r\n    return options && options.asBytes ? digestbytes :\r\n        options && options.asString ? bin.bytesToString(digestbytes) :\r\n        crypt.bytesToHex(digestbytes);\r\n  };\r\n\r\n})();\r\n", "/*! ***** BEGIN LICENSE BLOCK *****\n *!\n *! Copyright 2011-2012, 2014 <PERSON><PERSON><PERSON> <<EMAIL>>\n *!\n *! This file is part of digest.js\n *!\n *! digest.js is free software: you can redistribute it and/or modify it under\n *! the terms of the GNU General Public License as published by the Free Software\n *! Foundation, either version 3 of the License, or (at your option) any later\n *! version.\n *!\n *! digest.js is distributed in the hope that it will be useful, but\n *! WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY\n *! or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for\n *! more details.\n *!\n *! You should have received a copy of the GNU General Public License along with\n *! digest.js. If not, see http://www.gnu.org/licenses/.\n *!\n *! ***** END LICENSE BLOCK *****  */\n\n/*global ArrayBuffer: true, Uint8Array: true, Uint32Array:true */\n/*jslint browser: true, bitwise: true, plusplus: true, vars: true, indent: 4, maxerr: 50 */\n\n\n(function () {\n    \"use strict\";\n    if (!ArrayBuffer.prototype.slice) {\n        ArrayBuffer.prototype.slice = function (start, end) {\n            var i;\n            var that = new Uint8Array(this);\n            if (end === undefined) {\n                end = that.length;\n            }\n            var result = new ArrayBuffer(end - start);\n            var resultArray = new Uint8Array(result);\n            for (i = 0; i < resultArray.length; i++) {\n                resultArray[i] = that[i + start];\n            }\n            return result;\n        };\n    }\n}());\n\n\n;(function (global) {\n    \"use strict\";\n\n    /* SHA-1 */\n\n    function sha1Engine() {}\n\n    sha1Engine.prototype.processBlock = function (input) {\n\n        var A = this.current[0];\n        var B = this.current[1];\n        var C = this.current[2];\n        var D = this.current[3];\n        var E = this.current[4];\n\n        var W = [\n            input[0] << 24 | input[1] << 16 | input[2] << 8 | input[3],\n            input[4] << 24 | input[5] << 16 | input[6] << 8 | input[7],\n            input[8] << 24 | input[9] << 16 | input[10] << 8 | input[11],\n            input[12] << 24 | input[13] << 16 | input[14] << 8 | input[15],\n            input[16] << 24 | input[17] << 16 | input[18] << 8 | input[19],\n            input[20] << 24 | input[21] << 16 | input[22] << 8 | input[23],\n            input[24] << 24 | input[25] << 16 | input[26] << 8 | input[27],\n            input[28] << 24 | input[29] << 16 | input[30] << 8 | input[31],\n            input[32] << 24 | input[33] << 16 | input[34] << 8 | input[35],\n            input[36] << 24 | input[37] << 16 | input[38] << 8 | input[39],\n            input[40] << 24 | input[41] << 16 | input[42] << 8 | input[43],\n            input[44] << 24 | input[45] << 16 | input[46] << 8 | input[47],\n            input[48] << 24 | input[49] << 16 | input[50] << 8 | input[51],\n            input[52] << 24 | input[53] << 16 | input[54] << 8 | input[55],\n            input[56] << 24 | input[57] << 16 | input[58] << 8 | input[59],\n            input[60] << 24 | input[61] << 16 | input[62] << 8 | input[63]\n        ];\n        var T;\n        var i;\n\n        for (i = 16; i < 80; i++) {\n            W.push((((W[i-3] ^ W[i-8] ^ W[i-14] ^ W[i-16]) << 1) | ((W[i-3] ^ W[i-8] ^ W[i-14] ^ W[i-16]) >>> 31)));\n        }\n\n        for (i = 0; i < 80; i++) {\n            T = ((A << 5) | (A >>> 27)) + E + W[i];\n            if (i < 20) {\n                T += ((B & C) | (~B & D)) + 0x5A827999 | 0;\n            } else if (i < 40) {\n                T += (B ^ C ^ D) + 0x6ED9EBA1 | 0;\n            } else if (i < 60) {\n                T += ((B & C) | (B & D) | (C & D)) + 0x8F1BBCDC| 0;\n            } else {\n                T += (B ^ C ^ D) + 0xCA62C1D6 | 0;\n            }\n            E = D;\n            D = C;\n            C = ((B << 30) | (B >>> 2));\n            B = A;\n            A = T;\n        }\n\n        this.current[0] += A;\n        this.current[1] += B;\n        this.current[2] += C;\n        this.current[3] += D;\n        this.current[4] += E;\n        this.currentLen += 64;\n    };\n\n    sha1Engine.prototype.doPadding = function () {\n        var datalen = (this.inLen + this.currentLen) * 8;\n        var msw = 0; // FIXME\n        var lsw = datalen & 0xFFFFFFFF;\n        var zeros = this.inLen <= 55 ? 55 - this.inLen : 119 - this.inLen;\n        var pad = new Uint8Array(new ArrayBuffer(zeros + 1 + 8));\n        pad[0] = 0x80;\n        pad[pad.length - 1] = lsw & 0xFF;\n        pad[pad.length - 2] = (lsw >>> 8) & 0xFF;\n        pad[pad.length - 3] = (lsw >>> 16) & 0xFF;\n        pad[pad.length - 4] = (lsw >>> 24) & 0xFF;\n        pad[pad.length - 5] = msw & 0xFF;\n        pad[pad.length - 6] = (msw >>> 8) & 0xFF;\n        pad[pad.length - 7] = (msw >>> 16) & 0xFF;\n        pad[pad.length - 8] = (msw >>> 24) & 0xFF;\n        return pad;\n    };\n\n    sha1Engine.prototype.getDigest = function () {\n        var rv = new Uint8Array(new ArrayBuffer(20));\n        rv[3] = this.current[0] & 0xFF;\n        rv[2] = (this.current[0] >>> 8) & 0xFF;\n        rv[1] = (this.current[0] >>> 16) & 0xFF;\n        rv[0] = (this.current[0] >>> 24) & 0xFF;\n        rv[7] = this.current[1] & 0xFF;\n        rv[6] = (this.current[1] >>> 8) & 0xFF;\n        rv[5] = (this.current[1] >>> 16) & 0xFF;\n        rv[4] = (this.current[1] >>> 24) & 0xFF;\n        rv[11] = this.current[2] & 0xFF;\n        rv[10] = (this.current[2] >>> 8) & 0xFF;\n        rv[9] = (this.current[2] >>> 16) & 0xFF;\n        rv[8] = (this.current[2] >>> 24) & 0xFF;\n        rv[15] = this.current[3] & 0xFF;\n        rv[14] = (this.current[3] >>> 8) & 0xFF;\n        rv[13] = (this.current[3] >>> 16) & 0xFF;\n        rv[12] = (this.current[3] >>> 24) & 0xFF;\n        rv[19] = this.current[4] & 0xFF;\n        rv[18] = (this.current[4] >>> 8) & 0xFF;\n        rv[17] = (this.current[4] >>> 16) & 0xFF;\n        rv[16] = (this.current[4] >>> 24) & 0xFF;\n        return rv.buffer;\n    };\n\n    sha1Engine.prototype.reset = function () {\n        this.currentLen = 0;\n        this.inLen = 0;\n        this.current = new Uint32Array(new ArrayBuffer(20));\n        this.current[0] = 0x67452301;\n        this.current[1] = 0xEFCDAB89;\n        this.current[2] = 0x98BADCFE;\n        this.current[3] = 0x10325476;\n        this.current[4] = 0xC3D2E1F0;\n    };\n\n    sha1Engine.prototype.blockLen = 64;\n    sha1Engine.prototype.digestLen = 20;\n\n    /* Input utility functions */\n\n    var fromASCII = function (s) {\n        var buffer = new ArrayBuffer(s.length);\n        var b = new Uint8Array(buffer);\n        var i;\n        for (i = 0; i < s.length; i++) {\n            b[i] = s.charCodeAt(i);\n        }\n        return b;\n    };\n\n    var fromInteger = function (v) {\n        var buffer = new ArrayBuffer(1);\n        var b = new Uint8Array(buffer);\n        b[0] = v;\n        return b;\n    };\n\n    var convertToUint8Array = function (input) {\n        if (input.constructor === Uint8Array) {\n            return input;\n        } else if (input.constructor === ArrayBuffer) {\n            return new Uint8Array(input);\n        } else if (input.constructor === String) {\n            return fromASCII(input);\n        } else if (input.constructor === Number) {\n            if (input > 0xFF) {\n                throw \"For more than one byte, use an array buffer\";\n            } else if (input < 0) {\n                throw \"Input value must be positive\";\n            }\n            return fromInteger(input);\n        } else {\n            throw \"Unsupported type\";\n        }\n    };\n\n    var convertToUInt32 = function (i) {\n        var tmp = new Uint8Array(new ArrayBuffer(4));\n        tmp[0] = (i & 0xFF000000) >> 24;\n        tmp[1] = (i & 0x00FF0000) >> 16;\n        tmp[2] = (i & 0x0000FF00) >> 8;\n        tmp[3] = (i & 0x000000FF);\n        return tmp;\n    };\n\n    /* Digest implementation */\n    var dg = function (Constructor) {\n        var update = function (input) {\n            var len = input.length;\n            var offset = 0;\n            while (len > 0) {\n                var copyLen = this.blockLen - this.inLen;\n                if (copyLen > len) {\n                    copyLen = len;\n                }\n                var tmpInput = input.subarray(offset, offset + copyLen);\n                this.inbuf.set(tmpInput, this.inLen);\n                offset += copyLen;\n                len -= copyLen;\n                this.inLen += copyLen;\n                if (this.inLen === this.blockLen) {\n\t\t    //var t0 = performance.now();\n                    this.processBlock(this.inbuf);\n\t\t    //var t1 = performance.now();\n\t\t    //console.log(t1 - t0);\n                    this.inLen = 0;\n                }\n            }\n        };\n\n        var finalize = function () {\n            var padding = this.doPadding();\n            this.update(padding);\n            var result = this.getDigest();\n            this.reset();\n            return result;\n        };\n\n        var engine = (function () {\n            if (!Constructor) {\n                throw \"Unsupported algorithm: \" + Constructor.toString();\n            }\n            Constructor.prototype.update = update;\n            Constructor.prototype.finalize = finalize;\n            var engine = new Constructor();\n            engine.inbuf = new Uint8Array(new ArrayBuffer(engine.blockLen));\n            engine.reset();\n            return engine;\n        }());\n\n        return {\n            update: function (input) {\n                engine.update(convertToUint8Array(input));\n            },\n\n            finalize: function () {\n                return engine.finalize();\n            },\n\n            digest: function (input) {\n                engine.update(convertToUint8Array(input));\n                return engine.finalize();\n            },\n\n            reset: function () {\n                engine.reset();\n            },\n\n            digestLength: function () {\n                return engine.digestLen;\n            }\n        };\n    };\n\n    /* HMAC implementation */\n    var hmac = function (digest) {\n        var initialized = false;\n        var key, ipad, opad;\n        var init = function () {\n            var i, kbuf;\n            if (initialized) {\n                return;\n            }\n            if (key === undefined) {\n                throw \"MAC key is not defined\";\n            }\n            if (key.byteLength > 64) { /* B = 64 */\n                kbuf = new Uint8Array(digest.digest(key));\n            } else {\n                kbuf = new Uint8Array(key);\n            }\n            ipad = new Uint8Array(new ArrayBuffer(64));\n            for (i = 0; i < kbuf.length; i++) {\n                ipad[i] = 0x36 ^ kbuf[i];\n            }\n            for (i = kbuf.length; i < 64; i++) {\n                ipad[i] = 0x36;\n            }\n            opad = new Uint8Array(new ArrayBuffer(64));\n            for (i = 0; i < kbuf.length; i++) {\n                opad[i] = 0x5c ^ kbuf[i];\n            }\n            for (i = kbuf.length; i < 64; i++) {\n                opad[i] = 0x5c;\n            }\n            initialized = true;\n            digest.update(ipad.buffer);\n        };\n\n        var resetMac = function () {\n            initialized = false;\n            key = undefined;\n            ipad = undefined;\n            opad = undefined;\n            digest.reset();\n        };\n\n        var finalizeMac = function () {\n            var result = digest.finalize();\n            digest.reset();\n            digest.update(opad.buffer);\n            digest.update(result);\n            result = digest.finalize();\n            resetMac();\n            return result;\n        };\n\n        var setKeyMac = function (k) {\n            key = k;\n        };\n\n        return {\n            setKey: function (key) {\n                setKeyMac(convertToUint8Array(key));\n                init();\n            },\n\n            update: function (input) {\n                digest.update(input);\n            },\n\n            finalize: function () {\n                return finalizeMac();\n            },\n\n            mac: function (input) {\n                this.update(input);\n                return this.finalize();\n            },\n\n            reset: function () {\n                resetMac();\n            },\n\n            hmacLength: function () {\n                return digest.digestLength();\n            }\n        };\n    };\n\n    var Digest = {\n        SHA1: function () {\n            return dg(sha1Engine);\n        },\n\n        HMAC_SHA1: function () {\n            return hmac(dg(sha1Engine));\n        }\n    };\n\n    if (\"undefined\" !== typeof exports) { /* Node Support */\n        if ((\"undefined\" !== typeof module) && module.exports) {\n          module.exports = exports = Digest;\n        } else {\n            exports = Digest;\n        }\n    } else { /* Browsers and Web Workers*/\n        global.Digest = Digest;\n    }\n}(this));\n", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nexports.__esModule = true;\nexports.unix = unix;\nexports.blobToBuffer = blobToBuffer;\nexports.assertOptions = assertOptions;\nexports.getContentMd5 = getContentMd5;\nexports.getCanonicalizedOSSHeaders = getCanonicalizedOSSHeaders;\nexports.getCanonicalizedResource = getCanonicalizedResource;\nexports.getSignature = getSignature;\n\nvar _md = _interopRequireDefault(require(\"md5\"));\n\nvar _base64Js = _interopRequireDefault(require(\"base64-js\"));\n\nvar _digest = _interopRequireDefault(require(\"../../vendor/digest\"));\n\nfunction isDate(obj) {\n  return obj && Object.prototype.toString.call(obj) === '[object Date]' && obj.toString !== 'Invalid Date';\n}\n\nfunction unix(date) {\n  var d;\n\n  if (date) {\n    d = new Date(date);\n  }\n\n  if (!isDate(d)) {\n    d = new Date();\n  }\n\n  return Math.round(d.getTime() / 1000);\n}\n\nfunction blobToBuffer(blob) {\n  return new Promise(function (resolve, reject) {\n    var fr = new FileReader();\n\n    fr.onload = function () {\n      var result = new Uint8Array(fr.result);\n      resolve(result);\n    };\n\n    fr.onerror = function () {\n      reject(fr.error);\n    };\n\n    fr.readAsArrayBuffer(blob);\n  });\n}\n\nfunction assertOptions(options) {\n  var accessKeyId = options.accessKeyId,\n      accessKeySecret = options.accessKeySecret,\n      bucket = options.bucket,\n      endpoint = options.endpoint;\n\n  if (!accessKeyId) {\n    throw new Error('need accessKeyId');\n  }\n\n  if (!accessKeySecret) {\n    throw new Error('need accessKeySecret');\n  }\n\n  if (!bucket && !endpoint) {\n    throw new Error('need bucket or endpoint');\n  }\n}\n\nfunction hexToBuffer(hex) {\n  var arr = [];\n\n  for (var i = 0; i < hex.length; i += 2) {\n    arr.push(parseInt(hex[i] + hex[i + 1], 16));\n  }\n\n  return Uint8Array.from(arr);\n}\n\nfunction getContentMd5(buf) {\n  // md5 doesn't work for Uint8Array\n  var bytes = Array.prototype.slice.call(buf, 0);\n  var md5Buf = hexToBuffer((0, _md[\"default\"])(bytes));\n  return _base64Js[\"default\"].fromByteArray(md5Buf);\n}\n\nfunction getCanonicalizedOSSHeaders(headers) {\n  var result = '';\n  var headerNames = Object.keys(headers);\n  headerNames = headerNames.map(function (name) {\n    return name.toLowerCase();\n  });\n  headerNames.sort();\n  headerNames.forEach(function (name) {\n    if (name.indexOf('x-oss-') === 0) {\n      result += name + \":\" + headers[name] + \"\\n\";\n    }\n  });\n  return result;\n}\n\nfunction getCanonicalizedResource(bucket, objectName, parameters) {\n  if (bucket === void 0) {\n    bucket = '';\n  }\n\n  if (objectName === void 0) {\n    objectName = '';\n  }\n\n  var resourcePath = '';\n\n  if (bucket) {\n    resourcePath += \"/\" + bucket;\n  }\n\n  if (objectName) {\n    if (objectName.charAt(0) !== '/') {\n      resourcePath += '/';\n    }\n\n    resourcePath += objectName;\n  }\n\n  var canonicalizedResource = \"\" + resourcePath;\n  var separatorString = '?';\n\n  if (parameters) {\n    var compareFunc = function compareFunc(entry1, entry2) {\n      if (entry1[0] > entry2[0]) {\n        return 1;\n      }\n\n      if (entry1[0] < entry2[0]) {\n        return -1;\n      }\n\n      return 0;\n    };\n\n    var processFunc = function processFunc(key) {\n      canonicalizedResource += separatorString + key;\n\n      if (parameters[key]) {\n        canonicalizedResource += \"=\" + parameters[key];\n      }\n\n      separatorString = '&';\n    };\n\n    Object.keys(parameters).sort(compareFunc).forEach(processFunc);\n  }\n\n  return canonicalizedResource;\n}\n\nfunction getSignature(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$type = _options.type,\n      type = _options$type === void 0 ? 'header' : _options$type,\n      _options$verb = _options.verb,\n      verb = _options$verb === void 0 ? '' : _options$verb,\n      _options$contentMd = _options.contentMd5,\n      contentMd5 = _options$contentMd === void 0 ? '' : _options$contentMd,\n      _options$expires = _options.expires,\n      expires = _options$expires === void 0 ? unix() + 3600 : _options$expires,\n      bucket = _options.bucket,\n      objectName = _options.objectName,\n      accessKeySecret = _options.accessKeySecret,\n      _options$headers = _options.headers,\n      headers = _options$headers === void 0 ? {} : _options$headers,\n      subResource = _options.subResource;\n  var date = headers['x-oss-date'] || '';\n  var contentType = headers['Content-Type'] || '';\n  var data = [verb, contentMd5, contentType];\n\n  if (type === 'header') {\n    data.push(date);\n  } else {\n    data.push(expires);\n  }\n\n  var canonicalizedOSSHeaders = getCanonicalizedOSSHeaders(headers);\n  var canonicalizedResource = getCanonicalizedResource(bucket, objectName, subResource);\n  data.push(\"\" + canonicalizedOSSHeaders + canonicalizedResource);\n  var text = data.join('\\n');\n  var hmac = new _digest[\"default\"].HMAC_SHA1();\n  hmac.setKey(accessKeySecret);\n  hmac.update(text);\n  var hashBuf = new Uint8Array(hmac.finalize());\n\n  var signature = _base64Js[\"default\"].fromByteArray(hashBuf);\n\n  return signature;\n}", "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\n\nexports.__esModule = true;\nexports[\"default\"] = void 0;\n\nvar _objectAssign = _interopRequireDefault(require(\"object-assign\"));\n\nvar _ajax = _interopRequireDefault(require(\"./utils/ajax\"));\n\nvar _utils = require(\"./utils\");\n\nvar TinyOSS =\n/*#__PURE__*/\nfunction () {\n  function TinyOSS(options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    (0, _utils.assertOptions)(options);\n    this.opts = (0, _objectAssign[\"default\"])({\n      region: 'oss-cn-hangzhou',\n      internal: false,\n      cname: false,\n      secure: false,\n      timeout: 60000\n    }, options);\n    var _this$opts = this.opts,\n        bucket = _this$opts.bucket,\n        region = _this$opts.region,\n        endpoint = _this$opts.endpoint,\n        internal = _this$opts.internal;\n    this.host = '';\n\n    if (endpoint) {\n      this.host = endpoint;\n    } else {\n      var host = bucket;\n\n      if (internal) {\n        host += '-internal';\n      }\n\n      host += \".\" + region + \".aliyuncs.com\";\n      this.host = host;\n    }\n  }\n\n  var _proto = TinyOSS.prototype;\n\n  _proto.put = function put(objectName, blob, options) {\n    var _this = this;\n\n    if (options === void 0) {\n      options = {};\n    }\n\n    return new Promise(function (resolve, reject) {\n      (0, _utils.blobToBuffer)(blob).then(function (buf) {\n        var _this$opts2 = _this.opts,\n            accessKeyId = _this$opts2.accessKeyId,\n            accessKeySecret = _this$opts2.accessKeySecret,\n            stsToken = _this$opts2.stsToken,\n            bucket = _this$opts2.bucket;\n        var verb = 'PUT';\n        var contentMd5 = (0, _utils.getContentMd5)(buf);\n        var contentType = blob.type;\n        var headers = {\n          'Content-Md5': contentMd5,\n          'Content-Type': contentType,\n          'x-oss-date': new Date().toGMTString()\n        };\n\n        if (stsToken) {\n          headers['x-oss-security-token'] = stsToken;\n        }\n\n        var signature = (0, _utils.getSignature)({\n          verb: verb,\n          contentMd5: contentMd5,\n          headers: headers,\n          bucket: bucket,\n          objectName: objectName,\n          accessKeyId: accessKeyId,\n          accessKeySecret: accessKeySecret\n        });\n        headers.Authorization = \"OSS \" + accessKeyId + \":\" + signature;\n        var protocol = _this.opts.secure ? 'https' : 'http';\n        var url = protocol + \"://\" + _this.host + \"/\" + objectName;\n        return (0, _ajax[\"default\"])(url, {\n          method: verb,\n          headers: headers,\n          data: blob,\n          timeout: _this.opts.timeout,\n          onprogress: options.onprogress\n        });\n      }).then(resolve)[\"catch\"](reject);\n    });\n  } // https://help.aliyun.com/document_detail/45126.html\n  ;\n\n  _proto.putSymlink = function putSymlink(objectName, targetObjectName) {\n    var _this$opts3 = this.opts,\n        accessKeyId = _this$opts3.accessKeyId,\n        accessKeySecret = _this$opts3.accessKeySecret,\n        stsToken = _this$opts3.stsToken,\n        bucket = _this$opts3.bucket;\n    var verb = 'PUT';\n    var headers = {\n      'x-oss-date': new Date().toGMTString(),\n      'x-oss-symlink-target': encodeURI(targetObjectName)\n    };\n\n    if (stsToken) {\n      headers['x-oss-security-token'] = stsToken;\n    }\n\n    var signature = (0, _utils.getSignature)({\n      verb: verb,\n      headers: headers,\n      bucket: bucket,\n      objectName: objectName,\n      accessKeyId: accessKeyId,\n      accessKeySecret: accessKeySecret,\n      subResource: {\n        symlink: ''\n      }\n    });\n    headers.Authorization = \"OSS \" + accessKeyId + \":\" + signature;\n    var protocol = this.opts.secure ? 'https' : 'http';\n    var url = protocol + \"://\" + this.host + \"/\" + objectName + \"?symlink\";\n    return (0, _ajax[\"default\"])(url, {\n      method: verb,\n      headers: headers,\n      timeout: this.opts.timeout\n    });\n  };\n\n  _proto.signatureUrl = function signatureUrl(objectName, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    var _options = options,\n        _options$expires = _options.expires,\n        expires = _options$expires === void 0 ? 1800 : _options$expires,\n        method = _options.method,\n        process = _options.process,\n        response = _options.response;\n    var _this$opts4 = this.opts,\n        accessKeyId = _this$opts4.accessKeyId,\n        accessKeySecret = _this$opts4.accessKeySecret,\n        stsToken = _this$opts4.stsToken,\n        bucket = _this$opts4.bucket;\n    var headers = {};\n    var subResource = {};\n\n    if (process) {\n      var processKeyword = 'x-oss-process';\n      subResource[processKeyword] = process;\n    }\n\n    if (response) {\n      Object.keys(response).forEach(function (k) {\n        var key = \"response-\" + k.toLowerCase();\n        subResource[key] = response[k];\n      });\n    }\n\n    Object.keys(options).forEach(function (key) {\n      var lowerKey = key.toLowerCase();\n      var value = options[key];\n\n      if (lowerKey.indexOf('x-oss-') === 0) {\n        headers[lowerKey] = value;\n      } else if (lowerKey.indexOf('content-md5') === 0) {\n        headers[key] = value;\n      } else if (lowerKey.indexOf('content-type') === 0) {\n        headers[key] = value;\n      } else if (lowerKey !== 'expires' && lowerKey !== 'response' && lowerKey !== 'process' && lowerKey !== 'method') {\n        subResource[lowerKey] = value;\n      }\n    });\n    var securityToken = options['security-token'] || stsToken;\n\n    if (securityToken) {\n      subResource['security-token'] = securityToken;\n    }\n\n    var expireUnix = (0, _utils.unix)() + expires;\n    var signature = (0, _utils.getSignature)({\n      type: 'url',\n      verb: method || 'GET',\n      accessKeyId: accessKeyId,\n      accessKeySecret: accessKeySecret,\n      bucket: bucket,\n      objectName: objectName,\n      headers: headers,\n      subResource: subResource,\n      expires: expireUnix\n    });\n    var protocol = this.opts.secure ? 'https' : 'http';\n    var url = protocol + \"://\" + this.host + \"/\" + objectName;\n    url += \"?OSSAccessKeyId=\" + accessKeyId;\n    url += \"&Expires=\" + expireUnix;\n    url += \"&Signature=\" + encodeURIComponent(signature);\n    Object.keys(subResource).forEach(function (k) {\n      url += \"&\" + k + \"=\" + encodeURIComponent(subResource[k]);\n    });\n    return url;\n  };\n\n  return TinyOSS;\n}();\n\nexports[\"default\"] = TinyOSS;", "\"use strict\";\n\nmodule.exports = require('./TinyOSS')[\"default\"];"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AAAA,aAAS,uBAAuB,GAAG;AACjC,aAAO,KAAK,EAAE,aAAa,IAAI;AAAA,QAC7B,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,UAAU,wBAAwB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAS,IAAI,OAAO;AAAA;AAAA;;;ACL9G;AAAA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAEA,YAAQ,aAAa;AACrB,YAAQ,SAAS,IAAI;AAErB,aAAS,KAAK,KAAK,SAAS;AAC1B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AAEA,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,WAAW,SACX,iBAAiB,SAAS,OAC1B,QAAQ,mBAAmB,SAAS,OAAO,gBAC3C,gBAAgB,SAAS,MACzB,OAAO,kBAAkB,SAAS,OAAO,eACzC,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,CAAC,IAAI,kBAC7C,kBAAkB,SAAS,QAC3B,SAAS,oBAAoB,SAAS,QAAQ,iBAC9C,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,IAAI,kBAC5C,aAAa,SAAS;AAC1B,YAAI,MAAM,IAAI,eAAe;AAC7B,YAAI;AAEJ,YAAI,SAAS;AACX,oBAAU,WAAW,WAAY;AAC/B,mBAAO,IAAI,MAAM,yBAAyB,UAAU,IAAI,CAAC;AAAA,UAC3D,GAAG,OAAO;AAAA,QACZ;AAEA,YAAI,UAAU,WAAY;AACxB,iBAAO,IAAI,MAAM,eAAe,CAAC;AAAA,QACnC;AAEA,YAAI,IAAI,QAAQ;AAEd,cAAI,OAAO,aAAa;AAAA,QAC1B;AAEA,YAAI,qBAAqB,WAAY;AACnC,cAAI,IAAI,eAAe,GAAG;AACxB,gBAAI,QAAS,cAAa,OAAO;AAEjC,gBAAI,IAAI,UAAU,OAAO,IAAI,SAAS,KAAK;AACzC,sBAAQ,IAAI,UAAU,GAAG;AAAA,YAC3B,OAAO;AACL,kBAAI,MAAM,IAAI,MAAM,sBAAsB;AAC1C,qBAAO,GAAG;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAEA,YAAI,KAAK,QAAQ,KAAK,KAAK;AAC3B,eAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,cAAI,iBAAiB,KAAK,QAAQ,GAAG,CAAC;AAAA,QACxC,CAAC;AAED,YAAI;AACF,cAAI,KAAK,IAAI;AAAA,QACf,SAAS,KAAK;AACZ,iBAAO,GAAG;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;ACjEA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAAA,KAAC,WAAW;AACV,UAAI,YACE,oEAEN,QAAQ;AAAA;AAAA,QAEN,MAAM,SAAS,GAAG,GAAG;AACnB,iBAAQ,KAAK,IAAM,MAAO,KAAK;AAAA,QACjC;AAAA;AAAA,QAGA,MAAM,SAAS,GAAG,GAAG;AACnB,iBAAQ,KAAM,KAAK,IAAO,MAAM;AAAA,QAClC;AAAA;AAAA,QAGA,QAAQ,SAAS,GAAG;AAElB,cAAI,EAAE,eAAe,QAAQ;AAC3B,mBAAO,MAAM,KAAK,GAAG,CAAC,IAAI,WAAa,MAAM,KAAK,GAAG,EAAE,IAAI;AAAA,UAC7D;AAGA,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,cAAE,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,CAAC;AAC1B,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,aAAa,SAAS,GAAG;AACvB,mBAAS,QAAQ,CAAC,GAAG,IAAI,GAAG;AAC1B,kBAAM,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,CAAC;AAC5C,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,cAAc,SAAS,OAAO;AAC5B,mBAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,KAAK;AAC7D,kBAAM,MAAM,CAAC,KAAK,MAAM,CAAC,KAAM,KAAK,IAAI;AAC1C,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,cAAc,SAAS,OAAO;AAC5B,mBAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,SAAS,IAAI,KAAK;AACtD,kBAAM,KAAM,MAAM,MAAM,CAAC,MAAO,KAAK,IAAI,KAAO,GAAI;AACtD,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,YAAY,SAAS,OAAO;AAC1B,mBAAS,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/C,gBAAI,MAAM,MAAM,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;AACtC,gBAAI,MAAM,MAAM,CAAC,IAAI,IAAK,SAAS,EAAE,CAAC;AAAA,UACxC;AACA,iBAAO,IAAI,KAAK,EAAE;AAAA,QACpB;AAAA;AAAA,QAGA,YAAY,SAAS,KAAK;AACxB,mBAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/C,kBAAM,KAAK,SAAS,IAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;AAC3C,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,eAAe,SAAS,OAAO;AAC7B,mBAAS,SAAS,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACrD,gBAAI,UAAW,MAAM,CAAC,KAAK,KAAO,MAAM,IAAI,CAAC,KAAK,IAAK,MAAM,IAAI,CAAC;AAClE,qBAAS,IAAI,GAAG,IAAI,GAAG;AACrB,kBAAI,IAAI,IAAI,IAAI,KAAK,MAAM,SAAS;AAClC,uBAAO,KAAK,UAAU,OAAQ,YAAY,KAAK,IAAI,KAAM,EAAI,CAAC;AAAA;AAE9D,uBAAO,KAAK,GAAG;AAAA,UACrB;AACA,iBAAO,OAAO,KAAK,EAAE;AAAA,QACvB;AAAA;AAAA,QAGA,eAAe,SAAS,QAAQ;AAE9B,mBAAS,OAAO,QAAQ,kBAAkB,EAAE;AAE5C,mBAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,OAAO,QAC9C,QAAQ,EAAE,IAAI,GAAG;AACnB,gBAAI,SAAS,EAAG;AAChB,kBAAM,MAAO,UAAU,QAAQ,OAAO,OAAO,IAAI,CAAC,CAAC,IAC5C,KAAK,IAAI,GAAG,KAAK,QAAQ,CAAC,IAAI,MAAQ,QAAQ,IAC9C,UAAU,QAAQ,OAAO,OAAO,CAAC,CAAC,MAAO,IAAI,QAAQ,CAAG;AAAA,UACjE;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO,UAAU;AAAA,IACnB,GAAG;AAAA;AAAA;;;AC/FH;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAAA,QAAI,UAAU;AAAA;AAAA,MAEZ,MAAM;AAAA;AAAA,QAEJ,eAAe,SAAS,KAAK;AAC3B,iBAAO,QAAQ,IAAI,cAAc,SAAS,mBAAmB,GAAG,CAAC,CAAC;AAAA,QACpE;AAAA;AAAA,QAGA,eAAe,SAAS,OAAO;AAC7B,iBAAO,mBAAmB,OAAO,QAAQ,IAAI,cAAc,KAAK,CAAC,CAAC;AAAA,QACpE;AAAA,MACF;AAAA;AAAA,MAGA,KAAK;AAAA;AAAA,QAEH,eAAe,SAAS,KAAK;AAC3B,mBAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC1C,kBAAM,KAAK,IAAI,WAAW,CAAC,IAAI,GAAI;AACrC,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,eAAe,SAAS,OAAO;AAC7B,mBAAS,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC1C,gBAAI,KAAK,OAAO,aAAa,MAAM,CAAC,CAAC,CAAC;AACxC,iBAAO,IAAI,KAAK,EAAE;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AASA,WAAO,UAAU,SAAU,KAAK;AAC9B,aAAO,OAAO,SAAS,SAAS,GAAG,KAAK,aAAa,GAAG,KAAK,CAAC,CAAC,IAAI;AAAA,IACrE;AAEA,aAAS,SAAU,KAAK;AACtB,aAAO,CAAC,CAAC,IAAI,eAAe,OAAO,IAAI,YAAY,aAAa,cAAc,IAAI,YAAY,SAAS,GAAG;AAAA,IAC5G;AAGA,aAAS,aAAc,KAAK;AAC1B,aAAO,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,UAAU,cAAc,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,IAC7G;AAAA;AAAA;;;ACpBA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAAA,KAAC,WAAU;AACT,UAAI,QAAQ,iBACR,OAAO,kBAAmB,MAC1B,WAAW,qBACX,MAAM,kBAAmB,KAG7B,MAAM,SAAU,SAAS,SAAS;AAEhC,YAAI,QAAQ,eAAe;AACzB,cAAI,WAAW,QAAQ,aAAa;AAClC,sBAAU,IAAI,cAAc,OAAO;AAAA;AAEnC,sBAAU,KAAK,cAAc,OAAO;AAAA,iBAC/B,SAAS,OAAO;AACvB,oBAAU,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC;AAAA,iBACxC,CAAC,MAAM,QAAQ,OAAO,KAAK,QAAQ,gBAAgB;AAC1D,oBAAU,QAAQ,SAAS;AAG7B,YAAI,IAAI,MAAM,aAAa,OAAO,GAC9B,IAAI,QAAQ,SAAS,GACrB,IAAK,YACL,IAAI,YACJ,IAAI,aACJ,IAAK;AAGT,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,YAAE,CAAC,KAAM,EAAE,CAAC,KAAM,IAAM,EAAE,CAAC,MAAM,MAAO,YAC/B,EAAE,CAAC,KAAK,KAAO,EAAE,CAAC,MAAO,KAAM;AAAA,QAC1C;AAGA,UAAE,MAAM,CAAC,KAAK,OAAS,IAAI;AAC3B,WAAK,IAAI,OAAQ,KAAM,KAAK,EAAE,IAAI;AAGlC,YAAI,KAAK,IAAI,KACT,KAAK,IAAI,KACT,KAAK,IAAI,KACT,KAAK,IAAI;AAEb,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AAErC,cAAI,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK;AAET,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,SAAS;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,SAAS;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAI,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,MAAM;AACtC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAI,GAAI,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,SAAS;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,UAAU;AAE3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,SAAS;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAI,GAAI,QAAQ;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAI,SAAS;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAI,GAAG,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAI,GAAG,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,SAAS;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAE3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,OAAO;AACvC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,SAAS;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAI,GAAI,SAAS;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,QAAQ;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,SAAS;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAE1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,SAAS;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAI,GAAI,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,QAAQ;AACxC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAI,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,SAAS;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAK,UAAU;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAI,GAAG,UAAU;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAE,EAAE,GAAG,IAAI,WAAW;AAC3C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAK,SAAS;AAC1C,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAG,CAAC,GAAG,IAAI,UAAU;AAE1C,cAAK,IAAI,OAAQ;AACjB,cAAK,IAAI,OAAQ;AACjB,cAAK,IAAI,OAAQ;AACjB,cAAK,IAAI,OAAQ;AAAA,QACnB;AAEA,eAAO,MAAM,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,MAClC;AAGA,UAAI,MAAO,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,YAAI,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,MAAM,KAAK;AAC3C,gBAAS,KAAK,IAAM,MAAO,KAAK,KAAO;AAAA,MACzC;AACA,UAAI,MAAO,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,YAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,MAAM,KAAK;AAC3C,gBAAS,KAAK,IAAM,MAAO,KAAK,KAAO;AAAA,MACzC;AACA,UAAI,MAAO,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,YAAI,IAAI,KAAK,IAAI,IAAI,MAAM,MAAM,KAAK;AACtC,gBAAS,KAAK,IAAM,MAAO,KAAK,KAAO;AAAA,MACzC;AACA,UAAI,MAAO,SAAU,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,YAAI,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,MAAM,KAAK;AACzC,gBAAS,KAAK,IAAM,MAAO,KAAK,KAAO;AAAA,MACzC;AAGA,UAAI,aAAa;AACjB,UAAI,cAAc;AAElB,aAAO,UAAU,SAAU,SAAS,SAAS;AAC3C,YAAI,YAAY,UAAa,YAAY;AACvC,gBAAM,IAAI,MAAM,sBAAsB,OAAO;AAE/C,YAAI,cAAc,MAAM,aAAa,IAAI,SAAS,OAAO,CAAC;AAC1D,eAAO,WAAW,QAAQ,UAAU,cAChC,WAAW,QAAQ,WAAW,IAAI,cAAc,WAAW,IAC3D,MAAM,WAAW,WAAW;AAAA,MAClC;AAAA,IAEF,GAAG;AAAA;AAAA;;;AC/JH;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAyBA,KAAC,WAAY;AACT;AACA,UAAI,CAAC,YAAY,UAAU,OAAO;AAC9B,oBAAY,UAAU,QAAQ,SAAU,OAAO,KAAK;AAChD,cAAI;AACJ,cAAI,OAAO,IAAI,WAAW,IAAI;AAC9B,cAAI,QAAQ,QAAW;AACnB,kBAAM,KAAK;AAAA,UACf;AACA,cAAI,SAAS,IAAI,YAAY,MAAM,KAAK;AACxC,cAAI,cAAc,IAAI,WAAW,MAAM;AACvC,eAAK,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACrC,wBAAY,CAAC,IAAI,KAAK,IAAI,KAAK;AAAA,UACnC;AACA,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ,GAAE;AAGD,KAAC,SAAU,QAAQ;AAChB;AAIA,eAAS,aAAa;AAAA,MAAC;AAEvB,iBAAW,UAAU,eAAe,SAAU,OAAO;AAEjD,YAAI,IAAI,KAAK,QAAQ,CAAC;AACtB,YAAI,IAAI,KAAK,QAAQ,CAAC;AACtB,YAAI,IAAI,KAAK,QAAQ,CAAC;AACtB,YAAI,IAAI,KAAK,QAAQ,CAAC;AACtB,YAAI,IAAI,KAAK,QAAQ,CAAC;AAEtB,YAAI,IAAI;AAAA,UACJ,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC;AAAA,UACzD,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC;AAAA,UACzD,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC3D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,UAC7D,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,IAAI,MAAM,EAAE;AAAA,QACjE;AACA,YAAI;AACJ,YAAI;AAEJ,aAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACtB,YAAE,MAAQ,EAAE,IAAE,CAAC,IAAI,EAAE,IAAE,CAAC,IAAI,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE,MAAM,KAAO,EAAE,IAAE,CAAC,IAAI,EAAE,IAAE,CAAC,IAAI,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE,OAAO,EAAI;AAAA,QAC1G;AAEA,aAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,eAAM,KAAK,IAAM,MAAM,MAAO,IAAI,EAAE,CAAC;AACrC,cAAI,IAAI,IAAI;AACR,kBAAO,IAAI,IAAM,CAAC,IAAI,KAAM,aAAa;AAAA,UAC7C,WAAW,IAAI,IAAI;AACf,kBAAM,IAAI,IAAI,KAAK,aAAa;AAAA,UACpC,WAAW,IAAI,IAAI;AACf,kBAAO,IAAI,IAAM,IAAI,IAAM,IAAI,KAAM,aAAY;AAAA,UACrD,OAAO;AACH,kBAAM,IAAI,IAAI,KAAK,aAAa;AAAA,UACpC;AACA,cAAI;AACJ,cAAI;AACJ,cAAM,KAAK,KAAO,MAAM;AACxB,cAAI;AACJ,cAAI;AAAA,QACR;AAEA,aAAK,QAAQ,CAAC,KAAK;AACnB,aAAK,QAAQ,CAAC,KAAK;AACnB,aAAK,QAAQ,CAAC,KAAK;AACnB,aAAK,QAAQ,CAAC,KAAK;AACnB,aAAK,QAAQ,CAAC,KAAK;AACnB,aAAK,cAAc;AAAA,MACvB;AAEA,iBAAW,UAAU,YAAY,WAAY;AACzC,YAAI,WAAW,KAAK,QAAQ,KAAK,cAAc;AAC/C,YAAI,MAAM;AACV,YAAI,MAAM,UAAU;AACpB,YAAI,QAAQ,KAAK,SAAS,KAAK,KAAK,KAAK,QAAQ,MAAM,KAAK;AAC5D,YAAI,MAAM,IAAI,WAAW,IAAI,YAAY,QAAQ,IAAI,CAAC,CAAC;AACvD,YAAI,CAAC,IAAI;AACT,YAAI,IAAI,SAAS,CAAC,IAAI,MAAM;AAC5B,YAAI,IAAI,SAAS,CAAC,IAAK,QAAQ,IAAK;AACpC,YAAI,IAAI,SAAS,CAAC,IAAK,QAAQ,KAAM;AACrC,YAAI,IAAI,SAAS,CAAC,IAAK,QAAQ,KAAM;AACrC,YAAI,IAAI,SAAS,CAAC,IAAI,MAAM;AAC5B,YAAI,IAAI,SAAS,CAAC,IAAK,QAAQ,IAAK;AACpC,YAAI,IAAI,SAAS,CAAC,IAAK,QAAQ,KAAM;AACrC,YAAI,IAAI,SAAS,CAAC,IAAK,QAAQ,KAAM;AACrC,eAAO;AAAA,MACX;AAEA,iBAAW,UAAU,YAAY,WAAY;AACzC,YAAI,KAAK,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;AAC3C,WAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC1B,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,IAAK;AAClC,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACnC,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACnC,WAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC1B,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,IAAK;AAClC,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACnC,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACnC,WAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC3B,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,IAAK;AACnC,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACnC,WAAG,CAAC,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACnC,WAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC3B,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,IAAK;AACnC,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACpC,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACpC,WAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC3B,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,IAAK;AACnC,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACpC,WAAG,EAAE,IAAK,KAAK,QAAQ,CAAC,MAAM,KAAM;AACpC,eAAO,GAAG;AAAA,MACd;AAEA,iBAAW,UAAU,QAAQ,WAAY;AACrC,aAAK,aAAa;AAClB,aAAK,QAAQ;AACb,aAAK,UAAU,IAAI,YAAY,IAAI,YAAY,EAAE,CAAC;AAClD,aAAK,QAAQ,CAAC,IAAI;AAClB,aAAK,QAAQ,CAAC,IAAI;AAClB,aAAK,QAAQ,CAAC,IAAI;AAClB,aAAK,QAAQ,CAAC,IAAI;AAClB,aAAK,QAAQ,CAAC,IAAI;AAAA,MACtB;AAEA,iBAAW,UAAU,WAAW;AAChC,iBAAW,UAAU,YAAY;AAIjC,UAAI,YAAY,SAAU,GAAG;AACzB,YAAI,SAAS,IAAI,YAAY,EAAE,MAAM;AACrC,YAAI,IAAI,IAAI,WAAW,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3B,YAAE,CAAC,IAAI,EAAE,WAAW,CAAC;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AAEA,UAAI,cAAc,SAAU,GAAG;AAC3B,YAAI,SAAS,IAAI,YAAY,CAAC;AAC9B,YAAI,IAAI,IAAI,WAAW,MAAM;AAC7B,UAAE,CAAC,IAAI;AACP,eAAO;AAAA,MACX;AAEA,UAAI,sBAAsB,SAAU,OAAO;AACvC,YAAI,MAAM,gBAAgB,YAAY;AAClC,iBAAO;AAAA,QACX,WAAW,MAAM,gBAAgB,aAAa;AAC1C,iBAAO,IAAI,WAAW,KAAK;AAAA,QAC/B,WAAW,MAAM,gBAAgB,QAAQ;AACrC,iBAAO,UAAU,KAAK;AAAA,QAC1B,WAAW,MAAM,gBAAgB,QAAQ;AACrC,cAAI,QAAQ,KAAM;AACd,kBAAM;AAAA,UACV,WAAW,QAAQ,GAAG;AAClB,kBAAM;AAAA,UACV;AACA,iBAAO,YAAY,KAAK;AAAA,QAC5B,OAAO;AACH,gBAAM;AAAA,QACV;AAAA,MACJ;AAEA,UAAI,kBAAkB,SAAU,GAAG;AAC/B,YAAI,MAAM,IAAI,WAAW,IAAI,YAAY,CAAC,CAAC;AAC3C,YAAI,CAAC,KAAK,IAAI,eAAe;AAC7B,YAAI,CAAC,KAAK,IAAI,aAAe;AAC7B,YAAI,CAAC,KAAK,IAAI,UAAe;AAC7B,YAAI,CAAC,IAAK,IAAI;AACd,eAAO;AAAA,MACX;AAGA,UAAI,KAAK,SAAU,aAAa;AAC5B,YAAI,SAAS,SAAU,OAAO;AAC1B,cAAI,MAAM,MAAM;AAChB,cAAI,SAAS;AACb,iBAAO,MAAM,GAAG;AACZ,gBAAI,UAAU,KAAK,WAAW,KAAK;AACnC,gBAAI,UAAU,KAAK;AACf,wBAAU;AAAA,YACd;AACA,gBAAI,WAAW,MAAM,SAAS,QAAQ,SAAS,OAAO;AACtD,iBAAK,MAAM,IAAI,UAAU,KAAK,KAAK;AACnC,sBAAU;AACV,mBAAO;AACP,iBAAK,SAAS;AACd,gBAAI,KAAK,UAAU,KAAK,UAAU;AAE9B,mBAAK,aAAa,KAAK,KAAK;AAG5B,mBAAK,QAAQ;AAAA,YACjB;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,WAAW,WAAY;AACvB,cAAI,UAAU,KAAK,UAAU;AAC7B,eAAK,OAAO,OAAO;AACnB,cAAI,SAAS,KAAK,UAAU;AAC5B,eAAK,MAAM;AACX,iBAAO;AAAA,QACX;AAEA,YAAI,SAAU,WAAY;AACtB,cAAI,CAAC,aAAa;AACd,kBAAM,4BAA4B,YAAY,SAAS;AAAA,UAC3D;AACA,sBAAY,UAAU,SAAS;AAC/B,sBAAY,UAAU,WAAW;AACjC,cAAIC,UAAS,IAAI,YAAY;AAC7B,UAAAA,QAAO,QAAQ,IAAI,WAAW,IAAI,YAAYA,QAAO,QAAQ,CAAC;AAC9D,UAAAA,QAAO,MAAM;AACb,iBAAOA;AAAA,QACX,EAAE;AAEF,eAAO;AAAA,UACH,QAAQ,SAAU,OAAO;AACrB,mBAAO,OAAO,oBAAoB,KAAK,CAAC;AAAA,UAC5C;AAAA,UAEA,UAAU,WAAY;AAClB,mBAAO,OAAO,SAAS;AAAA,UAC3B;AAAA,UAEA,QAAQ,SAAU,OAAO;AACrB,mBAAO,OAAO,oBAAoB,KAAK,CAAC;AACxC,mBAAO,OAAO,SAAS;AAAA,UAC3B;AAAA,UAEA,OAAO,WAAY;AACf,mBAAO,MAAM;AAAA,UACjB;AAAA,UAEA,cAAc,WAAY;AACtB,mBAAO,OAAO;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AAGA,UAAI,OAAO,SAAU,QAAQ;AACzB,YAAI,cAAc;AAClB,YAAI,KAAK,MAAM;AACf,YAAI,OAAO,WAAY;AACnB,cAAI,GAAG;AACP,cAAI,aAAa;AACb;AAAA,UACJ;AACA,cAAI,QAAQ,QAAW;AACnB,kBAAM;AAAA,UACV;AACA,cAAI,IAAI,aAAa,IAAI;AACrB,mBAAO,IAAI,WAAW,OAAO,OAAO,GAAG,CAAC;AAAA,UAC5C,OAAO;AACH,mBAAO,IAAI,WAAW,GAAG;AAAA,UAC7B;AACA,iBAAO,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;AACzC,eAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAC9B,iBAAK,CAAC,IAAI,KAAO,KAAK,CAAC;AAAA,UAC3B;AACA,eAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC/B,iBAAK,CAAC,IAAI;AAAA,UACd;AACA,iBAAO,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;AACzC,eAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAC9B,iBAAK,CAAC,IAAI,KAAO,KAAK,CAAC;AAAA,UAC3B;AACA,eAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC/B,iBAAK,CAAC,IAAI;AAAA,UACd;AACA,wBAAc;AACd,iBAAO,OAAO,KAAK,MAAM;AAAA,QAC7B;AAEA,YAAI,WAAW,WAAY;AACvB,wBAAc;AACd,gBAAM;AACN,iBAAO;AACP,iBAAO;AACP,iBAAO,MAAM;AAAA,QACjB;AAEA,YAAI,cAAc,WAAY;AAC1B,cAAI,SAAS,OAAO,SAAS;AAC7B,iBAAO,MAAM;AACb,iBAAO,OAAO,KAAK,MAAM;AACzB,iBAAO,OAAO,MAAM;AACpB,mBAAS,OAAO,SAAS;AACzB,mBAAS;AACT,iBAAO;AAAA,QACX;AAEA,YAAI,YAAY,SAAU,GAAG;AACzB,gBAAM;AAAA,QACV;AAEA,eAAO;AAAA,UACH,QAAQ,SAAUC,MAAK;AACnB,sBAAU,oBAAoBA,IAAG,CAAC;AAClC,iBAAK;AAAA,UACT;AAAA,UAEA,QAAQ,SAAU,OAAO;AACrB,mBAAO,OAAO,KAAK;AAAA,UACvB;AAAA,UAEA,UAAU,WAAY;AAClB,mBAAO,YAAY;AAAA,UACvB;AAAA,UAEA,KAAK,SAAU,OAAO;AAClB,iBAAK,OAAO,KAAK;AACjB,mBAAO,KAAK,SAAS;AAAA,UACzB;AAAA,UAEA,OAAO,WAAY;AACf,qBAAS;AAAA,UACb;AAAA,UAEA,YAAY,WAAY;AACpB,mBAAO,OAAO,aAAa;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,SAAS;AAAA,QACT,MAAM,WAAY;AACd,iBAAO,GAAG,UAAU;AAAA,QACxB;AAAA,QAEA,WAAW,WAAY;AACnB,iBAAO,KAAK,GAAG,UAAU,CAAC;AAAA,QAC9B;AAAA,MACJ;AAEA,UAAI,gBAAgB,OAAO,SAAS;AAChC,YAAK,gBAAgB,OAAO,UAAW,OAAO,SAAS;AACrD,iBAAO,UAAU,UAAU;AAAA,QAC7B,OAAO;AACH,oBAAU;AAAA,QACd;AAAA,MACJ,OAAO;AACH,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ,GAAE,OAAI;AAAA;AAAA;;;ACrYN;AAAA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAEA,QAAI,yBAAyB;AAE7B,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,eAAe;AACvB,YAAQ,gBAAgB;AACxB,YAAQ,gBAAgB;AACxB,YAAQ,6BAA6B;AACrC,YAAQ,2BAA2B;AACnC,YAAQ,eAAe;AAEvB,QAAI,MAAM,uBAAuB,aAAc;AAE/C,QAAI,YAAY,uBAAuB,mBAAoB;AAE3D,QAAI,UAAU,uBAAuB,gBAA8B;AAEnE,aAAS,OAAO,KAAK;AACnB,aAAO,OAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,mBAAmB,IAAI,aAAa;AAAA,IAC5F;AAEA,aAAS,KAAK,MAAM;AAClB,UAAI;AAEJ,UAAI,MAAM;AACR,YAAI,IAAI,KAAK,IAAI;AAAA,MACnB;AAEA,UAAI,CAAC,OAAO,CAAC,GAAG;AACd,YAAI,oBAAI,KAAK;AAAA,MACf;AAEA,aAAO,KAAK,MAAM,EAAE,QAAQ,IAAI,GAAI;AAAA,IACtC;AAEA,aAAS,aAAa,MAAM;AAC1B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,YAAI,KAAK,IAAI,WAAW;AAExB,WAAG,SAAS,WAAY;AACtB,cAAI,SAAS,IAAI,WAAW,GAAG,MAAM;AACrC,kBAAQ,MAAM;AAAA,QAChB;AAEA,WAAG,UAAU,WAAY;AACvB,iBAAO,GAAG,KAAK;AAAA,QACjB;AAEA,WAAG,kBAAkB,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH;AAEA,aAAS,cAAc,SAAS;AAC9B,UAAI,cAAc,QAAQ,aACtB,kBAAkB,QAAQ,iBAC1B,SAAS,QAAQ,QACjB,WAAW,QAAQ;AAEvB,UAAI,CAAC,aAAa;AAChB,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACpC;AAEA,UAAI,CAAC,iBAAiB;AACpB,cAAM,IAAI,MAAM,sBAAsB;AAAA,MACxC;AAEA,UAAI,CAAC,UAAU,CAAC,UAAU;AACxB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAAA,IACF;AAEA,aAAS,YAAY,KAAK;AACxB,UAAI,MAAM,CAAC;AAEX,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,YAAI,KAAK,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;AAAA,MAC5C;AAEA,aAAO,WAAW,KAAK,GAAG;AAAA,IAC5B;AAEA,aAAS,cAAc,KAAK;AAE1B,UAAI,QAAQ,MAAM,UAAU,MAAM,KAAK,KAAK,CAAC;AAC7C,UAAI,SAAS,aAAa,GAAG,IAAI,SAAS,GAAG,KAAK,CAAC;AACnD,aAAO,UAAU,SAAS,EAAE,cAAc,MAAM;AAAA,IAClD;AAEA,aAAS,2BAA2B,SAAS;AAC3C,UAAI,SAAS;AACb,UAAI,cAAc,OAAO,KAAK,OAAO;AACrC,oBAAc,YAAY,IAAI,SAAU,MAAM;AAC5C,eAAO,KAAK,YAAY;AAAA,MAC1B,CAAC;AACD,kBAAY,KAAK;AACjB,kBAAY,QAAQ,SAAU,MAAM;AAClC,YAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAChC,oBAAU,OAAO,MAAM,QAAQ,IAAI,IAAI;AAAA,QACzC;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,yBAAyB,QAAQ,YAAY,YAAY;AAChE,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AAEA,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AAEA,UAAI,eAAe;AAEnB,UAAI,QAAQ;AACV,wBAAgB,MAAM;AAAA,MACxB;AAEA,UAAI,YAAY;AACd,YAAI,WAAW,OAAO,CAAC,MAAM,KAAK;AAChC,0BAAgB;AAAA,QAClB;AAEA,wBAAgB;AAAA,MAClB;AAEA,UAAI,wBAAwB,KAAK;AACjC,UAAI,kBAAkB;AAEtB,UAAI,YAAY;AACd,YAAI,cAAc,SAASC,aAAY,QAAQ,QAAQ;AACrD,cAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG;AACzB,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG;AACzB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,cAAc,SAASC,aAAY,KAAK;AAC1C,mCAAyB,kBAAkB;AAE3C,cAAI,WAAW,GAAG,GAAG;AACnB,qCAAyB,MAAM,WAAW,GAAG;AAAA,UAC/C;AAEA,4BAAkB;AAAA,QACpB;AAEA,eAAO,KAAK,UAAU,EAAE,KAAK,WAAW,EAAE,QAAQ,WAAW;AAAA,MAC/D;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,SAAS;AAC7B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AAEA,UAAI,WAAW,SACX,gBAAgB,SAAS,MACzB,OAAO,kBAAkB,SAAS,WAAW,eAC7C,gBAAgB,SAAS,MACzB,OAAO,kBAAkB,SAAS,KAAK,eACvC,qBAAqB,SAAS,YAC9B,aAAa,uBAAuB,SAAS,KAAK,oBAClD,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,KAAK,IAAI,OAAO,kBACxD,SAAS,SAAS,QAClB,aAAa,SAAS,YACtB,kBAAkB,SAAS,iBAC3B,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,CAAC,IAAI,kBAC7C,cAAc,SAAS;AAC3B,UAAI,OAAO,QAAQ,YAAY,KAAK;AACpC,UAAI,cAAc,QAAQ,cAAc,KAAK;AAC7C,UAAI,OAAO,CAAC,MAAM,YAAY,WAAW;AAEzC,UAAI,SAAS,UAAU;AACrB,aAAK,KAAK,IAAI;AAAA,MAChB,OAAO;AACL,aAAK,KAAK,OAAO;AAAA,MACnB;AAEA,UAAI,0BAA0B,2BAA2B,OAAO;AAChE,UAAI,wBAAwB,yBAAyB,QAAQ,YAAY,WAAW;AACpF,WAAK,KAAK,KAAK,0BAA0B,qBAAqB;AAC9D,UAAI,OAAO,KAAK,KAAK,IAAI;AACzB,UAAI,OAAO,IAAI,QAAQ,SAAS,EAAE,UAAU;AAC5C,WAAK,OAAO,eAAe;AAC3B,WAAK,OAAO,IAAI;AAChB,UAAI,UAAU,IAAI,WAAW,KAAK,SAAS,CAAC;AAE5C,UAAI,YAAY,UAAU,SAAS,EAAE,cAAc,OAAO;AAE1D,aAAO;AAAA,IACT;AAAA;AAAA;;;AC1MA;AAAA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAEA,QAAI,yBAAyB;AAE7B,YAAQ,aAAa;AACrB,YAAQ,SAAS,IAAI;AAErB,QAAI,gBAAgB,uBAAuB,uBAAwB;AAEnE,QAAI,QAAQ,uBAAuB,cAAuB;AAE1D,QAAI,SAAS;AAEb,QAAI,UAEJ,WAAY;AACV,eAASC,SAAQ,SAAS;AACxB,YAAI,YAAY,QAAQ;AACtB,oBAAU,CAAC;AAAA,QACb;AAEA,SAAC,GAAG,OAAO,eAAe,OAAO;AACjC,aAAK,QAAQ,GAAG,cAAc,SAAS,GAAG;AAAA,UACxC,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS;AAAA,QACX,GAAG,OAAO;AACV,YAAI,aAAa,KAAK,MAClB,SAAS,WAAW,QACpB,SAAS,WAAW,QACpB,WAAW,WAAW,UACtB,WAAW,WAAW;AAC1B,aAAK,OAAO;AAEZ,YAAI,UAAU;AACZ,eAAK,OAAO;AAAA,QACd,OAAO;AACL,cAAI,OAAO;AAEX,cAAI,UAAU;AACZ,oBAAQ;AAAA,UACV;AAEA,kBAAQ,MAAM,SAAS;AACvB,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAEA,UAAI,SAASA,SAAQ;AAErB,aAAO,MAAM,SAAS,IAAI,YAAY,MAAM,SAAS;AACnD,YAAI,QAAQ;AAEZ,YAAI,YAAY,QAAQ;AACtB,oBAAU,CAAC;AAAA,QACb;AAEA,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,WAAC,GAAG,OAAO,cAAc,IAAI,EAAE,KAAK,SAAU,KAAK;AACjD,gBAAI,cAAc,MAAM,MACpB,cAAc,YAAY,aAC1B,kBAAkB,YAAY,iBAC9B,WAAW,YAAY,UACvB,SAAS,YAAY;AACzB,gBAAI,OAAO;AACX,gBAAI,cAAc,GAAG,OAAO,eAAe,GAAG;AAC9C,gBAAI,cAAc,KAAK;AACvB,gBAAI,UAAU;AAAA,cACZ,eAAe;AAAA,cACf,gBAAgB;AAAA,cAChB,eAAc,oBAAI,KAAK,GAAE,YAAY;AAAA,YACvC;AAEA,gBAAI,UAAU;AACZ,sBAAQ,sBAAsB,IAAI;AAAA,YACpC;AAEA,gBAAI,aAAa,GAAG,OAAO,cAAc;AAAA,cACvC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AACD,oBAAQ,gBAAgB,SAAS,cAAc,MAAM;AACrD,gBAAI,WAAW,MAAM,KAAK,SAAS,UAAU;AAC7C,gBAAI,MAAM,WAAW,QAAQ,MAAM,OAAO,MAAM;AAChD,oBAAQ,GAAG,MAAM,SAAS,GAAG,KAAK;AAAA,cAChC,QAAQ;AAAA,cACR;AAAA,cACA,MAAM;AAAA,cACN,SAAS,MAAM,KAAK;AAAA,cACpB,YAAY,QAAQ;AAAA,YACtB,CAAC;AAAA,UACH,CAAC,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AAGA,aAAO,aAAa,SAAS,WAAW,YAAY,kBAAkB;AACpE,YAAI,cAAc,KAAK,MACnB,cAAc,YAAY,aAC1B,kBAAkB,YAAY,iBAC9B,WAAW,YAAY,UACvB,SAAS,YAAY;AACzB,YAAI,OAAO;AACX,YAAI,UAAU;AAAA,UACZ,eAAc,oBAAI,KAAK,GAAE,YAAY;AAAA,UACrC,wBAAwB,UAAU,gBAAgB;AAAA,QACpD;AAEA,YAAI,UAAU;AACZ,kBAAQ,sBAAsB,IAAI;AAAA,QACpC;AAEA,YAAI,aAAa,GAAG,OAAO,cAAc;AAAA,UACvC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AACD,gBAAQ,gBAAgB,SAAS,cAAc,MAAM;AACrD,YAAI,WAAW,KAAK,KAAK,SAAS,UAAU;AAC5C,YAAI,MAAM,WAAW,QAAQ,KAAK,OAAO,MAAM,aAAa;AAC5D,gBAAQ,GAAG,MAAM,SAAS,GAAG,KAAK;AAAA,UAChC,QAAQ;AAAA,UACR;AAAA,UACA,SAAS,KAAK,KAAK;AAAA,QACrB,CAAC;AAAA,MACH;AAEA,aAAO,eAAe,SAAS,aAAa,YAAY,SAAS;AAC/D,YAAI,YAAY,QAAQ;AACtB,oBAAU,CAAC;AAAA,QACb;AAEA,YAAI,WAAW,SACX,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,OAAO,kBAC/C,SAAS,SAAS,QAClB,UAAU,SAAS,SACnB,WAAW,SAAS;AACxB,YAAI,cAAc,KAAK,MACnB,cAAc,YAAY,aAC1B,kBAAkB,YAAY,iBAC9B,WAAW,YAAY,UACvB,SAAS,YAAY;AACzB,YAAI,UAAU,CAAC;AACf,YAAI,cAAc,CAAC;AAEnB,YAAI,SAAS;AACX,cAAI,iBAAiB;AACrB,sBAAY,cAAc,IAAI;AAAA,QAChC;AAEA,YAAI,UAAU;AACZ,iBAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,GAAG;AACzC,gBAAI,MAAM,cAAc,EAAE,YAAY;AACtC,wBAAY,GAAG,IAAI,SAAS,CAAC;AAAA,UAC/B,CAAC;AAAA,QACH;AAEA,eAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,cAAI,WAAW,IAAI,YAAY;AAC/B,cAAI,QAAQ,QAAQ,GAAG;AAEvB,cAAI,SAAS,QAAQ,QAAQ,MAAM,GAAG;AACpC,oBAAQ,QAAQ,IAAI;AAAA,UACtB,WAAW,SAAS,QAAQ,aAAa,MAAM,GAAG;AAChD,oBAAQ,GAAG,IAAI;AAAA,UACjB,WAAW,SAAS,QAAQ,cAAc,MAAM,GAAG;AACjD,oBAAQ,GAAG,IAAI;AAAA,UACjB,WAAW,aAAa,aAAa,aAAa,cAAc,aAAa,aAAa,aAAa,UAAU;AAC/G,wBAAY,QAAQ,IAAI;AAAA,UAC1B;AAAA,QACF,CAAC;AACD,YAAI,gBAAgB,QAAQ,gBAAgB,KAAK;AAEjD,YAAI,eAAe;AACjB,sBAAY,gBAAgB,IAAI;AAAA,QAClC;AAEA,YAAI,cAAc,GAAG,OAAO,MAAM,IAAI;AACtC,YAAI,aAAa,GAAG,OAAO,cAAc;AAAA,UACvC,MAAM;AAAA,UACN,MAAM,UAAU;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,YAAI,WAAW,KAAK,KAAK,SAAS,UAAU;AAC5C,YAAI,MAAM,WAAW,QAAQ,KAAK,OAAO,MAAM;AAC/C,eAAO,qBAAqB;AAC5B,eAAO,cAAc;AACrB,eAAO,gBAAgB,mBAAmB,SAAS;AACnD,eAAO,KAAK,WAAW,EAAE,QAAQ,SAAU,GAAG;AAC5C,iBAAO,MAAM,IAAI,MAAM,mBAAmB,YAAY,CAAC,CAAC;AAAA,QAC1D,CAAC;AACD,eAAO;AAAA,MACT;AAEA,aAAOA;AAAA,IACT,EAAE;AAEF,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACzNrB;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AAEA,WAAO,UAAU,kBAAqB,SAAS;AAAA;AAAA;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "engine", "key", "import_dist", "compareFunc", "processFunc", "import_dist", "TinyOSS", "import_dist"]}