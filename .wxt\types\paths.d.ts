// Generated by wxt
import "wxt/browser";

declare module "wxt/browser" {
  export type PublicPath =
    | ""
    | "/"
    | "/assets/images/logo-2.png"
    | "/assets/images/logo.png"
    | "/background.js"
    | "/content-scripts/appmsg.js"
    | "/injected.js"
    | "/mpmd/icon-256-gray.png"
    | "/mpmd/icon-256.png"
    | "/mpmd/logo.svg"
    | "/popup.html"
  type HtmlPublicPath = Extract<PublicPath, `${string}.html`>
  export interface WxtRuntime {
    getURL(path: PublicPath): string;
    getURL(path: `${HtmlPublicPath}${string}`): string;
  }
}
