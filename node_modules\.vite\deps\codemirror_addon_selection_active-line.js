import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  require_codemirror
} from "./chunk-SWSTTIYR.js";
import {
  __commonJS,
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/codemirror/addon/selection/active-line.js
var require_active_line = __commonJS({
  "node_modules/codemirror/addon/selection/active-line.js"(exports, module) {
    var import_dist = __toESM(require_dist());
    var import_dist2 = __toESM(require_dist2());
    var import_dist3 = __toESM(require_dist3());
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      var WRAP_CLASS = "CodeMirror-activeline";
      var BACK_CLASS = "CodeMirror-activeline-background";
      var GUTT_CLASS = "CodeMirror-activeline-gutter";
      CodeMirror2.defineOption("styleActiveLine", false, function(cm, val, old) {
        var prev = old == CodeMirror2.Init ? false : old;
        if (val == prev) return;
        if (prev) {
          cm.off("beforeSelectionChange", selectionChange);
          clearActiveLines(cm);
          delete cm.state.activeLines;
        }
        if (val) {
          cm.state.activeLines = [];
          updateActiveLines(cm, cm.listSelections());
          cm.on("beforeSelectionChange", selectionChange);
        }
      });
      function clearActiveLines(cm) {
        for (var i = 0; i < cm.state.activeLines.length; i++) {
          cm.removeLineClass(cm.state.activeLines[i], "wrap", WRAP_CLASS);
          cm.removeLineClass(cm.state.activeLines[i], "background", BACK_CLASS);
          cm.removeLineClass(cm.state.activeLines[i], "gutter", GUTT_CLASS);
        }
      }
      function sameArray(a, b) {
        if (a.length != b.length) return false;
        for (var i = 0; i < a.length; i++)
          if (a[i] != b[i]) return false;
        return true;
      }
      function updateActiveLines(cm, ranges) {
        var active = [];
        for (var i = 0; i < ranges.length; i++) {
          var range = ranges[i];
          var option = cm.getOption("styleActiveLine");
          if (typeof option == "object" && option.nonEmpty ? range.anchor.line != range.head.line : !range.empty())
            continue;
          var line = cm.getLineHandleVisualStart(range.head.line);
          if (active[active.length - 1] != line) active.push(line);
        }
        if (sameArray(cm.state.activeLines, active)) return;
        cm.operation(function() {
          clearActiveLines(cm);
          for (var i2 = 0; i2 < active.length; i2++) {
            cm.addLineClass(active[i2], "wrap", WRAP_CLASS);
            cm.addLineClass(active[i2], "background", BACK_CLASS);
            cm.addLineClass(active[i2], "gutter", GUTT_CLASS);
          }
          cm.state.activeLines = active;
        });
      }
      function selectionChange(cm, sel) {
        updateActiveLines(cm, sel.ranges);
      }
    });
  }
});
export default require_active_line();
//# sourceMappingURL=codemirror_addon_selection_active-line.js.map
