// Generated by wxt
// Types for the #import virtual module
declare module '#imports' {
  export { browser, Browser } from 'wxt/browser';
  export { storage, StorageArea, WxtStorage, WxtStorageItem, StorageItemKey, StorageAreaChanges, MigrationError } from 'wxt/utils/storage';
  export { useAppConfig } from 'wxt/utils/app-config';
  export { ContentScriptContext, WxtWindowEventMap } from 'wxt/utils/content-script-context';
  export { createIframeUi, IframeContentScriptUi, IframeContentScriptUiOptions } from 'wxt/utils/content-script-ui/iframe';
  export { createIntegratedUi, IntegratedContentScriptUi, IntegratedContentScriptUiOptions } from 'wxt/utils/content-script-ui/integrated';
  export { createShadowRootUi, ShadowRootContentScriptUi, ShadowRootContentScriptUiOptions } from 'wxt/utils/content-script-ui/shadow-root';
  export { ContentScriptUi, ContentScriptUiOptions, ContentScriptOverlayAlignment, ContentScriptAppendMode, ContentScriptInlinePositioningOptions, ContentScriptOverlayPositioningOptions, ContentScriptModalPositioningOptions, ContentScriptPositioningOptions, ContentScriptAnchoredOptions, AutoMountOptions, StopAutoMount, AutoMount } from 'wxt/utils/content-script-ui/types';
  export { defineAppConfig, WxtAppConfig } from 'wxt/utils/define-app-config';
  export { defineBackground } from 'wxt/utils/define-background';
  export { defineContentScript } from 'wxt/utils/define-content-script';
  export { defineUnlistedScript } from 'wxt/utils/define-unlisted-script';
  export { defineWxtPlugin } from 'wxt/utils/define-wxt-plugin';
  export { injectScript, ScriptPublicPath, InjectScriptOptions } from 'wxt/utils/inject-script';
  export { InvalidMatchPattern, MatchPattern } from 'wxt/utils/match-patterns';
  export { fakeBrowser } from 'wxt/testing';
  export { default as MDAlert, resolveVariants, createSyntaxPattern, ucfirst } from '../src/utils/MDAlert';
  export { default as MDFootnotes } from '../src/utils/MDFootnotes';
  export { MarkedKatexOptions } from '../src/utils/MDKatex.d';
  export { MDKatex } from '../src/utils/MDKatex';
  export { default as MDSlider } from '../src/utils/MDSlider';
  export { addSpacingToMarkdown } from '../src/utils/autoSpace';
  export { copyPlain, copyHtml } from '../src/utils/clipboard';
  export { toggleFormat } from '../src/utils/editor';
  export { default as fetch } from '../src/utils/fetch';
  export { default as file } from '../src/utils/file';
  export { addPrefix, customizeTheme, customCssWithTemplate, css2json, getStyleString, formatDoc, sanitizeTitle, downloadMD, exportHTML, createTable, toBase64, checkImage, removeLeft, solveWeChatImage, mergeCss, createEmptyNode, modifyHtmlStructure, processClipboardContent, renderMarkdown, postProcessHtml, modifyHtmlContent } from '../src/utils/index';
  export { initRenderer } from '../src/utils/renderer';
  export { setupComponents } from '../src/utils/setup-components';
  export { utf16to8, utf8to16, base64encode, base64decode, safe64 } from '../src/utils/tokenTools';
}
