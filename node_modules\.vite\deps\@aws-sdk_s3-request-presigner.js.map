{"version": 3, "sources": ["../../@aws-sdk/s3-request-presigner/dist-es/index.js", "../../@aws-sdk/s3-request-presigner/dist-es/getSignedUrl.js", "../../@aws-sdk/util-format-url/dist-es/index.js", "../../@aws-sdk/s3-request-presigner/dist-es/presigner.js", "../../@aws-sdk/s3-request-presigner/dist-es/constants.js"], "sourcesContent": ["export * from \"./getSignedUrl\";\nexport * from \"./presigner\";\n", "import { formatUrl } from \"@aws-sdk/util-format-url\";\nimport { getEndpointFromInstructions } from \"@smithy/middleware-endpoint\";\nimport { HttpRequest } from \"@smithy/protocol-http\";\nimport { S3RequestPresigner } from \"./presigner\";\nexport const getSignedUrl = async (client, command, options = {}) => {\n    let s3Presigner;\n    let region;\n    if (typeof client.config.endpointProvider === \"function\") {\n        const endpointV2 = await getEndpointFromInstructions(command.input, command.constructor, client.config);\n        const authScheme = endpointV2.properties?.authSchemes?.[0];\n        if (authScheme?.name === \"sigv4a\") {\n            region = authScheme?.signingRegionSet?.join(\",\");\n        }\n        else {\n            region = authScheme?.signingRegion;\n        }\n        s3Presigner = new S3RequestPresigner({\n            ...client.config,\n            signingName: authScheme?.signingName,\n            region: async () => region,\n        });\n    }\n    else {\n        s3Presigner = new S3RequestPresigner(client.config);\n    }\n    const presignInterceptMiddleware = (next, context) => async (args) => {\n        const { request } = args;\n        if (!HttpRequest.isInstance(request)) {\n            throw new Error(\"Request to be presigned is not an valid HTTP request.\");\n        }\n        delete request.headers[\"amz-sdk-invocation-id\"];\n        delete request.headers[\"amz-sdk-request\"];\n        delete request.headers[\"x-amz-user-agent\"];\n        let presigned;\n        const presignerOptions = {\n            ...options,\n            signingRegion: options.signingRegion ?? context[\"signing_region\"] ?? region,\n            signingService: options.signingService ?? context[\"signing_service\"],\n        };\n        if (context.s3ExpressIdentity) {\n            presigned = await s3Presigner.presignWithCredentials(request, context.s3ExpressIdentity, presignerOptions);\n        }\n        else {\n            presigned = await s3Presigner.presign(request, presignerOptions);\n        }\n        return {\n            response: {},\n            output: {\n                $metadata: { httpStatusCode: 200 },\n                presigned,\n            },\n        };\n    };\n    const middlewareName = \"presignInterceptMiddleware\";\n    const clientStack = client.middlewareStack.clone();\n    clientStack.addRelativeTo(presignInterceptMiddleware, {\n        name: middlewareName,\n        relation: \"before\",\n        toMiddleware: \"awsAuthMiddleware\",\n        override: true,\n    });\n    const handler = command.resolveMiddleware(clientStack, client.config, {});\n    const { output } = await handler({ input: command.input });\n    const { presigned } = output;\n    return formatUrl(presigned);\n};\n", "import { buildQueryString } from \"@smithy/querystring-builder\";\nexport function formatUrl(request) {\n    const { port, query } = request;\n    let { protocol, path, hostname } = request;\n    if (protocol && protocol.slice(-1) !== \":\") {\n        protocol += \":\";\n    }\n    if (port) {\n        hostname += `:${port}`;\n    }\n    if (path && path.charAt(0) !== \"/\") {\n        path = `/${path}`;\n    }\n    let queryString = query ? buildQueryString(query) : \"\";\n    if (queryString && queryString[0] !== \"?\") {\n        queryString = `?${queryString}`;\n    }\n    let auth = \"\";\n    if (request.username != null || request.password != null) {\n        const username = request.username ?? \"\";\n        const password = request.password ?? \"\";\n        auth = `${username}:${password}@`;\n    }\n    let fragment = \"\";\n    if (request.fragment) {\n        fragment = `#${request.fragment}`;\n    }\n    return `${protocol}//${auth}${hostname}${path}${queryString}${fragment}`;\n}\n", "import { SignatureV4MultiRegion } from \"@aws-sdk/signature-v4-multi-region\";\nimport { SHA256_HEADER, UNSIGNED_PAYLOAD } from \"./constants\";\nexport class S3RequestPresigner {\n    signer;\n    constructor(options) {\n        const resolvedOptions = {\n            service: options.signingName || options.service || \"s3\",\n            uriEscapePath: options.uriEscapePath || false,\n            applyChecksum: options.applyChecksum || false,\n            ...options,\n        };\n        this.signer = new SignatureV4MultiRegion(resolvedOptions);\n    }\n    presign(requestToSign, { unsignableHeaders = new Set(), hoistableHeaders = new Set(), unhoistableHeaders = new Set(), ...options } = {}) {\n        this.prepareRequest(requestToSign, {\n            unsignableHeaders,\n            unhoistableHeaders,\n            hoistableHeaders,\n        });\n        return this.signer.presign(requestToSign, {\n            expiresIn: 900,\n            unsignableHeaders,\n            unhoistableHeaders,\n            ...options,\n        });\n    }\n    presignWithCredentials(requestToSign, credentials, { unsignableHeaders = new Set(), hoistableHeaders = new Set(), unhoistableHeaders = new Set(), ...options } = {}) {\n        this.prepareRequest(requestToSign, {\n            unsignableHeaders,\n            unhoistableHeaders,\n            hoistableHeaders,\n        });\n        return this.signer.presignWithCredentials(requestToSign, credentials, {\n            expiresIn: 900,\n            unsignableHeaders,\n            unhoistableHeaders,\n            ...options,\n        });\n    }\n    prepareRequest(requestToSign, { unsignableHeaders = new Set(), unhoistableHeaders = new Set(), hoistableHeaders = new Set(), } = {}) {\n        unsignableHeaders.add(\"content-type\");\n        Object.keys(requestToSign.headers)\n            .map((header) => header.toLowerCase())\n            .filter((header) => header.startsWith(\"x-amz-server-side-encryption\"))\n            .forEach((header) => {\n            if (!hoistableHeaders.has(header)) {\n                unhoistableHeaders.add(header);\n            }\n        });\n        requestToSign.headers[SHA256_HEADER] = UNSIGNED_PAYLOAD;\n        const currentHostHeader = requestToSign.headers.host;\n        const port = requestToSign.port;\n        const expectedHostHeader = `${requestToSign.hostname}${requestToSign.port != null ? \":\" + port : \"\"}`;\n        if (!currentHostHeader || (currentHostHeader === requestToSign.hostname && requestToSign.port != null)) {\n            requestToSign.headers.host = expectedHostHeader;\n        }\n    }\n}\n", "export const UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nexport const SHA256_HEADER = \"X-Amz-Content-Sha256\";\nexport const ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nexport const CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nexport const AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nexport const SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nexport const EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nexport const HOST_HEADER = \"host\";\nexport const ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AACO,SAAS,UAAU,SAAS;AAC/B,QAAM,EAAE,MAAM,MAAM,IAAI;AACxB,MAAI,EAAE,UAAU,MAAM,SAAS,IAAI;AACnC,MAAI,YAAY,SAAS,MAAM,EAAE,MAAM,KAAK;AACxC,gBAAY;AAAA,EAChB;AACA,MAAI,MAAM;AACN,gBAAY,IAAI,IAAI;AAAA,EACxB;AACA,MAAI,QAAQ,KAAK,OAAO,CAAC,MAAM,KAAK;AAChC,WAAO,IAAI,IAAI;AAAA,EACnB;AACA,MAAI,cAAc,QAAQ,iBAAiB,KAAK,IAAI;AACpD,MAAI,eAAe,YAAY,CAAC,MAAM,KAAK;AACvC,kBAAc,IAAI,WAAW;AAAA,EACjC;AACA,MAAI,OAAO;AACX,MAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY,MAAM;AACtD,UAAM,WAAW,QAAQ,YAAY;AACrC,UAAM,WAAW,QAAQ,YAAY;AACrC,WAAO,GAAG,QAAQ,IAAI,QAAQ;AAAA,EAClC;AACA,MAAI,WAAW;AACf,MAAI,QAAQ,UAAU;AAClB,eAAW,IAAI,QAAQ,QAAQ;AAAA,EACnC;AACA,SAAO,GAAG,QAAQ,KAAK,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,WAAW,GAAG,QAAQ;AAC1E;;;AC5BA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAO,IAAM,mBAAmB;AACzB,IAAM,gBAAgB;;;ADCtB,IAAM,qBAAN,MAAyB;AAAA,EAE5B,YAAY,SAAS;AADrB;AAEI,UAAM,kBAAkB;AAAA,MACpB,SAAS,QAAQ,eAAe,QAAQ,WAAW;AAAA,MACnD,eAAe,QAAQ,iBAAiB;AAAA,MACxC,eAAe,QAAQ,iBAAiB;AAAA,MACxC,GAAG;AAAA,IACP;AACA,SAAK,SAAS,IAAI,uBAAuB,eAAe;AAAA,EAC5D;AAAA,EACA,QAAQ,eAAe,EAAE,oBAAoB,oBAAI,IAAI,GAAG,mBAAmB,oBAAI,IAAI,GAAG,qBAAqB,oBAAI,IAAI,GAAG,GAAG,QAAQ,IAAI,CAAC,GAAG;AACrI,SAAK,eAAe,eAAe;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO,KAAK,OAAO,QAAQ,eAAe;AAAA,MACtC,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EACA,uBAAuB,eAAe,aAAa,EAAE,oBAAoB,oBAAI,IAAI,GAAG,mBAAmB,oBAAI,IAAI,GAAG,qBAAqB,oBAAI,IAAI,GAAG,GAAG,QAAQ,IAAI,CAAC,GAAG;AACjK,SAAK,eAAe,eAAe;AAAA,MAC/B;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,WAAO,KAAK,OAAO,uBAAuB,eAAe,aAAa;AAAA,MAClE,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA,EACA,eAAe,eAAe,EAAE,oBAAoB,oBAAI,IAAI,GAAG,qBAAqB,oBAAI,IAAI,GAAG,mBAAmB,oBAAI,IAAI,EAAG,IAAI,CAAC,GAAG;AACjI,sBAAkB,IAAI,cAAc;AACpC,WAAO,KAAK,cAAc,OAAO,EAC5B,IAAI,CAAC,WAAW,OAAO,YAAY,CAAC,EACpC,OAAO,CAAC,WAAW,OAAO,WAAW,8BAA8B,CAAC,EACpE,QAAQ,CAAC,WAAW;AACrB,UAAI,CAAC,iBAAiB,IAAI,MAAM,GAAG;AAC/B,2BAAmB,IAAI,MAAM;AAAA,MACjC;AAAA,IACJ,CAAC;AACD,kBAAc,QAAQ,aAAa,IAAI;AACvC,UAAM,oBAAoB,cAAc,QAAQ;AAChD,UAAM,OAAO,cAAc;AAC3B,UAAM,qBAAqB,GAAG,cAAc,QAAQ,GAAG,cAAc,QAAQ,OAAO,MAAM,OAAO,EAAE;AACnG,QAAI,CAAC,qBAAsB,sBAAsB,cAAc,YAAY,cAAc,QAAQ,MAAO;AACpG,oBAAc,QAAQ,OAAO;AAAA,IACjC;AAAA,EACJ;AACJ;;;AFrDO,IAAM,eAAe,OAAO,QAAQ,SAAS,UAAU,CAAC,MAAM;AAJrE;AAKI,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,OAAO,OAAO,qBAAqB,YAAY;AACtD,UAAM,aAAa,MAAM,4BAA4B,QAAQ,OAAO,QAAQ,aAAa,OAAO,MAAM;AACtG,UAAM,cAAa,sBAAW,eAAX,mBAAuB,gBAAvB,mBAAqC;AACxD,SAAI,yCAAY,UAAS,UAAU;AAC/B,gBAAS,8CAAY,qBAAZ,mBAA8B,KAAK;AAAA,IAChD,OACK;AACD,eAAS,yCAAY;AAAA,IACzB;AACA,kBAAc,IAAI,mBAAmB;AAAA,MACjC,GAAG,OAAO;AAAA,MACV,aAAa,yCAAY;AAAA,MACzB,QAAQ,YAAY;AAAA,IACxB,CAAC;AAAA,EACL,OACK;AACD,kBAAc,IAAI,mBAAmB,OAAO,MAAM;AAAA,EACtD;AACA,QAAM,6BAA6B,CAAC,MAAM,YAAY,OAAO,SAAS;AAClE,UAAM,EAAE,QAAQ,IAAI;AACpB,QAAI,CAAC,YAAY,WAAW,OAAO,GAAG;AAClC,YAAM,IAAI,MAAM,uDAAuD;AAAA,IAC3E;AACA,WAAO,QAAQ,QAAQ,uBAAuB;AAC9C,WAAO,QAAQ,QAAQ,iBAAiB;AACxC,WAAO,QAAQ,QAAQ,kBAAkB;AACzC,QAAIC;AACJ,UAAM,mBAAmB;AAAA,MACrB,GAAG;AAAA,MACH,eAAe,QAAQ,iBAAiB,QAAQ,gBAAgB,KAAK;AAAA,MACrE,gBAAgB,QAAQ,kBAAkB,QAAQ,iBAAiB;AAAA,IACvE;AACA,QAAI,QAAQ,mBAAmB;AAC3B,MAAAA,aAAY,MAAM,YAAY,uBAAuB,SAAS,QAAQ,mBAAmB,gBAAgB;AAAA,IAC7G,OACK;AACD,MAAAA,aAAY,MAAM,YAAY,QAAQ,SAAS,gBAAgB;AAAA,IACnE;AACA,WAAO;AAAA,MACH,UAAU,CAAC;AAAA,MACX,QAAQ;AAAA,QACJ,WAAW,EAAE,gBAAgB,IAAI;AAAA,QACjC,WAAAA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,iBAAiB;AACvB,QAAM,cAAc,OAAO,gBAAgB,MAAM;AACjD,cAAY,cAAc,4BAA4B;AAAA,IAClD,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc;AAAA,IACd,UAAU;AAAA,EACd,CAAC;AACD,QAAM,UAAU,QAAQ,kBAAkB,aAAa,OAAO,QAAQ,CAAC,CAAC;AACxE,QAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,EAAE,OAAO,QAAQ,MAAM,CAAC;AACzD,QAAM,EAAE,UAAU,IAAI;AACtB,SAAO,UAAU,SAAS;AAC9B;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "presigned"]}