{"version": 3, "sources": ["../../@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-2O5ZK7RR.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  MermaidGeneratedSharedModule,\n  RadarGeneratedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/radar/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/radar/tokenBuilder.ts\nvar RadarTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"RadarTokenBuilder\");\n  }\n  constructor() {\n    super([\"radar-beta\"]);\n  }\n};\n\n// src/language/radar/module.ts\nvar RadarModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new RadarTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createRadarServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Radar = inject(\n    createDefaultCoreModule({ shared }),\n    RadarGeneratedModule,\n    RadarModule\n  );\n  shared.ServiceRegistry.register(Radar);\n  return { shared, Radar };\n}\n__name(createRadarServices, \"createRadarServices\");\n\nexport {\n  RadarModule,\n  createRadarServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA;AAiBA,IAAI,qBAAoB,mBAAc,4BAA4B;AAAA,EAIhE,cAAc;AACZ,UAAM,CAAC,YAAY,CAAC;AAAA,EACtB;AACF,GALI,OAAO,IAAM,mBAAmB,GAFZ;AAUxB,IAAI,cAAc;AAAA,EAChB,QAAQ;AAAA,IACN,cAA8B,OAAO,MAAM,IAAI,kBAAkB,GAAG,cAAc;AAAA,IAClF,gBAAgC,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,oBAAoB,UAAU,iBAAiB;AACtD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,KAAK;AACrC,SAAO,EAAE,QAAQ,MAAM;AACzB;AACA,OAAO,qBAAqB,qBAAqB;", "names": ["import_dist"]}