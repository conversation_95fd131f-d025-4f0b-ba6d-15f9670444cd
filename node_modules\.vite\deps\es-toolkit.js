import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  __publicField,
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/es-toolkit/dist/index.mjs
var import_dist553 = __toESM(require_dist(), 1);
var import_dist554 = __toESM(require_dist2(), 1);
var import_dist555 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/array/at.mjs
var import_dist = __toESM(require_dist(), 1);
var import_dist2 = __toESM(require_dist2(), 1);
var import_dist3 = __toESM(require_dist3(), 1);
function at(arr, indices) {
  const result = new Array(indices.length);
  const length = arr.length;
  for (let i = 0; i < indices.length; i++) {
    let index = indices[i];
    index = Number.isInteger(index) ? index : Math.trunc(index) || 0;
    if (index < 0) {
      index += length;
    }
    result[i] = arr[index];
  }
  return result;
}

// node_modules/es-toolkit/dist/array/chunk.mjs
var import_dist4 = __toESM(require_dist(), 1);
var import_dist5 = __toESM(require_dist2(), 1);
var import_dist6 = __toESM(require_dist3(), 1);
function chunk(arr, size) {
  if (!Number.isInteger(size) || size <= 0) {
    throw new Error("Size must be an integer greater than zero.");
  }
  const chunkLength = Math.ceil(arr.length / size);
  const result = Array(chunkLength);
  for (let index = 0; index < chunkLength; index++) {
    const start = index * size;
    const end = start + size;
    result[index] = arr.slice(start, end);
  }
  return result;
}

// node_modules/es-toolkit/dist/array/compact.mjs
var import_dist7 = __toESM(require_dist(), 1);
var import_dist8 = __toESM(require_dist2(), 1);
var import_dist9 = __toESM(require_dist3(), 1);
function compact(arr) {
  const result = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (item) {
      result.push(item);
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/array/countBy.mjs
var import_dist10 = __toESM(require_dist(), 1);
var import_dist11 = __toESM(require_dist2(), 1);
var import_dist12 = __toESM(require_dist3(), 1);
function countBy(arr, mapper) {
  const result = {};
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = mapper(item);
    result[key] = (result[key] ?? 0) + 1;
  }
  return result;
}

// node_modules/es-toolkit/dist/array/difference.mjs
var import_dist13 = __toESM(require_dist(), 1);
var import_dist14 = __toESM(require_dist2(), 1);
var import_dist15 = __toESM(require_dist3(), 1);
function difference(firstArr, secondArr) {
  const secondSet = new Set(secondArr);
  return firstArr.filter((item) => !secondSet.has(item));
}

// node_modules/es-toolkit/dist/array/differenceBy.mjs
var import_dist16 = __toESM(require_dist(), 1);
var import_dist17 = __toESM(require_dist2(), 1);
var import_dist18 = __toESM(require_dist3(), 1);
function differenceBy(firstArr, secondArr, mapper) {
  const mappedSecondSet = new Set(secondArr.map((item) => mapper(item)));
  return firstArr.filter((item) => {
    return !mappedSecondSet.has(mapper(item));
  });
}

// node_modules/es-toolkit/dist/array/differenceWith.mjs
var import_dist19 = __toESM(require_dist(), 1);
var import_dist20 = __toESM(require_dist2(), 1);
var import_dist21 = __toESM(require_dist3(), 1);
function differenceWith(firstArr, secondArr, areItemsEqual) {
  return firstArr.filter((firstItem) => {
    return secondArr.every((secondItem) => {
      return !areItemsEqual(firstItem, secondItem);
    });
  });
}

// node_modules/es-toolkit/dist/array/drop.mjs
var import_dist22 = __toESM(require_dist(), 1);
var import_dist23 = __toESM(require_dist2(), 1);
var import_dist24 = __toESM(require_dist3(), 1);
function drop(arr, itemsCount) {
  itemsCount = Math.max(itemsCount, 0);
  return arr.slice(itemsCount);
}

// node_modules/es-toolkit/dist/array/dropRight.mjs
var import_dist25 = __toESM(require_dist(), 1);
var import_dist26 = __toESM(require_dist2(), 1);
var import_dist27 = __toESM(require_dist3(), 1);
function dropRight(arr, itemsCount) {
  itemsCount = Math.min(-itemsCount, 0);
  if (itemsCount === 0) {
    return arr.slice();
  }
  return arr.slice(0, itemsCount);
}

// node_modules/es-toolkit/dist/array/dropRightWhile.mjs
var import_dist28 = __toESM(require_dist(), 1);
var import_dist29 = __toESM(require_dist2(), 1);
var import_dist30 = __toESM(require_dist3(), 1);
function dropRightWhile(arr, canContinueDropping) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (!canContinueDropping(arr[i], i, arr)) {
      return arr.slice(0, i + 1);
    }
  }
  return [];
}

// node_modules/es-toolkit/dist/array/dropWhile.mjs
var import_dist31 = __toESM(require_dist(), 1);
var import_dist32 = __toESM(require_dist2(), 1);
var import_dist33 = __toESM(require_dist3(), 1);
function dropWhile(arr, canContinueDropping) {
  const dropEndIndex = arr.findIndex((item, index, arr2) => !canContinueDropping(item, index, arr2));
  if (dropEndIndex === -1) {
    return [];
  }
  return arr.slice(dropEndIndex);
}

// node_modules/es-toolkit/dist/array/fill.mjs
var import_dist34 = __toESM(require_dist(), 1);
var import_dist35 = __toESM(require_dist2(), 1);
var import_dist36 = __toESM(require_dist3(), 1);
function fill(array, value, start = 0, end = array.length) {
  const length = array.length;
  const finalStart = Math.max(start >= 0 ? start : length + start, 0);
  const finalEnd = Math.min(end >= 0 ? end : length + end, length);
  for (let i = finalStart; i < finalEnd; i++) {
    array[i] = value;
  }
  return array;
}

// node_modules/es-toolkit/dist/array/flatMap.mjs
var import_dist40 = __toESM(require_dist(), 1);
var import_dist41 = __toESM(require_dist2(), 1);
var import_dist42 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/array/flatten.mjs
var import_dist37 = __toESM(require_dist(), 1);
var import_dist38 = __toESM(require_dist2(), 1);
var import_dist39 = __toESM(require_dist3(), 1);
function flatten(arr, depth = 1) {
  const result = [];
  const flooredDepth = Math.floor(depth);
  const recursive = (arr2, currentDepth) => {
    for (let i = 0; i < arr2.length; i++) {
      const item = arr2[i];
      if (Array.isArray(item) && currentDepth < flooredDepth) {
        recursive(item, currentDepth + 1);
      } else {
        result.push(item);
      }
    }
  };
  recursive(arr, 0);
  return result;
}

// node_modules/es-toolkit/dist/array/flatMap.mjs
function flatMap(arr, iteratee, depth = 1) {
  return flatten(arr.map((item) => iteratee(item)), depth);
}

// node_modules/es-toolkit/dist/array/flatMapDeep.mjs
var import_dist46 = __toESM(require_dist(), 1);
var import_dist47 = __toESM(require_dist2(), 1);
var import_dist48 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/array/flattenDeep.mjs
var import_dist43 = __toESM(require_dist(), 1);
var import_dist44 = __toESM(require_dist2(), 1);
var import_dist45 = __toESM(require_dist3(), 1);
function flattenDeep(arr) {
  return flatten(arr, Infinity);
}

// node_modules/es-toolkit/dist/array/flatMapDeep.mjs
function flatMapDeep(arr, iteratee) {
  return flattenDeep(arr.map((item) => iteratee(item)));
}

// node_modules/es-toolkit/dist/array/forEachRight.mjs
var import_dist49 = __toESM(require_dist(), 1);
var import_dist50 = __toESM(require_dist2(), 1);
var import_dist51 = __toESM(require_dist3(), 1);
function forEachRight(arr, callback) {
  for (let i = arr.length - 1; i >= 0; i--) {
    const element = arr[i];
    callback(element, i, arr);
  }
}

// node_modules/es-toolkit/dist/array/groupBy.mjs
var import_dist52 = __toESM(require_dist(), 1);
var import_dist53 = __toESM(require_dist2(), 1);
var import_dist54 = __toESM(require_dist3(), 1);
function groupBy(arr, getKeyFromItem) {
  const result = {};
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = getKeyFromItem(item);
    if (!Object.hasOwn(result, key)) {
      result[key] = [];
    }
    result[key].push(item);
  }
  return result;
}

// node_modules/es-toolkit/dist/array/head.mjs
var import_dist55 = __toESM(require_dist(), 1);
var import_dist56 = __toESM(require_dist2(), 1);
var import_dist57 = __toESM(require_dist3(), 1);
function head(arr) {
  return arr[0];
}

// node_modules/es-toolkit/dist/array/initial.mjs
var import_dist58 = __toESM(require_dist(), 1);
var import_dist59 = __toESM(require_dist2(), 1);
var import_dist60 = __toESM(require_dist3(), 1);
function initial(arr) {
  return arr.slice(0, -1);
}

// node_modules/es-toolkit/dist/array/intersection.mjs
var import_dist61 = __toESM(require_dist(), 1);
var import_dist62 = __toESM(require_dist2(), 1);
var import_dist63 = __toESM(require_dist3(), 1);
function intersection(firstArr, secondArr) {
  const secondSet = new Set(secondArr);
  return firstArr.filter((item) => {
    return secondSet.has(item);
  });
}

// node_modules/es-toolkit/dist/array/intersectionBy.mjs
var import_dist64 = __toESM(require_dist(), 1);
var import_dist65 = __toESM(require_dist2(), 1);
var import_dist66 = __toESM(require_dist3(), 1);
function intersectionBy(firstArr, secondArr, mapper) {
  const mappedSecondSet = new Set(secondArr.map(mapper));
  return firstArr.filter((item) => mappedSecondSet.has(mapper(item)));
}

// node_modules/es-toolkit/dist/array/intersectionWith.mjs
var import_dist67 = __toESM(require_dist(), 1);
var import_dist68 = __toESM(require_dist2(), 1);
var import_dist69 = __toESM(require_dist3(), 1);
function intersectionWith(firstArr, secondArr, areItemsEqual) {
  return firstArr.filter((firstItem) => {
    return secondArr.some((secondItem) => {
      return areItemsEqual(firstItem, secondItem);
    });
  });
}

// node_modules/es-toolkit/dist/array/isSubset.mjs
var import_dist70 = __toESM(require_dist(), 1);
var import_dist71 = __toESM(require_dist2(), 1);
var import_dist72 = __toESM(require_dist3(), 1);
function isSubset(superset, subset) {
  return difference(subset, superset).length === 0;
}

// node_modules/es-toolkit/dist/array/isSubsetWith.mjs
var import_dist73 = __toESM(require_dist(), 1);
var import_dist74 = __toESM(require_dist2(), 1);
var import_dist75 = __toESM(require_dist3(), 1);
function isSubsetWith(superset, subset, areItemsEqual) {
  return differenceWith(subset, superset, areItemsEqual).length === 0;
}

// node_modules/es-toolkit/dist/array/keyBy.mjs
var import_dist76 = __toESM(require_dist(), 1);
var import_dist77 = __toESM(require_dist2(), 1);
var import_dist78 = __toESM(require_dist3(), 1);
function keyBy(arr, getKeyFromItem) {
  const result = {};
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = getKeyFromItem(item);
    result[key] = item;
  }
  return result;
}

// node_modules/es-toolkit/dist/array/last.mjs
var import_dist79 = __toESM(require_dist(), 1);
var import_dist80 = __toESM(require_dist2(), 1);
var import_dist81 = __toESM(require_dist3(), 1);
function last(arr) {
  return arr[arr.length - 1];
}

// node_modules/es-toolkit/dist/array/maxBy.mjs
var import_dist82 = __toESM(require_dist(), 1);
var import_dist83 = __toESM(require_dist2(), 1);
var import_dist84 = __toESM(require_dist3(), 1);
function maxBy(items, getValue) {
  if (items.length === 0) {
    return void 0;
  }
  let maxElement = items[0];
  let max = getValue(maxElement);
  for (let i = 1; i < items.length; i++) {
    const element = items[i];
    const value = getValue(element);
    if (value > max) {
      max = value;
      maxElement = element;
    }
  }
  return maxElement;
}

// node_modules/es-toolkit/dist/array/minBy.mjs
var import_dist85 = __toESM(require_dist(), 1);
var import_dist86 = __toESM(require_dist2(), 1);
var import_dist87 = __toESM(require_dist3(), 1);
function minBy(items, getValue) {
  if (items.length === 0) {
    return void 0;
  }
  let minElement = items[0];
  let min = getValue(minElement);
  for (let i = 1; i < items.length; i++) {
    const element = items[i];
    const value = getValue(element);
    if (value < min) {
      min = value;
      minElement = element;
    }
  }
  return minElement;
}

// node_modules/es-toolkit/dist/array/orderBy.mjs
var import_dist91 = __toESM(require_dist(), 1);
var import_dist92 = __toESM(require_dist2(), 1);
var import_dist93 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/_internal/compareValues.mjs
var import_dist88 = __toESM(require_dist(), 1);
var import_dist89 = __toESM(require_dist2(), 1);
var import_dist90 = __toESM(require_dist3(), 1);
function compareValues(a, b, order) {
  if (a < b) {
    return order === "asc" ? -1 : 1;
  }
  if (a > b) {
    return order === "asc" ? 1 : -1;
  }
  return 0;
}

// node_modules/es-toolkit/dist/array/orderBy.mjs
function orderBy(arr, criteria, orders) {
  return arr.slice().sort((a, b) => {
    const ordersLength = orders.length;
    for (let i = 0; i < criteria.length; i++) {
      const order = ordersLength > i ? orders[i] : orders[ordersLength - 1];
      const criterion = criteria[i];
      const criterionIsFunction = typeof criterion === "function";
      const valueA = criterionIsFunction ? criterion(a) : a[criterion];
      const valueB = criterionIsFunction ? criterion(b) : b[criterion];
      const result = compareValues(valueA, valueB, order);
      if (result !== 0) {
        return result;
      }
    }
    return 0;
  });
}

// node_modules/es-toolkit/dist/array/partition.mjs
var import_dist94 = __toESM(require_dist(), 1);
var import_dist95 = __toESM(require_dist2(), 1);
var import_dist96 = __toESM(require_dist3(), 1);
function partition(arr, isInTruthy) {
  const truthy = [];
  const falsy = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (isInTruthy(item)) {
      truthy.push(item);
    } else {
      falsy.push(item);
    }
  }
  return [truthy, falsy];
}

// node_modules/es-toolkit/dist/array/pull.mjs
var import_dist97 = __toESM(require_dist(), 1);
var import_dist98 = __toESM(require_dist2(), 1);
var import_dist99 = __toESM(require_dist3(), 1);
function pull(arr, valuesToRemove) {
  const valuesSet = new Set(valuesToRemove);
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if (valuesSet.has(arr[i])) {
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return arr;
}

// node_modules/es-toolkit/dist/array/pullAt.mjs
var import_dist100 = __toESM(require_dist(), 1);
var import_dist101 = __toESM(require_dist2(), 1);
var import_dist102 = __toESM(require_dist3(), 1);
function pullAt(arr, indicesToRemove) {
  const removed = at(arr, indicesToRemove);
  const indices = new Set(indicesToRemove.slice().sort((x, y) => y - x));
  for (const index of indices) {
    arr.splice(index, 1);
  }
  return removed;
}

// node_modules/es-toolkit/dist/array/remove.mjs
var import_dist103 = __toESM(require_dist(), 1);
var import_dist104 = __toESM(require_dist2(), 1);
var import_dist105 = __toESM(require_dist3(), 1);
function remove(arr, shouldRemoveElement) {
  const originalArr = arr.slice();
  const removed = [];
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if (shouldRemoveElement(arr[i], i, originalArr)) {
      removed.push(arr[i]);
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return removed;
}

// node_modules/es-toolkit/dist/array/sample.mjs
var import_dist106 = __toESM(require_dist(), 1);
var import_dist107 = __toESM(require_dist2(), 1);
var import_dist108 = __toESM(require_dist3(), 1);
function sample(arr) {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
}

// node_modules/es-toolkit/dist/array/sampleSize.mjs
var import_dist115 = __toESM(require_dist(), 1);
var import_dist116 = __toESM(require_dist2(), 1);
var import_dist117 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/math/randomInt.mjs
var import_dist112 = __toESM(require_dist(), 1);
var import_dist113 = __toESM(require_dist2(), 1);
var import_dist114 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/math/random.mjs
var import_dist109 = __toESM(require_dist(), 1);
var import_dist110 = __toESM(require_dist2(), 1);
var import_dist111 = __toESM(require_dist3(), 1);
function random(minimum, maximum) {
  if (maximum == null) {
    maximum = minimum;
    minimum = 0;
  }
  if (minimum >= maximum) {
    throw new Error("Invalid input: The maximum value must be greater than the minimum value.");
  }
  return Math.random() * (maximum - minimum) + minimum;
}

// node_modules/es-toolkit/dist/math/randomInt.mjs
function randomInt(minimum, maximum) {
  return Math.floor(random(minimum, maximum));
}

// node_modules/es-toolkit/dist/array/sampleSize.mjs
function sampleSize(array, size) {
  if (size > array.length) {
    throw new Error("Size must be less than or equal to the length of array.");
  }
  const result = new Array(size);
  const selected = /* @__PURE__ */ new Set();
  for (let step = array.length - size, resultIndex = 0; step < array.length; step++, resultIndex++) {
    let index = randomInt(0, step + 1);
    if (selected.has(index)) {
      index = step;
    }
    selected.add(index);
    result[resultIndex] = array[index];
  }
  return result;
}

// node_modules/es-toolkit/dist/array/shuffle.mjs
var import_dist118 = __toESM(require_dist(), 1);
var import_dist119 = __toESM(require_dist2(), 1);
var import_dist120 = __toESM(require_dist3(), 1);
function shuffle(arr) {
  const result = arr.slice();
  for (let i = result.length - 1; i >= 1; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  return result;
}

// node_modules/es-toolkit/dist/array/sortBy.mjs
var import_dist121 = __toESM(require_dist(), 1);
var import_dist122 = __toESM(require_dist2(), 1);
var import_dist123 = __toESM(require_dist3(), 1);
function sortBy(arr, criteria) {
  return orderBy(arr, criteria, ["asc"]);
}

// node_modules/es-toolkit/dist/array/tail.mjs
var import_dist124 = __toESM(require_dist(), 1);
var import_dist125 = __toESM(require_dist2(), 1);
var import_dist126 = __toESM(require_dist3(), 1);
function tail(arr) {
  return arr.slice(1);
}

// node_modules/es-toolkit/dist/array/take.mjs
var import_dist139 = __toESM(require_dist(), 1);
var import_dist140 = __toESM(require_dist2(), 1);
var import_dist141 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/util/toInteger.mjs
var import_dist136 = __toESM(require_dist(), 1);
var import_dist137 = __toESM(require_dist2(), 1);
var import_dist138 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/util/toFinite.mjs
var import_dist133 = __toESM(require_dist(), 1);
var import_dist134 = __toESM(require_dist2(), 1);
var import_dist135 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/util/toNumber.mjs
var import_dist130 = __toESM(require_dist(), 1);
var import_dist131 = __toESM(require_dist2(), 1);
var import_dist132 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/predicate/isSymbol.mjs
var import_dist127 = __toESM(require_dist(), 1);
var import_dist128 = __toESM(require_dist2(), 1);
var import_dist129 = __toESM(require_dist3(), 1);
function isSymbol(value) {
  return typeof value === "symbol" || value instanceof Symbol;
}

// node_modules/es-toolkit/dist/compat/util/toNumber.mjs
function toNumber(value) {
  if (isSymbol(value)) {
    return NaN;
  }
  return Number(value);
}

// node_modules/es-toolkit/dist/compat/util/toFinite.mjs
function toFinite(value) {
  if (!value) {
    return value === 0 ? value : 0;
  }
  value = toNumber(value);
  if (value === Infinity || value === -Infinity) {
    const sign = value < 0 ? -1 : 1;
    return sign * Number.MAX_VALUE;
  }
  return value === value ? value : 0;
}

// node_modules/es-toolkit/dist/compat/util/toInteger.mjs
function toInteger(value) {
  const finite = toFinite(value);
  const remainder = finite % 1;
  return remainder ? finite - remainder : finite;
}

// node_modules/es-toolkit/dist/array/take.mjs
function take(arr, count, guard) {
  count = guard || count === void 0 ? 1 : toInteger(count);
  return arr.slice(0, count);
}

// node_modules/es-toolkit/dist/array/takeRight.mjs
var import_dist142 = __toESM(require_dist(), 1);
var import_dist143 = __toESM(require_dist2(), 1);
var import_dist144 = __toESM(require_dist3(), 1);
function takeRight(arr, count = 1, guard) {
  count = guard || count === void 0 ? 1 : toInteger(count);
  if (count <= 0 || arr == null || arr.length === 0) {
    return [];
  }
  return arr.slice(-count);
}

// node_modules/es-toolkit/dist/array/takeRightWhile.mjs
var import_dist145 = __toESM(require_dist(), 1);
var import_dist146 = __toESM(require_dist2(), 1);
var import_dist147 = __toESM(require_dist3(), 1);
function takeRightWhile(arr, shouldContinueTaking) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (!shouldContinueTaking(arr[i])) {
      return arr.slice(i + 1);
    }
  }
  return arr.slice();
}

// node_modules/es-toolkit/dist/array/takeWhile.mjs
var import_dist148 = __toESM(require_dist(), 1);
var import_dist149 = __toESM(require_dist2(), 1);
var import_dist150 = __toESM(require_dist3(), 1);
function takeWhile(arr, shouldContinueTaking) {
  const result = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (!shouldContinueTaking(item)) {
      break;
    }
    result.push(item);
  }
  return result;
}

// node_modules/es-toolkit/dist/array/toFilled.mjs
var import_dist151 = __toESM(require_dist(), 1);
var import_dist152 = __toESM(require_dist2(), 1);
var import_dist153 = __toESM(require_dist3(), 1);
function toFilled(arr, value, start = 0, end = arr.length) {
  const length = arr.length;
  const finalStart = Math.max(start >= 0 ? start : length + start, 0);
  const finalEnd = Math.min(end >= 0 ? end : length + end, length);
  const newArr = arr.slice();
  for (let i = finalStart; i < finalEnd; i++) {
    newArr[i] = value;
  }
  return newArr;
}

// node_modules/es-toolkit/dist/array/union.mjs
var import_dist157 = __toESM(require_dist(), 1);
var import_dist158 = __toESM(require_dist2(), 1);
var import_dist159 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/array/uniq.mjs
var import_dist154 = __toESM(require_dist(), 1);
var import_dist155 = __toESM(require_dist2(), 1);
var import_dist156 = __toESM(require_dist3(), 1);
function uniq(arr) {
  return Array.from(new Set(arr));
}

// node_modules/es-toolkit/dist/array/union.mjs
function union(arr1, arr2) {
  return uniq(arr1.concat(arr2));
}

// node_modules/es-toolkit/dist/array/unionBy.mjs
var import_dist163 = __toESM(require_dist(), 1);
var import_dist164 = __toESM(require_dist2(), 1);
var import_dist165 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/array/uniqBy.mjs
var import_dist160 = __toESM(require_dist(), 1);
var import_dist161 = __toESM(require_dist2(), 1);
var import_dist162 = __toESM(require_dist3(), 1);
function uniqBy(arr, mapper) {
  const map = /* @__PURE__ */ new Map();
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = mapper(item);
    if (!map.has(key)) {
      map.set(key, item);
    }
  }
  return Array.from(map.values());
}

// node_modules/es-toolkit/dist/array/unionBy.mjs
function unionBy(arr1, arr2, mapper) {
  return uniqBy(arr1.concat(arr2), mapper);
}

// node_modules/es-toolkit/dist/array/unionWith.mjs
var import_dist169 = __toESM(require_dist(), 1);
var import_dist170 = __toESM(require_dist2(), 1);
var import_dist171 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/array/uniqWith.mjs
var import_dist166 = __toESM(require_dist(), 1);
var import_dist167 = __toESM(require_dist2(), 1);
var import_dist168 = __toESM(require_dist3(), 1);
function uniqWith(arr, areItemsEqual) {
  const result = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const isUniq = result.every((v) => !areItemsEqual(v, item));
    if (isUniq) {
      result.push(item);
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/array/unionWith.mjs
function unionWith(arr1, arr2, areItemsEqual) {
  return uniqWith(arr1.concat(arr2), areItemsEqual);
}

// node_modules/es-toolkit/dist/array/unzip.mjs
var import_dist172 = __toESM(require_dist(), 1);
var import_dist173 = __toESM(require_dist2(), 1);
var import_dist174 = __toESM(require_dist3(), 1);
function unzip(zipped) {
  let maxLen = 0;
  for (let i = 0; i < zipped.length; i++) {
    if (zipped[i].length > maxLen) {
      maxLen = zipped[i].length;
    }
  }
  const result = new Array(maxLen);
  for (let i = 0; i < maxLen; i++) {
    result[i] = new Array(zipped.length);
    for (let j = 0; j < zipped.length; j++) {
      result[i][j] = zipped[j][i];
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/array/unzipWith.mjs
var import_dist175 = __toESM(require_dist(), 1);
var import_dist176 = __toESM(require_dist2(), 1);
var import_dist177 = __toESM(require_dist3(), 1);
function unzipWith(target, iteratee) {
  const maxLength = Math.max(...target.map((innerArray) => innerArray.length));
  const result = new Array(maxLength);
  for (let i = 0; i < maxLength; i++) {
    const group = new Array(target.length);
    for (let j = 0; j < target.length; j++) {
      group[j] = target[j][i];
    }
    result[i] = iteratee(...group);
  }
  return result;
}

// node_modules/es-toolkit/dist/array/windowed.mjs
var import_dist178 = __toESM(require_dist(), 1);
var import_dist179 = __toESM(require_dist2(), 1);
var import_dist180 = __toESM(require_dist3(), 1);
function windowed(arr, size, step = 1, { partialWindows = false } = {}) {
  if (size <= 0 || !Number.isInteger(size)) {
    throw new Error("Size must be a positive integer.");
  }
  if (step <= 0 || !Number.isInteger(step)) {
    throw new Error("Step must be a positive integer.");
  }
  const result = [];
  const end = partialWindows ? arr.length : arr.length - size + 1;
  for (let i = 0; i < end; i += step) {
    result.push(arr.slice(i, i + size));
  }
  return result;
}

// node_modules/es-toolkit/dist/array/without.mjs
var import_dist181 = __toESM(require_dist(), 1);
var import_dist182 = __toESM(require_dist2(), 1);
var import_dist183 = __toESM(require_dist3(), 1);
function without(array, ...values) {
  return difference(array, values);
}

// node_modules/es-toolkit/dist/array/xor.mjs
var import_dist184 = __toESM(require_dist(), 1);
var import_dist185 = __toESM(require_dist2(), 1);
var import_dist186 = __toESM(require_dist3(), 1);
function xor(arr1, arr2) {
  return difference(union(arr1, arr2), intersection(arr1, arr2));
}

// node_modules/es-toolkit/dist/array/xorBy.mjs
var import_dist187 = __toESM(require_dist(), 1);
var import_dist188 = __toESM(require_dist2(), 1);
var import_dist189 = __toESM(require_dist3(), 1);
function xorBy(arr1, arr2, mapper) {
  const union2 = unionBy(arr1, arr2, mapper);
  const intersection2 = intersectionBy(arr1, arr2, mapper);
  return differenceBy(union2, intersection2, mapper);
}

// node_modules/es-toolkit/dist/array/xorWith.mjs
var import_dist190 = __toESM(require_dist(), 1);
var import_dist191 = __toESM(require_dist2(), 1);
var import_dist192 = __toESM(require_dist3(), 1);
function xorWith(arr1, arr2, areElementsEqual) {
  const union2 = unionWith(arr1, arr2, areElementsEqual);
  const intersection2 = intersectionWith(arr1, arr2, areElementsEqual);
  return differenceWith(union2, intersection2, areElementsEqual);
}

// node_modules/es-toolkit/dist/array/zip.mjs
var import_dist193 = __toESM(require_dist(), 1);
var import_dist194 = __toESM(require_dist2(), 1);
var import_dist195 = __toESM(require_dist3(), 1);
function zip(...arrs) {
  let rowCount = 0;
  for (let i = 0; i < arrs.length; i++) {
    if (arrs[i].length > rowCount) {
      rowCount = arrs[i].length;
    }
  }
  const columnCount = arrs.length;
  const result = Array(rowCount);
  for (let i = 0; i < rowCount; ++i) {
    const row = Array(columnCount);
    for (let j = 0; j < columnCount; ++j) {
      row[j] = arrs[j][i];
    }
    result[i] = row;
  }
  return result;
}

// node_modules/es-toolkit/dist/array/zipObject.mjs
var import_dist196 = __toESM(require_dist(), 1);
var import_dist197 = __toESM(require_dist2(), 1);
var import_dist198 = __toESM(require_dist3(), 1);
function zipObject(keys, values) {
  const result = {};
  for (let i = 0; i < keys.length; i++) {
    result[keys[i]] = values[i];
  }
  return result;
}

// node_modules/es-toolkit/dist/array/zipWith.mjs
var import_dist199 = __toESM(require_dist(), 1);
var import_dist200 = __toESM(require_dist2(), 1);
var import_dist201 = __toESM(require_dist3(), 1);
function zipWith(arr1, ...rest2) {
  const arrs = [arr1, ...rest2.slice(0, -1)];
  const combine = rest2[rest2.length - 1];
  const maxIndex = Math.max(...arrs.map((arr) => arr.length));
  const result = Array(maxIndex);
  for (let i = 0; i < maxIndex; i++) {
    const elements = arrs.map((arr) => arr[i]);
    result[i] = combine(...elements);
  }
  return result;
}

// node_modules/es-toolkit/dist/error/AbortError.mjs
var import_dist202 = __toESM(require_dist(), 1);
var import_dist203 = __toESM(require_dist2(), 1);
var import_dist204 = __toESM(require_dist3(), 1);
var AbortError = class extends Error {
  constructor(message = "The operation was aborted") {
    super(message);
    this.name = "AbortError";
  }
};

// node_modules/es-toolkit/dist/error/TimeoutError.mjs
var import_dist205 = __toESM(require_dist(), 1);
var import_dist206 = __toESM(require_dist2(), 1);
var import_dist207 = __toESM(require_dist3(), 1);
var TimeoutError = class extends Error {
  constructor(message = "The operation was timed out") {
    super(message);
    this.name = "TimeoutError";
  }
};

// node_modules/es-toolkit/dist/function/after.mjs
var import_dist208 = __toESM(require_dist(), 1);
var import_dist209 = __toESM(require_dist2(), 1);
var import_dist210 = __toESM(require_dist3(), 1);
function after(n, func) {
  if (!Number.isInteger(n) || n < 0) {
    throw new Error(`n must be a non-negative integer.`);
  }
  let counter = 0;
  return (...args) => {
    if (++counter >= n) {
      return func(...args);
    }
    return void 0;
  };
}

// node_modules/es-toolkit/dist/function/ary.mjs
var import_dist211 = __toESM(require_dist(), 1);
var import_dist212 = __toESM(require_dist2(), 1);
var import_dist213 = __toESM(require_dist3(), 1);
function ary(func, n) {
  return function(...args) {
    return func.apply(this, args.slice(0, n));
  };
}

// node_modules/es-toolkit/dist/function/asyncNoop.mjs
var import_dist214 = __toESM(require_dist(), 1);
var import_dist215 = __toESM(require_dist2(), 1);
var import_dist216 = __toESM(require_dist3(), 1);
async function asyncNoop() {
}

// node_modules/es-toolkit/dist/function/before.mjs
var import_dist217 = __toESM(require_dist(), 1);
var import_dist218 = __toESM(require_dist2(), 1);
var import_dist219 = __toESM(require_dist3(), 1);
function before(n, func) {
  if (!Number.isInteger(n) || n < 0) {
    throw new Error("n must be a non-negative integer.");
  }
  let counter = 0;
  return (...args) => {
    if (++counter < n) {
      return func(...args);
    }
    return void 0;
  };
}

// node_modules/es-toolkit/dist/function/curry.mjs
var import_dist220 = __toESM(require_dist(), 1);
var import_dist221 = __toESM(require_dist2(), 1);
var import_dist222 = __toESM(require_dist3(), 1);
function curry(func) {
  if (func.length === 0 || func.length === 1) {
    return func;
  }
  return function(arg) {
    return makeCurry(func, func.length, [arg]);
  };
}
function makeCurry(origin, argsLength, args) {
  if (args.length === argsLength) {
    return origin(...args);
  } else {
    const next = function(arg) {
      return makeCurry(origin, argsLength, [...args, arg]);
    };
    return next;
  }
}

// node_modules/es-toolkit/dist/function/curryRight.mjs
var import_dist223 = __toESM(require_dist(), 1);
var import_dist224 = __toESM(require_dist2(), 1);
var import_dist225 = __toESM(require_dist3(), 1);
function curryRight(func) {
  if (func.length === 0 || func.length === 1) {
    return func;
  }
  return function(arg) {
    return makeCurryRight(func, func.length, [arg]);
  };
}
function makeCurryRight(origin, argsLength, args) {
  if (args.length === argsLength) {
    return origin(...args);
  } else {
    const next = function(arg) {
      return makeCurryRight(origin, argsLength, [arg, ...args]);
    };
    return next;
  }
}

// node_modules/es-toolkit/dist/function/debounce.mjs
var import_dist226 = __toESM(require_dist(), 1);
var import_dist227 = __toESM(require_dist2(), 1);
var import_dist228 = __toESM(require_dist3(), 1);
function debounce(func, debounceMs, { signal, edges } = {}) {
  let pendingThis = void 0;
  let pendingArgs = null;
  const leading = edges != null && edges.includes("leading");
  const trailing = edges == null || edges.includes("trailing");
  const invoke = () => {
    if (pendingArgs !== null) {
      func.apply(pendingThis, pendingArgs);
      pendingThis = void 0;
      pendingArgs = null;
    }
  };
  const onTimerEnd = () => {
    if (trailing) {
      invoke();
    }
    cancel();
  };
  let timeoutId = null;
  const schedule = () => {
    if (timeoutId != null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = null;
      onTimerEnd();
    }, debounceMs);
  };
  const cancelTimer = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  const cancel = () => {
    cancelTimer();
    pendingThis = void 0;
    pendingArgs = null;
  };
  const flush = () => {
    cancelTimer();
    invoke();
  };
  const debounced = function(...args) {
    if (signal == null ? void 0 : signal.aborted) {
      return;
    }
    pendingThis = this;
    pendingArgs = args;
    const isFirstCall = timeoutId == null;
    schedule();
    if (leading && isFirstCall) {
      invoke();
    }
  };
  debounced.schedule = schedule;
  debounced.cancel = cancel;
  debounced.flush = flush;
  signal == null ? void 0 : signal.addEventListener("abort", cancel, { once: true });
  return debounced;
}

// node_modules/es-toolkit/dist/function/flow.mjs
var import_dist229 = __toESM(require_dist(), 1);
var import_dist230 = __toESM(require_dist2(), 1);
var import_dist231 = __toESM(require_dist3(), 1);
function flow(...funcs) {
  return function(...args) {
    let result = funcs.length ? funcs[0].apply(this, args) : args[0];
    for (let i = 1; i < funcs.length; i++) {
      result = funcs[i].call(this, result);
    }
    return result;
  };
}

// node_modules/es-toolkit/dist/function/flowRight.mjs
var import_dist232 = __toESM(require_dist(), 1);
var import_dist233 = __toESM(require_dist2(), 1);
var import_dist234 = __toESM(require_dist3(), 1);
function flowRight(...funcs) {
  return flow(...funcs.reverse());
}

// node_modules/es-toolkit/dist/function/identity.mjs
var import_dist235 = __toESM(require_dist(), 1);
var import_dist236 = __toESM(require_dist2(), 1);
var import_dist237 = __toESM(require_dist3(), 1);
function identity(x) {
  return x;
}

// node_modules/es-toolkit/dist/function/memoize.mjs
var import_dist238 = __toESM(require_dist(), 1);
var import_dist239 = __toESM(require_dist2(), 1);
var import_dist240 = __toESM(require_dist3(), 1);
function memoize(fn, options = {}) {
  const { cache = /* @__PURE__ */ new Map(), getCacheKey } = options;
  const memoizedFn = function(arg) {
    const key = getCacheKey ? getCacheKey(arg) : arg;
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result = fn.call(this, arg);
    cache.set(key, result);
    return result;
  };
  memoizedFn.cache = cache;
  return memoizedFn;
}

// node_modules/es-toolkit/dist/function/negate.mjs
var import_dist241 = __toESM(require_dist(), 1);
var import_dist242 = __toESM(require_dist2(), 1);
var import_dist243 = __toESM(require_dist3(), 1);
function negate(func) {
  return (...args) => !func(...args);
}

// node_modules/es-toolkit/dist/function/noop.mjs
var import_dist244 = __toESM(require_dist(), 1);
var import_dist245 = __toESM(require_dist2(), 1);
var import_dist246 = __toESM(require_dist3(), 1);
function noop() {
}

// node_modules/es-toolkit/dist/function/once.mjs
var import_dist247 = __toESM(require_dist(), 1);
var import_dist248 = __toESM(require_dist2(), 1);
var import_dist249 = __toESM(require_dist3(), 1);
function once(func) {
  let called = false;
  let cache;
  return function(...args) {
    if (!called) {
      called = true;
      cache = func(...args);
    }
    return cache;
  };
}

// node_modules/es-toolkit/dist/function/partial.mjs
var import_dist250 = __toESM(require_dist(), 1);
var import_dist251 = __toESM(require_dist2(), 1);
var import_dist252 = __toESM(require_dist3(), 1);
function partial(func, ...partialArgs) {
  return partialImpl(func, placeholderSymbol, ...partialArgs);
}
function partialImpl(func, placeholder, ...partialArgs) {
  const partialed = function(...providedArgs) {
    let providedArgsIndex = 0;
    const substitutedArgs = partialArgs.slice().map((arg) => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);
    const remainingArgs = providedArgs.slice(providedArgsIndex);
    return func.apply(this, substitutedArgs.concat(remainingArgs));
  };
  if (func.prototype) {
    partialed.prototype = Object.create(func.prototype);
  }
  return partialed;
}
var placeholderSymbol = Symbol("partial.placeholder");
partial.placeholder = placeholderSymbol;

// node_modules/es-toolkit/dist/function/partialRight.mjs
var import_dist253 = __toESM(require_dist(), 1);
var import_dist254 = __toESM(require_dist2(), 1);
var import_dist255 = __toESM(require_dist3(), 1);
function partialRight(func, ...partialArgs) {
  return partialRightImpl(func, placeholderSymbol2, ...partialArgs);
}
function partialRightImpl(func, placeholder, ...partialArgs) {
  const partialedRight = function(...providedArgs) {
    const placeholderLength = partialArgs.filter((arg) => arg === placeholder).length;
    const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
    const remainingArgs = providedArgs.slice(0, rangeLength);
    let providedArgsIndex = rangeLength;
    const substitutedArgs = partialArgs.slice().map((arg) => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);
    return func.apply(this, remainingArgs.concat(substitutedArgs));
  };
  if (func.prototype) {
    partialedRight.prototype = Object.create(func.prototype);
  }
  return partialedRight;
}
var placeholderSymbol2 = Symbol("partialRight.placeholder");
partialRight.placeholder = placeholderSymbol2;

// node_modules/es-toolkit/dist/function/rest.mjs
var import_dist256 = __toESM(require_dist(), 1);
var import_dist257 = __toESM(require_dist2(), 1);
var import_dist258 = __toESM(require_dist3(), 1);
function rest(func, startIndex = func.length - 1) {
  return function(...args) {
    const rest2 = args.slice(startIndex);
    const params = args.slice(0, startIndex);
    while (params.length < startIndex) {
      params.push(void 0);
    }
    return func.apply(this, [...params, rest2]);
  };
}

// node_modules/es-toolkit/dist/function/retry.mjs
var import_dist262 = __toESM(require_dist(), 1);
var import_dist263 = __toESM(require_dist2(), 1);
var import_dist264 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/promise/delay.mjs
var import_dist259 = __toESM(require_dist(), 1);
var import_dist260 = __toESM(require_dist2(), 1);
var import_dist261 = __toESM(require_dist3(), 1);
function delay(ms, { signal } = {}) {
  return new Promise((resolve, reject) => {
    const abortError = () => {
      reject(new AbortError());
    };
    const abortHandler = () => {
      clearTimeout(timeoutId);
      abortError();
    };
    if (signal == null ? void 0 : signal.aborted) {
      return abortError();
    }
    const timeoutId = setTimeout(() => {
      signal == null ? void 0 : signal.removeEventListener("abort", abortHandler);
      resolve();
    }, ms);
    signal == null ? void 0 : signal.addEventListener("abort", abortHandler, { once: true });
  });
}

// node_modules/es-toolkit/dist/function/retry.mjs
var DEFAULT_DELAY = 0;
var DEFAULT_RETRIES = Number.POSITIVE_INFINITY;
async function retry(func, _options) {
  let delay$1;
  let retries;
  let signal;
  if (typeof _options === "number") {
    delay$1 = DEFAULT_DELAY;
    retries = _options;
    signal = void 0;
  } else {
    delay$1 = (_options == null ? void 0 : _options.delay) ?? DEFAULT_DELAY;
    retries = (_options == null ? void 0 : _options.retries) ?? DEFAULT_RETRIES;
    signal = _options == null ? void 0 : _options.signal;
  }
  let error;
  for (let attempts = 0; attempts < retries; attempts++) {
    if (signal == null ? void 0 : signal.aborted) {
      throw error ?? new Error(`The retry operation was aborted due to an abort signal.`);
    }
    try {
      return await func();
    } catch (err) {
      error = err;
      const currentDelay = typeof delay$1 === "function" ? delay$1(attempts) : delay$1;
      await delay(currentDelay);
    }
  }
  throw error;
}

// node_modules/es-toolkit/dist/function/spread.mjs
var import_dist265 = __toESM(require_dist(), 1);
var import_dist266 = __toESM(require_dist2(), 1);
var import_dist267 = __toESM(require_dist3(), 1);
function spread(func) {
  return function(argsArr) {
    return func.apply(this, argsArr);
  };
}

// node_modules/es-toolkit/dist/function/throttle.mjs
var import_dist268 = __toESM(require_dist(), 1);
var import_dist269 = __toESM(require_dist2(), 1);
var import_dist270 = __toESM(require_dist3(), 1);
function throttle(func, throttleMs, { signal, edges = ["leading", "trailing"] } = {}) {
  let pendingAt = null;
  const debounced = debounce(func, throttleMs, { signal, edges });
  const throttled = function(...args) {
    if (pendingAt == null) {
      pendingAt = Date.now();
    } else {
      if (Date.now() - pendingAt >= throttleMs) {
        pendingAt = Date.now();
        debounced.cancel();
      }
    }
    debounced(...args);
  };
  throttled.cancel = debounced.cancel;
  throttled.flush = debounced.flush;
  return throttled;
}

// node_modules/es-toolkit/dist/function/unary.mjs
var import_dist271 = __toESM(require_dist(), 1);
var import_dist272 = __toESM(require_dist2(), 1);
var import_dist273 = __toESM(require_dist3(), 1);
function unary(func) {
  return ary(func, 1);
}

// node_modules/es-toolkit/dist/math/clamp.mjs
var import_dist274 = __toESM(require_dist(), 1);
var import_dist275 = __toESM(require_dist2(), 1);
var import_dist276 = __toESM(require_dist3(), 1);
function clamp(value, bound1, bound2) {
  if (bound2 == null) {
    return Math.min(value, bound1);
  }
  return Math.min(Math.max(value, bound1), bound2);
}

// node_modules/es-toolkit/dist/math/inRange.mjs
var import_dist277 = __toESM(require_dist(), 1);
var import_dist278 = __toESM(require_dist2(), 1);
var import_dist279 = __toESM(require_dist3(), 1);
function inRange(value, minimum, maximum) {
  if (maximum == null) {
    maximum = minimum;
    minimum = 0;
  }
  if (minimum >= maximum) {
    throw new Error("The maximum value must be greater than the minimum value.");
  }
  return minimum <= value && value < maximum;
}

// node_modules/es-toolkit/dist/math/mean.mjs
var import_dist283 = __toESM(require_dist(), 1);
var import_dist284 = __toESM(require_dist2(), 1);
var import_dist285 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/math/sum.mjs
var import_dist280 = __toESM(require_dist(), 1);
var import_dist281 = __toESM(require_dist2(), 1);
var import_dist282 = __toESM(require_dist3(), 1);
function sum(nums) {
  let result = 0;
  for (let i = 0; i < nums.length; i++) {
    result += nums[i];
  }
  return result;
}

// node_modules/es-toolkit/dist/math/mean.mjs
function mean(nums) {
  return sum(nums) / nums.length;
}

// node_modules/es-toolkit/dist/math/meanBy.mjs
var import_dist286 = __toESM(require_dist(), 1);
var import_dist287 = __toESM(require_dist2(), 1);
var import_dist288 = __toESM(require_dist3(), 1);
function meanBy(items, getValue) {
  const nums = items.map((x) => getValue(x));
  return mean(nums);
}

// node_modules/es-toolkit/dist/math/median.mjs
var import_dist289 = __toESM(require_dist(), 1);
var import_dist290 = __toESM(require_dist2(), 1);
var import_dist291 = __toESM(require_dist3(), 1);
function median(nums) {
  if (nums.length === 0) {
    return NaN;
  }
  const sorted = nums.slice().sort((a, b) => a - b);
  const middleIndex = Math.floor(sorted.length / 2);
  if (sorted.length % 2 === 0) {
    return (sorted[middleIndex - 1] + sorted[middleIndex]) / 2;
  } else {
    return sorted[middleIndex];
  }
}

// node_modules/es-toolkit/dist/math/medianBy.mjs
var import_dist292 = __toESM(require_dist(), 1);
var import_dist293 = __toESM(require_dist2(), 1);
var import_dist294 = __toESM(require_dist3(), 1);
function medianBy(items, getValue) {
  const nums = items.map((x) => getValue(x));
  return median(nums);
}

// node_modules/es-toolkit/dist/math/range.mjs
var import_dist295 = __toESM(require_dist(), 1);
var import_dist296 = __toESM(require_dist2(), 1);
var import_dist297 = __toESM(require_dist3(), 1);
function range(start, end, step = 1) {
  if (end == null) {
    end = start;
    start = 0;
  }
  if (!Number.isInteger(step) || step === 0) {
    throw new Error(`The step value must be a non-zero integer.`);
  }
  const length = Math.max(Math.ceil((end - start) / step), 0);
  const result = new Array(length);
  for (let i = 0; i < length; i++) {
    result[i] = start + i * step;
  }
  return result;
}

// node_modules/es-toolkit/dist/math/rangeRight.mjs
var import_dist298 = __toESM(require_dist(), 1);
var import_dist299 = __toESM(require_dist2(), 1);
var import_dist300 = __toESM(require_dist3(), 1);
function rangeRight(start, end, step = 1) {
  if (end == null) {
    end = start;
    start = 0;
  }
  if (!Number.isInteger(step) || step === 0) {
    throw new Error(`The step value must be a non-zero integer.`);
  }
  const length = Math.max(Math.ceil((end - start) / step), 0);
  const result = new Array(length);
  for (let i = 0; i < length; i++) {
    result[i] = start + (length - i - 1) * step;
  }
  return result;
}

// node_modules/es-toolkit/dist/math/round.mjs
var import_dist301 = __toESM(require_dist(), 1);
var import_dist302 = __toESM(require_dist2(), 1);
var import_dist303 = __toESM(require_dist3(), 1);
function round(value, precision = 0) {
  if (!Number.isInteger(precision)) {
    throw new Error("Precision must be an integer.");
  }
  const multiplier = Math.pow(10, precision);
  return Math.round(value * multiplier) / multiplier;
}

// node_modules/es-toolkit/dist/math/sumBy.mjs
var import_dist304 = __toESM(require_dist(), 1);
var import_dist305 = __toESM(require_dist2(), 1);
var import_dist306 = __toESM(require_dist3(), 1);
function sumBy(items, getValue) {
  let result = 0;
  for (let i = 0; i < items.length; i++) {
    result += getValue(items[i]);
  }
  return result;
}

// node_modules/es-toolkit/dist/object/clone.mjs
var import_dist313 = __toESM(require_dist(), 1);
var import_dist314 = __toESM(require_dist2(), 1);
var import_dist315 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/predicate/isPrimitive.mjs
var import_dist307 = __toESM(require_dist(), 1);
var import_dist308 = __toESM(require_dist2(), 1);
var import_dist309 = __toESM(require_dist3(), 1);
function isPrimitive(value) {
  return value == null || typeof value !== "object" && typeof value !== "function";
}

// node_modules/es-toolkit/dist/predicate/isTypedArray.mjs
var import_dist310 = __toESM(require_dist(), 1);
var import_dist311 = __toESM(require_dist2(), 1);
var import_dist312 = __toESM(require_dist3(), 1);
function isTypedArray(x) {
  return ArrayBuffer.isView(x) && !(x instanceof DataView);
}

// node_modules/es-toolkit/dist/object/clone.mjs
function clone(obj) {
  if (isPrimitive(obj)) {
    return obj;
  }
  if (Array.isArray(obj) || isTypedArray(obj) || obj instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && obj instanceof SharedArrayBuffer) {
    return obj.slice(0);
  }
  const prototype = Object.getPrototypeOf(obj);
  const Constructor = prototype.constructor;
  if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {
    return new Constructor(obj);
  }
  if (obj instanceof RegExp) {
    const newRegExp = new Constructor(obj);
    newRegExp.lastIndex = obj.lastIndex;
    return newRegExp;
  }
  if (obj instanceof DataView) {
    return new Constructor(obj.buffer.slice(0));
  }
  if (obj instanceof Error) {
    const newError = new Constructor(obj.message);
    newError.stack = obj.stack;
    newError.name = obj.name;
    newError.cause = obj.cause;
    return newError;
  }
  if (typeof File !== "undefined" && obj instanceof File) {
    const newFile = new Constructor([obj], obj.name, { type: obj.type, lastModified: obj.lastModified });
    return newFile;
  }
  if (typeof obj === "object") {
    const newObject = Object.create(prototype);
    return Object.assign(newObject, obj);
  }
  return obj;
}

// node_modules/es-toolkit/dist/object/cloneDeep.mjs
var import_dist328 = __toESM(require_dist(), 1);
var import_dist329 = __toESM(require_dist2(), 1);
var import_dist330 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/object/cloneDeepWith.mjs
var import_dist325 = __toESM(require_dist(), 1);
var import_dist326 = __toESM(require_dist2(), 1);
var import_dist327 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs
var import_dist316 = __toESM(require_dist(), 1);
var import_dist317 = __toESM(require_dist2(), 1);
var import_dist318 = __toESM(require_dist3(), 1);
function getSymbols(object) {
  return Object.getOwnPropertySymbols(object).filter((symbol) => Object.prototype.propertyIsEnumerable.call(object, symbol));
}

// node_modules/es-toolkit/dist/compat/_internal/getTag.mjs
var import_dist319 = __toESM(require_dist(), 1);
var import_dist320 = __toESM(require_dist2(), 1);
var import_dist321 = __toESM(require_dist3(), 1);
function getTag(value) {
  if (value == null) {
    return value === void 0 ? "[object Undefined]" : "[object Null]";
  }
  return Object.prototype.toString.call(value);
}

// node_modules/es-toolkit/dist/compat/_internal/tags.mjs
var import_dist322 = __toESM(require_dist(), 1);
var import_dist323 = __toESM(require_dist2(), 1);
var import_dist324 = __toESM(require_dist3(), 1);
var regexpTag = "[object RegExp]";
var stringTag = "[object String]";
var numberTag = "[object Number]";
var booleanTag = "[object Boolean]";
var argumentsTag = "[object Arguments]";
var symbolTag = "[object Symbol]";
var dateTag = "[object Date]";
var mapTag = "[object Map]";
var setTag = "[object Set]";
var arrayTag = "[object Array]";
var functionTag = "[object Function]";
var arrayBufferTag = "[object ArrayBuffer]";
var objectTag = "[object Object]";
var errorTag = "[object Error]";
var dataViewTag = "[object DataView]";
var uint8ArrayTag = "[object Uint8Array]";
var uint8ClampedArrayTag = "[object Uint8ClampedArray]";
var uint16ArrayTag = "[object Uint16Array]";
var uint32ArrayTag = "[object Uint32Array]";
var bigUint64ArrayTag = "[object BigUint64Array]";
var int8ArrayTag = "[object Int8Array]";
var int16ArrayTag = "[object Int16Array]";
var int32ArrayTag = "[object Int32Array]";
var bigInt64ArrayTag = "[object BigInt64Array]";
var float32ArrayTag = "[object Float32Array]";
var float64ArrayTag = "[object Float64Array]";

// node_modules/es-toolkit/dist/object/cloneDeepWith.mjs
function cloneDeepWith(obj, cloneValue) {
  return cloneDeepWithImpl(obj, void 0, obj, /* @__PURE__ */ new Map(), cloneValue);
}
function cloneDeepWithImpl(valueToClone, keyToClone, objectToClone, stack = /* @__PURE__ */ new Map(), cloneValue = void 0) {
  const cloned = cloneValue == null ? void 0 : cloneValue(valueToClone, keyToClone, objectToClone, stack);
  if (cloned != null) {
    return cloned;
  }
  if (isPrimitive(valueToClone)) {
    return valueToClone;
  }
  if (stack.has(valueToClone)) {
    return stack.get(valueToClone);
  }
  if (Array.isArray(valueToClone)) {
    const result = new Array(valueToClone.length);
    stack.set(valueToClone, result);
    for (let i = 0; i < valueToClone.length; i++) {
      result[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);
    }
    if (Object.hasOwn(valueToClone, "index")) {
      result.index = valueToClone.index;
    }
    if (Object.hasOwn(valueToClone, "input")) {
      result.input = valueToClone.input;
    }
    return result;
  }
  if (valueToClone instanceof Date) {
    return new Date(valueToClone.getTime());
  }
  if (valueToClone instanceof RegExp) {
    const result = new RegExp(valueToClone.source, valueToClone.flags);
    result.lastIndex = valueToClone.lastIndex;
    return result;
  }
  if (valueToClone instanceof Map) {
    const result = /* @__PURE__ */ new Map();
    stack.set(valueToClone, result);
    for (const [key, value] of valueToClone) {
      result.set(key, cloneDeepWithImpl(value, key, objectToClone, stack, cloneValue));
    }
    return result;
  }
  if (valueToClone instanceof Set) {
    const result = /* @__PURE__ */ new Set();
    stack.set(valueToClone, result);
    for (const value of valueToClone) {
      result.add(cloneDeepWithImpl(value, void 0, objectToClone, stack, cloneValue));
    }
    return result;
  }
  if (typeof Buffer !== "undefined" && Buffer.isBuffer(valueToClone)) {
    return valueToClone.subarray();
  }
  if (isTypedArray(valueToClone)) {
    const result = new (Object.getPrototypeOf(valueToClone)).constructor(valueToClone.length);
    stack.set(valueToClone, result);
    for (let i = 0; i < valueToClone.length; i++) {
      result[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);
    }
    return result;
  }
  if (valueToClone instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && valueToClone instanceof SharedArrayBuffer) {
    return valueToClone.slice(0);
  }
  if (valueToClone instanceof DataView) {
    const result = new DataView(valueToClone.buffer.slice(0), valueToClone.byteOffset, valueToClone.byteLength);
    stack.set(valueToClone, result);
    copyProperties(result, valueToClone, objectToClone, stack, cloneValue);
    return result;
  }
  if (typeof File !== "undefined" && valueToClone instanceof File) {
    const result = new File([valueToClone], valueToClone.name, {
      type: valueToClone.type
    });
    stack.set(valueToClone, result);
    copyProperties(result, valueToClone, objectToClone, stack, cloneValue);
    return result;
  }
  if (valueToClone instanceof Blob) {
    const result = new Blob([valueToClone], { type: valueToClone.type });
    stack.set(valueToClone, result);
    copyProperties(result, valueToClone, objectToClone, stack, cloneValue);
    return result;
  }
  if (valueToClone instanceof Error) {
    const result = new valueToClone.constructor();
    stack.set(valueToClone, result);
    result.message = valueToClone.message;
    result.name = valueToClone.name;
    result.stack = valueToClone.stack;
    result.cause = valueToClone.cause;
    copyProperties(result, valueToClone, objectToClone, stack, cloneValue);
    return result;
  }
  if (typeof valueToClone === "object" && isCloneableObject(valueToClone)) {
    const result = Object.create(Object.getPrototypeOf(valueToClone));
    stack.set(valueToClone, result);
    copyProperties(result, valueToClone, objectToClone, stack, cloneValue);
    return result;
  }
  return valueToClone;
}
function copyProperties(target, source, objectToClone = target, stack, cloneValue) {
  const keys = [...Object.keys(source), ...getSymbols(source)];
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const descriptor = Object.getOwnPropertyDescriptor(target, key);
    if (descriptor == null || descriptor.writable) {
      target[key] = cloneDeepWithImpl(source[key], key, objectToClone, stack, cloneValue);
    }
  }
}
function isCloneableObject(object) {
  switch (getTag(object)) {
    case argumentsTag:
    case arrayTag:
    case arrayBufferTag:
    case dataViewTag:
    case booleanTag:
    case dateTag:
    case float32ArrayTag:
    case float64ArrayTag:
    case int8ArrayTag:
    case int16ArrayTag:
    case int32ArrayTag:
    case mapTag:
    case numberTag:
    case objectTag:
    case regexpTag:
    case setTag:
    case stringTag:
    case symbolTag:
    case uint8ArrayTag:
    case uint8ClampedArrayTag:
    case uint16ArrayTag:
    case uint32ArrayTag: {
      return true;
    }
    default: {
      return false;
    }
  }
}

// node_modules/es-toolkit/dist/object/cloneDeep.mjs
function cloneDeep(obj) {
  return cloneDeepWithImpl(obj, void 0, obj, /* @__PURE__ */ new Map(), void 0);
}

// node_modules/es-toolkit/dist/object/findKey.mjs
var import_dist331 = __toESM(require_dist(), 1);
var import_dist332 = __toESM(require_dist2(), 1);
var import_dist333 = __toESM(require_dist3(), 1);
function findKey(obj, predicate) {
  const keys = Object.keys(obj);
  return keys.find((key) => predicate(obj[key], key, obj));
}

// node_modules/es-toolkit/dist/object/flattenObject.mjs
var import_dist337 = __toESM(require_dist(), 1);
var import_dist338 = __toESM(require_dist2(), 1);
var import_dist339 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/predicate/isPlainObject.mjs
var import_dist334 = __toESM(require_dist(), 1);
var import_dist335 = __toESM(require_dist2(), 1);
var import_dist336 = __toESM(require_dist3(), 1);
function isPlainObject(value) {
  if (!value || typeof value !== "object") {
    return false;
  }
  const proto = Object.getPrototypeOf(value);
  const hasObjectPrototype = proto === null || proto === Object.prototype || Object.getPrototypeOf(proto) === null;
  if (!hasObjectPrototype) {
    return false;
  }
  return Object.prototype.toString.call(value) === "[object Object]";
}

// node_modules/es-toolkit/dist/object/flattenObject.mjs
function flattenObject(object, { delimiter = "." } = {}) {
  return flattenObjectImpl(object, "", delimiter);
}
function flattenObjectImpl(object, prefix = "", delimiter = ".") {
  const result = {};
  const keys = Object.keys(object);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = object[key];
    const prefixedKey = prefix ? `${prefix}${delimiter}${key}` : key;
    if (isPlainObject(value) && Object.keys(value).length > 0) {
      Object.assign(result, flattenObjectImpl(value, prefixedKey, delimiter));
      continue;
    }
    if (Array.isArray(value)) {
      Object.assign(result, flattenObjectImpl(value, prefixedKey, delimiter));
      continue;
    }
    result[prefixedKey] = value;
  }
  return result;
}

// node_modules/es-toolkit/dist/object/invert.mjs
var import_dist340 = __toESM(require_dist(), 1);
var import_dist341 = __toESM(require_dist2(), 1);
var import_dist342 = __toESM(require_dist3(), 1);
function invert(obj) {
  const result = {};
  const keys = Object.keys(obj);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = obj[key];
    result[value] = key;
  }
  return result;
}

// node_modules/es-toolkit/dist/object/mapKeys.mjs
var import_dist343 = __toESM(require_dist(), 1);
var import_dist344 = __toESM(require_dist2(), 1);
var import_dist345 = __toESM(require_dist3(), 1);
function mapKeys(object, getNewKey) {
  const result = {};
  const keys = Object.keys(object);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = object[key];
    result[getNewKey(value, key, object)] = value;
  }
  return result;
}

// node_modules/es-toolkit/dist/object/mapValues.mjs
var import_dist346 = __toESM(require_dist(), 1);
var import_dist347 = __toESM(require_dist2(), 1);
var import_dist348 = __toESM(require_dist3(), 1);
function mapValues(object, getNewValue) {
  const result = {};
  const keys = Object.keys(object);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = object[key];
    result[key] = getNewValue(value, key, object);
  }
  return result;
}

// node_modules/es-toolkit/dist/object/merge.mjs
var import_dist349 = __toESM(require_dist(), 1);
var import_dist350 = __toESM(require_dist2(), 1);
var import_dist351 = __toESM(require_dist3(), 1);
function merge(target, source) {
  const sourceKeys = Object.keys(source);
  for (let i = 0; i < sourceKeys.length; i++) {
    const key = sourceKeys[i];
    const sourceValue = source[key];
    const targetValue = target[key];
    if (Array.isArray(sourceValue)) {
      if (Array.isArray(targetValue)) {
        target[key] = merge(targetValue, sourceValue);
      } else {
        target[key] = merge([], sourceValue);
      }
    } else if (isPlainObject(sourceValue)) {
      if (isPlainObject(targetValue)) {
        target[key] = merge(targetValue, sourceValue);
      } else {
        target[key] = merge({}, sourceValue);
      }
    } else if (targetValue === void 0 || sourceValue !== void 0) {
      target[key] = sourceValue;
    }
  }
  return target;
}

// node_modules/es-toolkit/dist/object/mergeWith.mjs
var import_dist355 = __toESM(require_dist(), 1);
var import_dist356 = __toESM(require_dist2(), 1);
var import_dist357 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/predicate/isObjectLike.mjs
var import_dist352 = __toESM(require_dist(), 1);
var import_dist353 = __toESM(require_dist2(), 1);
var import_dist354 = __toESM(require_dist3(), 1);
function isObjectLike(value) {
  return typeof value === "object" && value !== null;
}

// node_modules/es-toolkit/dist/object/mergeWith.mjs
function mergeWith(target, source, merge2) {
  const sourceKeys = Object.keys(source);
  for (let i = 0; i < sourceKeys.length; i++) {
    const key = sourceKeys[i];
    const sourceValue = source[key];
    const targetValue = target[key];
    const merged = merge2(targetValue, sourceValue, key, target, source);
    if (merged != null) {
      target[key] = merged;
    } else if (Array.isArray(sourceValue)) {
      target[key] = mergeWith(targetValue ?? [], sourceValue, merge2);
    } else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {
      target[key] = mergeWith(targetValue ?? {}, sourceValue, merge2);
    } else if (targetValue === void 0 || sourceValue !== void 0) {
      target[key] = sourceValue;
    }
  }
  return target;
}

// node_modules/es-toolkit/dist/object/omit.mjs
var import_dist358 = __toESM(require_dist(), 1);
var import_dist359 = __toESM(require_dist2(), 1);
var import_dist360 = __toESM(require_dist3(), 1);
function omit(obj, keys) {
  const result = { ...obj };
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    delete result[key];
  }
  return result;
}

// node_modules/es-toolkit/dist/object/omitBy.mjs
var import_dist361 = __toESM(require_dist(), 1);
var import_dist362 = __toESM(require_dist2(), 1);
var import_dist363 = __toESM(require_dist3(), 1);
function omitBy(obj, shouldOmit) {
  const result = {};
  const keys = Object.keys(obj);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = obj[key];
    if (!shouldOmit(value, key)) {
      result[key] = value;
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/object/pick.mjs
var import_dist364 = __toESM(require_dist(), 1);
var import_dist365 = __toESM(require_dist2(), 1);
var import_dist366 = __toESM(require_dist3(), 1);
function pick(obj, keys) {
  const result = {};
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (Object.hasOwn(obj, key)) {
      result[key] = obj[key];
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/object/pickBy.mjs
var import_dist367 = __toESM(require_dist(), 1);
var import_dist368 = __toESM(require_dist2(), 1);
var import_dist369 = __toESM(require_dist3(), 1);
function pickBy(obj, shouldPick) {
  const result = {};
  const keys = Object.keys(obj);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = obj[key];
    if (shouldPick(value, key)) {
      result[key] = value;
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/object/toCamelCaseKeys.mjs
var import_dist382 = __toESM(require_dist(), 1);
var import_dist383 = __toESM(require_dist2(), 1);
var import_dist384 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/predicate/isArray.mjs
var import_dist370 = __toESM(require_dist(), 1);
var import_dist371 = __toESM(require_dist2(), 1);
var import_dist372 = __toESM(require_dist3(), 1);
function isArray(value) {
  return Array.isArray(value);
}

// node_modules/es-toolkit/dist/string/camelCase.mjs
var import_dist379 = __toESM(require_dist(), 1);
var import_dist380 = __toESM(require_dist2(), 1);
var import_dist381 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/string/capitalize.mjs
var import_dist373 = __toESM(require_dist(), 1);
var import_dist374 = __toESM(require_dist2(), 1);
var import_dist375 = __toESM(require_dist3(), 1);
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// node_modules/es-toolkit/dist/string/words.mjs
var import_dist376 = __toESM(require_dist(), 1);
var import_dist377 = __toESM(require_dist2(), 1);
var import_dist378 = __toESM(require_dist3(), 1);
var CASE_SPLIT_PATTERN = new RegExp("\\p{Lu}?\\p{Ll}+|[0-9]+|\\p{Lu}+(?!\\p{Ll})|\\p{Emoji_Presentation}|\\p{Extended_Pictographic}|\\p{L}+", "gu");
function words(str) {
  return Array.from(str.match(CASE_SPLIT_PATTERN) ?? []);
}

// node_modules/es-toolkit/dist/string/camelCase.mjs
function camelCase(str) {
  const words$1 = words(str);
  if (words$1.length === 0) {
    return "";
  }
  const [first, ...rest2] = words$1;
  return `${first.toLowerCase()}${rest2.map((word) => capitalize(word)).join("")}`;
}

// node_modules/es-toolkit/dist/object/toCamelCaseKeys.mjs
function toCamelCaseKeys(obj) {
  if (isArray(obj)) {
    return obj.map((item) => toCamelCaseKeys(item));
  }
  if (isPlainObject(obj)) {
    const result = {};
    const keys = Object.keys(obj);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const camelKey = camelCase(key);
      const camelCaseKeys = toCamelCaseKeys(obj[key]);
      result[camelKey] = camelCaseKeys;
    }
    return result;
  }
  return obj;
}

// node_modules/es-toolkit/dist/object/toMerged.mjs
var import_dist385 = __toESM(require_dist(), 1);
var import_dist386 = __toESM(require_dist2(), 1);
var import_dist387 = __toESM(require_dist3(), 1);
function toMerged(target, source) {
  return merge(cloneDeep(target), source);
}

// node_modules/es-toolkit/dist/object/toSnakeCaseKeys.mjs
var import_dist394 = __toESM(require_dist(), 1);
var import_dist395 = __toESM(require_dist2(), 1);
var import_dist396 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/predicate/isPlainObject.mjs
var import_dist388 = __toESM(require_dist(), 1);
var import_dist389 = __toESM(require_dist2(), 1);
var import_dist390 = __toESM(require_dist3(), 1);
function isPlainObject2(object) {
  var _a;
  if (typeof object !== "object") {
    return false;
  }
  if (object == null) {
    return false;
  }
  if (Object.getPrototypeOf(object) === null) {
    return true;
  }
  if (Object.prototype.toString.call(object) !== "[object Object]") {
    const tag = object[Symbol.toStringTag];
    if (tag == null) {
      return false;
    }
    const isTagReadonly = !((_a = Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)) == null ? void 0 : _a.writable);
    if (isTagReadonly) {
      return false;
    }
    return object.toString() === `[object ${tag}]`;
  }
  let proto = object;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  return Object.getPrototypeOf(object) === proto;
}

// node_modules/es-toolkit/dist/string/snakeCase.mjs
var import_dist391 = __toESM(require_dist(), 1);
var import_dist392 = __toESM(require_dist2(), 1);
var import_dist393 = __toESM(require_dist3(), 1);
function snakeCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join("_");
}

// node_modules/es-toolkit/dist/object/toSnakeCaseKeys.mjs
function toSnakeCaseKeys(obj) {
  if (isArray(obj)) {
    return obj.map((item) => toSnakeCaseKeys(item));
  }
  if (isPlainObject2(obj)) {
    const result = {};
    const keys = Object.keys(obj);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const snakeKey = snakeCase(key);
      const snakeCaseKeys = toSnakeCaseKeys(obj[key]);
      result[snakeKey] = snakeCaseKeys;
    }
    return result;
  }
  return obj;
}

// node_modules/es-toolkit/dist/predicate/isArrayBuffer.mjs
var import_dist397 = __toESM(require_dist(), 1);
var import_dist398 = __toESM(require_dist2(), 1);
var import_dist399 = __toESM(require_dist3(), 1);
function isArrayBuffer(value) {
  return value instanceof ArrayBuffer;
}

// node_modules/es-toolkit/dist/predicate/isBlob.mjs
var import_dist400 = __toESM(require_dist(), 1);
var import_dist401 = __toESM(require_dist2(), 1);
var import_dist402 = __toESM(require_dist3(), 1);
function isBlob(x) {
  if (typeof Blob === "undefined") {
    return false;
  }
  return x instanceof Blob;
}

// node_modules/es-toolkit/dist/predicate/isBoolean.mjs
var import_dist403 = __toESM(require_dist(), 1);
var import_dist404 = __toESM(require_dist2(), 1);
var import_dist405 = __toESM(require_dist3(), 1);
function isBoolean(x) {
  return typeof x === "boolean";
}

// node_modules/es-toolkit/dist/predicate/isBrowser.mjs
var import_dist406 = __toESM(require_dist(), 1);
var import_dist407 = __toESM(require_dist2(), 1);
var import_dist408 = __toESM(require_dist3(), 1);
function isBrowser() {
  return typeof window !== "undefined" && (window == null ? void 0 : window.document) != null;
}

// node_modules/es-toolkit/dist/predicate/isBuffer.mjs
var import_dist409 = __toESM(require_dist(), 1);
var import_dist410 = __toESM(require_dist2(), 1);
var import_dist411 = __toESM(require_dist3(), 1);
function isBuffer(x) {
  return typeof Buffer !== "undefined" && Buffer.isBuffer(x);
}

// node_modules/es-toolkit/dist/predicate/isDate.mjs
var import_dist412 = __toESM(require_dist(), 1);
var import_dist413 = __toESM(require_dist2(), 1);
var import_dist414 = __toESM(require_dist3(), 1);
function isDate(value) {
  return value instanceof Date;
}

// node_modules/es-toolkit/dist/predicate/isEqual.mjs
var import_dist421 = __toESM(require_dist(), 1);
var import_dist422 = __toESM(require_dist2(), 1);
var import_dist423 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/predicate/isEqualWith.mjs
var import_dist418 = __toESM(require_dist(), 1);
var import_dist419 = __toESM(require_dist2(), 1);
var import_dist420 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/compat/util/eq.mjs
var import_dist415 = __toESM(require_dist(), 1);
var import_dist416 = __toESM(require_dist2(), 1);
var import_dist417 = __toESM(require_dist3(), 1);
function eq(value, other) {
  return value === other || Number.isNaN(value) && Number.isNaN(other);
}

// node_modules/es-toolkit/dist/predicate/isEqualWith.mjs
function isEqualWith(a, b, areValuesEqual) {
  return isEqualWithImpl(a, b, void 0, void 0, void 0, void 0, areValuesEqual);
}
function isEqualWithImpl(a, b, property, aParent, bParent, stack, areValuesEqual) {
  const result = areValuesEqual(a, b, property, aParent, bParent, stack);
  if (result !== void 0) {
    return result;
  }
  if (typeof a === typeof b) {
    switch (typeof a) {
      case "bigint":
      case "string":
      case "boolean":
      case "symbol":
      case "undefined": {
        return a === b;
      }
      case "number": {
        return a === b || Object.is(a, b);
      }
      case "function": {
        return a === b;
      }
      case "object": {
        return areObjectsEqual(a, b, stack, areValuesEqual);
      }
    }
  }
  return areObjectsEqual(a, b, stack, areValuesEqual);
}
function areObjectsEqual(a, b, stack, areValuesEqual) {
  if (Object.is(a, b)) {
    return true;
  }
  let aTag = getTag(a);
  let bTag = getTag(b);
  if (aTag === argumentsTag) {
    aTag = objectTag;
  }
  if (bTag === argumentsTag) {
    bTag = objectTag;
  }
  if (aTag !== bTag) {
    return false;
  }
  switch (aTag) {
    case stringTag:
      return a.toString() === b.toString();
    case numberTag: {
      const x = a.valueOf();
      const y = b.valueOf();
      return eq(x, y);
    }
    case booleanTag:
    case dateTag:
    case symbolTag:
      return Object.is(a.valueOf(), b.valueOf());
    case regexpTag: {
      return a.source === b.source && a.flags === b.flags;
    }
    case functionTag: {
      return a === b;
    }
  }
  stack = stack ?? /* @__PURE__ */ new Map();
  const aStack = stack.get(a);
  const bStack = stack.get(b);
  if (aStack != null && bStack != null) {
    return aStack === b;
  }
  stack.set(a, b);
  stack.set(b, a);
  try {
    switch (aTag) {
      case mapTag: {
        if (a.size !== b.size) {
          return false;
        }
        for (const [key, value] of a.entries()) {
          if (!b.has(key) || !isEqualWithImpl(value, b.get(key), key, a, b, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      case setTag: {
        if (a.size !== b.size) {
          return false;
        }
        const aValues = Array.from(a.values());
        const bValues = Array.from(b.values());
        for (let i = 0; i < aValues.length; i++) {
          const aValue = aValues[i];
          const index = bValues.findIndex((bValue) => {
            return isEqualWithImpl(aValue, bValue, void 0, a, b, stack, areValuesEqual);
          });
          if (index === -1) {
            return false;
          }
          bValues.splice(index, 1);
        }
        return true;
      }
      case arrayTag:
      case uint8ArrayTag:
      case uint8ClampedArrayTag:
      case uint16ArrayTag:
      case uint32ArrayTag:
      case bigUint64ArrayTag:
      case int8ArrayTag:
      case int16ArrayTag:
      case int32ArrayTag:
      case bigInt64ArrayTag:
      case float32ArrayTag:
      case float64ArrayTag: {
        if (typeof Buffer !== "undefined" && Buffer.isBuffer(a) !== Buffer.isBuffer(b)) {
          return false;
        }
        if (a.length !== b.length) {
          return false;
        }
        for (let i = 0; i < a.length; i++) {
          if (!isEqualWithImpl(a[i], b[i], i, a, b, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      case arrayBufferTag: {
        if (a.byteLength !== b.byteLength) {
          return false;
        }
        return areObjectsEqual(new Uint8Array(a), new Uint8Array(b), stack, areValuesEqual);
      }
      case dataViewTag: {
        if (a.byteLength !== b.byteLength || a.byteOffset !== b.byteOffset) {
          return false;
        }
        return areObjectsEqual(new Uint8Array(a), new Uint8Array(b), stack, areValuesEqual);
      }
      case errorTag: {
        return a.name === b.name && a.message === b.message;
      }
      case objectTag: {
        const areEqualInstances = areObjectsEqual(a.constructor, b.constructor, stack, areValuesEqual) || isPlainObject(a) && isPlainObject(b);
        if (!areEqualInstances) {
          return false;
        }
        const aKeys = [...Object.keys(a), ...getSymbols(a)];
        const bKeys = [...Object.keys(b), ...getSymbols(b)];
        if (aKeys.length !== bKeys.length) {
          return false;
        }
        for (let i = 0; i < aKeys.length; i++) {
          const propKey = aKeys[i];
          const aProp = a[propKey];
          if (!Object.hasOwn(b, propKey)) {
            return false;
          }
          const bProp = b[propKey];
          if (!isEqualWithImpl(aProp, bProp, propKey, a, b, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      default: {
        return false;
      }
    }
  } finally {
    stack.delete(a);
    stack.delete(b);
  }
}

// node_modules/es-toolkit/dist/predicate/isEqual.mjs
function isEqual(a, b) {
  return isEqualWith(a, b, noop);
}

// node_modules/es-toolkit/dist/predicate/isError.mjs
var import_dist424 = __toESM(require_dist(), 1);
var import_dist425 = __toESM(require_dist2(), 1);
var import_dist426 = __toESM(require_dist3(), 1);
function isError(value) {
  return value instanceof Error;
}

// node_modules/es-toolkit/dist/predicate/isFile.mjs
var import_dist427 = __toESM(require_dist(), 1);
var import_dist428 = __toESM(require_dist2(), 1);
var import_dist429 = __toESM(require_dist3(), 1);
function isFile(x) {
  if (typeof File === "undefined") {
    return false;
  }
  return isBlob(x) && x instanceof File;
}

// node_modules/es-toolkit/dist/predicate/isFunction.mjs
var import_dist430 = __toESM(require_dist(), 1);
var import_dist431 = __toESM(require_dist2(), 1);
var import_dist432 = __toESM(require_dist3(), 1);
function isFunction(value) {
  return typeof value === "function";
}

// node_modules/es-toolkit/dist/predicate/isJSON.mjs
var import_dist433 = __toESM(require_dist(), 1);
var import_dist434 = __toESM(require_dist2(), 1);
var import_dist435 = __toESM(require_dist3(), 1);
function isJSON(value) {
  if (typeof value !== "string") {
    return false;
  }
  try {
    JSON.parse(value);
    return true;
  } catch {
    return false;
  }
}

// node_modules/es-toolkit/dist/predicate/isJSONValue.mjs
var import_dist436 = __toESM(require_dist(), 1);
var import_dist437 = __toESM(require_dist2(), 1);
var import_dist438 = __toESM(require_dist3(), 1);
function isJSONValue(value) {
  switch (typeof value) {
    case "object": {
      return value === null || isJSONArray(value) || isJSONObject(value);
    }
    case "string":
    case "number":
    case "boolean": {
      return true;
    }
    default: {
      return false;
    }
  }
}
function isJSONArray(value) {
  if (!Array.isArray(value)) {
    return false;
  }
  return value.every((item) => isJSONValue(item));
}
function isJSONObject(obj) {
  if (!isPlainObject(obj)) {
    return false;
  }
  const keys = Reflect.ownKeys(obj);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    const value = obj[key];
    if (typeof key !== "string") {
      return false;
    }
    if (!isJSONValue(value)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/predicate/isLength.mjs
var import_dist439 = __toESM(require_dist(), 1);
var import_dist440 = __toESM(require_dist2(), 1);
var import_dist441 = __toESM(require_dist3(), 1);
function isLength(value) {
  return Number.isSafeInteger(value) && value >= 0;
}

// node_modules/es-toolkit/dist/predicate/isMap.mjs
var import_dist442 = __toESM(require_dist(), 1);
var import_dist443 = __toESM(require_dist2(), 1);
var import_dist444 = __toESM(require_dist3(), 1);
function isMap(value) {
  return value instanceof Map;
}

// node_modules/es-toolkit/dist/predicate/isNil.mjs
var import_dist445 = __toESM(require_dist(), 1);
var import_dist446 = __toESM(require_dist2(), 1);
var import_dist447 = __toESM(require_dist3(), 1);
function isNil(x) {
  return x == null;
}

// node_modules/es-toolkit/dist/predicate/isNode.mjs
var import_dist448 = __toESM(require_dist(), 1);
var import_dist449 = __toESM(require_dist2(), 1);
var import_dist450 = __toESM(require_dist3(), 1);
function isNode() {
  var _a;
  return typeof process !== "undefined" && ((_a = process == null ? void 0 : process.versions) == null ? void 0 : _a.node) != null;
}

// node_modules/es-toolkit/dist/predicate/isNotNil.mjs
var import_dist451 = __toESM(require_dist(), 1);
var import_dist452 = __toESM(require_dist2(), 1);
var import_dist453 = __toESM(require_dist3(), 1);
function isNotNil(x) {
  return x != null;
}

// node_modules/es-toolkit/dist/predicate/isNull.mjs
var import_dist454 = __toESM(require_dist(), 1);
var import_dist455 = __toESM(require_dist2(), 1);
var import_dist456 = __toESM(require_dist3(), 1);
function isNull(x) {
  return x === null;
}

// node_modules/es-toolkit/dist/predicate/isPromise.mjs
var import_dist457 = __toESM(require_dist(), 1);
var import_dist458 = __toESM(require_dist2(), 1);
var import_dist459 = __toESM(require_dist3(), 1);
function isPromise(value) {
  return value instanceof Promise;
}

// node_modules/es-toolkit/dist/predicate/isRegExp.mjs
var import_dist460 = __toESM(require_dist(), 1);
var import_dist461 = __toESM(require_dist2(), 1);
var import_dist462 = __toESM(require_dist3(), 1);
function isRegExp(value) {
  return value instanceof RegExp;
}

// node_modules/es-toolkit/dist/predicate/isSet.mjs
var import_dist463 = __toESM(require_dist(), 1);
var import_dist464 = __toESM(require_dist2(), 1);
var import_dist465 = __toESM(require_dist3(), 1);
function isSet(value) {
  return value instanceof Set;
}

// node_modules/es-toolkit/dist/predicate/isString.mjs
var import_dist466 = __toESM(require_dist(), 1);
var import_dist467 = __toESM(require_dist2(), 1);
var import_dist468 = __toESM(require_dist3(), 1);
function isString(value) {
  return typeof value === "string";
}

// node_modules/es-toolkit/dist/predicate/isSymbol.mjs
var import_dist469 = __toESM(require_dist(), 1);
var import_dist470 = __toESM(require_dist2(), 1);
var import_dist471 = __toESM(require_dist3(), 1);
function isSymbol2(value) {
  return typeof value === "symbol";
}

// node_modules/es-toolkit/dist/predicate/isUndefined.mjs
var import_dist472 = __toESM(require_dist(), 1);
var import_dist473 = __toESM(require_dist2(), 1);
var import_dist474 = __toESM(require_dist3(), 1);
function isUndefined(x) {
  return x === void 0;
}

// node_modules/es-toolkit/dist/predicate/isWeakMap.mjs
var import_dist475 = __toESM(require_dist(), 1);
var import_dist476 = __toESM(require_dist2(), 1);
var import_dist477 = __toESM(require_dist3(), 1);
function isWeakMap(value) {
  return value instanceof WeakMap;
}

// node_modules/es-toolkit/dist/predicate/isWeakSet.mjs
var import_dist478 = __toESM(require_dist(), 1);
var import_dist479 = __toESM(require_dist2(), 1);
var import_dist480 = __toESM(require_dist3(), 1);
function isWeakSet(value) {
  return value instanceof WeakSet;
}

// node_modules/es-toolkit/dist/promise/mutex.mjs
var import_dist484 = __toESM(require_dist(), 1);
var import_dist485 = __toESM(require_dist2(), 1);
var import_dist486 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/promise/semaphore.mjs
var import_dist481 = __toESM(require_dist(), 1);
var import_dist482 = __toESM(require_dist2(), 1);
var import_dist483 = __toESM(require_dist3(), 1);
var Semaphore = class {
  constructor(capacity) {
    __publicField(this, "capacity");
    __publicField(this, "available");
    __publicField(this, "deferredTasks", []);
    this.capacity = capacity;
    this.available = capacity;
  }
  async acquire() {
    if (this.available > 0) {
      this.available--;
      return;
    }
    return new Promise((resolve) => {
      this.deferredTasks.push(resolve);
    });
  }
  release() {
    const deferredTask = this.deferredTasks.shift();
    if (deferredTask != null) {
      deferredTask();
      return;
    }
    if (this.available < this.capacity) {
      this.available++;
    }
  }
};

// node_modules/es-toolkit/dist/promise/mutex.mjs
var Mutex = class {
  constructor() {
    __publicField(this, "semaphore", new Semaphore(1));
  }
  get isLocked() {
    return this.semaphore.available === 0;
  }
  async acquire() {
    return this.semaphore.acquire();
  }
  release() {
    this.semaphore.release();
  }
};

// node_modules/es-toolkit/dist/promise/timeout.mjs
var import_dist487 = __toESM(require_dist(), 1);
var import_dist488 = __toESM(require_dist2(), 1);
var import_dist489 = __toESM(require_dist3(), 1);
async function timeout(ms) {
  await delay(ms);
  throw new TimeoutError();
}

// node_modules/es-toolkit/dist/promise/withTimeout.mjs
var import_dist490 = __toESM(require_dist(), 1);
var import_dist491 = __toESM(require_dist2(), 1);
var import_dist492 = __toESM(require_dist3(), 1);
async function withTimeout(run, ms) {
  return Promise.race([run(), timeout(ms)]);
}

// node_modules/es-toolkit/dist/string/constantCase.mjs
var import_dist493 = __toESM(require_dist(), 1);
var import_dist494 = __toESM(require_dist2(), 1);
var import_dist495 = __toESM(require_dist3(), 1);
function constantCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toUpperCase()).join("_");
}

// node_modules/es-toolkit/dist/string/deburr.mjs
var import_dist496 = __toESM(require_dist(), 1);
var import_dist497 = __toESM(require_dist2(), 1);
var import_dist498 = __toESM(require_dist3(), 1);
var deburrMap = new Map(Object.entries({
  Æ: "Ae",
  Ð: "D",
  Ø: "O",
  Þ: "Th",
  ß: "ss",
  æ: "ae",
  ð: "d",
  ø: "o",
  þ: "th",
  Đ: "D",
  đ: "d",
  Ħ: "H",
  ħ: "h",
  ı: "i",
  Ĳ: "IJ",
  ĳ: "ij",
  ĸ: "k",
  Ŀ: "L",
  ŀ: "l",
  Ł: "L",
  ł: "l",
  ŉ: "'n",
  Ŋ: "N",
  ŋ: "n",
  Œ: "Oe",
  œ: "oe",
  Ŧ: "T",
  ŧ: "t",
  ſ: "s"
}));
function deburr(str) {
  str = str.normalize("NFD");
  let result = "";
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    if (char >= "̀" && char <= "ͯ" || char >= "︠" && char <= "︣") {
      continue;
    }
    result += deburrMap.get(char) ?? char;
  }
  return result;
}

// node_modules/es-toolkit/dist/string/escape.mjs
var import_dist499 = __toESM(require_dist(), 1);
var import_dist500 = __toESM(require_dist2(), 1);
var import_dist501 = __toESM(require_dist3(), 1);
var htmlEscapes = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&#39;"
};
function escape(str) {
  return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
}

// node_modules/es-toolkit/dist/string/escapeRegExp.mjs
var import_dist502 = __toESM(require_dist(), 1);
var import_dist503 = __toESM(require_dist2(), 1);
var import_dist504 = __toESM(require_dist3(), 1);
function escapeRegExp(str) {
  return str.replace(/[\\^$.*+?()[\]{}|]/g, "\\$&");
}

// node_modules/es-toolkit/dist/string/kebabCase.mjs
var import_dist505 = __toESM(require_dist(), 1);
var import_dist506 = __toESM(require_dist2(), 1);
var import_dist507 = __toESM(require_dist3(), 1);
function kebabCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join("-");
}

// node_modules/es-toolkit/dist/string/lowerCase.mjs
var import_dist508 = __toESM(require_dist(), 1);
var import_dist509 = __toESM(require_dist2(), 1);
var import_dist510 = __toESM(require_dist3(), 1);
function lowerCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join(" ");
}

// node_modules/es-toolkit/dist/string/lowerFirst.mjs
var import_dist511 = __toESM(require_dist(), 1);
var import_dist512 = __toESM(require_dist2(), 1);
var import_dist513 = __toESM(require_dist3(), 1);
function lowerFirst(str) {
  return str.substring(0, 1).toLowerCase() + str.substring(1);
}

// node_modules/es-toolkit/dist/string/pad.mjs
var import_dist514 = __toESM(require_dist(), 1);
var import_dist515 = __toESM(require_dist2(), 1);
var import_dist516 = __toESM(require_dist3(), 1);
function pad(str, length, chars = " ") {
  return str.padStart(Math.floor((length - str.length) / 2) + str.length, chars).padEnd(length, chars);
}

// node_modules/es-toolkit/dist/string/pascalCase.mjs
var import_dist517 = __toESM(require_dist(), 1);
var import_dist518 = __toESM(require_dist2(), 1);
var import_dist519 = __toESM(require_dist3(), 1);
function pascalCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => capitalize(word)).join("");
}

// node_modules/es-toolkit/dist/string/reverseString.mjs
var import_dist520 = __toESM(require_dist(), 1);
var import_dist521 = __toESM(require_dist2(), 1);
var import_dist522 = __toESM(require_dist3(), 1);
function reverseString(value) {
  return [...value].reverse().join("");
}

// node_modules/es-toolkit/dist/string/startCase.mjs
var import_dist523 = __toESM(require_dist(), 1);
var import_dist524 = __toESM(require_dist2(), 1);
var import_dist525 = __toESM(require_dist3(), 1);
function startCase(str) {
  const words$1 = words(str.trim());
  let result = "";
  for (let i = 0; i < words$1.length; i++) {
    const word = words$1[i];
    if (result) {
      result += " ";
    }
    result += word[0].toUpperCase() + word.slice(1).toLowerCase();
  }
  return result;
}

// node_modules/es-toolkit/dist/string/trim.mjs
var import_dist532 = __toESM(require_dist(), 1);
var import_dist533 = __toESM(require_dist2(), 1);
var import_dist534 = __toESM(require_dist3(), 1);

// node_modules/es-toolkit/dist/string/trimEnd.mjs
var import_dist526 = __toESM(require_dist(), 1);
var import_dist527 = __toESM(require_dist2(), 1);
var import_dist528 = __toESM(require_dist3(), 1);
function trimEnd(str, chars) {
  if (chars === void 0) {
    return str.trimEnd();
  }
  let endIndex = str.length;
  switch (typeof chars) {
    case "string": {
      if (chars.length !== 1) {
        throw new Error(`The 'chars' parameter should be a single character string.`);
      }
      while (endIndex > 0 && str[endIndex - 1] === chars) {
        endIndex--;
      }
      break;
    }
    case "object": {
      while (endIndex > 0 && chars.includes(str[endIndex - 1])) {
        endIndex--;
      }
    }
  }
  return str.substring(0, endIndex);
}

// node_modules/es-toolkit/dist/string/trimStart.mjs
var import_dist529 = __toESM(require_dist(), 1);
var import_dist530 = __toESM(require_dist2(), 1);
var import_dist531 = __toESM(require_dist3(), 1);
function trimStart(str, chars) {
  if (chars === void 0) {
    return str.trimStart();
  }
  let startIndex = 0;
  switch (typeof chars) {
    case "string": {
      while (startIndex < str.length && str[startIndex] === chars) {
        startIndex++;
      }
      break;
    }
    case "object": {
      while (startIndex < str.length && chars.includes(str[startIndex])) {
        startIndex++;
      }
    }
  }
  return str.substring(startIndex);
}

// node_modules/es-toolkit/dist/string/trim.mjs
function trim(str, chars) {
  if (chars === void 0) {
    return str.trim();
  }
  return trimStart(trimEnd(str, chars), chars);
}

// node_modules/es-toolkit/dist/string/unescape.mjs
var import_dist535 = __toESM(require_dist(), 1);
var import_dist536 = __toESM(require_dist2(), 1);
var import_dist537 = __toESM(require_dist3(), 1);
var htmlUnescapes = {
  "&amp;": "&",
  "&lt;": "<",
  "&gt;": ">",
  "&quot;": '"',
  "&#39;": "'"
};
function unescape(str) {
  return str.replace(/&(?:amp|lt|gt|quot|#(0+)?39);/g, (match) => htmlUnescapes[match] || "'");
}

// node_modules/es-toolkit/dist/string/upperCase.mjs
var import_dist538 = __toESM(require_dist(), 1);
var import_dist539 = __toESM(require_dist2(), 1);
var import_dist540 = __toESM(require_dist3(), 1);
function upperCase(str) {
  const words$1 = words(str);
  let result = "";
  for (let i = 0; i < words$1.length; i++) {
    result += words$1[i].toUpperCase();
    if (i < words$1.length - 1) {
      result += " ";
    }
  }
  return result;
}

// node_modules/es-toolkit/dist/string/upperFirst.mjs
var import_dist541 = __toESM(require_dist(), 1);
var import_dist542 = __toESM(require_dist2(), 1);
var import_dist543 = __toESM(require_dist3(), 1);
function upperFirst(str) {
  return str.substring(0, 1).toUpperCase() + str.substring(1);
}

// node_modules/es-toolkit/dist/util/attempt.mjs
var import_dist544 = __toESM(require_dist(), 1);
var import_dist545 = __toESM(require_dist2(), 1);
var import_dist546 = __toESM(require_dist3(), 1);
function attempt(func) {
  try {
    return [null, func()];
  } catch (error) {
    return [error, null];
  }
}

// node_modules/es-toolkit/dist/util/attemptAsync.mjs
var import_dist547 = __toESM(require_dist(), 1);
var import_dist548 = __toESM(require_dist2(), 1);
var import_dist549 = __toESM(require_dist3(), 1);
async function attemptAsync(func) {
  try {
    const result = await func();
    return [null, result];
  } catch (error) {
    return [error, null];
  }
}

// node_modules/es-toolkit/dist/util/invariant.mjs
var import_dist550 = __toESM(require_dist(), 1);
var import_dist551 = __toESM(require_dist2(), 1);
var import_dist552 = __toESM(require_dist3(), 1);
function invariant(condition, message) {
  if (condition) {
    return;
  }
  if (typeof message === "string") {
    throw new Error(message);
  }
  throw message;
}
export {
  AbortError,
  Mutex,
  Semaphore,
  TimeoutError,
  after,
  ary,
  invariant as assert,
  asyncNoop,
  at,
  attempt,
  attemptAsync,
  before,
  camelCase,
  capitalize,
  chunk,
  clamp,
  clone,
  cloneDeep,
  cloneDeepWith,
  compact,
  constantCase,
  countBy,
  curry,
  curryRight,
  debounce,
  deburr,
  delay,
  difference,
  differenceBy,
  differenceWith,
  drop,
  dropRight,
  dropRightWhile,
  dropWhile,
  escape,
  escapeRegExp,
  fill,
  findKey,
  flatMap,
  flatMapDeep,
  flatten,
  flattenDeep,
  flattenObject,
  flow,
  flowRight,
  forEachRight,
  groupBy,
  head,
  identity,
  inRange,
  initial,
  intersection,
  intersectionBy,
  intersectionWith,
  invariant,
  invert,
  isArrayBuffer,
  isBlob,
  isBoolean,
  isBrowser,
  isBuffer,
  isDate,
  isEqual,
  isEqualWith,
  isError,
  isFile,
  isFunction,
  isJSON,
  isJSONArray,
  isJSONObject,
  isJSONValue,
  isLength,
  isMap,
  isNil,
  isNode,
  isNotNil,
  isNull,
  isPlainObject,
  isPrimitive,
  isPromise,
  isRegExp,
  isSet,
  isString,
  isSubset,
  isSubsetWith,
  isSymbol2 as isSymbol,
  isTypedArray,
  isUndefined,
  isWeakMap,
  isWeakSet,
  kebabCase,
  keyBy,
  last,
  lowerCase,
  lowerFirst,
  mapKeys,
  mapValues,
  maxBy,
  mean,
  meanBy,
  median,
  medianBy,
  memoize,
  merge,
  mergeWith,
  minBy,
  negate,
  noop,
  omit,
  omitBy,
  once,
  orderBy,
  pad,
  partial,
  partialRight,
  partition,
  pascalCase,
  pick,
  pickBy,
  pull,
  pullAt,
  random,
  randomInt,
  range,
  rangeRight,
  remove,
  rest,
  retry,
  reverseString,
  round,
  sample,
  sampleSize,
  shuffle,
  snakeCase,
  sortBy,
  spread,
  startCase,
  sum,
  sumBy,
  tail,
  take,
  takeRight,
  takeRightWhile,
  takeWhile,
  throttle,
  timeout,
  toCamelCaseKeys,
  toFilled,
  toMerged,
  toSnakeCaseKeys,
  trim,
  trimEnd,
  trimStart,
  unary,
  unescape,
  union,
  unionBy,
  unionWith,
  uniq,
  uniqBy,
  uniqWith,
  unzip,
  unzipWith,
  upperCase,
  upperFirst,
  windowed,
  withTimeout,
  without,
  words,
  xor,
  xorBy,
  xorWith,
  zip,
  zipObject,
  zipWith
};
//# sourceMappingURL=es-toolkit.js.map
