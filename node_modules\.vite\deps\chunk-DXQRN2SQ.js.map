{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs"], "sourcesContent": ["import {\n  __name\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    __name(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\nexport {\n  ImperativeState\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA;AAKA,IAAI,mBAAkB,WAAM;AAAA;AAAA;AAAA;AAAA,EAI1B,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EAIA,QAAQ;AACN,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AACF,GALI,OAAO,IAAM,iBAAiB,GATZ;", "names": ["import_dist"]}