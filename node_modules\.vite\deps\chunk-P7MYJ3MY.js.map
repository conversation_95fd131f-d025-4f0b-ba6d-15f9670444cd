{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/chunk-H2D2JQ3I.mjs", "../../@iconify/utils/lib/index.mjs", "../../@iconify/utils/lib/customisations/defaults.mjs", "../../@iconify/utils/lib/icon/defaults.mjs", "../../@iconify/utils/lib/customisations/merge.mjs", "../../@iconify/utils/lib/customisations/bool.mjs", "../../@iconify/utils/lib/customisations/flip.mjs", "../../@iconify/utils/lib/customisations/rotate.mjs", "../../@iconify/utils/lib/icon/name.mjs", "../../@iconify/utils/lib/icon/merge.mjs", "../../@iconify/utils/lib/icon/transformations.mjs", "../../@iconify/utils/lib/icon/square.mjs", "../../@iconify/utils/lib/icon-set/tree.mjs", "../../@iconify/utils/lib/icon-set/parse.mjs", "../../@iconify/utils/lib/icon-set/get-icon.mjs", "../../@iconify/utils/lib/icon-set/validate.mjs", "../../@iconify/utils/lib/icon-set/validate-basic.mjs", "../../@iconify/utils/lib/icon-set/expand.mjs", "../../@iconify/utils/lib/icon-set/minify.mjs", "../../@iconify/utils/lib/icon-set/get-icons.mjs", "../../@iconify/utils/lib/icon-set/convert-info.mjs", "../../@iconify/utils/lib/svg/build.mjs", "../../@iconify/utils/lib/svg/size.mjs", "../../@iconify/utils/lib/svg/defs.mjs", "../../@iconify/utils/lib/svg/id.mjs", "../../@iconify/utils/lib/svg/encode-svg-for-css.mjs", "../../@iconify/utils/lib/svg/url.mjs", "../../@iconify/utils/lib/svg/trim.mjs", "../../@iconify/utils/lib/svg/pretty.mjs", "../../@iconify/utils/lib/svg/html.mjs", "../../@iconify/utils/lib/svg/inner-html.mjs", "../../@iconify/utils/lib/svg/viewbox.mjs", "../../@iconify/utils/lib/svg/parse.mjs", "../../@iconify/utils/lib/colors/keywords.mjs", "../../@iconify/utils/lib/colors/index.mjs", "../../@iconify/utils/lib/css/icon.mjs", "../../@iconify/utils/lib/css/common.mjs", "../../@iconify/utils/lib/css/format.mjs", "../../@iconify/utils/lib/css/icons.mjs", "../../@iconify/utils/lib/loader/utils.mjs", "../../@iconify/utils/lib/loader/custom.mjs", "../../@iconify/utils/lib/loader/modern.mjs", "../../@iconify/utils/lib/loader/loader.mjs", "../../@iconify/utils/lib/emoji/cleanup.mjs", "../../@iconify/utils/lib/emoji/convert.mjs", "../../@iconify/utils/lib/emoji/data.mjs", "../../@iconify/utils/lib/emoji/format.mjs", "../../@iconify/utils/lib/emoji/test/parse.mjs", "../../@iconify/utils/lib/emoji/test/variations.mjs", "../../@iconify/utils/lib/emoji/test/missing.mjs", "../../@iconify/utils/lib/emoji/test/components.mjs", "../../@iconify/utils/lib/emoji/regex/create.mjs", "../../@iconify/utils/lib/emoji/regex/tree.mjs", "../../@iconify/utils/lib/emoji/regex/base.mjs", "../../@iconify/utils/lib/emoji/regex/numbers.mjs", "../../@iconify/utils/lib/emoji/regex/similar.mjs", "../../@iconify/utils/lib/emoji/parse.mjs", "../../@iconify/utils/lib/emoji/test/similar.mjs", "../../@iconify/utils/lib/emoji/test/name.mjs", "../../@iconify/utils/lib/emoji/test/tree.mjs", "../../@iconify/utils/lib/emoji/replace/replace.mjs", "../../@iconify/utils/lib/emoji/replace/find.mjs", "../../@iconify/utils/lib/misc/strings.mjs", "../../@iconify/utils/lib/misc/objects.mjs", "../../@iconify/utils/lib/misc/title.mjs"], "sourcesContent": ["import {\n  __name,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/rendering-util/icons.ts\nimport { getIconData, iconToHTML, iconToSVG, replaceIDs, stringToIcon } from \"@iconify/utils\";\nvar unknownIcon = {\n  body: '<g><rect width=\"80\" height=\"80\" style=\"fill: #087ebf; stroke-width: 0px;\"/><text transform=\"translate(21.16 64.67)\" style=\"fill: #fff; font-family: ArialMT, Arial; font-size: 67.75px;\"><tspan x=\"0\" y=\"0\">?</tspan></text></g>',\n  height: 80,\n  width: 80\n};\nvar iconsStore = /* @__PURE__ */ new Map();\nvar loaderStore = /* @__PURE__ */ new Map();\nvar registerIconPacks = /* @__PURE__ */ __name((iconLoaders) => {\n  for (const iconLoader of iconLoaders) {\n    if (!iconLoader.name) {\n      throw new Error(\n        'Invalid icon loader. Must have a \"name\" property with non-empty string value.'\n      );\n    }\n    log.debug(\"Registering icon pack:\", iconLoader.name);\n    if (\"loader\" in iconLoader) {\n      loaderStore.set(iconLoader.name, iconLoader.loader);\n    } else if (\"icons\" in iconLoader) {\n      iconsStore.set(iconLoader.name, iconLoader.icons);\n    } else {\n      log.error(\"Invalid icon loader:\", iconLoader);\n      throw new Error('Invalid icon loader. Must have either \"icons\" or \"loader\" property.');\n    }\n  }\n}, \"registerIconPacks\");\nvar getRegisteredIconData = /* @__PURE__ */ __name(async (iconName, fallbackPrefix) => {\n  const data = stringToIcon(iconName, true, fallbackPrefix !== void 0);\n  if (!data) {\n    throw new Error(`Invalid icon name: ${iconName}`);\n  }\n  const prefix = data.prefix || fallbackPrefix;\n  if (!prefix) {\n    throw new Error(`Icon name must contain a prefix: ${iconName}`);\n  }\n  let icons = iconsStore.get(prefix);\n  if (!icons) {\n    const loader = loaderStore.get(prefix);\n    if (!loader) {\n      throw new Error(`Icon set not found: ${data.prefix}`);\n    }\n    try {\n      const loaded = await loader();\n      icons = { ...loaded, prefix };\n      iconsStore.set(prefix, icons);\n    } catch (e) {\n      log.error(e);\n      throw new Error(`Failed to load icon set: ${data.prefix}`);\n    }\n  }\n  const iconData = getIconData(icons, data.name);\n  if (!iconData) {\n    throw new Error(`Icon not found: ${iconName}`);\n  }\n  return iconData;\n}, \"getRegisteredIconData\");\nvar getIconSVG = /* @__PURE__ */ __name(async (iconName, customisations) => {\n  let iconData;\n  try {\n    iconData = await getRegisteredIconData(iconName, customisations?.fallbackPrefix);\n  } catch (e) {\n    log.error(e);\n    iconData = unknownIcon;\n  }\n  const renderData = iconToSVG(iconData, customisations);\n  const svg = iconToHTML(replaceIDs(renderData.body), renderData.attributes);\n  return svg;\n}, \"getIconSVG\");\n\nexport {\n  unknownIcon,\n  registerIconPacks,\n  getIconSVG\n};\n", "export { defaultIconCustomisations, defaultIconSizeCustomisations } from './customisations/defaults.mjs';\nexport { mergeCustomisations } from './customisations/merge.mjs';\nexport { toBoolean } from './customisations/bool.mjs';\nexport { flipFromString } from './customisations/flip.mjs';\nexport { rotateFromString } from './customisations/rotate.mjs';\nexport { matchIconName, stringToIcon, validateIconName } from './icon/name.mjs';\nexport { mergeIconData } from './icon/merge.mjs';\nexport { mergeIconTransformations } from './icon/transformations.mjs';\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations } from './icon/defaults.mjs';\nexport { makeIconSquare } from './icon/square.mjs';\nexport { getIconsTree } from './icon-set/tree.mjs';\nexport { parseIconSet, parseIconSetAsync } from './icon-set/parse.mjs';\nexport { validateIconSet } from './icon-set/validate.mjs';\nexport { quicklyValidateIconSet } from './icon-set/validate-basic.mjs';\nexport { expandIconSet } from './icon-set/expand.mjs';\nexport { minifyIconSet } from './icon-set/minify.mjs';\nexport { getIcons } from './icon-set/get-icons.mjs';\nexport { getIconData } from './icon-set/get-icon.mjs';\nexport { convertIconSetInfo } from './icon-set/convert-info.mjs';\nexport { iconToSVG } from './svg/build.mjs';\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent } from './svg/defs.mjs';\nexport { replaceIDs } from './svg/id.mjs';\nexport { calculateSize } from './svg/size.mjs';\nexport { encodeSvgForCss } from './svg/encode-svg-for-css.mjs';\nexport { trimSVG } from './svg/trim.mjs';\nexport { prettifySVG } from './svg/pretty.mjs';\nexport { iconToHTML } from './svg/html.mjs';\nexport { svgToData, svgToURL } from './svg/url.mjs';\nexport { cleanUpInnerHTML } from './svg/inner-html.mjs';\nexport { getSVGViewBox } from './svg/viewbox.mjs';\nexport { buildParsedSVG, convertParsedSVG, parseSVGContent } from './svg/parse.mjs';\nexport { colorKeywords } from './colors/keywords.mjs';\nexport { colorToString, compareColors, stringToColor } from './colors/index.mjs';\nexport { getIconCSS, getIconContentCSS } from './css/icon.mjs';\nexport { getIconsCSS, getIconsContentCSS } from './css/icons.mjs';\nexport { mergeIconProps } from './loader/utils.mjs';\nexport { getCustomIcon } from './loader/custom.mjs';\nexport { searchForIcon } from './loader/modern.mjs';\nexport { loadIcon } from './loader/loader.mjs';\nexport { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './emoji/cleanup.mjs';\nexport { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number } from './emoji/convert.mjs';\nexport { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString } from './emoji/format.mjs';\nexport { parseEmojiTestFile } from './emoji/test/parse.mjs';\nexport { getQualifiedEmojiVariations } from './emoji/test/variations.mjs';\nexport { findMissingEmojis } from './emoji/test/missing.mjs';\nexport { createOptimisedRegex, createOptimisedRegexForEmojiSequences } from './emoji/regex/create.mjs';\nexport { prepareEmojiForIconSet, prepareEmojiForIconsList } from './emoji/parse.mjs';\nexport { findAndReplaceEmojisInText } from './emoji/replace/replace.mjs';\nexport { camelToKebab, camelize, pascalize, snakelize } from './misc/strings.mjs';\nexport { commonObjectProps, compareObjects, unmergeObjects } from './misc/objects.mjs';\nexport { sanitiseTitleAttribute } from './misc/title.mjs';\nimport './css/common.mjs';\nimport './css/format.mjs';\nimport 'debug';\nimport './emoji/data.mjs';\nimport './emoji/test/components.mjs';\nimport './emoji/regex/tree.mjs';\nimport './emoji/regex/base.mjs';\nimport './emoji/regex/numbers.mjs';\nimport './emoji/regex/similar.mjs';\nimport './emoji/test/similar.mjs';\nimport './emoji/test/name.mjs';\nimport './emoji/test/tree.mjs';\nimport './emoji/replace/find.mjs';\n", "import { defaultIconTransformations } from '../icon/defaults.mjs';\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ...defaultIconTransformations\n});\n\nexport { defaultIconCustomisations, defaultIconSizeCustomisations };\n", "const defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\nexport { defaultExtendedIconProps, defaultIconDimensions, defaultIconProps, defaultIconTransformations };\n", "import { defaultIconSizeCustomisations } from './defaults.mjs';\nimport '../icon/defaults.mjs';\n\nfunction mergeCustomisations(defaults, item) {\n  const result = {\n    ...defaults\n  };\n  for (const key in item) {\n    const value = item[key];\n    const valueType = typeof value;\n    if (key in defaultIconSizeCustomisations) {\n      if (value === null || value && (valueType === \"string\" || valueType === \"number\")) {\n        result[key] = value;\n      }\n    } else if (valueType === typeof result[key]) {\n      result[key] = key === \"rotate\" ? value % 4 : value;\n    }\n  }\n  return result;\n}\n\nexport { mergeCustomisations };\n", "function toBoolean(name, value, defaultValue) {\n  switch (typeof value) {\n    case \"boolean\":\n      return value;\n    case \"number\":\n      return !!value;\n    case \"string\":\n      switch (value.toLowerCase()) {\n        case \"1\":\n        case \"true\":\n        case name.toLowerCase():\n          return true;\n        case \"0\":\n        case \"false\":\n        case \"\":\n          return false;\n      }\n  }\n  return defaultValue;\n}\n\nexport { toBoolean };\n", "const separator = /[\\s,]+/;\nfunction flipFromString(custom, flip) {\n  flip.split(separator).forEach((str) => {\n    const value = str.trim();\n    switch (value) {\n      case \"horizontal\":\n        custom.hFlip = true;\n        break;\n      case \"vertical\":\n        custom.vFlip = true;\n        break;\n    }\n  });\n}\n\nexport { flipFromString };\n", "function rotateFromString(value, defaultValue = 0) {\n  const units = value.replace(/^-?[0-9.]*/, \"\");\n  function cleanup(value2) {\n    while (value2 < 0) {\n      value2 += 4;\n    }\n    return value2 % 4;\n  }\n  if (units === \"\") {\n    const num = parseInt(value);\n    return isNaN(num) ? 0 : cleanup(num);\n  } else if (units !== value) {\n    let split = 0;\n    switch (units) {\n      case \"%\":\n        split = 25;\n        break;\n      case \"deg\":\n        split = 90;\n    }\n    if (split) {\n      let num = parseFloat(value.slice(0, value.length - units.length));\n      if (isNaN(num)) {\n        return 0;\n      }\n      num = num / split;\n      return num % 1 === 0 ? cleanup(num) : 0;\n    }\n  }\n  return defaultValue;\n}\n\nexport { rotateFromString };\n", "const matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\nexport { matchIconName, stringToIcon, validateIconName };\n", "import { defaultExtendedIconProps, defaultIconTransformations } from './defaults.mjs';\nimport { mergeIconTransformations } from './transformations.mjs';\n\nfunction mergeIconData(parent, child) {\n  const result = mergeIconTransformations(parent, child);\n  for (const key in defaultExtendedIconProps) {\n    if (key in defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\nexport { mergeIconData };\n", "function mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\nexport { mergeIconTransformations };\n", "function makeIconSquare(icon) {\n  if (icon.width !== icon.height) {\n    const max = Math.max(icon.width, icon.height);\n    return {\n      ...icon,\n      width: max,\n      height: max,\n      left: icon.left - (max - icon.width) / 2,\n      top: icon.top - (max - icon.height) / 2\n    };\n  }\n  return icon;\n}\nfunction makeViewBoxSquare(viewBox) {\n  const [left, top, width, height] = viewBox;\n  if (width !== height) {\n    const max = Math.max(width, height);\n    return [left - (max - width) / 2, top - (max - height) / 2, max, max];\n  }\n  return viewBox;\n}\n\nexport { makeIconSquare, makeViewBoxSquare };\n", "function getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\nexport { getIconsTree };\n", "import { internalGetIconData } from './get-icon.mjs';\nimport { getIconsTree } from './tree.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/defaults.mjs';\nimport '../icon/transformations.mjs';\n\nfunction parseIconSet(data, callback) {\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    data.not_found.forEach((name) => {\n      callback(name, null);\n      names.push(name);\n    });\n  }\n  const tree = getIconsTree(data);\n  for (const name in tree) {\n    const item = tree[name];\n    if (item) {\n      callback(name, internalGetIconData(data, name, item));\n      names.push(name);\n    }\n  }\n  return names;\n}\nasync function parseIconSetAsync(data, callback) {\n  const names = [];\n  if (typeof data !== \"object\" || typeof data.icons !== \"object\") {\n    return names;\n  }\n  if (data.not_found instanceof Array) {\n    for (let i = 0; i < data.not_found.length; i++) {\n      const name = data.not_found[i];\n      await callback(name, null);\n      names.push(name);\n    }\n  }\n  const tree = getIconsTree(data);\n  for (const name in tree) {\n    const item = tree[name];\n    if (item) {\n      await callback(name, internalGetIconData(data, name, item));\n      names.push(name);\n    }\n  }\n  return names;\n}\n\nexport { parseIconSet, parseIconSetAsync };\n", "import { mergeIconData } from '../icon/merge.mjs';\nimport { getIconsTree } from './tree.mjs';\nimport '../icon/defaults.mjs';\nimport '../icon/transformations.mjs';\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = mergeIconData(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return mergeIconData(data, currentProps);\n}\nfunction getIconData(data, name) {\n  if (data.icons[name]) {\n    return internalGetIconData(data, name, []);\n  }\n  const tree = getIconsTree(data, [name])[name];\n  return tree ? internalGetIconData(data, name, tree) : null;\n}\n\nexport { getIconData, internalGetIconData };\n", "import { defaultExtendedIconProps } from '../icon/defaults.mjs';\nimport { getIconsTree } from './tree.mjs';\n\nconst matchChar = /^[a-f0-9]+(-[a-f0-9]+)*$/;\nfunction validateIconProps(item, fix, checkOtherProps) {\n  for (const key in item) {\n    const attr = key;\n    const value = item[attr];\n    const type = typeof value;\n    if (type === \"undefined\") {\n      delete item[attr];\n      continue;\n    }\n    const expectedType = typeof defaultExtendedIconProps[attr];\n    if (expectedType !== \"undefined\") {\n      if (type !== expectedType) {\n        if (fix) {\n          delete item[attr];\n          continue;\n        }\n        return attr;\n      }\n      continue;\n    }\n    if (checkOtherProps && type === \"object\") {\n      if (fix) {\n        delete item[attr];\n      } else {\n        return key;\n      }\n    }\n  }\n  return null;\n}\nfunction validateIconSet(obj, options) {\n  const fix = !!(options && options.fix);\n  if (typeof obj !== \"object\" || obj === null || typeof obj.icons !== \"object\" || !obj.icons) {\n    throw new Error(\"Bad icon set\");\n  }\n  const data = obj;\n  if (options && typeof options.prefix === \"string\") {\n    data.prefix = options.prefix;\n  } else if (\n    // Prefix must be a string and not empty\n    typeof data.prefix !== \"string\" || !data.prefix\n  ) {\n    throw new Error(\"Invalid prefix\");\n  }\n  if (options && typeof options.provider === \"string\") {\n    data.provider = options.provider;\n  } else if (data.provider !== void 0) {\n    const value = data.provider;\n    if (typeof value !== \"string\") {\n      if (fix) {\n        delete data.provider;\n      } else {\n        throw new Error(\"Invalid provider\");\n      }\n    }\n  }\n  if (data.aliases !== void 0) {\n    if (typeof data.aliases !== \"object\" || data.aliases === null) {\n      if (fix) {\n        delete data.aliases;\n      } else {\n        throw new Error(\"Invalid aliases list\");\n      }\n    }\n  }\n  const tree = getIconsTree(data);\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in tree) {\n    const treeItem = tree[name];\n    const isAlias = !icons[name];\n    const parentObj = isAlias ? aliases : icons;\n    if (!treeItem) {\n      if (fix) {\n        delete parentObj[name];\n        continue;\n      }\n      throw new Error(`Invalid alias: ${name}`);\n    }\n    if (!name) {\n      if (fix) {\n        delete parentObj[name];\n        continue;\n      }\n      throw new Error(`Invalid icon name: \"${name}\"`);\n    }\n    const item = parentObj[name];\n    if (!isAlias) {\n      if (typeof item.body !== \"string\") {\n        if (fix) {\n          delete parentObj[name];\n          continue;\n        }\n        throw new Error(`Invalid icon: \"${name}\"`);\n      }\n    }\n    const requiredProp = isAlias ? \"parent\" : \"body\";\n    const key = typeof item[requiredProp] !== \"string\" ? requiredProp : validateIconProps(item, fix, true);\n    if (key !== null) {\n      throw new Error(`Invalid property \"${key}\" in \"${name}\"`);\n    }\n  }\n  if (data.not_found !== void 0 && !(data.not_found instanceof Array)) {\n    if (fix) {\n      delete data.not_found;\n    } else {\n      throw new Error(\"Invalid not_found list\");\n    }\n  }\n  if (!Object.keys(data.icons).length && !(data.not_found && data.not_found.length)) {\n    throw new Error(\"Icon set is empty\");\n  }\n  if (fix && !Object.keys(aliases).length) {\n    delete data.aliases;\n  }\n  const failedOptionalProp = validateIconProps(data, false, false);\n  if (failedOptionalProp) {\n    throw new Error(`Invalid value type for \"${failedOptionalProp}\"`);\n  }\n  if (data.chars !== void 0) {\n    if (typeof data.chars !== \"object\" || data.chars === null) {\n      if (fix) {\n        delete data.chars;\n      } else {\n        throw new Error(\"Invalid characters map\");\n      }\n    }\n  }\n  if (typeof data.chars === \"object\") {\n    const chars = data.chars;\n    Object.keys(chars).forEach((char) => {\n      if (!matchChar.exec(char) || typeof chars[char] !== \"string\") {\n        if (fix) {\n          delete chars[char];\n          return;\n        }\n        throw new Error(`Invalid character \"${char}\"`);\n      }\n      const target = chars[char];\n      if (!data.icons[target] && (!data.aliases || !data.aliases[target])) {\n        if (fix) {\n          delete chars[char];\n          return;\n        }\n        throw new Error(\n          `Character \"${char}\" points to missing icon \"${target}\"`\n        );\n      }\n    });\n    if (fix && !Object.keys(data.chars).length) {\n      delete data.chars;\n    }\n  }\n  return data;\n}\n\nexport { matchChar, validateIconSet };\n", "import { defaultIconDimensions, defaultExtendedIconProps } from '../icon/defaults.mjs';\n\nconst optionalPropertyDefaults = {\n  provider: \"\",\n  aliases: {},\n  not_found: {},\n  ...defaultIconDimensions\n};\nfunction checkOptionalProps(item, defaults) {\n  for (const prop in defaults) {\n    if (prop in item && typeof item[prop] !== typeof defaults[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction quicklyValidateIconSet(obj) {\n  if (typeof obj !== \"object\" || obj === null) {\n    return null;\n  }\n  const data = obj;\n  if (typeof data.prefix !== \"string\" || !obj.icons || typeof obj.icons !== \"object\") {\n    return null;\n  }\n  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {\n    return null;\n  }\n  const icons = data.icons;\n  for (const name in icons) {\n    const icon = icons[name];\n    if (\n      // Name cannot be empty\n      !name || // Must have body\n      typeof icon.body !== \"string\" || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  for (const name in aliases) {\n    const icon = aliases[name];\n    const parent = icon.parent;\n    if (\n      // Name cannot be empty\n      !name || // Parent must be set and point to existing icon\n      typeof parent !== \"string\" || !icons[parent] && !aliases[parent] || // Check other props\n      !checkOptionalProps(\n        icon,\n        defaultExtendedIconProps\n      )\n    ) {\n      return null;\n    }\n  }\n  return data;\n}\n\nexport { quicklyValidateIconSet };\n", "import { defaultIconDimensions } from '../icon/defaults.mjs';\n\nfunction expandIconSet(data) {\n  const icons = Object.keys(data.icons);\n  Object.keys(\n    defaultIconDimensions\n  ).forEach((prop) => {\n    if (typeof data[prop] !== typeof defaultIconDimensions[prop]) {\n      return;\n    }\n    const value = data[prop];\n    icons.forEach((name) => {\n      const item = data.icons[name];\n      if (!(prop in item)) {\n        item[prop] = value;\n      }\n    });\n    delete data[prop];\n  });\n}\n\nexport { expandIconSet };\n", "import { defaultIconDimensions } from '../icon/defaults.mjs';\n\nfunction minifyIconSet(data) {\n  const icons = Object.keys(data.icons);\n  Object.keys(\n    defaultIconDimensions\n  ).forEach((prop) => {\n    if (data[prop] === defaultIconDimensions[prop]) {\n      delete data[prop];\n    }\n    const defaultValue = defaultIconDimensions[prop];\n    const propType = typeof defaultValue;\n    const hasMinifiedDefault = typeof data[prop] === propType && data[prop] !== defaultValue;\n    let maxCount = 0;\n    let maxValue = null;\n    const counters = /* @__PURE__ */ new Map();\n    for (let i = 0; i < icons.length; i++) {\n      const item = data.icons[icons[i]];\n      let value;\n      if (typeof item[prop] === propType) {\n        value = item[prop];\n      } else if (hasMinifiedDefault) {\n        value = data[prop];\n      } else {\n        value = defaultIconDimensions[prop];\n      }\n      if (i === 0) {\n        maxCount = 1;\n        maxValue = value;\n        counters.set(value, 1);\n        continue;\n      }\n      if (!counters.has(value)) {\n        counters.set(value, 1);\n        continue;\n      }\n      const count = counters.get(value) + 1;\n      counters.set(value, count);\n      if (count > maxCount) {\n        maxCount = count;\n        maxValue = value;\n      }\n    }\n    const canMinify = maxValue !== null && maxCount > 1;\n    const oldDefault = hasMinifiedDefault ? data[prop] : null;\n    const newDefault = canMinify ? maxValue : oldDefault;\n    if (newDefault === defaultValue) {\n      delete data[prop];\n    } else if (canMinify) {\n      data[prop] = newDefault;\n    }\n    icons.forEach((key) => {\n      const item = data.icons[key];\n      const value = prop in item ? item[prop] : hasMinifiedDefault ? oldDefault : defaultValue;\n      if (value === newDefault || newDefault === null && value === defaultValue) {\n        delete item[prop];\n        return;\n      }\n      if (canMinify && !(prop in item)) {\n        item[prop] = value;\n      }\n    });\n  });\n}\n\nexport { minifyIconSet };\n", "import { defaultIconDimensions } from '../icon/defaults.mjs';\nimport { getIconsTree } from './tree.mjs';\n\nconst propsToCopy = Object.keys(defaultIconDimensions).concat([\n  \"provider\"\n]);\nfunction getIcons(data, names, not_found) {\n  const icons = /* @__PURE__ */ Object.create(null);\n  const aliases = /* @__PURE__ */ Object.create(null);\n  const result = {\n    prefix: data.prefix,\n    icons\n  };\n  const sourceIcons = data.icons;\n  const sourceAliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  if (data.lastModified) {\n    result.lastModified = data.lastModified;\n  }\n  const tree = getIconsTree(data, names);\n  let empty = true;\n  for (const name in tree) {\n    if (!tree[name]) {\n      if (not_found && names.indexOf(name) !== -1) {\n        (result.not_found || (result.not_found = [])).push(name);\n      }\n    } else if (sourceIcons[name]) {\n      icons[name] = {\n        ...sourceIcons[name]\n      };\n      empty = false;\n    } else {\n      aliases[name] = {\n        ...sourceAliases[name]\n      };\n      result.aliases = aliases;\n    }\n  }\n  propsToCopy.forEach((attr) => {\n    if (attr in data) {\n      result[attr] = data[attr];\n    }\n  });\n  return empty && not_found !== true ? null : result;\n}\n\nexport { getIcons, propsToCopy };\n", "const minDisplayHeight = 16;\nconst maxDisplayHeight = 24;\nfunction validateDisplayHeight(value) {\n  while (value < minDisplayHeight) {\n    value *= 2;\n  }\n  while (value > maxDisplayHeight) {\n    value /= 2;\n  }\n  return value === Math.round(value) && value >= minDisplayHeight && value <= maxDisplayHeight ? value : 0;\n}\nfunction convertIconSetInfo(data, expectedPrefix = \"\") {\n  if (typeof data !== \"object\" || data === null) {\n    return null;\n  }\n  const source = data;\n  const getSourceNestedString = (field, key, defaultValue = \"\") => {\n    if (typeof source[field] !== \"object\") {\n      return defaultValue;\n    }\n    const obj = source[field];\n    return typeof obj[key] === \"string\" ? obj[key] : defaultValue;\n  };\n  let name;\n  if (typeof source.name === \"string\") {\n    name = source.name;\n  } else if (typeof source.title === \"string\") {\n    name = source.title;\n  } else {\n    return null;\n  }\n  if (expectedPrefix !== \"\" && typeof source.prefix === \"string\" && source.prefix !== expectedPrefix) {\n    return null;\n  }\n  const info = {\n    name\n  };\n  switch (typeof source.total) {\n    case \"number\":\n      info.total = source.total;\n      break;\n    case \"string\": {\n      const num = parseInt(source.total);\n      if (num > 0) {\n        info.total = num;\n      }\n      break;\n    }\n  }\n  if (typeof source.version === \"string\") {\n    info.version = source.version;\n  }\n  info.author = {\n    name: getSourceNestedString(\n      \"author\",\n      \"name\",\n      typeof source.author === \"string\" ? source.author : \"\"\n    )\n  };\n  if (typeof source.author === \"object\") {\n    const sourceAuthor = source.author;\n    if (typeof sourceAuthor.url === \"string\") {\n      info.author.url = sourceAuthor.url;\n    }\n  }\n  info.license = {\n    title: getSourceNestedString(\n      \"license\",\n      \"title\",\n      typeof source.license === \"string\" ? source.license : \"\"\n    )\n  };\n  if (typeof source.license === \"object\") {\n    const sourceLicense = source.license;\n    if (typeof sourceLicense.spdx === \"string\") {\n      info.license.spdx = sourceLicense.spdx;\n    }\n    if (typeof sourceLicense.url === \"string\") {\n      info.license.url = sourceLicense.url;\n    }\n  }\n  if (source.samples instanceof Array) {\n    const samples = [];\n    source.samples.forEach((item) => {\n      if (typeof item === \"string\" && !samples.includes(item)) {\n        samples.push(item);\n      }\n    });\n    if (samples.length) {\n      info.samples = samples;\n    }\n  }\n  if (typeof source.height === \"number\" || typeof source.height === \"string\") {\n    const num = parseInt(source.height);\n    if (num > 0) {\n      info.height = num;\n    }\n  }\n  if (source.height instanceof Array) {\n    source.height.forEach((item) => {\n      const num = parseInt(item);\n      if (num > 0) {\n        if (!(info.height instanceof Array)) {\n          info.height = [];\n        }\n        info.height.push(num);\n      }\n    });\n    switch (info.height.length) {\n      case 0:\n        delete info.height;\n        break;\n      case 1:\n        info.height = info.height[0];\n    }\n  }\n  if (typeof info.height === \"number\") {\n    const displayHeight = validateDisplayHeight(info.height);\n    if (displayHeight && displayHeight !== info.height) {\n      info.displayHeight = displayHeight;\n    }\n  }\n  [\"samplesHeight\", \"displayHeight\"].forEach((prop) => {\n    const value = source[prop];\n    if (typeof value === \"number\" || typeof value === \"string\") {\n      const displayHeight = validateDisplayHeight(\n        parseInt(value)\n      );\n      if (displayHeight) {\n        info.displayHeight = displayHeight;\n      }\n    }\n  });\n  if (typeof source.category === \"string\") {\n    info.category = source.category;\n  }\n  if (source.tags instanceof Array) {\n    info.tags = source.tags;\n  }\n  switch (typeof source.palette) {\n    case \"boolean\":\n      info.palette = source.palette;\n      break;\n    case \"string\":\n      switch (source.palette.toLowerCase()) {\n        case \"colorless\":\n        case \"false\":\n          info.palette = false;\n          break;\n        case \"colorful\":\n        case \"true\":\n          info.palette = true;\n      }\n      break;\n  }\n  if (source.hidden === true) {\n    info.hidden = true;\n  }\n  Object.keys(source).forEach((key) => {\n    const value = source[key];\n    if (typeof value !== \"string\") {\n      return;\n    }\n    switch (key) {\n      case \"url\":\n      case \"uri\":\n        info.author.url = value;\n        break;\n      case \"licenseURL\":\n      case \"licenseURI\":\n        info.license.url = value;\n        break;\n      case \"licenseID\":\n      case \"licenseSPDX\":\n        info.license.spdx = value;\n        break;\n    }\n  });\n  return info;\n}\n\nexport { convertIconSetInfo };\n", "import { defaultIconProps } from '../icon/defaults.mjs';\nimport { defaultIconCustomisations } from '../customisations/defaults.mjs';\nimport { calculateSize } from './size.mjs';\nimport { wrapSVGContent } from './defs.mjs';\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ...defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ...defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = wrapSVGContent(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = calculateSize(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\nexport { iconToSVG, isUnsetKeyword };\n", "const unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\nexport { calculateSize };\n", "function splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\nexport { mergeDefsAndContent, splitSVGDefs, wrapSVGContent };\n", "const regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\nexport { replaceIDs };\n", "import { encodeSVGforURL } from './url.mjs';\n\nfunction encodeSvgForCss(svg) {\n  let useSvg = svg.startsWith(\"<svg>\") ? svg.replace(\"<svg>\", \"<svg >\") : svg;\n  if (!useSvg.includes(\" xmlns:xlink=\") && useSvg.includes(\" xlink:\")) {\n    useSvg = useSvg.replace(\n      \"<svg \",\n      '<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" '\n    );\n  }\n  if (!useSvg.includes(\" xmlns=\")) {\n    useSvg = useSvg.replace(\n      \"<svg \",\n      '<svg xmlns=\"http://www.w3.org/2000/svg\" '\n    );\n  }\n  return encodeSVGforURL(useSvg);\n}\n\nexport { encodeSvgForCss };\n", "function encodeSVGforURL(svg) {\n  return svg.replace(/\"/g, \"'\").replace(/%/g, \"%25\").replace(/#/g, \"%23\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/\\s+/g, \" \");\n}\nfunction svgToData(svg) {\n  return \"data:image/svg+xml,\" + encodeSVGforURL(svg);\n}\nfunction svgToURL(svg) {\n  return 'url(\"' + svgToData(svg) + '\")';\n}\n\nexport { encodeSVGforURL, svgToData, svgToURL };\n", "function trimSVG(str) {\n  return str.replace(/(['\"])\\s*\\n\\s*([^>\\\\/\\s])/g, \"$1 $2\").replace(/([\"';{}><])\\s*\\n\\s*/g, \"$1\").replace(/\\s*\\n\\s*/g, \" \").replace(/\\s+\"/g, '\"').replace(/=\"\\s+/g, '=\"').replace(/(\\s)+\\/>/g, \"/>\").trim();\n}\n\nexport { trimSVG };\n", "const skipTags = [\"script\", \"style\"];\nfunction prettifySVG(content, tab = \"\t\", depth = 0) {\n  let result = \"\";\n  let level = 0;\n  content = content.replace(/(\\s)*\\/>/g, \" />\");\n  while (content.length > 0) {\n    const openIndex = content.indexOf(\"<\");\n    let closeIndex = content.indexOf(\">\");\n    if (openIndex === -1 && closeIndex === -1) {\n      return result;\n    }\n    if (openIndex === -1 || closeIndex === -1 || closeIndex < openIndex) {\n      return null;\n    }\n    const text = content.slice(0, openIndex);\n    const trimmedText = text.trim();\n    if (trimmedText.length) {\n      if (text.trimStart() !== text && text.trimEnd() !== text) {\n        result += trimmedText + \"\\n\" + tab.repeat(level + depth);\n      } else {\n        result = result.trim() + text;\n      }\n    }\n    content = content.slice(openIndex);\n    closeIndex -= openIndex;\n    const lastChar = content.slice(closeIndex - 1, closeIndex);\n    const isClosing = content.slice(0, 2) === \"</\";\n    let isSelfClosing = lastChar === \"/\" || lastChar === \"?\";\n    if (isClosing && isSelfClosing) {\n      return null;\n    }\n    const tagName = content.slice(isClosing ? 2 : 1).split(/[\\s>]/).shift();\n    const ignoreTagContent = !isSelfClosing && !isClosing && skipTags.includes(tagName);\n    if (!ignoreTagContent) {\n      const nextOpenIndex = content.indexOf(\"<\", 1);\n      if (nextOpenIndex !== -1 && nextOpenIndex < closeIndex) {\n        return null;\n      }\n    }\n    if (isClosing && tab.length) {\n      if (result.slice(0 - tab.length) === tab) {\n        result = result.slice(0, result.length - tab.length);\n      }\n    }\n    result += content.slice(0, closeIndex + 1);\n    content = content.slice(closeIndex + 1);\n    if (ignoreTagContent) {\n      const closingIndex = content.indexOf(\"</\" + tagName);\n      const closingEnd = content.indexOf(\">\", closingIndex);\n      if (closingIndex < 0 || closingEnd < 0) {\n        return null;\n      }\n      result += content.slice(0, closingEnd + 1);\n      content = content.slice(closingEnd + 1);\n      isSelfClosing = true;\n    }\n    if (isClosing) {\n      level--;\n      if (level < 0) {\n        return null;\n      }\n    } else if (!isSelfClosing) {\n      level++;\n    }\n    result += \"\\n\" + tab.repeat(level + depth);\n  }\n  return level === 0 ? result : null;\n}\n\nexport { prettifySVG };\n", "function iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\nexport { iconToHTML };\n", "let policy;\nfunction createPolicy() {\n  try {\n    policy = window.trustedTypes.createPolicy(\"iconify\", {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n      createHTML: (s) => s\n    });\n  } catch (err) {\n    policy = null;\n  }\n}\nfunction cleanUpInnerHTML(html) {\n  if (policy === void 0) {\n    createPolicy();\n  }\n  return policy ? policy.createHTML(html) : html;\n}\n\nexport { cleanUpInnerHTML };\n", "function getSVGViewBox(value) {\n  const result = value.trim().split(/\\s+/).map(Number);\n  if (result.length === 4 && result.reduce((prev, value2) => prev && !isNaN(value2), true)) {\n    return result;\n  }\n}\n\nexport { getSVGViewBox };\n", "import { wrapSVGContent } from './defs.mjs';\nimport { getSVGViewBox } from './viewbox.mjs';\n\nfunction parseSVGContent(content) {\n  const match = content.trim().match(\n    /(?:<(?:\\?xml|!DOCTYPE)[^>]+>\\s*)*<svg([^>]+)>([\\s\\S]+)<\\/svg[^>]*>/\n  );\n  if (!match) {\n    return;\n  }\n  const body = match[2].trim();\n  const attribsList = match[1].match(/[\\w:-]+=\"[^\"]*\"/g);\n  const attribs = /* @__PURE__ */ Object.create(null);\n  attribsList?.forEach((row) => {\n    const match2 = row.match(/([\\w:-]+)=\"([^\"]*)\"/);\n    if (match2) {\n      attribs[match2[1]] = match2[2];\n    }\n  });\n  return {\n    attribs,\n    body\n  };\n}\nfunction build(data) {\n  const attribs = data.attribs;\n  const viewBox = getSVGViewBox(attribs[\"viewBox\"] ?? \"\");\n  if (!viewBox) {\n    return;\n  }\n  const groupAttributes = [];\n  for (const key in attribs) {\n    if (key === \"style\" || key.startsWith(\"fill\") || key.startsWith(\"stroke\")) {\n      groupAttributes.push(`${key}=\"${attribs[key]}\"`);\n    }\n  }\n  let body = data.body;\n  if (groupAttributes.length) {\n    body = wrapSVGContent(\n      body,\n      \"<g \" + groupAttributes.join(\" \") + \">\",\n      \"</g>\"\n    );\n  }\n  return {\n    // Copy dimensions if exist\n    width: attribs.width,\n    height: attribs.height,\n    viewBox,\n    body\n  };\n}\nfunction buildParsedSVG(data) {\n  const result = build(data);\n  if (result) {\n    return {\n      attributes: {\n        // Copy dimensions if exist\n        width: result.width,\n        height: result.height,\n        // Merge viewBox\n        viewBox: result.viewBox.join(\" \")\n      },\n      viewBox: result.viewBox,\n      body: result.body\n    };\n  }\n}\nfunction convertParsedSVG(data) {\n  const result = build(data);\n  if (result) {\n    const viewBox = result.viewBox;\n    return {\n      left: viewBox[0],\n      top: viewBox[1],\n      width: viewBox[2],\n      height: viewBox[3],\n      body: result.body\n    };\n  }\n}\n\nexport { buildParsedSVG, convertParsedSVG, parseSVGContent };\n", "const colorKeywords = {\n  transparent: {\n    type: \"transparent\"\n  },\n  none: {\n    type: \"none\"\n  },\n  currentcolor: {\n    type: \"current\"\n  }\n};\nfunction add(keyword, colors) {\n  const type = \"rgb\";\n  const r = colors[0];\n  const length = colors.length;\n  colorKeywords[keyword] = {\n    type,\n    r,\n    g: length > 1 ? colors[1] : r,\n    b: length > 2 ? colors[2] : r,\n    alpha: length > 3 ? colors[3] : 1\n  };\n}\nadd(\"silver\", [192]);\nadd(\"gray\", [128]);\nadd(\"white\", [255]);\nadd(\"maroon\", [128, 0, 0]);\nadd(\"red\", [255, 0, 0]);\nadd(\"purple\", [128, 0]);\nadd(\"fuchsia\", [255, 0]);\nadd(\"green\", [0, 128]);\nadd(\"lime\", [0, 255]);\nadd(\"olive\", [128, 128, 0]);\nadd(\"yellow\", [255, 255, 0]);\nadd(\"navy\", [0, 0, 128]);\nadd(\"blue\", [0, 0, 255]);\nadd(\"teal\", [0, 128, 128]);\nadd(\"aqua\", [0, 255, 255]);\nadd(\"aliceblue\", [240, 248, 255]);\nadd(\"antiquewhite\", [250, 235, 215]);\nadd(\"aqua\", [0, 255, 255]);\nadd(\"aquamarine\", [127, 255, 212]);\nadd(\"azure\", [240, 255, 255]);\nadd(\"beige\", [245, 245, 220]);\nadd(\"bisque\", [255, 228, 196]);\nadd(\"black\", [0]);\nadd(\"blanchedalmond\", [255, 235, 205]);\nadd(\"blue\", [0, 0, 255]);\nadd(\"blueviolet\", [138, 43, 226]);\nadd(\"brown\", [165, 42, 42]);\nadd(\"burlywood\", [222, 184, 135]);\nadd(\"cadetblue\", [95, 158, 160]);\nadd(\"chartreuse\", [127, 255, 0]);\nadd(\"chocolate\", [210, 105, 30]);\nadd(\"coral\", [255, 127, 80]);\nadd(\"cornflowerblue\", [100, 149, 237]);\nadd(\"cornsilk\", [255, 248, 220]);\nadd(\"crimson\", [220, 20, 60]);\nadd(\"cyan\", [0, 255, 255]);\nadd(\"darkblue\", [0, 0, 139]);\nadd(\"darkcyan\", [0, 139, 139]);\nadd(\"darkgoldenrod\", [184, 134, 11]);\nadd(\"darkgray\", [169]);\nadd(\"darkgreen\", [0, 100]);\nadd(\"darkgrey\", [169]);\nadd(\"darkkhaki\", [189, 183, 107]);\nadd(\"darkmagenta\", [139, 0]);\nadd(\"darkolivegreen\", [85, 107, 47]);\nadd(\"darkorange\", [255, 140, 0]);\nadd(\"darkorchid\", [153, 50, 204]);\nadd(\"darkred\", [139, 0, 0]);\nadd(\"darksalmon\", [233, 150, 122]);\nadd(\"darkseagreen\", [143, 188]);\nadd(\"darkslateblue\", [72, 61, 139]);\nadd(\"darkslategray\", [47, 79, 79]);\nadd(\"darkslategrey\", [47, 79, 79]);\nadd(\"darkturquoise\", [0, 206, 209]);\nadd(\"darkviolet\", [148, 0, 211]);\nadd(\"deeppink\", [255, 20, 147]);\nadd(\"deepskyblue\", [0, 191, 255]);\nadd(\"dimgray\", [105]);\nadd(\"dimgrey\", [105]);\nadd(\"dodgerblue\", [30, 144, 255]);\nadd(\"firebrick\", [178, 34, 34]);\nadd(\"floralwhite\", [255, 250, 240]);\nadd(\"forestgreen\", [34, 139]);\nadd(\"fuchsia\", [255, 0]);\nadd(\"gainsboro\", [220]);\nadd(\"ghostwhite\", [248, 248, 255]);\nadd(\"gold\", [255, 215, 0]);\nadd(\"goldenrod\", [218, 165, 32]);\nadd(\"gray\", [128]);\nadd(\"green\", [0, 128]);\nadd(\"greenyellow\", [173, 255, 47]);\nadd(\"grey\", [128]);\nadd(\"honeydew\", [240, 255]);\nadd(\"hotpink\", [255, 105, 180]);\nadd(\"indianred\", [205, 92, 92]);\nadd(\"indigo\", [75, 0, 130]);\nadd(\"ivory\", [255, 255, 240]);\nadd(\"khaki\", [240, 230, 140]);\nadd(\"lavender\", [230, 230, 250]);\nadd(\"lavenderblush\", [255, 240, 245]);\nadd(\"lawngreen\", [124, 252, 0]);\nadd(\"lemonchiffon\", [255, 250, 205]);\nadd(\"lightblue\", [173, 216, 230]);\nadd(\"lightcoral\", [240, 128, 128]);\nadd(\"lightcyan\", [224, 255, 255]);\nadd(\"lightgoldenrodyellow\", [250, 250, 210]);\nadd(\"lightgray\", [211]);\nadd(\"lightgreen\", [144, 238]);\nadd(\"lightgrey\", [211]);\nadd(\"lightpink\", [255, 182, 193]);\nadd(\"lightsalmon\", [255, 160, 122]);\nadd(\"lightseagreen\", [32, 178, 170]);\nadd(\"lightskyblue\", [135, 206, 250]);\nadd(\"lightslategray\", [119, 136, 153]);\nadd(\"lightslategrey\", [119, 136, 153]);\nadd(\"lightsteelblue\", [176, 196, 222]);\nadd(\"lightyellow\", [255, 255, 224]);\nadd(\"lime\", [0, 255]);\nadd(\"limegreen\", [50, 205]);\nadd(\"linen\", [250, 240, 230]);\nadd(\"magenta\", [255, 0]);\nadd(\"maroon\", [128, 0, 0]);\nadd(\"mediumaquamarine\", [102, 205, 170]);\nadd(\"mediumblue\", [0, 0, 205]);\nadd(\"mediumorchid\", [186, 85, 211]);\nadd(\"mediumpurple\", [147, 112, 219]);\nadd(\"mediumseagreen\", [60, 179, 113]);\nadd(\"mediumslateblue\", [123, 104, 238]);\nadd(\"mediumspringgreen\", [0, 250, 154]);\nadd(\"mediumturquoise\", [72, 209, 204]);\nadd(\"mediumvioletred\", [199, 21, 133]);\nadd(\"midnightblue\", [25, 25, 112]);\nadd(\"mintcream\", [245, 255, 250]);\nadd(\"mistyrose\", [255, 228, 225]);\nadd(\"moccasin\", [255, 228, 181]);\nadd(\"navajowhite\", [255, 222, 173]);\nadd(\"navy\", [0, 0, 128]);\nadd(\"oldlace\", [253, 245, 230]);\nadd(\"olive\", [128, 128, 0]);\nadd(\"olivedrab\", [107, 142, 35]);\nadd(\"orange\", [255, 165, 0]);\nadd(\"orangered\", [255, 69, 0]);\nadd(\"orchid\", [218, 112, 214]);\nadd(\"palegoldenrod\", [238, 232, 170]);\nadd(\"palegreen\", [152, 251]);\nadd(\"paleturquoise\", [175, 238, 238]);\nadd(\"palevioletred\", [219, 112, 147]);\nadd(\"papayawhip\", [255, 239, 213]);\nadd(\"peachpuff\", [255, 218, 185]);\nadd(\"peru\", [205, 133, 63]);\nadd(\"pink\", [255, 192, 203]);\nadd(\"plum\", [221, 160]);\nadd(\"powderblue\", [176, 224, 230]);\nadd(\"purple\", [128, 0]);\nadd(\"rebeccapurple\", [102, 51, 153]);\nadd(\"red\", [255, 0, 0]);\nadd(\"rosybrown\", [188, 143, 143]);\nadd(\"royalblue\", [65, 105, 225]);\nadd(\"saddlebrown\", [139, 69, 19]);\nadd(\"salmon\", [250, 128, 114]);\nadd(\"sandybrown\", [244, 164, 96]);\nadd(\"seagreen\", [46, 139, 87]);\nadd(\"seashell\", [255, 245, 238]);\nadd(\"sienna\", [160, 82, 45]);\nadd(\"silver\", [192]);\nadd(\"skyblue\", [135, 206, 235]);\nadd(\"slateblue\", [106, 90, 205]);\nadd(\"slategray\", [112, 128, 144]);\nadd(\"slategrey\", [112, 128, 144]);\nadd(\"snow\", [255, 250, 250]);\nadd(\"springgreen\", [0, 255, 127]);\nadd(\"steelblue\", [70, 130, 180]);\nadd(\"tan\", [210, 180, 140]);\nadd(\"teal\", [0, 128, 128]);\nadd(\"thistle\", [216, 191]);\nadd(\"tomato\", [255, 99, 71]);\nadd(\"turquoise\", [64, 224, 208]);\nadd(\"violet\", [238, 130]);\nadd(\"wheat\", [245, 222, 179]);\nadd(\"white\", [255]);\nadd(\"whitesmoke\", [245]);\nadd(\"yellow\", [255, 255, 0]);\nadd(\"yellowgreen\", [154, 205, 50]);\n\nexport { colorKeywords };\n", "import { colorKeywords } from './keywords.mjs';\n\nfunction rgb2hsl(rgb) {\n  const c1 = rgb.r / 255, c2 = rgb.g / 255, c3 = rgb.b / 255, kmin = Math.min(c1, Math.min(c2, c3)), kmax = Math.max(c1, Math.max(c2, c3)), l = (kmax + kmin) / 2;\n  let s, h, delta;\n  if (kmax === kmin) {\n    s = h = 0;\n  } else {\n    if (l < 0.5) {\n      s = (kmax - kmin) / (kmax + kmin);\n    } else {\n      s = (kmax - kmin) / (2 - kmax - kmin);\n    }\n    delta = kmax - kmin;\n    if (kmax === c1) {\n      h = (c2 - c3) / delta;\n    } else if (kmax === c2) {\n      h = 2 + (c3 - c1) / delta;\n    } else {\n      h = 4 + (c1 - c2) / delta;\n    }\n    h = h * 60;\n    if (h < 0) {\n      h += 360;\n    }\n  }\n  return {\n    type: \"hsl\",\n    h,\n    s: s * 100,\n    l: l * 100,\n    alpha: rgb.alpha\n  };\n}\nfunction fromFunction(value) {\n  if (value.slice(-1) !== \")\") {\n    return null;\n  }\n  const parts = value.slice(0, value.length - 1).split(\"(\");\n  if (parts.length !== 2) {\n    return null;\n  }\n  const func = parts[0].trim();\n  const content = parts[1].trim();\n  let values;\n  let alphaStr;\n  switch (func) {\n    case \"lch\":\n    case \"lab\": {\n      const parts2 = content.split(\"/\");\n      switch (parts2.length) {\n        case 2:\n          alphaStr = parts2[1].trim();\n          break;\n        case 1:\n          break;\n        default:\n          return null;\n      }\n      values = parts2[0].trim().split(/[\\s,]+/);\n      break;\n    }\n    case \"rgb\":\n    case \"rgba\":\n    case \"hsl\":\n    case \"hsla\": {\n      values = content.trim().split(/[\\s,]+/);\n      if (values.length === 4) {\n        alphaStr = values.pop().trim();\n      }\n      break;\n    }\n    default: {\n      return {\n        type: \"function\",\n        func,\n        value: content\n      };\n    }\n  }\n  let alpha = 1;\n  if (typeof alphaStr === \"string\") {\n    alpha = parseFloat(alphaStr);\n    const index = alphaStr.indexOf(\"%\");\n    const hasPercentage = index !== -1;\n    if (isNaN(alpha) || hasPercentage && index !== alphaStr.length - 1) {\n      return null;\n    }\n    if (hasPercentage) {\n      alpha /= 100;\n    }\n  }\n  if (alpha < 0 || alpha > 1 || values.length !== 3) {\n    return null;\n  }\n  if (alpha === 0) {\n    return {\n      type: \"transparent\"\n    };\n  }\n  const isPercentage = [];\n  const numbers = [];\n  for (let i = 0; i < 3; i++) {\n    const colorStr = values[i];\n    const index = colorStr.indexOf(\"%\");\n    const hasPercentage = index !== -1;\n    if (hasPercentage && index !== colorStr.length - 1) {\n      return null;\n    }\n    const colorNum = parseFloat(colorStr);\n    if (isNaN(colorNum)) {\n      return null;\n    }\n    isPercentage.push(hasPercentage);\n    numbers.push(colorNum);\n  }\n  switch (func) {\n    case \"rgb\":\n    case \"rgba\": {\n      const hasPercengage = isPercentage[0];\n      if (hasPercengage !== isPercentage[1] || hasPercengage !== isPercentage[2]) {\n        return null;\n      }\n      let r = numbers[0];\n      let g = numbers[1];\n      let b = numbers[2];\n      if (hasPercengage) {\n        r = r * 255 / 100;\n        g = g * 255 / 100;\n        b = b * 255 / 100;\n      }\n      return {\n        type: \"rgb\",\n        r,\n        g,\n        b,\n        alpha\n      };\n    }\n    case \"hsl\":\n    case \"hsla\": {\n      if (isPercentage[0] || !isPercentage[1] || !isPercentage[2]) {\n        return null;\n      }\n      return {\n        type: \"hsl\",\n        h: numbers[0],\n        s: numbers[1],\n        l: numbers[2],\n        alpha\n      };\n    }\n    case \"lab\":\n    case \"lch\": {\n      if (!isPercentage[0] || isPercentage[1] || isPercentage[2]) {\n        return null;\n      }\n      return func === \"lab\" ? {\n        type: \"lab\",\n        l: numbers[0],\n        a: numbers[1],\n        b: numbers[2],\n        alpha\n      } : {\n        type: \"lch\",\n        l: numbers[0],\n        c: numbers[1],\n        h: numbers[2],\n        alpha\n      };\n    }\n  }\n  return null;\n}\nfunction fromHex(value) {\n  if (value.slice(0, 1) === \"#\") {\n    value = value.slice(1);\n  }\n  if (!/^[\\da-f]+$/i.test(value)) {\n    return null;\n  }\n  let alpha = 1;\n  const hex = [\"\", \"\", \"\"];\n  switch (value.length) {\n    case 4: {\n      alpha = parseInt(value[3] + value[3], 16) / 255;\n    }\n    case 3: {\n      hex[0] = value[0] + value[0];\n      hex[1] = value[1] + value[1];\n      hex[2] = value[2] + value[2];\n      break;\n    }\n    case 8: {\n      alpha = parseInt(value[6] + value[7], 16) / 255;\n    }\n    case 6: {\n      hex[0] = value[0] + value[1];\n      hex[1] = value[2] + value[3];\n      hex[2] = value[4] + value[5];\n      break;\n    }\n    default:\n      return null;\n  }\n  return alpha === 0 ? {\n    type: \"transparent\"\n  } : {\n    type: \"rgb\",\n    r: parseInt(hex[0], 16),\n    g: parseInt(hex[1], 16),\n    b: parseInt(hex[2], 16),\n    alpha\n  };\n}\nfunction stringToColor(value) {\n  value = value.toLowerCase().trim();\n  if (colorKeywords[value]) {\n    return { ...colorKeywords[value] };\n  }\n  if (value.indexOf(\"(\") !== -1) {\n    return fromFunction(value);\n  }\n  return fromHex(value);\n}\nfunction compareColors(color1, color2) {\n  if (color1.type === color2.type) {\n    let testKeys = new Set(Object.keys(color1));\n    switch (color1.type) {\n      case \"hsl\": {\n        if (color1.s === 0) {\n          testKeys.delete(\"h\");\n        }\n        if (color1.l === 0 || color1.l === 100) {\n          testKeys.delete(\"h\");\n          testKeys.delete(\"s\");\n        }\n      }\n      case \"rgb\":\n        if (color1.alpha === 0) {\n          testKeys = /* @__PURE__ */ new Set([\"a\"]);\n        }\n    }\n    const keys = Array.from(testKeys);\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      if (color1[key] !== color2[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const list = [color1, color2].sort((a, b) => a.type.localeCompare(b.type));\n  const item1 = list[0];\n  const item2 = list[1];\n  switch (item1.type) {\n    case \"hsl\": {\n      switch (item2.type) {\n        case \"rgb\": {\n          return compareColors(item1, rgb2hsl(item2));\n        }\n        case \"transparent\":\n          return item1.alpha === 0;\n      }\n      return false;\n    }\n    case \"rgb\": {\n      switch (item2.type) {\n        case \"transparent\":\n          return item1.alpha === 0;\n      }\n    }\n  }\n  return false;\n}\nfunction colorToHexString(color, canCompact = true) {\n  if (color.alpha !== 1) {\n    return null;\n  }\n  let result = \"\";\n  const attrs = [\"r\", \"g\", \"b\"];\n  for (let i = 0; i < attrs.length; i++) {\n    const value = color[attrs[i]];\n    if (Math.round(value) !== value) {\n      return null;\n    }\n    const hex = value.toString(16);\n    result += (value < 16 ? \"0\" : \"\") + hex;\n  }\n  if (result.length !== 6) {\n    return null;\n  }\n  if (canCompact && result[0] === result[1] && result[2] === result[3] && result[4] === result[5]) {\n    result = result[0] + result[2] + result[4];\n  }\n  return \"#\" + result;\n}\nfunction colorToString(color) {\n  if (color.alpha === 0) {\n    return \"transparent\";\n  }\n  switch (color.type) {\n    case \"none\":\n    case \"transparent\":\n      return color.type;\n    case \"current\":\n      return \"currentColor\";\n    case \"rgb\": {\n      const hex = colorToHexString(color);\n      if (hex !== null) {\n        return hex;\n      }\n      const list = [color.r, color.g, color.b];\n      if (color.alpha !== 1) {\n        list.push(color.alpha);\n      }\n      return \"rgb\" + (list.length === 4 ? \"a(\" : \"(\") + list.join(\", \") + \")\";\n    }\n    case \"hsl\": {\n      const list = [\n        color.h,\n        color.s.toString() + \"%\",\n        color.l.toString() + \"%\"\n      ];\n      if (color.alpha !== 1) {\n        list.push(color.alpha);\n      }\n      return \"hsl\" + (list.length === 4 ? \"a(\" : \"(\") + list.join(\", \") + \")\";\n    }\n    case \"lab\": {\n      const list = [color.l.toString() + \"%\", color.a, color.b];\n      if (color.alpha !== 1) {\n        list.push(\"/ \" + color.alpha.toString());\n      }\n      return \"lab(\" + list.join(\" \") + \")\";\n    }\n    case \"lch\": {\n      const list = [color.l.toString() + \"%\", color.c, color.h];\n      if (color.alpha !== 1) {\n        list.push(\"/ \" + color.alpha.toString());\n      }\n      return \"lch(\" + list.join(\" \") + \")\";\n    }\n    case \"function\": {\n      return color.func + \"(\" + color.value + \")\";\n    }\n  }\n}\n\nexport { colorToHexString, colorToString, compareColors, stringToColor };\n", "import { defaultIconProps } from '../icon/defaults.mjs';\nimport { getCommonCSSRules, generateItemCSSRules, generateItemContent } from './common.mjs';\nimport { formatCSS } from './format.mjs';\nimport '../svg/html.mjs';\nimport '../svg/size.mjs';\nimport '../svg/url.mjs';\nimport '../icon/square.mjs';\nimport '../svg/build.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/defs.mjs';\n\nfunction getIconCSS(icon, options = {}) {\n  const body = options.customise ? options.customise(icon.body) : icon.body;\n  const mode = options.mode || (options.color || !body.includes(\"currentColor\") ? \"background\" : \"mask\");\n  let varName = options.varName;\n  if (varName === void 0 && mode === \"mask\") {\n    varName = \"svg\";\n  }\n  const newOptions = {\n    ...options,\n    // Override mode and varName\n    mode,\n    varName\n  };\n  if (mode === \"background\") {\n    delete newOptions.varName;\n  }\n  const rules = {\n    ...options.rules,\n    ...getCommonCSSRules(newOptions),\n    ...generateItemCSSRules(\n      {\n        ...defaultIconProps,\n        ...icon,\n        body\n      },\n      newOptions\n    )\n  };\n  const selector = options.iconSelector || \".icon\";\n  return formatCSS(\n    [\n      {\n        selector,\n        rules\n      }\n    ],\n    newOptions.format\n  );\n}\nfunction getIconContentCSS(icon, options) {\n  const body = options.customise ? options.customise(icon.body) : icon.body;\n  const content = generateItemContent(\n    {\n      ...defaultIconProps,\n      ...icon,\n      body\n    },\n    options\n  );\n  const selector = options.iconSelector || \".icon::after\";\n  return formatCSS(\n    [\n      {\n        selector,\n        rules: {\n          ...options.rules,\n          content\n        }\n      }\n    ],\n    options.format\n  );\n}\n\nexport { getIconCSS, getIconContentCSS };\n", "import { iconToHTML } from '../svg/html.mjs';\nimport { calculateSize } from '../svg/size.mjs';\nimport { svgToURL } from '../svg/url.mjs';\nimport { makeViewBoxSquare } from '../icon/square.mjs';\nimport { iconToSVG } from '../svg/build.mjs';\nimport '../icon/defaults.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/defs.mjs';\n\nfunction getCommonCSSRules(options) {\n  const result = {\n    display: \"inline-block\",\n    width: \"1em\",\n    height: \"1em\"\n  };\n  const varName = options.varName;\n  if (options.pseudoSelector) {\n    result[\"content\"] = \"''\";\n  }\n  switch (options.mode) {\n    case \"background\":\n      if (varName) {\n        result[\"background-image\"] = \"var(--\" + varName + \")\";\n      }\n      result[\"background-repeat\"] = \"no-repeat\";\n      result[\"background-size\"] = \"100% 100%\";\n      break;\n    case \"mask\":\n      result[\"background-color\"] = \"currentColor\";\n      if (varName) {\n        result[\"mask-image\"] = result[\"-webkit-mask-image\"] = \"var(--\" + varName + \")\";\n      }\n      result[\"mask-repeat\"] = result[\"-webkit-mask-repeat\"] = \"no-repeat\";\n      result[\"mask-size\"] = result[\"-webkit-mask-size\"] = \"100% 100%\";\n      break;\n  }\n  return result;\n}\nfunction generateItemCSSRules(icon, options) {\n  const result = {};\n  const varName = options.varName;\n  const buildResult = iconToSVG(icon);\n  let viewBox = buildResult.viewBox;\n  if (viewBox[2] !== viewBox[3]) {\n    if (options.forceSquare) {\n      viewBox = makeViewBoxSquare(viewBox);\n    } else {\n      result[\"width\"] = calculateSize(\"1em\", viewBox[2] / viewBox[3]);\n    }\n  }\n  const svg = iconToHTML(\n    buildResult.body.replace(/currentColor/g, options.color || \"black\"),\n    {\n      viewBox: `${viewBox[0]} ${viewBox[1]} ${viewBox[2]} ${viewBox[3]}`,\n      width: `${viewBox[2]}`,\n      height: `${viewBox[3]}`\n    }\n  );\n  const url = svgToURL(svg);\n  if (varName) {\n    result[\"--\" + varName] = url;\n  } else {\n    switch (options.mode) {\n      case \"background\":\n        result[\"background-image\"] = url;\n        break;\n      case \"mask\":\n        result[\"mask-image\"] = result[\"-webkit-mask-image\"] = url;\n        break;\n    }\n  }\n  return result;\n}\nfunction generateItemContent(icon, options) {\n  const buildResult = iconToSVG(icon);\n  const viewBox = buildResult.viewBox;\n  const height = options.height;\n  const width = options.width ?? calculateSize(height, viewBox[2] / viewBox[3]);\n  const svg = iconToHTML(\n    buildResult.body.replace(/currentColor/g, options.color || \"black\"),\n    {\n      viewBox: `${viewBox[0]} ${viewBox[1]} ${viewBox[2]} ${viewBox[3]}`,\n      width: width.toString(),\n      height: height.toString()\n    }\n  );\n  return svgToURL(svg);\n}\n\nexport { generateItemCSSRules, generateItemContent, getCommonCSSRules };\n", "const format = {\n  selectorStart: {\n    compressed: \"{\",\n    compact: \" {\",\n    expanded: \" {\"\n  },\n  selectorEnd: {\n    compressed: \"}\",\n    compact: \"; }\\n\",\n    expanded: \";\\n}\\n\"\n  },\n  rule: {\n    compressed: \"{key}:\",\n    compact: \" {key}: \",\n    expanded: \"\\n  {key}: \"\n  }\n};\nfunction formatCSS(data, mode = \"expanded\") {\n  const results = [];\n  for (let i = 0; i < data.length; i++) {\n    const { selector, rules } = data[i];\n    const fullSelector = selector instanceof Array ? selector.join(mode === \"compressed\" ? \",\" : \", \") : selector;\n    let entry = fullSelector + format.selectorStart[mode];\n    let firstRule = true;\n    for (const key in rules) {\n      if (!firstRule) {\n        entry += \";\";\n      }\n      entry += format.rule[mode].replace(\"{key}\", key) + rules[key];\n      firstRule = false;\n    }\n    entry += format.selectorEnd[mode];\n    results.push(entry);\n  }\n  return results.join(mode === \"compressed\" ? \"\" : \"\\n\");\n}\n\nexport { formatCSS };\n", "import { getIconData } from '../icon-set/get-icon.mjs';\nimport { defaultIconProps } from '../icon/defaults.mjs';\nimport { getCommonCSSRules, generateItemCSSRules, generateItemContent } from './common.mjs';\nimport { formatCSS } from './format.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/transformations.mjs';\nimport '../icon-set/tree.mjs';\nimport '../svg/html.mjs';\nimport '../svg/size.mjs';\nimport '../svg/url.mjs';\nimport '../icon/square.mjs';\nimport '../svg/build.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/defs.mjs';\n\nconst commonSelector = \".icon--{prefix}\";\nconst iconSelector = \".icon--{prefix}--{name}\";\nconst contentSelector = \".icon--{prefix}--{name}::after\";\nconst defaultSelectors = {\n  commonSelector,\n  iconSelector,\n  overrideSelector: commonSelector + iconSelector\n};\nfunction getIconsCSSData(iconSet, names, options = {}) {\n  const css = [];\n  const errors = [];\n  const palette = options.color ? true : void 0;\n  let mode = options.mode || typeof palette === \"boolean\" && (palette ? \"background\" : \"mask\");\n  if (!mode) {\n    for (let i = 0; i < names.length; i++) {\n      const name = names[i];\n      const icon = getIconData(iconSet, name);\n      if (icon) {\n        const body = options.customise ? options.customise(icon.body, name) : icon.body;\n        mode = body.includes(\"currentColor\") ? \"mask\" : \"background\";\n        break;\n      }\n    }\n    if (!mode) {\n      mode = \"mask\";\n      errors.push(\n        \"/* cannot detect icon mode: not set in options and icon set is missing info, rendering as \" + mode + \" */\"\n      );\n    }\n  }\n  let varName = options.varName;\n  if (varName === void 0 && mode === \"mask\") {\n    varName = \"svg\";\n  }\n  const newOptions = {\n    ...options,\n    // Override mode and varName\n    mode,\n    varName\n  };\n  const { commonSelector: commonSelector2, iconSelector: iconSelector2, overrideSelector } = newOptions.iconSelector ? newOptions : defaultSelectors;\n  const iconSelectorWithPrefix = iconSelector2.replace(\n    /{prefix}/g,\n    iconSet.prefix\n  );\n  const commonRules = {\n    ...options.rules,\n    ...getCommonCSSRules(newOptions)\n  };\n  const hasCommonRules = commonSelector2 && commonSelector2 !== iconSelector2;\n  const commonSelectors = /* @__PURE__ */ new Set();\n  if (hasCommonRules) {\n    css.push({\n      selector: commonSelector2.replace(/{prefix}/g, iconSet.prefix),\n      rules: commonRules\n    });\n  }\n  for (let i = 0; i < names.length; i++) {\n    const name = names[i];\n    const iconData = getIconData(iconSet, name);\n    if (!iconData) {\n      errors.push(\"/* Could not find icon: \" + name + \" */\");\n      continue;\n    }\n    const body = options.customise ? options.customise(iconData.body, name) : iconData.body;\n    const rules = generateItemCSSRules(\n      {\n        ...defaultIconProps,\n        ...iconData,\n        body\n      },\n      newOptions\n    );\n    let requiresOverride = false;\n    if (hasCommonRules && overrideSelector) {\n      for (const key in rules) {\n        if (key in commonRules) {\n          requiresOverride = true;\n        }\n      }\n    }\n    const selector = (requiresOverride && overrideSelector ? overrideSelector.replace(/{prefix}/g, iconSet.prefix) : iconSelectorWithPrefix).replace(/{name}/g, name);\n    css.push({\n      selector,\n      rules\n    });\n    if (!hasCommonRules) {\n      commonSelectors.add(selector);\n    }\n  }\n  const result = {\n    css,\n    errors\n  };\n  if (!hasCommonRules && commonSelectors.size) {\n    const selector = Array.from(commonSelectors).join(\n      newOptions.format === \"compressed\" ? \",\" : \", \"\n    );\n    result.common = {\n      selector,\n      rules: commonRules\n    };\n  }\n  return result;\n}\nfunction getIconsCSS(iconSet, names, options = {}) {\n  const { css, errors, common } = getIconsCSSData(iconSet, names, options);\n  if (common) {\n    if (css.length === 1 && css[0].selector === common.selector) {\n      css[0].rules = {\n        // Common first, override later\n        ...common.rules,\n        ...css[0].rules\n      };\n    } else {\n      css.unshift(common);\n    }\n  }\n  return formatCSS(css, options.format) + (errors.length ? \"\\n\" + errors.join(\"\\n\") + \"\\n\" : \"\");\n}\nfunction getIconsContentCSS(iconSet, names, options) {\n  const errors = [];\n  const css = [];\n  const iconSelectorWithPrefix = (options.iconSelector ?? contentSelector).replace(/{prefix}/g, iconSet.prefix);\n  for (let i = 0; i < names.length; i++) {\n    const name = names[i];\n    const iconData = getIconData(iconSet, name);\n    if (!iconData) {\n      errors.push(\"/* Could not find icon: \" + name + \" */\");\n      continue;\n    }\n    const body = options.customise ? options.customise(iconData.body, name) : iconData.body;\n    const content = generateItemContent(\n      {\n        ...defaultIconProps,\n        ...iconData,\n        body\n      },\n      options\n    );\n    const selector = iconSelectorWithPrefix.replace(/{name}/g, name);\n    css.push({\n      selector,\n      rules: {\n        ...options.rules,\n        content\n      }\n    });\n  }\n  return formatCSS(css, options.format) + (errors.length ? \"\\n\" + errors.join(\"\\n\") + \"\\n\" : \"\");\n}\n\nexport { getIconsCSS, getIconsCSSData, getIconsContentCSS };\n", "import { isUnsetKeyword } from '../svg/build.mjs';\nimport { calculateSize } from '../svg/size.mjs';\nimport '../icon/defaults.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/defs.mjs';\n\nconst svgWidthRegex = /\\swidth\\s*=\\s*[\"']([\\w.]+)[\"']/;\nconst svgHeightRegex = /\\sheight\\s*=\\s*[\"']([\\w.]+)[\"']/;\nconst svgTagRegex = /<svg\\s+/;\nfunction configureSvgSize(svg, props, scale) {\n  const svgNode = svg.slice(0, svg.indexOf(\">\"));\n  const check = (prop, regex) => {\n    const result = regex.exec(svgNode);\n    const isSet = result != null;\n    const propValue = props[prop];\n    if (!propValue && !isUnsetKeyword(propValue)) {\n      if (typeof scale === \"number\") {\n        if (scale > 0) {\n          props[prop] = calculateSize(\n            // Base on result from iconToSVG() or 1em\n            result?.[1] ?? \"1em\",\n            scale\n          );\n        }\n      } else if (result) {\n        props[prop] = result[1];\n      }\n    }\n    return isSet;\n  };\n  return [check(\"width\", svgWidthRegex), check(\"height\", svgHeightRegex)];\n}\nasync function mergeIconProps(svg, collection, icon, options, propsProvider, afterCustomizations) {\n  const { scale, addXmlNs = false } = options ?? {};\n  const { additionalProps = {}, iconCustomizer } = options?.customizations ?? {};\n  const props = await propsProvider?.() ?? {};\n  await iconCustomizer?.(collection, icon, props);\n  Object.keys(additionalProps).forEach((p) => {\n    const v = additionalProps[p];\n    if (v !== void 0 && v !== null)\n      props[p] = v;\n  });\n  afterCustomizations?.(props);\n  const [widthOnSvg, heightOnSvg] = configureSvgSize(svg, props, scale);\n  if (addXmlNs) {\n    if (!svg.includes(\"xmlns=\") && !props[\"xmlns\"]) {\n      props[\"xmlns\"] = \"http://www.w3.org/2000/svg\";\n    }\n    if (!svg.includes(\"xmlns:xlink=\") && svg.includes(\"xlink:\") && !props[\"xmlns:xlink\"]) {\n      props[\"xmlns:xlink\"] = \"http://www.w3.org/1999/xlink\";\n    }\n  }\n  const propsToAdd = Object.keys(props).map(\n    (p) => p === \"width\" && widthOnSvg || p === \"height\" && heightOnSvg ? null : `${p}=\"${props[p]}\"`\n  ).filter((p) => p != null);\n  if (propsToAdd.length) {\n    svg = svg.replace(svgTagRegex, `<svg ${propsToAdd.join(\" \")} `);\n  }\n  if (options) {\n    const { defaultStyle, defaultClass } = options;\n    if (defaultClass && !svg.includes(\"class=\")) {\n      svg = svg.replace(svgTagRegex, `<svg class=\"${defaultClass}\" `);\n    }\n    if (defaultStyle && !svg.includes(\"style=\")) {\n      svg = svg.replace(svgTagRegex, `<svg style=\"${defaultStyle}\" `);\n    }\n  }\n  const usedProps = options?.usedProps;\n  if (usedProps) {\n    Object.keys(additionalProps).forEach((p) => {\n      const v = props[p];\n      if (v !== void 0 && v !== null)\n        usedProps[p] = v;\n    });\n    if (typeof props.width !== \"undefined\" && props.width !== null) {\n      usedProps.width = props.width;\n    }\n    if (typeof props.height !== \"undefined\" && props.height !== null) {\n      usedProps.height = props.height;\n    }\n  }\n  return svg;\n}\nfunction getPossibleIconNames(icon) {\n  return [\n    icon,\n    icon.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase(),\n    icon.replace(/([a-z])(\\d+)/g, \"$1-$2\")\n  ];\n}\n\nexport { getPossibleIconNames, mergeIconProps };\n", "import createDebugger from 'debug';\nimport { mergeIconProps } from './utils.mjs';\nimport { trimSVG } from '../svg/trim.mjs';\nimport '../svg/build.mjs';\nimport '../icon/defaults.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/size.mjs';\nimport '../svg/defs.mjs';\n\nconst debug = createDebugger(\"@iconify-loader:custom\");\nasync function getCustomIcon(custom, collection, icon, options) {\n  let result;\n  debug(`${collection}:${icon}`);\n  try {\n    if (typeof custom === \"function\") {\n      result = await custom(icon);\n    } else {\n      const inline = custom[icon];\n      result = typeof inline === \"function\" ? await inline() : inline;\n    }\n  } catch (err) {\n    console.warn(\n      `Failed to load custom icon \"${icon}\" in \"${collection}\":`,\n      err\n    );\n    return;\n  }\n  if (result) {\n    const cleanupIdx = result.indexOf(\"<svg\");\n    if (cleanupIdx > 0)\n      result = result.slice(cleanupIdx);\n    const { transform } = options?.customizations ?? {};\n    result = typeof transform === \"function\" ? await transform(result, collection, icon) : result;\n    if (!result.startsWith(\"<svg\")) {\n      console.warn(\n        `Custom icon \"${icon}\" in \"${collection}\" is not a valid SVG`\n      );\n      return result;\n    }\n    return await mergeIconProps(\n      options?.customizations?.trimCustomSvg === true ? trimSVG(result) : result,\n      collection,\n      icon,\n      options,\n      void 0\n    );\n  }\n}\n\nexport { getCustomIcon };\n", "import { iconToSVG, isUnsetKeyword } from '../svg/build.mjs';\nimport { getIconData } from '../icon-set/get-icon.mjs';\nimport { calculateSize } from '../svg/size.mjs';\nimport { mergeIconProps } from './utils.mjs';\nimport createDebugger from 'debug';\nimport { defaultIconCustomisations } from '../customisations/defaults.mjs';\nimport '../icon/defaults.mjs';\nimport '../svg/defs.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/transformations.mjs';\nimport '../icon-set/tree.mjs';\n\nconst debug = createDebugger(\"@iconify-loader:icon\");\nasync function searchForIcon(iconSet, collection, ids, options) {\n  let iconData;\n  const { customize } = options?.customizations ?? {};\n  for (const id of ids) {\n    iconData = getIconData(iconSet, id);\n    if (iconData) {\n      debug(`${collection}:${id}`);\n      let defaultCustomizations = {\n        ...defaultIconCustomisations\n      };\n      if (typeof customize === \"function\") {\n        iconData = Object.assign({}, iconData);\n        defaultCustomizations = customize(\n          defaultCustomizations,\n          iconData,\n          `${collection}:${id}`\n        ) ?? defaultCustomizations;\n      }\n      const {\n        attributes: { width, height, ...restAttributes },\n        body\n      } = iconToSVG(iconData, defaultCustomizations);\n      const scale = options?.scale;\n      return await mergeIconProps(\n        // DON'T remove space on <svg >\n        `<svg >${body}</svg>`,\n        collection,\n        id,\n        options,\n        () => {\n          return { ...restAttributes };\n        },\n        (props) => {\n          const check = (prop, defaultValue) => {\n            const propValue = props[prop];\n            let value;\n            if (!isUnsetKeyword(propValue)) {\n              if (propValue) {\n                return;\n              }\n              if (typeof scale === \"number\") {\n                if (scale) {\n                  value = calculateSize(\n                    // Base on result from iconToSVG() or 1em\n                    defaultValue ?? \"1em\",\n                    scale\n                  );\n                }\n              } else {\n                value = defaultValue;\n              }\n            }\n            if (!value) {\n              delete props[prop];\n            } else {\n              props[prop] = value;\n            }\n          };\n          check(\"width\", width);\n          check(\"height\", height);\n        }\n      );\n    }\n  }\n}\n\nexport { searchForIcon };\n", "import { getCustomIcon } from './custom.mjs';\nimport { searchForIcon } from './modern.mjs';\nimport 'debug';\nimport './utils.mjs';\nimport '../svg/build.mjs';\nimport '../icon/defaults.mjs';\nimport '../customisations/defaults.mjs';\nimport '../svg/size.mjs';\nimport '../svg/defs.mjs';\nimport '../svg/trim.mjs';\nimport '../icon-set/get-icon.mjs';\nimport '../icon/merge.mjs';\nimport '../icon/transformations.mjs';\nimport '../icon-set/tree.mjs';\n\nconst loadIcon = async (collection, icon, options) => {\n  const custom = options?.customCollections?.[collection];\n  if (custom) {\n    if (typeof custom === \"function\") {\n      let result;\n      try {\n        result = await custom(icon);\n      } catch (err) {\n        console.warn(\n          `Failed to load custom icon \"${icon}\" in \"${collection}\":`,\n          err\n        );\n        return;\n      }\n      if (result) {\n        if (typeof result === \"string\") {\n          return await getCustomIcon(\n            () => result,\n            collection,\n            icon,\n            options\n          );\n        }\n        if (\"icons\" in result) {\n          const ids = [\n            icon,\n            icon.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase(),\n            icon.replace(/([a-z])(\\d+)/g, \"$1-$2\")\n          ];\n          return await searchForIcon(\n            result,\n            collection,\n            ids,\n            options\n          );\n        }\n      }\n    } else {\n      return await getCustomIcon(custom, collection, icon, options);\n    }\n  }\n};\n\nexport { loadIcon };\n", "import { getEmojiCodePoint } from './convert.mjs';\nimport { joinerEmoji, vs16Emoji } from './data.mjs';\n\nfunction getEmojiSequenceFromString(value) {\n  return value.trim().split(/[^0-9A-F]+/i).filter((item) => item.length > 0).map(getEmojiCodePoint);\n}\nfunction getSequenceFromEmojiStringOrKeyword(value) {\n  if (!value.match(/^[0-9a-fA-F-\\s]+$/)) {\n    const results = [];\n    for (const codePoint of value) {\n      const code = codePoint.codePointAt(0);\n      if (code) {\n        results.push(code);\n      } else {\n        return getEmojiSequenceFromString(value);\n      }\n    }\n    return results;\n  }\n  return getEmojiSequenceFromString(value);\n}\nfunction splitEmojiSequences(sequence, separator = joinerEmoji) {\n  const results = [];\n  let queue = [];\n  for (let i = 0; i < sequence.length; i++) {\n    const code = sequence[i];\n    if (code === separator) {\n      results.push(queue);\n      queue = [];\n    } else {\n      queue.push(code);\n    }\n  }\n  results.push(queue);\n  return results;\n}\nfunction joinEmojiSequences(sequences, separator = joinerEmoji) {\n  let results = [];\n  for (let i = 0; i < sequences.length; i++) {\n    if (i > 0) {\n      results.push(separator);\n    }\n    results = results.concat(sequences[i]);\n  }\n  return results;\n}\nfunction getUnqualifiedEmojiSequence(sequence) {\n  return sequence.filter((num) => num !== vs16Emoji);\n}\n\nexport { getEmojiSequenceFromString, getSequenceFromEmojiStringOrKeyword, getUnqualifiedEmojiSequence, joinEmojiSequences, splitEmojiSequences };\n", "import { minUTF32, startUTF32Pair1, startUTF32Pair2, endUTF32Pair } from './data.mjs';\n\nfunction getEmojiCodePoint(code) {\n  return parseInt(code, 16);\n}\nfunction utf32FirstNum(code) {\n  return (code - minUTF32 >> 10 | 0) + startUTF32Pair1;\n}\nfunction utf32SecondNum(code) {\n  return (code - minUTF32 & 1023) + startUTF32Pair2;\n}\nfunction splitUTF32Number(code) {\n  if (code >= minUTF32) {\n    return [utf32FirstNum(code), utf32SecondNum(code)];\n  }\n}\nfunction isUTF32SplitNumber(value) {\n  if (value >= startUTF32Pair1) {\n    if (value < startUTF32Pair2) {\n      return 1;\n    }\n    if (value < endUTF32Pair) {\n      return 2;\n    }\n  }\n  return false;\n}\nfunction mergeUTF32Numbers(part1, part2) {\n  if (part1 < startUTF32Pair1 || part1 >= startUTF32Pair2 || part2 < startUTF32Pair2 || part2 >= endUTF32Pair) {\n    return;\n  }\n  return (part1 - startUTF32Pair1 << 10) + (part2 - startUTF32Pair2) + minUTF32;\n}\nfunction getEmojiUnicode(code) {\n  return String.fromCodePoint(\n    typeof code === \"number\" ? code : getEmojiCodePoint(code)\n  );\n}\nfunction convertEmojiSequenceToUTF16(numbers) {\n  const results = [];\n  for (let i = 0; i < numbers.length; i++) {\n    const code = numbers[i];\n    if (code >= minUTF32) {\n      results.push(utf32FirstNum(code));\n      results.push(utf32SecondNum(code));\n    } else {\n      results.push(code);\n    }\n  }\n  return results;\n}\nfunction convertEmojiSequenceToUTF32(numbers, throwOnError = true) {\n  const results = [];\n  for (let i = 0; i < numbers.length; i++) {\n    const code = numbers[i];\n    if (code >= minUTF32) {\n      results.push(code);\n      continue;\n    }\n    const part = isUTF32SplitNumber(code);\n    if (!part) {\n      results.push(code);\n      continue;\n    }\n    if (part === 1 && numbers.length > i + 1) {\n      const merged = mergeUTF32Numbers(code, numbers[i + 1]);\n      if (merged) {\n        i++;\n        results.push(merged);\n        continue;\n      }\n    }\n    if (throwOnError) {\n      const nextCode = numbers[i + 1];\n      throw new Error(\n        `Invalid UTF-16 sequence: ${code.toString(16)}-${nextCode ? nextCode.toString(16) : \"undefined\"}`\n      );\n    }\n    results.push(code);\n  }\n  return results;\n}\n\nexport { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32, getEmojiCodePoint, getEmojiUnicode, isUTF32SplitNumber, mergeUTF32Numbers, splitUTF32Number };\n", "const joinerEmoji = 8205;\nconst vs16Emoji = 65039;\nconst keycapEmoji = 8419;\nconst emojiComponents = {\n  // Hair styles\n  \"hair-style\": [129456, 129460],\n  // Skin tones\n  \"skin-tone\": [127995, 128e3]\n};\nconst minUTF32 = 65536;\nconst startUTF32Pair1 = 55296;\nconst startUTF32Pair2 = 56320;\nconst endUTF32Pair = 57344;\nconst emojiVersion = \"16.0\";\n\nexport { emojiComponents, emojiVersion, endUTF32Pair, joinerEmoji, keycapEmoji, minUTF32, startUTF32Pair1, startUTF32Pair2, vs16Emoji };\n", "import { convertEmojiSequenceToUTF16, convertEmojiSequenceToUTF32 } from './convert.mjs';\nimport './data.mjs';\n\nconst defaultUnicodeOptions = {\n  prefix: \"\",\n  separator: \"\",\n  case: \"lower\",\n  format: \"utf-32\",\n  add0: false,\n  throwOnError: true\n};\nfunction convert(sequence, options) {\n  const prefix = options.prefix;\n  const func = options.case === \"upper\" ? \"toUpperCase\" : \"toLowerCase\";\n  const cleanSequence = options.format === \"utf-16\" ? convertEmojiSequenceToUTF16(sequence) : convertEmojiSequenceToUTF32(sequence, options.throwOnError);\n  return cleanSequence.map((code) => {\n    let str = code.toString(16);\n    if (options.add0 && str.length < 4) {\n      str = \"0\".repeat(4 - str.length) + str;\n    }\n    return prefix + str[func]();\n  }).join(options.separator);\n}\nfunction getEmojiUnicodeString(code, options = {}) {\n  return convert([code], {\n    ...defaultUnicodeOptions,\n    ...options\n  });\n}\nconst defaultSequenceOptions = {\n  ...defaultUnicodeOptions,\n  separator: \"-\"\n};\nfunction getEmojiSequenceString(sequence, options = {}) {\n  return convert(sequence, {\n    ...defaultSequenceOptions,\n    ...options\n  });\n}\nfunction getEmojiSequenceKeyword(sequence) {\n  return sequence.map((code) => code.toString(16)).join(\"-\");\n}\n\nexport { getEmojiSequenceKeyword, getEmojiSequenceString, getEmojiUnicodeString };\n", "import { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from '../cleanup.mjs';\nimport { getEmojiSequenceKeyword } from '../format.mjs';\nimport '../convert.mjs';\nimport '../data.mjs';\n\nconst componentStatus = \"component\";\nconst allowedStatus = /* @__PURE__ */ new Set([\n  componentStatus,\n  \"fully-qualified\",\n  \"minimally-qualified\",\n  \"unqualified\"\n]);\nfunction getQualifiedTestData(data) {\n  const results = /* @__PURE__ */ Object.create(null);\n  for (const key in data) {\n    const item = data[key];\n    const sequence = getUnqualifiedEmojiSequence(item.sequence);\n    const shortKey = getEmojiSequenceKeyword(sequence);\n    if (!results[shortKey] || results[shortKey].sequence.length < sequence.length) {\n      results[shortKey] = item;\n    }\n  }\n  return results;\n}\nfunction parseEmojiTestFile(data) {\n  const results = /* @__PURE__ */ Object.create(null);\n  let group;\n  let subgroup;\n  data.split(\"\\n\").forEach((line) => {\n    line = line.trim();\n    const parts = line.split(\"#\");\n    if (parts.length < 2) {\n      return;\n    }\n    const firstChunk = parts.shift().trim();\n    const secondChunk = parts.join(\"#\").trim();\n    if (!firstChunk) {\n      const commentParts = secondChunk.split(\":\");\n      if (commentParts.length === 2) {\n        const key2 = commentParts[0].trim();\n        const value = commentParts[1].trim();\n        switch (key2) {\n          case \"group\":\n            group = value;\n            subgroup = void 0;\n            break;\n          case \"subgroup\":\n            subgroup = value;\n            break;\n        }\n      }\n      return;\n    }\n    if (!group || !subgroup) {\n      return;\n    }\n    const firstChunkParts = firstChunk.split(\";\");\n    if (firstChunkParts.length !== 2) {\n      return;\n    }\n    const code = firstChunkParts[0].trim();\n    if (!code || !code.match(/^[A-F0-9]+[A-F0-9\\s]*[A-F0-9]+$/)) {\n      return;\n    }\n    const status = firstChunkParts[1].trim();\n    if (!allowedStatus.has(status)) {\n      throw new Error(`Bad emoji type: ${status}`);\n    }\n    const secondChunkParts = secondChunk.split(/\\s+/);\n    if (secondChunkParts.length < 3) {\n      throw new Error(`Bad emoji comment for: ${code}`);\n    }\n    const emoji = secondChunkParts.shift();\n    const version = secondChunkParts.shift();\n    if (version.slice(0, 1) !== \"E\") {\n      throw new Error(`Bad unicode version \"${version}\" for: ${code}`);\n    }\n    const name = secondChunkParts.join(\" \");\n    const sequence = getEmojiSequenceFromString(code);\n    const key = getEmojiSequenceKeyword(sequence);\n    if (results[key]) {\n      throw new Error(`Duplicate entry for \"${code}\"`);\n    }\n    results[key] = {\n      group,\n      subgroup,\n      sequence,\n      emoji,\n      status,\n      version,\n      name\n    };\n  });\n  return getQualifiedTestData(results);\n}\n\nexport { componentStatus, parseEmojiTestFile };\n", "import { splitEmojiSequences, joinEmojiSequences, getUnqualifiedEmojiSequence } from '../cleanup.mjs';\nimport { convertEmojiSequenceToUTF32 } from '../convert.mjs';\nimport { vs16Emoji, keycapEmoji, emojiComponents } from '../data.mjs';\nimport { getEmojiSequenceKeyword } from '../format.mjs';\n\nfunction guessQualifiedEmojiSequence(sequence) {\n  const split = splitEmojiSequences(sequence).map((part) => {\n    if (part.indexOf(vs16Emoji) !== -1) {\n      return part;\n    }\n    if (part.length === 2) {\n      const lastNum = part[1];\n      if (lastNum === keycapEmoji) {\n        return [part[0], vs16Emoji, lastNum];\n      }\n      for (const key in emojiComponents) {\n        const range = emojiComponents[key];\n        if (lastNum >= range[0] && lastNum < range[1]) {\n          return [part[0], vs16Emoji, lastNum];\n        }\n      }\n    }\n    return part.length === 1 ? [part[0], vs16Emoji] : part;\n  });\n  return joinEmojiSequences(split);\n}\nfunction getQualifiedEmojiVariation(item) {\n  const unqualifiedSequence = getUnqualifiedEmojiSequence(\n    convertEmojiSequenceToUTF32(item.sequence)\n  );\n  const result = {\n    ...item,\n    sequence: guessQualifiedEmojiSequence(unqualifiedSequence)\n  };\n  if (result.sequenceKey) {\n    result.sequenceKey = getEmojiSequenceKeyword(unqualifiedSequence);\n  }\n  return result;\n}\nfunction getQualifiedEmojiVariations(items) {\n  const results = /* @__PURE__ */ Object.create(null);\n  for (let i = 0; i < items.length; i++) {\n    const result = getQualifiedEmojiVariation(items[i]);\n    const key = getEmojiSequenceKeyword(\n      getUnqualifiedEmojiSequence(result.sequence)\n    );\n    if (!results[key] || results[key].sequence.length < result.sequence.length) {\n      results[key] = result;\n    }\n  }\n  return Object.values(results);\n}\n\nexport { getQualifiedEmojiVariation, getQualifiedEmojiVariations, guessQualifiedEmojiSequence };\n", "import { getUnqualifiedEmojiSequence } from '../cleanup.mjs';\nimport { emojiComponents } from '../data.mjs';\nimport { getEmojiSequenceKeyword } from '../format.mjs';\nimport { replaceEmojiComponentsInCombinedSequence } from './components.mjs';\nimport '../convert.mjs';\n\nfunction findMissingEmojis(sequences, testDataTree) {\n  const results = [];\n  const existingItems = /* @__PURE__ */ Object.create(null);\n  const copiedItems = /* @__PURE__ */ Object.create(null);\n  sequences.forEach((item) => {\n    const sequence = getUnqualifiedEmojiSequence(item.sequence);\n    const key = getEmojiSequenceKeyword(sequence);\n    if (!existingItems[key] || // If multiple matches for same sequence exist, use longest version\n    existingItems[key].sequence.length < item.sequence.length) {\n      existingItems[key] = item;\n    }\n  });\n  const iterate = (type, parentTree, parentValues, parentItem, deep) => {\n    const childTree = parentTree.children?.[type];\n    if (!childTree) {\n      return;\n    }\n    const range = emojiComponents[type];\n    for (let number = range[0]; number < range[1]; number++) {\n      const values = {\n        \"hair-style\": [...parentValues[\"hair-style\"]],\n        \"skin-tone\": [...parentValues[\"skin-tone\"]]\n      };\n      values[type].push(number);\n      const sequence = replaceEmojiComponentsInCombinedSequence(\n        childTree.item.sequence,\n        values\n      );\n      const key = getEmojiSequenceKeyword(\n        getUnqualifiedEmojiSequence(sequence)\n      );\n      const oldItem = existingItems[key];\n      let item;\n      if (oldItem) {\n        item = oldItem;\n      } else {\n        item = copiedItems[key];\n        if (!item) {\n          item = {\n            ...parentItem,\n            sequence\n          };\n          if (item.sequenceKey) {\n            item.sequenceKey = key;\n          }\n          copiedItems[key] = item;\n          results.push(item);\n        }\n      }\n      if (deep || oldItem) {\n        for (const key2 in values) {\n          iterate(\n            key2,\n            childTree,\n            values,\n            item,\n            deep\n          );\n        }\n      }\n    }\n  };\n  const parse = (key, deep) => {\n    const treeItem = testDataTree[key];\n    const sequenceKey = treeItem.item.sequenceKey;\n    const rootItem = existingItems[sequenceKey];\n    if (!rootItem) {\n      return;\n    }\n    const values = {\n      \"skin-tone\": [],\n      \"hair-style\": []\n    };\n    for (const key2 in values) {\n      iterate(\n        key2,\n        treeItem,\n        values,\n        rootItem,\n        deep\n      );\n    }\n  };\n  for (const key in testDataTree) {\n    parse(key, false);\n    parse(key, true);\n  }\n  return results;\n}\n\nexport { findMissingEmojis };\n", "import { emojiComponents } from '../data.mjs';\nimport { getEmojiSequenceKeyword } from '../format.mjs';\nimport '../convert.mjs';\n\nfunction mapEmojiTestDataComponents(testSequences) {\n  const results = {\n    converted: /* @__PURE__ */ new Map(),\n    items: /* @__PURE__ */ new Map(),\n    names: /* @__PURE__ */ new Map(),\n    types: {},\n    keywords: {}\n  };\n  for (const key in emojiComponents) {\n    const type = key;\n    const range = emojiComponents[type];\n    for (let number = range[0]; number < range[1]; number++) {\n      const keyword = getEmojiSequenceKeyword([number]);\n      const item = testSequences[keyword];\n      if (!item) {\n        throw new Error(\n          `Missing emoji component in test sequence: \"${keyword}\"`\n        );\n      }\n      results.converted.set(number, keyword);\n      results.items.set(number, item);\n      results.items.set(keyword, item);\n      const name = item.name;\n      results.names.set(number, name);\n      results.names.set(keyword, name);\n      results.types[name] = type;\n      results.keywords[name] = keyword;\n    }\n  }\n  return results;\n}\nfunction emojiSequenceWithComponentsToString(sequence) {\n  return sequence.map((item) => typeof item === \"number\" ? item.toString(16) : item).join(\"-\");\n}\nfunction findEmojiComponentsInSequence(sequence) {\n  const components = [];\n  for (let index = 0; index < sequence.length; index++) {\n    const code = sequence[index];\n    for (const key in emojiComponents) {\n      const type = key;\n      const range = emojiComponents[type];\n      if (code >= range[0] && code < range[1]) {\n        components.push({\n          index,\n          type\n        });\n        break;\n      }\n    }\n  }\n  return components;\n}\nfunction replaceEmojiComponentsInCombinedSequence(sequence, values) {\n  const indexes = {\n    \"hair-style\": 0,\n    \"skin-tone\": 0\n  };\n  return sequence.map((item) => {\n    if (typeof item === \"number\") {\n      return item;\n    }\n    const index = indexes[item]++;\n    const list = values[item];\n    if (!list || !list.length) {\n      throw new Error(`Cannot replace ${item}: no valid values provided`);\n    }\n    return list[index >= list.length ? list.length - 1 : index];\n  });\n}\n\nexport { emojiSequenceWithComponentsToString, findEmojiComponentsInSequence, mapEmojiTestDataComponents, replaceEmojiComponentsInCombinedSequence };\n", "import { getSequenceFromEmojiStringOrKeyword } from '../cleanup.mjs';\nimport { convertEmojiSequenceToUTF32 } from '../convert.mjs';\nimport { getQualifiedEmojiVariations } from '../test/variations.mjs';\nimport { createEmojisTree, parseEmojiTree } from './tree.mjs';\nimport '../data.mjs';\nimport '../format.mjs';\nimport './base.mjs';\nimport './numbers.mjs';\nimport './similar.mjs';\n\nfunction createOptimisedRegexForEmojiSequences(sequences) {\n  sequences = sequences.map((item) => convertEmojiSequenceToUTF32(item));\n  const tree = createEmojisTree(sequences);\n  const regex = parseEmojiTree(tree);\n  return regex.regex;\n}\nfunction createOptimisedRegex(emojis) {\n  let sequences = emojis.map(\n    (item) => typeof item === \"string\" ? getSequenceFromEmojiStringOrKeyword(item) : item\n  );\n  sequences = getQualifiedEmojiVariations(\n    sequences.map((sequence) => {\n      return {\n        sequence\n      };\n    })\n  ).map((item) => item.sequence);\n  return createOptimisedRegexForEmojiSequences(sequences);\n}\n\nexport { createOptimisedRegex, createOptimisedRegexForEmojiSequences };\n", "import { createUTF16EmojiRegexItem, createOptionalEmojiRegexItem, createSequenceEmojiRegexItem, createSetEmojiRegexItem } from './base.mjs';\nimport { splitEmojiSequences } from '../cleanup.mjs';\nimport { convertEmojiSequenceToUTF32 } from '../convert.mjs';\nimport { createRegexForNumbersSequence } from './numbers.mjs';\nimport { joinerEmoji } from '../data.mjs';\nimport { mergeSimilarItemsInSet } from './similar.mjs';\n\nfunction createEmojisTree(sequences) {\n  const root = [];\n  for (let i = 0; i < sequences.length; i++) {\n    const split = splitEmojiSequences(\n      convertEmojiSequenceToUTF32(sequences[i])\n    );\n    let parent = root;\n    for (let j = 0; j < split.length; j++) {\n      const regex = createRegexForNumbersSequence(split[j]);\n      let item;\n      const match = parent.find(\n        (item2) => item2.regex.regex === regex.regex\n      );\n      if (!match) {\n        item = {\n          regex\n        };\n        parent.push(item);\n      } else {\n        item = match;\n      }\n      if (j === split.length - 1) {\n        item.end = true;\n        break;\n      }\n      parent = item.children || (item.children = []);\n    }\n  }\n  return root;\n}\nfunction parseEmojiTree(items) {\n  function mergeParsedChildren(items2) {\n    const parsedItems = [];\n    const mapWithoutEnd = /* @__PURE__ */ Object.create(null);\n    const mapWithEnd = /* @__PURE__ */ Object.create(null);\n    for (let i = 0; i < items2.length; i++) {\n      const item = items2[i];\n      const children = item.children;\n      if (children) {\n        const fullItem = item;\n        const target = item.end ? mapWithEnd : mapWithoutEnd;\n        const regex = children.regex;\n        if (!target[regex]) {\n          target[regex] = [fullItem];\n        } else {\n          target[regex].push(fullItem);\n        }\n      } else {\n        parsedItems.push(item.regex);\n      }\n    }\n    [mapWithEnd, mapWithoutEnd].forEach((source) => {\n      for (const regex in source) {\n        const items3 = source[regex];\n        const firstItem = items3[0];\n        let childSequence = [\n          createUTF16EmojiRegexItem([joinerEmoji]),\n          firstItem.children\n        ];\n        if (firstItem.end) {\n          childSequence = [\n            createOptionalEmojiRegexItem(\n              createSequenceEmojiRegexItem(childSequence)\n            )\n          ];\n        }\n        let mergedRegex;\n        if (items3.length === 1) {\n          mergedRegex = firstItem.regex;\n        } else {\n          mergedRegex = mergeSimilarItemsInSet(\n            createSetEmojiRegexItem(items3.map((item) => item.regex))\n          );\n        }\n        const sequence = createSequenceEmojiRegexItem([\n          mergedRegex,\n          ...childSequence\n        ]);\n        parsedItems.push(sequence);\n      }\n    });\n    if (parsedItems.length === 1) {\n      return parsedItems[0];\n    }\n    const set = createSetEmojiRegexItem(parsedItems);\n    const result = mergeSimilarItemsInSet(set);\n    return result;\n  }\n  function parseItemChildren(item) {\n    const result = {\n      regex: item.regex,\n      end: !!item.end\n    };\n    const children = item.children;\n    if (!children) {\n      return result;\n    }\n    const parsedChildren = children.map(parseItemChildren);\n    result.children = mergeParsedChildren(parsedChildren);\n    return result;\n  }\n  const parsed = items.map(parseItemChildren);\n  return mergeParsedChildren(parsed);\n}\n\nexport { createEmojisTree, parseEmojiTree };\n", "function toString(number) {\n  if (number < 255) {\n    if (number > 32 && number < 127) {\n      const char = String.fromCharCode(number);\n      if (\n        // 0-9\n        number > 47 && number < 58 || // A-Z\n        number > 64 && number < 91 || // _`a-z\n        number > 94 && number < 123\n      ) {\n        return char;\n      }\n      return \"\\\\\" + char;\n    }\n    return \"\\\\x\" + (number < 16 ? \"0\" : \"\") + number.toString(16).toUpperCase();\n  }\n  return \"\\\\u\" + number.toString(16).toUpperCase();\n}\nfunction wrapRegexInGroup(regex) {\n  return \"(?:\" + regex + \")\";\n}\nfunction updateUTF16EmojiRegexItem(item) {\n  const numbers = item.numbers;\n  if (numbers.length === 1) {\n    const num = numbers[0];\n    return item.regex = toString(num);\n  }\n  numbers.sort((a, b) => a - b);\n  const chars = [];\n  let range = null;\n  const addRange = () => {\n    if (range) {\n      const { start, last, numbers: numbers2 } = range;\n      range = null;\n      if (last > start + 1) {\n        chars.push(toString(start) + \"-\" + toString(last));\n      } else {\n        for (let i = 0; i < numbers2.length; i++) {\n          chars.push(toString(numbers2[i]));\n        }\n      }\n    }\n  };\n  for (let i = 0; i < numbers.length; i++) {\n    const num = numbers[i];\n    if (range) {\n      if (range.last === num) {\n        continue;\n      }\n      if (range.last === num - 1) {\n        range.numbers.push(num);\n        range.last = num;\n        continue;\n      }\n    }\n    addRange();\n    range = {\n      start: num,\n      last: num,\n      numbers: [num]\n    };\n  }\n  addRange();\n  if (!chars.length) {\n    throw new Error(\"Unexpected empty range\");\n  }\n  return item.regex = \"[\" + chars.join(\"\") + \"]\";\n}\nfunction createUTF16EmojiRegexItem(numbers) {\n  const result = {\n    type: \"utf16\",\n    regex: \"\",\n    numbers,\n    length: 1,\n    group: true\n  };\n  updateUTF16EmojiRegexItem(result);\n  return result;\n}\nfunction updateSequenceEmojiRegexItem(item) {\n  return item.regex = item.items.map((childItem) => {\n    if (!childItem.group && childItem.type === \"set\") {\n      return wrapRegexInGroup(childItem.regex);\n    }\n    return childItem.regex;\n  }).join(\"\");\n}\nfunction createSequenceEmojiRegexItem(sequence, numbers) {\n  let items = [];\n  sequence.forEach((item) => {\n    if (item.type === \"sequence\") {\n      items = items.concat(item.items);\n    } else {\n      items.push(item);\n    }\n  });\n  if (!items.length) {\n    throw new Error(\"Empty sequence\");\n  }\n  const result = {\n    type: \"sequence\",\n    items,\n    regex: \"\",\n    length: items.reduce((length, item) => item.length + length, 0),\n    group: false\n  };\n  if (sequence.length === 1) {\n    const firstItem = sequence[0];\n    result.group = firstItem.group;\n    if (firstItem.type !== \"optional\") {\n      const numbers2 = firstItem.numbers;\n      if (numbers2) {\n        result.numbers = numbers2;\n      }\n    }\n  }\n  if (numbers) {\n    result.numbers = numbers;\n  }\n  updateSequenceEmojiRegexItem(result);\n  return result;\n}\nfunction updateSetEmojiRegexItem(item) {\n  if (item.sets.length === 1) {\n    const firstItem = item.sets[0];\n    item.group = firstItem.group;\n    return item.regex = firstItem.regex;\n  }\n  item.group = false;\n  return item.regex = item.sets.map((childItem) => childItem.regex).join(\"|\");\n}\nfunction createSetEmojiRegexItem(set) {\n  let sets = [];\n  let numbers = [];\n  set.forEach((item) => {\n    if (item.type === \"set\") {\n      sets = sets.concat(item.sets);\n    } else {\n      sets.push(item);\n    }\n    if (numbers) {\n      if (item.type === \"optional\" || !item.numbers) {\n        numbers = null;\n      } else {\n        numbers = [...numbers, ...item.numbers];\n      }\n    }\n  });\n  sets.sort((a, b) => {\n    if (a.length === b.length) {\n      return a.regex.localeCompare(b.regex);\n    }\n    return b.length - a.length;\n  });\n  const result = {\n    type: \"set\",\n    sets,\n    regex: \"\",\n    length: sets.reduce(\n      (length, item) => length ? Math.min(length, item.length) : item.length,\n      0\n    ),\n    group: false\n  };\n  if (numbers) {\n    result.numbers = numbers;\n  }\n  if (set.length === 1) {\n    const firstItem = set[0];\n    result.group = firstItem.group;\n  }\n  updateSetEmojiRegexItem(result);\n  return result;\n}\nfunction updateOptionalEmojiRegexItem(item) {\n  const childItem = item.item;\n  const regex = (childItem.group ? childItem.regex : wrapRegexInGroup(childItem.regex)) + \"?\";\n  return item.regex = regex;\n}\nfunction createOptionalEmojiRegexItem(item) {\n  if (item.type === \"optional\") {\n    return item;\n  }\n  const result = {\n    type: \"optional\",\n    item,\n    regex: \"\",\n    length: item.length,\n    group: true\n  };\n  updateOptionalEmojiRegexItem(result);\n  return result;\n}\nfunction cloneEmojiRegexItem(item, shallow = false) {\n  const result = {\n    ...item\n  };\n  if (result.type !== \"optional\" && result.numbers) {\n    result.numbers = [...result.numbers];\n  }\n  switch (result.type) {\n    case \"utf16\":\n      break;\n    case \"sequence\":\n      if (shallow) {\n        result.items = [...result.items];\n      } else {\n        result.items = result.items.map(\n          (item2) => cloneEmojiRegexItem(item2, false)\n        );\n      }\n      break;\n    case \"set\":\n      if (shallow) {\n        result.sets = [...result.sets];\n      } else {\n        result.sets = result.sets.map(\n          (item2) => cloneEmojiRegexItem(item2, false)\n        );\n      }\n      break;\n    case \"optional\":\n      if (!shallow) {\n        result.item = cloneEmojiRegexItem(result.item, false);\n      }\n      break;\n  }\n  return result;\n}\n\nexport { cloneEmojiRegexItem, createOptionalEmojiRegexItem, createSequenceEmojiRegexItem, createSetEmojiRegexItem, createUTF16EmojiRegexItem, updateOptionalEmojiRegexItem, updateSequenceEmojiRegexItem, updateSetEmojiRegexItem, updateUTF16EmojiRegexItem, wrapRegexInGroup };\n", "import { splitUTF32Number } from '../convert.mjs';\nimport { createUTF16EmojiRegexItem, createSequenceEmojiRegexItem, createSetEmojiRegexItem, createOptionalEmojiRegexItem } from './base.mjs';\nimport { vs16Emoji } from '../data.mjs';\n\nfunction createEmojiRegexItemForNumbers(numbers) {\n  const utf32 = [];\n  const utf16 = [];\n  numbers.sort((a, b) => a - b);\n  let lastNumber;\n  for (let i = 0; i < numbers.length; i++) {\n    const number = numbers[i];\n    if (number === lastNumber) {\n      continue;\n    }\n    lastNumber = number;\n    const split = splitUTF32Number(number);\n    if (!split) {\n      utf16.push(number);\n      continue;\n    }\n    const [first, second] = split;\n    const item = utf32.find((item2) => item2.first === first);\n    if (item) {\n      item.second.push(second);\n      item.numbers.push(number);\n    } else {\n      utf32.push({\n        first,\n        second: [second],\n        numbers: [number]\n      });\n    }\n  }\n  const results = [];\n  if (utf16.length) {\n    results.push(createUTF16EmojiRegexItem(utf16));\n  }\n  if (utf32.length) {\n    const utf32Set = [];\n    for (let i = 0; i < utf32.length; i++) {\n      const item = utf32[i];\n      const secondRegex = createUTF16EmojiRegexItem(item.second);\n      const listItem = utf32Set.find(\n        (item2) => item2.second.regex === secondRegex.regex\n      );\n      if (listItem) {\n        listItem.first.push(item.first);\n        listItem.numbers = [...listItem.numbers, ...item.numbers];\n      } else {\n        utf32Set.push({\n          second: secondRegex,\n          first: [item.first],\n          numbers: [...item.numbers]\n        });\n      }\n    }\n    for (let i = 0; i < utf32Set.length; i++) {\n      const item = utf32Set[i];\n      const firstRegex = createUTF16EmojiRegexItem(item.first);\n      const secondRegex = item.second;\n      results.push(\n        createSequenceEmojiRegexItem(\n          [firstRegex, secondRegex],\n          item.numbers\n        )\n      );\n    }\n  }\n  return results.length === 1 ? results[0] : createSetEmojiRegexItem(results);\n}\nfunction createRegexForNumbersSequence(numbers, optionalVariations = true) {\n  const items = [];\n  for (let i = 0; i < numbers.length; i++) {\n    const num = numbers[i];\n    const split = splitUTF32Number(num);\n    if (!split) {\n      const item = createUTF16EmojiRegexItem([num]);\n      if (optionalVariations && num === vs16Emoji) {\n        items.push(createOptionalEmojiRegexItem(item));\n      } else {\n        items.push(item);\n      }\n    } else {\n      items.push(createUTF16EmojiRegexItem([split[0]]));\n      items.push(createUTF16EmojiRegexItem([split[1]]));\n    }\n  }\n  if (items.length === 1) {\n    return items[0];\n  }\n  const result = createSequenceEmojiRegexItem(items);\n  if (numbers.length === 1 && items[0].type === \"utf16\") {\n    result.numbers = [...numbers];\n  }\n  return result;\n}\nfunction optimiseNumbersSet(set) {\n  const mandatoryMatches = {\n    numbers: [],\n    items: []\n  };\n  const optionalMatches = {\n    numbers: [],\n    items: []\n  };\n  const filteredItems = set.sets.filter((item) => {\n    if (item.type === \"optional\") {\n      const parentItem = item.item;\n      if (parentItem.numbers) {\n        optionalMatches.items.push(item);\n        optionalMatches.numbers = optionalMatches.numbers.concat(\n          parentItem.numbers\n        );\n        return false;\n      }\n      return true;\n    }\n    if (item.numbers) {\n      mandatoryMatches.items.push(item);\n      mandatoryMatches.numbers = mandatoryMatches.numbers.concat(\n        item.numbers\n      );\n      return false;\n    }\n    return true;\n  });\n  if (mandatoryMatches.items.length + optionalMatches.items.length < 2) {\n    return set;\n  }\n  const optionalNumbers = new Set(optionalMatches.numbers);\n  let foundMatches = false;\n  mandatoryMatches.numbers = mandatoryMatches.numbers.filter((number) => {\n    if (optionalNumbers.has(number)) {\n      foundMatches = true;\n      return false;\n    }\n    return true;\n  });\n  if (mandatoryMatches.items.length) {\n    if (!foundMatches && mandatoryMatches.items.length === 1) {\n      filteredItems.push(mandatoryMatches.items[0]);\n    } else if (mandatoryMatches.numbers.length) {\n      filteredItems.push(\n        createEmojiRegexItemForNumbers(mandatoryMatches.numbers)\n      );\n    }\n  }\n  switch (optionalMatches.items.length) {\n    case 0:\n      break;\n    case 1:\n      filteredItems.push(optionalMatches.items[0]);\n      break;\n    default:\n      filteredItems.push(\n        createOptionalEmojiRegexItem(\n          createEmojiRegexItemForNumbers(optionalMatches.numbers)\n        )\n      );\n  }\n  return filteredItems.length === 1 ? filteredItems[0] : createSetEmojiRegexItem(filteredItems);\n}\n\nexport { createEmojiRegexItemForNumbers, createRegexForNumbersSequence, optimiseNumbersSet };\n", "import { createSequenceEmojiRegexItem, createSetEmojiRegexItem, createOptionalEmojiRegexItem, cloneEmojiRegexItem } from './base.mjs';\nimport { optimiseNumbersSet } from './numbers.mjs';\nimport '../convert.mjs';\nimport '../data.mjs';\n\nfunction findSimilarRegexItemSequences(items) {\n  const startRegex = /* @__PURE__ */ Object.create(null);\n  const endRegex = /* @__PURE__ */ Object.create(null);\n  const addMapItem = (target, index, regex, slice) => {\n    if (!target[regex]) {\n      target[regex] = {\n        // Start with 0. One item will remain after replacement\n        score: 0,\n        slices: [\n          {\n            index,\n            slice\n          }\n        ]\n      };\n      return;\n    }\n    const item = target[regex];\n    item.score += regex.length;\n    item.slices.push({\n      index,\n      slice\n    });\n  };\n  for (let index = 0; index < items.length; index++) {\n    const baseItem = items[index];\n    switch (baseItem.type) {\n      case \"optional\":\n      case \"utf16\": {\n        addMapItem(startRegex, index, baseItem.regex, \"full\");\n        addMapItem(endRegex, index, baseItem.regex, \"full\");\n        break;\n      }\n      case \"sequence\": {\n        addMapItem(startRegex, index, baseItem.regex, \"full\");\n        addMapItem(endRegex, index, baseItem.regex, \"full\");\n        const sequence = baseItem.items;\n        for (let i = 1; i < sequence.length; i++) {\n          const startSequence = createSequenceEmojiRegexItem(\n            sequence.slice(0, i)\n          );\n          addMapItem(startRegex, index, startSequence.regex, i);\n          const endSequence = createSequenceEmojiRegexItem(\n            sequence.slice(i)\n          );\n          addMapItem(endRegex, index, endSequence.regex, i);\n        }\n        break;\n      }\n      case \"set\":\n        throw new Error(\"Unexpected set within a set\");\n    }\n  }\n  let result;\n  const checkResults = (target, type) => {\n    for (const regex in target) {\n      const item = target[regex];\n      if (!item.score) {\n        continue;\n      }\n      if (!result || result.score < item.score) {\n        result = {\n          score: item.score,\n          sequences: [\n            {\n              type,\n              slices: item.slices\n            }\n          ]\n        };\n        continue;\n      }\n      if (result.score === item.score) {\n        result.sequences.push({\n          type,\n          slices: item.slices\n        });\n      }\n    }\n  };\n  checkResults(startRegex, \"start\");\n  checkResults(endRegex, \"end\");\n  return result;\n}\nfunction mergeSimilarRegexItemSequences(items, merge, optimise) {\n  const { type, slices } = merge;\n  const indexes = /* @__PURE__ */ new Set();\n  let hasFullSequence = false;\n  let longestMatch = 0;\n  let longestMatchIndex = -1;\n  const differentSequences = [];\n  for (let i = 0; i < slices.length; i++) {\n    const { index, slice } = slices[i];\n    const item = items[index];\n    let length;\n    if (slice === \"full\") {\n      hasFullSequence = true;\n      if (item.type === \"sequence\") {\n        length = item.items.length;\n      } else {\n        length = 1;\n      }\n    } else {\n      if (item.type !== \"sequence\") {\n        throw new Error(\n          `Unexpected partial match for type \"${item.type}\"`\n        );\n      }\n      length = type === \"start\" ? slice : item.items.length - slice;\n      differentSequences.push(\n        type === \"start\" ? item.items.slice(slice) : item.items.slice(0, slice)\n      );\n    }\n    if (length > longestMatch) {\n      longestMatchIndex = index;\n      longestMatch = length;\n    }\n    indexes.add(index);\n  }\n  if (longestMatch < 1 || longestMatchIndex < 0) {\n    throw new Error(\"Cannot find common sequence\");\n  }\n  const commonItem = items[longestMatchIndex];\n  let sequence;\n  if (commonItem.type !== \"sequence\") {\n    if (longestMatch !== 1) {\n      throw new Error(\n        \"Something went wrong. Cannot have long match in non-sequence\"\n      );\n    }\n    sequence = [commonItem];\n  } else {\n    sequence = type === \"start\" ? commonItem.items.slice(0, longestMatch) : commonItem.items.slice(\n      commonItem.items.length - longestMatch\n    );\n  }\n  const setItems = [];\n  for (let i = 0; i < differentSequences.length; i++) {\n    const list = differentSequences[i];\n    if (list.length === 1) {\n      setItems.push(list[0]);\n    } else {\n      setItems.push(createSequenceEmojiRegexItem(list));\n    }\n  }\n  const set = createSetEmojiRegexItem(setItems);\n  let mergedChunk = set.sets.length === 1 ? (\n    // Do not run callback if only 1 item\n    set.sets[0]\n  ) : optimise ? (\n    // Run callback to optimise it\n    optimise(set)\n  ) : (\n    // Use set as is\n    set\n  );\n  if (hasFullSequence) {\n    mergedChunk = createOptionalEmojiRegexItem(mergedChunk);\n  }\n  sequence[type === \"start\" ? \"push\" : \"unshift\"](mergedChunk);\n  const results = [\n    createSequenceEmojiRegexItem(sequence),\n    ...items.filter((item, index) => !indexes.has(index))\n  ];\n  return results;\n}\nfunction mergeSimilarItemsInSet(set) {\n  const updatedSet = optimiseNumbersSet(set);\n  if (updatedSet.type !== \"set\") {\n    return updatedSet;\n  }\n  set = updatedSet;\n  let merges;\n  while (merges = findSimilarRegexItemSequences(set.sets)) {\n    const sequences = merges.sequences;\n    if (sequences.length === 1) {\n      const merged = mergeSimilarRegexItemSequences(\n        set.sets.map((item) => cloneEmojiRegexItem(item, true)),\n        sequences[0],\n        mergeSimilarItemsInSet\n      );\n      if (merged.length === 1) {\n        return merged[0];\n      }\n      set = createSetEmojiRegexItem(merged);\n      continue;\n    }\n    let newItem;\n    for (let i = 0; i < sequences.length; i++) {\n      const merged = mergeSimilarRegexItemSequences(\n        set.sets.map((item) => cloneEmojiRegexItem(item, true)),\n        sequences[i],\n        mergeSimilarItemsInSet\n      );\n      const mergedItem = merged.length === 1 ? merged[0] : createSetEmojiRegexItem(merged);\n      if (!newItem || mergedItem.regex.length < newItem.regex.length) {\n        newItem = mergedItem;\n      }\n    }\n    if (!newItem) {\n      throw new Error(\"Empty sequences list\");\n    }\n    if (newItem.type !== \"set\") {\n      return newItem;\n    }\n    set = newItem;\n  }\n  return set;\n}\n\nexport { findSimilarRegexItemSequences, mergeSimilarItemsInSet, mergeSimilarRegexItemSequences };\n", "import { getEmojiSequenceFromString, getUnqualifiedEmojiSequence } from './cleanup.mjs';\nimport { getEmojiSequenceKeyword } from './format.mjs';\nimport { createOptimisedRegexForEmojiSequences } from './regex/create.mjs';\nimport { findMissingEmojis } from './test/missing.mjs';\nimport { parseEmojiTestFile } from './test/parse.mjs';\nimport { combineSimilarEmojiTestData } from './test/similar.mjs';\nimport { getEmojiTestDataTree } from './test/tree.mjs';\nimport { getQualifiedEmojiVariations } from './test/variations.mjs';\nimport './convert.mjs';\nimport './data.mjs';\nimport './regex/tree.mjs';\nimport './regex/base.mjs';\nimport './regex/numbers.mjs';\nimport './regex/similar.mjs';\nimport './test/components.mjs';\nimport './test/name.mjs';\n\nfunction prepareEmojiForIconsList(icons, rawTestData) {\n  const testData = rawTestData ? parseEmojiTestFile(rawTestData) : void 0;\n  let iconsList = [];\n  for (const char in icons) {\n    const sequence = getEmojiSequenceFromString(char);\n    iconsList.push({\n      icon: icons[char],\n      sequence\n    });\n  }\n  iconsList = getQualifiedEmojiVariations(iconsList);\n  if (testData) {\n    iconsList = iconsList.concat(\n      findMissingEmojis(\n        iconsList,\n        getEmojiTestDataTree(combineSimilarEmojiTestData(testData))\n      )\n    );\n  }\n  const preparedIcons = iconsList.map((item) => {\n    const sequence = getEmojiSequenceKeyword(\n      getUnqualifiedEmojiSequence(item.sequence)\n    );\n    return {\n      icon: item.icon,\n      sequence\n    };\n  });\n  const regex = createOptimisedRegexForEmojiSequences(\n    iconsList.map((item) => item.sequence)\n  );\n  return {\n    regex,\n    icons: preparedIcons\n  };\n}\nfunction prepareEmojiForIconSet(iconSet, rawTestData) {\n  return prepareEmojiForIconsList(iconSet.chars || {}, rawTestData);\n}\n\nexport { prepareEmojiForIconSet, prepareEmojiForIconsList };\n", "import { vs16Emoji } from '../data.mjs';\nimport { emojiSequenceWithComponentsToString, mapEmojiTestDataComponents } from './components.mjs';\nimport { splitEmojiNameVariations } from './name.mjs';\nimport '../format.mjs';\nimport '../convert.mjs';\n\nfunction findComponentsInEmojiTestItem(item, componentsData) {\n  const name = splitEmojiNameVariations(\n    item.name,\n    item.sequence,\n    componentsData\n  );\n  const sequence = [...item.sequence];\n  name.variations?.forEach((item2) => {\n    if (typeof item2 !== \"string\") {\n      sequence[item2.index] = item2.type;\n    }\n  });\n  const sequenceKey = emojiSequenceWithComponentsToString(\n    sequence.filter((code) => code !== vs16Emoji)\n  );\n  return {\n    ...item,\n    name,\n    sequenceKey,\n    sequence\n  };\n}\nfunction combineSimilarEmojiTestData(data, componentsData) {\n  const results = /* @__PURE__ */ Object.create(null);\n  componentsData = componentsData || mapEmojiTestDataComponents(data);\n  for (const key in data) {\n    const sourceItem = data[key];\n    if (sourceItem.status !== \"component\") {\n      const item = findComponentsInEmojiTestItem(\n        sourceItem,\n        componentsData\n      );\n      results[item.sequenceKey] = item;\n    }\n  }\n  return results;\n}\n\nexport { combineSimilarEmojiTestData, findComponentsInEmojiTestItem };\n", "import { emojiComponents } from '../data.mjs';\n\nconst nameSplit = \": \";\nconst variationSplit = \", \";\nconst ignoredVariations = /* @__PURE__ */ new Set([\"person\"]);\nfunction splitEmojiNameVariations(name, sequence, componentsData) {\n  const parts = name.split(nameSplit);\n  const base = parts.shift();\n  if (!parts.length) {\n    return {\n      base,\n      key: base\n    };\n  }\n  const baseVariations = parts.join(nameSplit).split(variationSplit).filter((text) => {\n    const type = componentsData.types[text];\n    if (!type) {\n      return !ignoredVariations.has(text);\n    }\n    return false;\n  });\n  const key = base + (baseVariations.length ? nameSplit + baseVariations.join(variationSplit) : \"\");\n  const result = {\n    base,\n    key\n  };\n  let components = 0;\n  const variations = [\n    ...baseVariations\n  ];\n  for (let index = 0; index < sequence.length; index++) {\n    const num = sequence[index];\n    for (const key2 in emojiComponents) {\n      const type = key2;\n      const range = emojiComponents[type];\n      if (num >= range[0] && num < range[1]) {\n        variations.push({\n          index,\n          type\n        });\n        components++;\n      }\n    }\n  }\n  if (variations.length) {\n    result.variations = variations;\n  }\n  if (components) {\n    result.components = components;\n  }\n  return result;\n}\n\nexport { splitEmojiNameVariations };\n", "import { emojiComponents } from '../data.mjs';\n\nfunction mergeComponentTypes(value) {\n  return \"[\" + value.join(\",\") + \"]\";\n}\nfunction mergeComponentsCount(value) {\n  const keys = [];\n  for (const key in emojiComponents) {\n    const type = key;\n    for (let i = 0; i < value[type]; i++) {\n      keys.push(type);\n    }\n  }\n  return keys.length ? mergeComponentTypes(keys) : \"\";\n}\nfunction getGroupItem(items, components) {\n  const key = mergeComponentsCount(components);\n  const item = items[key];\n  if (item) {\n    item.parsed = true;\n    return item.item;\n  }\n}\nfunction getEmojiTestDataTree(data) {\n  const groups = /* @__PURE__ */ Object.create(null);\n  for (const key in data) {\n    const item = data[key];\n    const text = item.name.key;\n    const parent = groups[text] || (groups[text] = {});\n    const components = {\n      \"hair-style\": 0,\n      \"skin-tone\": 0\n    };\n    item.sequence.forEach((value) => {\n      if (typeof value !== \"number\") {\n        components[value]++;\n      }\n    });\n    const componentsKey = mergeComponentsCount(components);\n    if (parent[componentsKey]) {\n      throw new Error(`Duplicate components tree item for \"${text}\"`);\n    }\n    parent[componentsKey] = {\n      item: {\n        ...item,\n        components,\n        componentsKey\n      }\n    };\n  }\n  const results = /* @__PURE__ */ Object.create(null);\n  for (const key in groups) {\n    const items = groups[key];\n    const check = (parent, parentComponents, type) => {\n      const item = parse(parentComponents, [type]);\n      if (item) {\n        const children = parent.children || (parent.children = {});\n        children[type] = item;\n        return true;\n      }\n    };\n    const parse = (parentComponents, newComponents) => {\n      const components = {\n        \"hair-style\": 0,\n        \"skin-tone\": 0\n      };\n      const componentsList = parentComponents.concat(newComponents);\n      componentsList.forEach((type) => {\n        components[type]++;\n      });\n      let item = getGroupItem(items, components);\n      if (!item && newComponents.length === 1 && newComponents[0] === \"skin-tone\") {\n        const doubleComponents = {\n          ...components\n        };\n        doubleComponents[\"skin-tone\"]++;\n        item = getGroupItem(items, doubleComponents);\n      }\n      if (item) {\n        const result = {\n          item\n        };\n        for (const key2 in emojiComponents) {\n          check(result, componentsList, key2);\n        }\n        return result;\n      }\n    };\n    const root = parse([], []);\n    if (!root) {\n      throw new Error(`Cannot find parent item for \"${key}\"`);\n    }\n    for (const itemsKey in items) {\n      if (!items[itemsKey].parsed) {\n        throw new Error(`Error generating tree for \"${key}\"`);\n      }\n    }\n    if (root.children) {\n      results[key] = root;\n    }\n  }\n  return results;\n}\n\nexport { getEmojiTestDataTree };\n", "import { getEmojiMatchesInText, sortEmojiMatchesInText } from './find.mjs';\nimport '../convert.mjs';\nimport '../data.mjs';\nimport '../format.mjs';\n\nfunction findAndReplaceEmojisInText(regexp, content, callback) {\n  const matches = getEmojiMatchesInText(regexp, content);\n  if (!matches.length) {\n    return null;\n  }\n  const sortedMatches = sortEmojiMatchesInText(content, matches);\n  let result = \"\";\n  let replaced = false;\n  for (let i = 0; i < sortedMatches.length; i++) {\n    const item = sortedMatches[i];\n    result += item.prev;\n    const replacement = callback(\n      {\n        ...item.match\n      },\n      result\n    );\n    if (replacement === void 0) {\n      result += item.match.match;\n    } else {\n      result += replacement;\n      replaced = true;\n    }\n  }\n  result += sortedMatches[sortedMatches.length - 1].next;\n  return replaced ? result : null;\n}\n\nexport { findAndReplaceEmojisInText };\n", "import { convertEmojiSequenceToUTF32 } from '../convert.mjs';\nimport { vs16Emoji } from '../data.mjs';\nimport { getEmojiSequenceKeyword } from '../format.mjs';\n\nfunction createEmojiRegExp(regexp) {\n  return new RegExp(regexp, \"g\");\n}\nfunction getEmojiMatchesInText(regexp, content) {\n  const results = [];\n  const found = /* @__PURE__ */ new Set();\n  (regexp instanceof Array ? regexp : [regexp]).forEach((regexp2, index) => {\n    const matches = content.match(\n      typeof regexp2 === \"string\" ? createEmojiRegExp(regexp2) : regexp2\n    );\n    if (matches) {\n      for (let i = 0; i < matches.length; i++) {\n        const match = matches[i];\n        if (found.has(match)) {\n          continue;\n        }\n        found.add(match);\n        const sequence = [];\n        for (const codePoint of match) {\n          const num = codePoint.codePointAt(0);\n          if (num !== vs16Emoji) {\n            sequence.push(num);\n          }\n        }\n        results.push({\n          match,\n          sequence,\n          keyword: getEmojiSequenceKeyword(\n            convertEmojiSequenceToUTF32(sequence)\n          ),\n          regexp: index\n        });\n      }\n    }\n  });\n  results.sort((a, b) => {\n    const match1 = a.match;\n    const match2 = b.match;\n    if (match2.length === match1.length) {\n      return match1.localeCompare(match2);\n    }\n    return match2.length - match1.length;\n  });\n  return results;\n}\nfunction sortEmojiMatchesInText(content, matches) {\n  const ranges = [];\n  const check = (start, end) => {\n    for (let i = 0; i < ranges.length; i++) {\n      if (start < ranges[i].end && end > ranges[i].start) {\n        return false;\n      }\n    }\n    return true;\n  };\n  for (let i = 0; i < matches.length; i++) {\n    const match = matches[i];\n    const search = match.match;\n    let startFrom = 0;\n    let start;\n    while ((start = content.indexOf(search, startFrom)) !== -1) {\n      const end = start + search.length;\n      startFrom = end;\n      if (check(start, end)) {\n        ranges.push({\n          start,\n          end,\n          match\n        });\n      }\n    }\n  }\n  ranges.sort((a, b) => a.start - b.start);\n  const list = [];\n  let prevRange;\n  let lastEnd;\n  for (let i = 0; i < ranges.length; i++) {\n    const range = ranges[i];\n    const prev = content.slice(prevRange ? prevRange.end : 0, range.start);\n    list.push({\n      match: range.match,\n      prev\n    });\n    prevRange = range;\n    lastEnd = range.end;\n  }\n  if (!lastEnd) {\n    return [];\n  }\n  const replacements = list.map((item, index) => {\n    const nextItem = list[index + 1];\n    return {\n      ...item,\n      next: nextItem ? nextItem.prev : content.slice(lastEnd)\n    };\n  });\n  return replacements;\n}\n\nexport { createEmojiRegExp, getEmojiMatchesInText, sortEmojiMatchesInText };\n", "function camelize(str) {\n  return str.replace(/-([a-z0-9])/g, (g) => g[1].toUpperCase());\n}\nfunction pascalize(str) {\n  const camel = camelize(str);\n  return camel.slice(0, 1).toUpperCase() + camel.slice(1);\n}\nfunction camelToKebab(key) {\n  const result = key.replace(/:/g, \"-\").replace(/([A-Z])/g, \" $1\").trim();\n  return result.split(/\\s+/g).join(\"-\").toLowerCase();\n}\nfunction snakelize(str) {\n  const kebab = camelToKebab(str);\n  return kebab.replace(/-/g, \"_\");\n}\n\nexport { camelToKebab, camelize, pascalize, snakelize };\n", "function compareObjects(obj1, obj2, ref = obj1) {\n  for (const key in ref) {\n    if (obj1[key] !== obj2[key]) {\n      return false;\n    }\n  }\n  return Object.keys(obj1).length === Object.keys(obj2).length;\n}\nfunction unmergeObjects(obj1, obj2) {\n  const result = {\n    ...obj1\n  };\n  for (const key in obj2) {\n    if (result[key] === obj2[key]) {\n      delete result[key];\n    }\n  }\n  return result;\n}\nfunction commonObjectProps(item, reference) {\n  const result = /* @__PURE__ */ Object.create(null);\n  for (const key in reference) {\n    if (key in item) {\n      result[key] = item[key];\n    }\n  }\n  return result;\n}\n\nexport { commonObjectProps, compareObjects, unmergeObjects };\n", "function sanitiseTitleAttribute(content) {\n  return content.replace(/[<>&]+/g, \"\");\n}\n\nexport { sanitiseTitleAttribute };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAM,wBAAwB,OAAO;AAAA,EACnC;AAAA,IACE,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AACA,IAAM,6BAA6B,OAAO,OAAO;AAAA,EAC/C,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT,CAAC;AACD,IAAM,mBAAmB,OAAO,OAAO;AAAA,EACrC,GAAG;AAAA,EACH,GAAG;AACL,CAAC;AACD,IAAM,2BAA2B,OAAO,OAAO;AAAA,EAC7C,GAAG;AAAA,EACH,MAAM;AAAA,EACN,QAAQ;AACV,CAAC;;;ADnBD,IAAM,gCAAgC,OAAO,OAAO;AAAA,EAClD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,IAAM,4BAA4B,OAAO,OAAO;AAAA;AAAA,EAE9C,GAAG;AAAA;AAAA,EAEH,GAAG;AACL,CAAC;;;AEXD,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AACA,IAAM,eAAe,CAAC,OAAO,UAAU,iBAAiB,WAAW,OAAO;AACxE,QAAM,iBAAiB,MAAM,MAAM,GAAG;AACtC,MAAI,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK;AAC7B,QAAI,eAAe,SAAS,KAAK,eAAe,SAAS,GAAG;AAC1D,aAAO;AAAA,IACT;AACA,eAAW,eAAe,MAAM,EAAE,MAAM,CAAC;AAAA,EAC3C;AACA,MAAI,eAAe,SAAS,KAAK,CAAC,eAAe,QAAQ;AACvD,WAAO;AAAA,EACT;AACA,MAAI,eAAe,SAAS,GAAG;AAC7B,UAAM,QAAQ,eAAe,IAAI;AACjC,UAAM,SAAS,eAAe,IAAI;AAClC,UAAM,SAAS;AAAA;AAAA,MAEb,UAAU,eAAe,SAAS,IAAI,eAAe,CAAC,IAAI;AAAA,MAC1D;AAAA,MACA,MAAM;AAAA,IACR;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,QAAM,OAAO,eAAe,CAAC;AAC7B,QAAM,gBAAgB,KAAK,MAAM,GAAG;AACpC,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ,cAAc,MAAM;AAAA,MAC5B,MAAM,cAAc,KAAK,GAAG;AAAA,IAC9B;AACA,WAAO,YAAY,CAAC,iBAAiB,MAAM,IAAI,OAAO;AAAA,EACxD;AACA,MAAI,mBAAmB,aAAa,IAAI;AACtC,UAAM,SAAS;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,IACF;AACA,WAAO,YAAY,CAAC,iBAAiB,QAAQ,eAAe,IAAI,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAM,mBAAmB,CAAC,MAAM,oBAAoB;AAClD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AAAA;AAAA,IAEN,mBAAmB,KAAK,WAAW,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,KAAK;AACtE;;;AClDA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,SAAS,yBAAyB,MAAM,MAAM;AAC5C,QAAM,SAAS,CAAC;AAChB,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAC/B,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,WAAW,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM;AAC3D,MAAI,QAAQ;AACV,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;;;ADVA,SAAS,cAAc,QAAQ,OAAO;AACpC,QAAM,SAAS,yBAAyB,QAAQ,KAAK;AACrD,aAAW,OAAO,0BAA0B;AAC1C,QAAI,OAAO,4BAA4B;AACrC,UAAI,OAAO,UAAU,EAAE,OAAO,SAAS;AACrC,eAAO,GAAG,IAAI,2BAA2B,GAAG;AAAA,MAC9C;AAAA,IACF,WAAW,OAAO,OAAO;AACvB,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB,WAAW,OAAO,QAAQ;AACxB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;;;AEjBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,SAAS,aAAa,MAAM,OAAO;AACjC,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,QAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAS,QAAQ,MAAM;AACrB,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,SAAS,IAAI,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,EAAE,QAAQ,WAAW;AACvB,eAAS,IAAI,IAAI;AACjB,YAAM,SAAS,QAAQ,IAAI,KAAK,QAAQ,IAAI,EAAE;AAC9C,YAAM,QAAQ,UAAU,QAAQ,MAAM;AACtC,UAAI,OAAO;AACT,iBAAS,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK;AAAA,MACxC;AAAA,IACF;AACA,WAAO,SAAS,IAAI;AAAA,EACtB;AACA,GAAC,SAAS,OAAO,KAAK,KAAK,EAAE,OAAO,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,OAAO;AAC1E,SAAO;AACT;;;ACpBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAKA,SAAS,oBAAoB,MAAM,MAAM,MAAM;AAC7C,QAAM,QAAQ,KAAK;AACnB,QAAM,UAAU,KAAK,WAA2B,uBAAO,OAAO,IAAI;AAClE,MAAI,eAAe,CAAC;AACpB,WAAS,MAAM,OAAO;AACpB,mBAAe;AAAA,MACb,MAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI;AACV,OAAK,QAAQ,KAAK;AAClB,SAAO,cAAc,MAAM,YAAY;AACzC;AACA,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,KAAK,MAAM,IAAI,GAAG;AACpB,WAAO,oBAAoB,MAAM,MAAM,CAAC,CAAC;AAAA,EAC3C;AACA,QAAM,OAAO,aAAa,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI;AAC5C,SAAO,OAAO,oBAAoB,MAAM,MAAM,IAAI,IAAI;AACxD;;;ACzBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAEA,IAAM,2BAA2B;AAAA,EAC/B,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,WAAW,CAAC;AAAA,EACZ,GAAG;AACL;;;ACPA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAGA,IAAM,cAAc,OAAO,KAAK,qBAAqB,EAAE,OAAO;AAAA,EAC5D;AACF,CAAC;;;ACLD,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,SAAS,cAAc,MAAM,OAAO,WAAW;AAC7C,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,cAAY,aAAa;AACzB,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,KAAK,KAAK,OAAO,QAAQ,SAAS,IAAI;AAAA,EAC/C;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,UAAU;AACtC,MAAI,aAAa,QAAQ,CAAC,SAAS,QAAQ;AACzC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,CAAC;AAClB,MAAI,OAAO,SAAS,MAAM;AAC1B,MAAI,WAAW,UAAU,KAAK,IAAI;AAClC,SAAO,MAAM;AACX,QAAI,UAAU;AACZ,YAAM,MAAM,WAAW,IAAI;AAC3B,UAAI,MAAM,GAAG,GAAG;AACd,iBAAS,KAAK,IAAI;AAAA,MACpB,OAAO;AACL,iBAAS,KAAK,KAAK,KAAK,MAAM,QAAQ,SAAS,IAAI,SAAS;AAAA,MAC9D;AAAA,IACF,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,WAAO,SAAS,MAAM;AACtB,QAAI,SAAS,QAAQ;AACnB,aAAO,SAAS,KAAK,EAAE;AAAA,IACzB;AACA,eAAW,CAAC;AAAA,EACd;AACF;;;ACrCA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,SAAS,aAAa,SAAS,MAAM,QAAQ;AAC3C,MAAI,OAAO;AACX,QAAM,QAAQ,QAAQ,QAAQ,MAAM,GAAG;AACvC,SAAO,SAAS,GAAG;AACjB,UAAM,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACxC,UAAM,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACtC,QAAI,UAAU,MAAM,QAAQ,IAAI;AAC9B;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,QAAQ,KAAK,GAAG;AACvC,QAAI,WAAW,IAAI;AACjB;AAAA,IACF;AACA,YAAQ,QAAQ,MAAM,QAAQ,GAAG,GAAG,EAAE,KAAK;AAC3C,cAAU,QAAQ,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,QAAQ,MAAM,SAAS,CAAC;AAAA,EACrE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,oBAAoB,MAAM,SAAS;AAC1C,SAAO,OAAO,WAAW,OAAO,YAAY,UAAU;AACxD;AACA,SAAS,eAAe,MAAM,OAAO,KAAK;AACxC,QAAM,QAAQ,aAAa,IAAI;AAC/B,SAAO,oBAAoB,MAAM,MAAM,QAAQ,MAAM,UAAU,GAAG;AACpE;;;AFtBA,IAAM,iBAAiB,CAAC,UAAU,UAAU,WAAW,UAAU,eAAe,UAAU;AAC1F,SAAS,UAAU,MAAM,gBAAgB;AACvC,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,qBAAqB;AAAA,IACzB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,MAAM;AAAA,IACV,MAAM,SAAS;AAAA,IACf,KAAK,SAAS;AAAA,IACd,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,EACnB;AACA,MAAI,OAAO,SAAS;AACpB,GAAC,UAAU,kBAAkB,EAAE,QAAQ,CAAC,UAAU;AAChD,UAAM,kBAAkB,CAAC;AACzB,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,MAAM;AACpB,QAAI,WAAW,MAAM;AACrB,QAAI,OAAO;AACT,UAAI,OAAO;AACT,oBAAY;AAAA,MACd,OAAO;AACL,wBAAgB;AAAA,UACd,gBAAgB,IAAI,QAAQ,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACtF;AACA,wBAAgB,KAAK,aAAa;AAClC,YAAI,MAAM,IAAI,OAAO;AAAA,MACvB;AAAA,IACF,WAAW,OAAO;AAChB,sBAAgB;AAAA,QACd,gBAAgB,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS,IAAI;AAAA,MACvF;AACA,sBAAgB,KAAK,aAAa;AAClC,UAAI,MAAM,IAAI,OAAO;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,WAAW,GAAG;AAChB,kBAAY,KAAK,MAAM,WAAW,CAAC,IAAI;AAAA,IACzC;AACA,eAAW,WAAW;AACtB,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,oBAAY,IAAI,SAAS,IAAI,IAAI;AACjC,wBAAgB;AAAA,UACd,eAAe,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACrE;AACA;AAAA,MACF,KAAK;AACH,wBAAgB;AAAA,UACd,iBAAiB,IAAI,QAAQ,IAAI,IAAI,MAAM,SAAS,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,QACxG;AACA;AAAA,MACF,KAAK;AACH,oBAAY,IAAI,QAAQ,IAAI,IAAI;AAChC,wBAAgB;AAAA,UACd,gBAAgB,UAAU,SAAS,IAAI,MAAM,UAAU,SAAS,IAAI;AAAA,QACtE;AACA;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,GAAG;AACtB,UAAI,IAAI,SAAS,IAAI,KAAK;AACxB,oBAAY,IAAI;AAChB,YAAI,OAAO,IAAI;AACf,YAAI,MAAM;AAAA,MACZ;AACA,UAAI,IAAI,UAAU,IAAI,QAAQ;AAC5B,oBAAY,IAAI;AAChB,YAAI,QAAQ,IAAI;AAChB,YAAI,SAAS;AAAA,MACf;AAAA,IACF;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO;AAAA,QACL;AAAA,QACA,mBAAmB,gBAAgB,KAAK,GAAG,IAAI;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,sBAAsB,mBAAmB;AAC/C,QAAM,uBAAuB,mBAAmB;AAChD,QAAM,WAAW,IAAI;AACrB,QAAM,YAAY,IAAI;AACtB,MAAI;AACJ,MAAI;AACJ,MAAI,wBAAwB,MAAM;AAChC,aAAS,yBAAyB,OAAO,QAAQ,yBAAyB,SAAS,YAAY;AAC/F,YAAQ,cAAc,QAAQ,WAAW,SAAS;AAAA,EACpD,OAAO;AACL,YAAQ,wBAAwB,SAAS,WAAW;AACpD,aAAS,yBAAyB,OAAO,cAAc,OAAO,YAAY,QAAQ,IAAI,yBAAyB,SAAS,YAAY;AAAA,EACtI;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,CAAC,MAAM,UAAU;AAC/B,QAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,iBAAW,IAAI,IAAI,MAAM,SAAS;AAAA,IACpC;AAAA,EACF;AACA,UAAQ,SAAS,KAAK;AACtB,UAAQ,UAAU,MAAM;AACxB,QAAM,UAAU,CAAC,IAAI,MAAM,IAAI,KAAK,UAAU,SAAS;AACvD,aAAW,UAAU,QAAQ,KAAK,GAAG;AACrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AGpHA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAM,QAAQ;AACd,IAAM,eAAe,cAAc,KAAK,IAAI,EAAE,SAAS,EAAE,KAAK,KAAK,OAAO,IAAI,WAAW,GAAG,SAAS,EAAE;AACvG,IAAI,UAAU;AACd,SAAS,WAAW,MAAM,SAAS,cAAc;AAC/C,QAAM,MAAM,CAAC;AACb,MAAI;AACJ,SAAO,QAAQ,MAAM,KAAK,IAAI,GAAG;AAC/B,QAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EACnB;AACA,MAAI,CAAC,IAAI,QAAQ;AACf,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,KAAK,OAAO,IAAI,WAAW,KAAK,IAAI,GAAG,SAAS,EAAE;AAC7E,MAAI,QAAQ,CAAC,OAAO;AAClB,UAAM,QAAQ,OAAO,WAAW,aAAa,OAAO,EAAE,IAAI,UAAU,WAAW,SAAS;AACxF,UAAM,YAAY,GAAG,QAAQ,uBAAuB,MAAM;AAC1D,WAAO,KAAK;AAAA;AAAA;AAAA,MAGV,IAAI,OAAO,aAAa,YAAY,oBAAoB,GAAG;AAAA,MAC3D,OAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,KAAK,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG,EAAE;AAC/C,SAAO;AACT;;;ACzBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,SAAS,WAAW,MAAM,YAAY;AACpC,MAAI,oBAAoB,KAAK,QAAQ,QAAQ,MAAM,KAAK,KAAK;AAC7D,aAAW,QAAQ,YAAY;AAC7B,yBAAqB,MAAM,OAAO,OAAO,WAAW,IAAI,IAAI;AAAA,EAC9D;AACA,SAAO,4CAA4C,oBAAoB,MAAM,OAAO;AACtF;;;ACNA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAM,gBAAgB;AAAA,EACpB,aAAa;AAAA,IACX,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AACF;AACA,SAAS,IAAI,SAAS,QAAQ;AAC5B,QAAM,OAAO;AACb,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,SAAS,OAAO;AACtB,gBAAc,OAAO,IAAI;AAAA,IACvB;AAAA,IACA;AAAA,IACA,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,IAC5B,GAAG,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,IAC5B,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,EAClC;AACF;AACA,IAAI,UAAU,CAAC,GAAG,CAAC;AACnB,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,IAAI,SAAS,CAAC,GAAG,CAAC;AAClB,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AACtB,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACtB,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC;AACrB,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC;AACpB,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC;AAC1B,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3B,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,CAAC;AAChB,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC;AAChC,IAAI,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;AAC1B,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC;AAC/B,IAAI,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;AAC/B,IAAI,SAAS,CAAC,KAAK,KAAK,EAAE,CAAC;AAC3B,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;AAC5B,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC;AAC3B,IAAI,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC;AAC7B,IAAI,iBAAiB,CAAC,KAAK,KAAK,EAAE,CAAC;AACnC,IAAI,YAAY,CAAC,GAAG,CAAC;AACrB,IAAI,aAAa,CAAC,GAAG,GAAG,CAAC;AACzB,IAAI,YAAY,CAAC,GAAG,CAAC;AACrB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC3B,IAAI,kBAAkB,CAAC,IAAI,KAAK,EAAE,CAAC;AACnC,IAAI,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC;AAC/B,IAAI,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC;AAChC,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1B,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,gBAAgB,CAAC,KAAK,GAAG,CAAC;AAC9B,IAAI,iBAAiB,CAAC,IAAI,IAAI,GAAG,CAAC;AAClC,IAAI,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;AACjC,IAAI,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;AACjC,IAAI,iBAAiB,CAAC,GAAG,KAAK,GAAG,CAAC;AAClC,IAAI,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;AAC/B,IAAI,YAAY,CAAC,KAAK,IAAI,GAAG,CAAC;AAC9B,IAAI,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC;AAChC,IAAI,WAAW,CAAC,GAAG,CAAC;AACpB,IAAI,WAAW,CAAC,GAAG,CAAC;AACpB,IAAI,cAAc,CAAC,IAAI,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9B,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,eAAe,CAAC,IAAI,GAAG,CAAC;AAC5B,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,aAAa,CAAC,GAAG,CAAC;AACtB,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC;AACzB,IAAI,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;AAC/B,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC;AACrB,IAAI,eAAe,CAAC,KAAK,KAAK,EAAE,CAAC;AACjC,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,IAAI,YAAY,CAAC,KAAK,GAAG,CAAC;AAC1B,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG,CAAC;AAC9B,IAAI,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9B,IAAI,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC;AAC1B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC;AAC9B,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,wBAAwB,CAAC,KAAK,KAAK,GAAG,CAAC;AAC3C,IAAI,aAAa,CAAC,GAAG,CAAC;AACtB,IAAI,cAAc,CAAC,KAAK,GAAG,CAAC;AAC5B,IAAI,aAAa,CAAC,GAAG,CAAC;AACtB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,iBAAiB,CAAC,IAAI,KAAK,GAAG,CAAC;AACnC,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,kBAAkB,CAAC,KAAK,KAAK,GAAG,CAAC;AACrC,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC;AACpB,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;AAC1B,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB,IAAI,oBAAoB,CAAC,KAAK,KAAK,GAAG,CAAC;AACvC,IAAI,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC;AAC7B,IAAI,gBAAgB,CAAC,KAAK,IAAI,GAAG,CAAC;AAClC,IAAI,gBAAgB,CAAC,KAAK,KAAK,GAAG,CAAC;AACnC,IAAI,kBAAkB,CAAC,IAAI,KAAK,GAAG,CAAC;AACpC,IAAI,mBAAmB,CAAC,KAAK,KAAK,GAAG,CAAC;AACtC,IAAI,qBAAqB,CAAC,GAAG,KAAK,GAAG,CAAC;AACtC,IAAI,mBAAmB,CAAC,IAAI,KAAK,GAAG,CAAC;AACrC,IAAI,mBAAmB,CAAC,KAAK,IAAI,GAAG,CAAC;AACrC,IAAI,gBAAgB,CAAC,IAAI,IAAI,GAAG,CAAC;AACjC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,eAAe,CAAC,KAAK,KAAK,GAAG,CAAC;AAClC,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG,CAAC;AAC9B,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC;AAC1B,IAAI,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;AAC/B,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3B,IAAI,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;AAC7B,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7B,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,aAAa,CAAC,KAAK,GAAG,CAAC;AAC3B,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,iBAAiB,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,QAAQ,CAAC,KAAK,KAAK,EAAE,CAAC;AAC1B,IAAI,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAC3B,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC;AACtB,IAAI,cAAc,CAAC,KAAK,KAAK,GAAG,CAAC;AACjC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;AACtB,IAAI,iBAAiB,CAAC,KAAK,IAAI,GAAG,CAAC;AACnC,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AACtB,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC;AAChC,IAAI,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC;AAC7B,IAAI,cAAc,CAAC,KAAK,KAAK,EAAE,CAAC;AAChC,IAAI,YAAY,CAAC,IAAI,KAAK,EAAE,CAAC;AAC7B,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG,CAAC;AAC/B,IAAI,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC;AAC3B,IAAI,UAAU,CAAC,GAAG,CAAC;AACnB,IAAI,WAAW,CAAC,KAAK,KAAK,GAAG,CAAC;AAC9B,IAAI,aAAa,CAAC,KAAK,IAAI,GAAG,CAAC;AAC/B,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,KAAK,KAAK,GAAG,CAAC;AAChC,IAAI,QAAQ,CAAC,KAAK,KAAK,GAAG,CAAC;AAC3B,IAAI,eAAe,CAAC,GAAG,KAAK,GAAG,CAAC;AAChC,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC;AAC1B,IAAI,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;AACzB,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC;AACzB,IAAI,UAAU,CAAC,KAAK,IAAI,EAAE,CAAC;AAC3B,IAAI,aAAa,CAAC,IAAI,KAAK,GAAG,CAAC;AAC/B,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC;AACxB,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC;AAC5B,IAAI,SAAS,CAAC,GAAG,CAAC;AAClB,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,IAAI,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC;AAC3B,IAAI,eAAe,CAAC,KAAK,KAAK,EAAE,CAAC;;;ACzLjC,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAeA,IAAM,iBAAiB;AACvB,IAAM,eAAe;AAErB,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA,kBAAkB,iBAAiB;AACrC;;;ACtBA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAAA,mBAA2B;AAS3B,IAAM,YAAQ,aAAAC,SAAe,wBAAwB;;;ACTrD,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAIA,IAAAC,gBAA2B;AAQ3B,IAAMC,aAAQ,cAAAC,SAAe,sBAAsB;;;ACZnD,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAEA,IAAAC,gBAAO;;;ACFP,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;AAGA,IAAM,wBAAwB;AAAA,EAC5B,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,cAAc;AAChB;AAmBA,IAAM,yBAAyB;AAAA,EAC7B,GAAG;AAAA,EACH,WAAW;AACb;;;AChCA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;ACAA,IAAAC,iBAAA;AAAA,IAAAA,iBAAA;AAAA,IAAAA,iBAAA;;;A/DqDA,IAAAC,gBAAO;;;AD9CP,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AACT;AACA,IAAI,aAA6B,oBAAI,IAAI;AACzC,IAAI,cAA8B,oBAAI,IAAI;AAC1C,IAAI,oBAAoC,OAAO,CAAC,gBAAgB;AAC9D,aAAW,cAAc,aAAa;AACpC,QAAI,CAAC,WAAW,MAAM;AACpB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,0BAA0B,WAAW,IAAI;AACnD,QAAI,YAAY,YAAY;AAC1B,kBAAY,IAAI,WAAW,MAAM,WAAW,MAAM;AAAA,IACpD,WAAW,WAAW,YAAY;AAChC,iBAAW,IAAI,WAAW,MAAM,WAAW,KAAK;AAAA,IAClD,OAAO;AACL,UAAI,MAAM,wBAAwB,UAAU;AAC5C,YAAM,IAAI,MAAM,qEAAqE;AAAA,IACvF;AAAA,EACF;AACF,GAAG,mBAAmB;AACtB,IAAI,wBAAwC,OAAO,OAAO,UAAU,mBAAmB;AACrF,QAAM,OAAO,aAAa,UAAU,MAAM,mBAAmB,MAAM;AACnE,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,sBAAsB,QAAQ,EAAE;AAAA,EAClD;AACA,QAAM,SAAS,KAAK,UAAU;AAC9B,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,oCAAoC,QAAQ,EAAE;AAAA,EAChE;AACA,MAAI,QAAQ,WAAW,IAAI,MAAM;AACjC,MAAI,CAAC,OAAO;AACV,UAAM,SAAS,YAAY,IAAI,MAAM;AACrC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,uBAAuB,KAAK,MAAM,EAAE;AAAA,IACtD;AACA,QAAI;AACF,YAAM,SAAS,MAAM,OAAO;AAC5B,cAAQ,EAAE,GAAG,QAAQ,OAAO;AAC5B,iBAAW,IAAI,QAAQ,KAAK;AAAA,IAC9B,SAAS,GAAG;AACV,UAAI,MAAM,CAAC;AACX,YAAM,IAAI,MAAM,4BAA4B,KAAK,MAAM,EAAE;AAAA,IAC3D;AAAA,EACF;AACA,QAAM,WAAW,YAAY,OAAO,KAAK,IAAI;AAC7C,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,mBAAmB,QAAQ,EAAE;AAAA,EAC/C;AACA,SAAO;AACT,GAAG,uBAAuB;AAC1B,IAAI,aAA6B,OAAO,OAAO,UAAU,mBAAmB;AAC1E,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,sBAAsB,UAAU,iDAAgB,cAAc;AAAA,EACjF,SAAS,GAAG;AACV,QAAI,MAAM,CAAC;AACX,eAAW;AAAA,EACb;AACA,QAAM,aAAa,UAAU,UAAU,cAAc;AACrD,QAAM,MAAM,WAAW,WAAW,WAAW,IAAI,GAAG,WAAW,UAAU;AACzE,SAAO;AACT,GAAG,YAAY;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "createDebugger", "import_dist", "import_debug", "debug", "createDebugger", "import_dist", "import_debug", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_debug"]}