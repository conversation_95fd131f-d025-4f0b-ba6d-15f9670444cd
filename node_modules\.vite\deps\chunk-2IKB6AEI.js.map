{"version": 3, "sources": ["../../@internationalized/date/dist/packages/@internationalized/date/src/utils.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/GregorianCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/manipulation.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/string.ts", "../../@swc/helpers/esm/_class_private_field_init.js", "../../@swc/helpers/esm/_check_private_redeclaration.js", "../../@internationalized/date/dist/packages/@internationalized/date/src/CalendarDate.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/conversion.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/weekStartData.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/queries.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/BuddhistCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/EthiopicCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/HebrewCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/IndianCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/IslamicCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/JapaneseCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/PersianCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/calendars/TaiwanCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/createCalendar.ts", "../../@internationalized/date/dist/packages/@internationalized/date/src/DateFormatter.ts", "../../@floating-ui/dom/dist/floating-ui.dom.mjs", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@floating-ui/vue/dist/floating-ui.vue.mjs", "../../@floating-ui/vue/node_modules/vue-demi/lib/index.mjs", "../../@internationalized/number/dist/packages/@internationalized/number/src/NumberFormatter.ts", "../../@internationalized/number/dist/packages/@internationalized/number/src/NumberParser.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type Mutable<T> = {\n  -readonly[P in keyof T]: T[P]\n};\n\nexport function mod(amount: number, numerator: number): number {\n  return amount - numerator * Math.floor(amount / numerator);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst EPOCH = 1721426; // 001/01/03 Julian C.E.\nexport function gregorianToJulianDay(era: string, year: number, month: number, day: number): number {\n  year = getExtendedYear(era, year);\n\n  let y1 = year - 1;\n  let monthOffset = -2;\n  if (month <= 2) {\n    monthOffset = 0;\n  } else if (isLeapYear(year)) {\n    monthOffset = -1;\n  }\n\n  return (\n    EPOCH -\n    1 +\n    365 * y1 +\n    Math.floor(y1 / 4) -\n    Math.floor(y1 / 100) +\n    Math.floor(y1 / 400) +\n    Math.floor((367 * month - 362) / 12 + monthOffset + day)\n  );\n}\n\nexport function isLeapYear(year: number): boolean {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function getExtendedYear(era: string, year: number): number {\n  return era === 'BC' ? 1 - year : year;\n}\n\nexport function fromExtendedYear(year: number): [string, number] {\n  let era = 'AD';\n  if (year <= 0) {\n    era = 'BC';\n    year = 1 - year;\n  }\n\n  return [era, year];\n}\n\nconst daysInMonth = {\n  standard: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n  leapyear: [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\n};\n\n/**\n * The Gregorian calendar is the most commonly used calendar system in the world. It supports two eras: BC, and AD.\n * Years always contain 12 months, and 365 or 366 days depending on whether it is a leap year.\n */\nexport class GregorianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'gregory';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let jd0 = jd;\n    let depoch = jd0 - EPOCH;\n    let quadricent = Math.floor(depoch / 146097);\n    let dqc = mod(depoch, 146097);\n    let cent = Math.floor(dqc / 36524);\n    let dcent = mod(dqc, 36524);\n    let quad = Math.floor(dcent / 1461);\n    let dquad = mod(dcent, 1461);\n    let yindex = Math.floor(dquad / 365);\n\n    let extendedYear = quadricent * 400 + cent * 100 + quad * 4 + yindex + (cent !== 4 && yindex !== 4 ? 1 : 0);\n    let [era, year] = fromExtendedYear(extendedYear);\n    let yearDay = jd0 - gregorianToJulianDay(era, year, 1, 1);\n    let leapAdj = 2;\n    if (jd0 < gregorianToJulianDay(era, year, 3, 1)) {\n      leapAdj = 0;\n    } else if (isLeapYear(year)) {\n      leapAdj = 1;\n    }\n    let month = Math.floor(((yearDay + leapAdj) * 12 + 373) / 367);\n    let day = jd0 - gregorianToJulianDay(era, year, month, 1) + 1;\n\n    return new CalendarDate(era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return gregorianToJulianDay(date.era, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return daysInMonth[isLeapYear(date.year) ? 'leapyear' : 'standard'][date.month - 1];\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 366 : 365;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getYearsInEra(date: AnyCalendarDate): number {\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['BC', 'AD'];\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BC';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BC' ? 'AD' : 'BC';\n      date.year = 1 - date.year;\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, toAbsolute, toCalendar, toCalendarDateTime} from './conversion';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mutable} from './utils';\n\nconst ONE_HOUR = 3600000;\n\nexport function add(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function add(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): Mutable<AnyCalendarDate | AnyDateTime> {\n  let mutableDate: Mutable<AnyCalendarDate | AnyDateTime> = date.copy();\n  let days = 'hour' in mutableDate ? addTimeFields(mutableDate, duration) : 0;\n\n  addYears(mutableDate, duration.years || 0);\n  if (mutableDate.calendar.balanceYearMonth) {\n    mutableDate.calendar.balanceYearMonth(mutableDate, date);\n  }\n\n  mutableDate.month += duration.months || 0;\n\n  balanceYearMonth(mutableDate);\n  constrainMonthDay(mutableDate);\n\n  mutableDate.day += (duration.weeks || 0) * 7;\n  mutableDate.day += duration.days || 0;\n  mutableDate.day += days;\n\n  balanceDay(mutableDate);\n\n  if (mutableDate.calendar.balanceDate) {\n    mutableDate.calendar.balanceDate(mutableDate);\n  }\n\n  // Constrain in case adding ended up with a date outside the valid range for the calendar system.\n  // The behavior here is slightly different than when constraining in the `set` function in that\n  // we adjust smaller fields to their minimum/maximum values rather than constraining each field\n  // individually. This matches the general behavior of `add` vs `set` regarding how fields are balanced.\n  if (mutableDate.year < 1) {\n    mutableDate.year = 1;\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxYear = mutableDate.calendar.getYearsInEra(mutableDate);\n  if (mutableDate.year > maxYear) {\n    let isInverseEra = mutableDate.calendar.isInverseEra?.(mutableDate);\n    mutableDate.year = maxYear;\n    mutableDate.month = isInverseEra ? 1 : mutableDate.calendar.getMonthsInYear(mutableDate);\n    mutableDate.day = isInverseEra ? 1 : mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  if (mutableDate.month < 1) {\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxMonth = mutableDate.calendar.getMonthsInYear(mutableDate);\n  if (mutableDate.month > maxMonth) {\n    mutableDate.month = maxMonth;\n    mutableDate.day = mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  mutableDate.day = Math.max(1, Math.min(mutableDate.calendar.getDaysInMonth(mutableDate), mutableDate.day));\n  return mutableDate;\n}\n\nfunction addYears(date: Mutable<AnyCalendarDate>, years: number) {\n  if (date.calendar.isInverseEra?.(date)) {\n    years = -years;\n  }\n\n  date.year += years;\n}\n\nfunction balanceYearMonth(date: Mutable<AnyCalendarDate>) {\n  while (date.month < 1) {\n    addYears(date, -1);\n    date.month += date.calendar.getMonthsInYear(date);\n  }\n\n  let monthsInYear = 0;\n  while (date.month > (monthsInYear = date.calendar.getMonthsInYear(date))) {\n    date.month -= monthsInYear;\n    addYears(date, 1);\n  }\n}\n\nfunction balanceDay(date: Mutable<AnyCalendarDate>) {\n  while (date.day < 1) {\n    date.month--;\n    balanceYearMonth(date);\n    date.day += date.calendar.getDaysInMonth(date);\n  }\n\n  while (date.day > date.calendar.getDaysInMonth(date)) {\n    date.day -= date.calendar.getDaysInMonth(date);\n    date.month++;\n    balanceYearMonth(date);\n  }\n}\n\nfunction constrainMonthDay(date: Mutable<AnyCalendarDate>) {\n  date.month = Math.max(1, Math.min(date.calendar.getMonthsInYear(date), date.month));\n  date.day = Math.max(1, Math.min(date.calendar.getDaysInMonth(date), date.day));\n}\n\nexport function constrain(date: Mutable<AnyCalendarDate>): void {\n  if (date.calendar.constrainDate) {\n    date.calendar.constrainDate(date);\n  }\n\n  date.year = Math.max(1, Math.min(date.calendar.getYearsInEra(date), date.year));\n  constrainMonthDay(date);\n}\n\nexport function invertDuration(duration: DateTimeDuration): DateTimeDuration {\n  let inverseDuration = {};\n  for (let key in duration) {\n    if (typeof duration[key] === 'number') {\n      inverseDuration[key] = -duration[key];\n    }\n  }\n\n  return inverseDuration;\n}\n\nexport function subtract(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function subtract(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function subtract(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime {\n  return add(date, invertDuration(duration));\n}\n\nexport function set(date: CalendarDateTime, fields: DateFields): CalendarDateTime;\nexport function set(date: CalendarDate, fields: DateFields): CalendarDate;\nexport function set(date: CalendarDate | CalendarDateTime, fields: DateFields): Mutable<AnyCalendarDate> {\n  let mutableDate: Mutable<AnyCalendarDate> = date.copy();\n\n  if (fields.era != null) {\n    mutableDate.era = fields.era;\n  }\n\n  if (fields.year != null) {\n    mutableDate.year = fields.year;\n  }\n\n  if (fields.month != null) {\n    mutableDate.month = fields.month;\n  }\n\n  if (fields.day != null) {\n    mutableDate.day = fields.day;\n  }\n\n  constrain(mutableDate);\n  return mutableDate;\n}\n\nexport function setTime(value: CalendarDateTime, fields: TimeFields): CalendarDateTime;\nexport function setTime(value: Time, fields: TimeFields): Time;\nexport function setTime(value: Time | CalendarDateTime, fields: TimeFields): Mutable<Time | CalendarDateTime> {\n  let mutableValue: Mutable<Time | CalendarDateTime> = value.copy();\n\n  if (fields.hour != null) {\n    mutableValue.hour = fields.hour;\n  }\n\n  if (fields.minute != null) {\n    mutableValue.minute = fields.minute;\n  }\n\n  if (fields.second != null) {\n    mutableValue.second = fields.second;\n  }\n\n  if (fields.millisecond != null) {\n    mutableValue.millisecond = fields.millisecond;\n  }\n\n  constrainTime(mutableValue);\n  return mutableValue;\n}\n\nfunction balanceTime(time: Mutable<AnyTime>): number {\n  time.second += Math.floor(time.millisecond / 1000);\n  time.millisecond = nonNegativeMod(time.millisecond, 1000);\n\n  time.minute += Math.floor(time.second / 60);\n  time.second = nonNegativeMod(time.second, 60);\n\n  time.hour += Math.floor(time.minute / 60);\n  time.minute = nonNegativeMod(time.minute, 60);\n\n  let days = Math.floor(time.hour / 24);\n  time.hour = nonNegativeMod(time.hour, 24);\n\n  return days;\n}\n\nexport function constrainTime(time: Mutable<AnyTime>): void {\n  time.millisecond = Math.max(0, Math.min(time.millisecond, 1000));\n  time.second = Math.max(0, Math.min(time.second, 59));\n  time.minute = Math.max(0, Math.min(time.minute, 59));\n  time.hour = Math.max(0, Math.min(time.hour, 23));\n}\n\nfunction nonNegativeMod(a: number, b: number) {\n  let result = a % b;\n  if (result < 0) {\n    result += b;\n  }\n  return result;\n}\n\nfunction addTimeFields(time: Mutable<AnyTime>, duration: TimeDuration): number {\n  time.hour += duration.hours || 0;\n  time.minute += duration.minutes || 0;\n  time.second += duration.seconds || 0;\n  time.millisecond += duration.milliseconds || 0;\n  return balanceTime(time);\n}\n\nexport function addTime(time: Time, duration: TimeDuration): Time {\n  let res = time.copy();\n  addTimeFields(res, duration);\n  return res;\n}\n\nexport function subtractTime(time: Time, duration: TimeDuration): Time {\n  return addTime(time, invertDuration(duration));\n}\n\nexport function cycleDate(value: CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): CalendarDateTime;\nexport function cycleDate(value: CalendarDate, field: DateField, amount: number, options?: CycleOptions): CalendarDate;\nexport function cycleDate(value: CalendarDate | CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): Mutable<CalendarDate | CalendarDateTime> {\n  let mutable: Mutable<CalendarDate | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'era': {\n      let eras = value.calendar.getEras();\n      let eraIndex = eras.indexOf(value.era);\n      if (eraIndex < 0) {\n        throw new Error('Invalid era: ' + value.era);\n      }\n      eraIndex = cycleValue(eraIndex, amount, 0, eras.length - 1, options?.round);\n      mutable.era = eras[eraIndex];\n\n      // Constrain the year and other fields within the era, so the era doesn't change when we balance below.\n      constrain(mutable);\n      break;\n    }\n    case 'year': {\n      if (mutable.calendar.isInverseEra?.(mutable)) {\n        amount = -amount;\n      }\n\n      // The year field should not cycle within the era as that can cause weird behavior affecting other fields.\n      // We need to also allow values < 1 so that decrementing goes to the previous era. If we get -Infinity back\n      // we know we wrapped around after reaching 9999 (the maximum), so set the year back to 1.\n      mutable.year = cycleValue(value.year, amount, -Infinity, 9999, options?.round);\n      if (mutable.year === -Infinity) {\n        mutable.year = 1;\n      }\n\n      if (mutable.calendar.balanceYearMonth) {\n        mutable.calendar.balanceYearMonth(mutable, value);\n      }\n      break;\n    }\n    case 'month':\n      mutable.month = cycleValue(value.month, amount, 1, value.calendar.getMonthsInYear(value), options?.round);\n      break;\n    case 'day':\n      mutable.day = cycleValue(value.day, amount, 1, value.calendar.getDaysInMonth(value), options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  if (value.calendar.balanceDate) {\n    value.calendar.balanceDate(mutable);\n  }\n\n  constrain(mutable);\n  return mutable;\n}\n\nexport function cycleTime(value: CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime;\nexport function cycleTime(value: Time, field: TimeField, amount: number, options?: CycleTimeOptions): Time;\nexport function cycleTime(value: Time | CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): Mutable<Time | CalendarDateTime> {\n  let mutable: Mutable<Time | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'hour': {\n      let hours = value.hour;\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = hours >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n      mutable.hour = cycleValue(hours, amount, min, max, options?.round);\n      break;\n    }\n    case 'minute':\n      mutable.minute = cycleValue(value.minute, amount, 0, 59, options?.round);\n      break;\n    case 'second':\n      mutable.second = cycleValue(value.second, amount, 0, 59, options?.round);\n      break;\n    case 'millisecond':\n      mutable.millisecond = cycleValue(value.millisecond, amount, 0, 999, options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  return mutable;\n}\n\nfunction cycleValue(value: number, amount: number, min: number, max: number, round = false) {\n  if (round) {\n    value += Math.sign(amount);\n\n    if (value < min) {\n      value = max;\n    }\n\n    let div = Math.abs(amount);\n    if (amount > 0) {\n      value = Math.ceil(value / div) * div;\n    } else {\n      value = Math.floor(value / div) * div;\n    }\n\n    if (value > max) {\n      value = min;\n    }\n  } else {\n    value += amount;\n    if (value < min) {\n      value = max - (min - value - 1);\n    } else if (value > max) {\n      value = min + (value - max - 1);\n    }\n  }\n\n  return value;\n}\n\nexport function addZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  let ms: number;\n  if ((duration.years != null && duration.years !== 0) || (duration.months != null && duration.months !== 0) || (duration.weeks != null && duration.weeks !== 0) || (duration.days != null && duration.days !== 0)) {\n    let res = add(toCalendarDateTime(dateTime), {\n      years: duration.years,\n      months: duration.months,\n      weeks: duration.weeks,\n      days: duration.days\n    });\n\n    // Changing the date may change the timezone offset, so we need to recompute\n    // using the 'compatible' disambiguation.\n    ms = toAbsolute(res, dateTime.timeZone);\n  } else {\n    // Otherwise, preserve the offset of the original date.\n    ms = epochFromDate(dateTime) - dateTime.offset;\n  }\n\n  // Perform time manipulation in milliseconds rather than on the original time fields to account for DST.\n  // For example, adding one hour during a DST transition may result in the hour field staying the same or\n  // skipping an hour. This results in the offset field changing value instead of the specified field.\n  ms += duration.milliseconds || 0;\n  ms += (duration.seconds || 0) * 1000;\n  ms += (duration.minutes || 0) * 60 * 1000;\n  ms += (duration.hours || 0) * 60 * 60 * 1000;\n\n  let res = fromAbsolute(ms, dateTime.timeZone);\n  return toCalendar(res, dateTime.calendar);\n}\n\nexport function subtractZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  return addZoned(dateTime, invertDuration(duration));\n}\n\nexport function cycleZoned(dateTime: ZonedDateTime, field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n  // For date fields, we want the time to remain consistent and the UTC offset to potentially change to account for DST changes.\n  // For time fields, we want the time to change by the amount given. This may result in the hour field staying the same, but the UTC\n  // offset changing in the case of a backward DST transition, or skipping an hour in the case of a forward DST transition.\n  switch (field) {\n    case 'hour': {\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = dateTime.hour >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n\n      // The minimum and maximum hour may be affected by daylight saving time.\n      // For example, it might jump forward at midnight, and skip 1am.\n      // Or it might end at midnight and repeat the 11pm hour. To handle this, we get\n      // the possible absolute times for the min and max, and find the maximum range\n      // that is within the current day.\n      let plainDateTime = toCalendarDateTime(dateTime);\n      let minDate = toCalendar(setTime(plainDateTime, {hour: min}), new GregorianCalendar());\n      let minAbsolute = [toAbsolute(minDate, dateTime.timeZone, 'earlier'), toAbsolute(minDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === minDate.day)[0];\n\n      let maxDate = toCalendar(setTime(plainDateTime, {hour: max}), new GregorianCalendar());\n      let maxAbsolute = [toAbsolute(maxDate, dateTime.timeZone, 'earlier'), toAbsolute(maxDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === maxDate.day).pop()!;\n\n      // Since hours may repeat, we need to operate on the absolute time in milliseconds.\n      // This is done in hours from the Unix epoch so that cycleValue works correctly,\n      // and then converted back to milliseconds.\n      let ms = epochFromDate(dateTime) - dateTime.offset;\n      let hours = Math.floor(ms / ONE_HOUR);\n      let remainder = ms % ONE_HOUR;\n      ms = cycleValue(\n        hours,\n        amount,\n        Math.floor(minAbsolute / ONE_HOUR),\n        Math.floor(maxAbsolute / ONE_HOUR),\n        options?.round\n      ) * ONE_HOUR + remainder;\n\n      // Now compute the new timezone offset, and convert the absolute time back to local time.\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    case 'minute':\n    case 'second':\n    case 'millisecond':\n      // @ts-ignore\n      return cycleTime(dateTime, field, amount, options);\n    case 'era':\n    case 'year':\n    case 'month':\n    case 'day': {\n      let res = cycleDate(toCalendarDateTime(dateTime), field, amount, options);\n      let ms = toAbsolute(res, dateTime.timeZone);\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n}\n\nexport function setZoned(dateTime: ZonedDateTime, fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n  // Set the date/time fields, and recompute the UTC offset to account for DST changes.\n  // We also need to validate by converting back to a local time in case hours are skipped during forward DST transitions.\n  let plainDateTime = toCalendarDateTime(dateTime);\n  let res = setTime(set(plainDateTime, fields), fields);\n\n  // If the resulting plain date time values are equal, return the original time.\n  // We don't want to change the offset when setting the time to the same value.\n  if (res.compare(plainDateTime) === 0) {\n    return dateTime;\n  }\n\n  let ms = toAbsolute(res, dateTime.timeZone, disambiguation);\n  return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyDateTime, DateTimeDuration, Disambiguation} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, possibleAbsolutes, toAbsolute, toCalendar, toCalendarDateTime, toTimeZone} from './conversion';\nimport {getLocalTimeZone} from './queries';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mu<PERSON>} from './utils';\n\nconst TIME_RE = /^(\\d{2})(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst DATE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})$/;\nconst DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst ZONED_DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:([+-]\\d{2})(?::?(\\d{2}))?)?\\[(.*?)\\]$/;\nconst ABSOLUTE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:(?:([+-]\\d{2})(?::?(\\d{2}))?)|Z)$/;\nconst DATE_TIME_DURATION_RE =\n    /^((?<negative>-)|\\+)?P((?<years>\\d*)Y)?((?<months>\\d*)M)?((?<weeks>\\d*)W)?((?<days>\\d*)D)?((?<time>T)((?<hours>\\d*[.,]?\\d{1,9})H)?((?<minutes>\\d*[.,]?\\d{1,9})M)?((?<seconds>\\d*[.,]?\\d{1,9})S)?)?$/;\nconst requiredDurationTimeGroups = ['hours', 'minutes', 'seconds'];\nconst requiredDurationGroups = ['years', 'months', 'weeks', 'days', ...requiredDurationTimeGroups];\n\n/** Parses an ISO 8601 time string. */\nexport function parseTime(value: string): Time {\n  let m = value.match(TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 time string: ' + value);\n  }\n\n  return new Time(\n    parseNumber(m[1], 0, 23),\n    m[2] ? parseNumber(m[2], 0, 59) : 0,\n    m[3] ? parseNumber(m[3], 0, 59) : 0,\n    m[4] ? parseNumber(m[4], 0, Infinity) * 1000 : 0\n  );\n}\n\n/** Parses an ISO 8601 date string, with no time components. */\nexport function parseDate(value: string): CalendarDate {\n  let m = value.match(DATE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date string: ' + value);\n  }\n\n  let date: Mutable<CalendarDate> = new CalendarDate(\n    parseNumber(m[1], 0, 9999),\n    parseNumber(m[2], 1, 12),\n    1\n  );\n\n  date.day = parseNumber(m[3], 1, date.calendar.getDaysInMonth(date));\n  return date as CalendarDate;\n}\n\n/** Parses an ISO 8601 date and time string, with no time zone. */\nexport function parseDateTime(value: string): CalendarDateTime {\n  let m = value.match(DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<CalendarDateTime> = new CalendarDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n  return date as CalendarDateTime;\n}\n\n/**\n * Parses an ISO 8601 date and time string with a time zone extension and optional UTC offset\n * (e.g. \"2021-11-07T00:45[America/Los_Angeles]\" or \"2021-11-07T00:45-07:00[America/Los_Angeles]\").\n * Ambiguous times due to daylight saving time transitions are resolved according to the `disambiguation`\n * parameter.\n */\nexport function parseZonedDateTime(value: string, disambiguation?: Disambiguation): ZonedDateTime {\n  let m = value.match(ZONED_DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[10],\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  let plainDateTime = toCalendarDateTime(date as ZonedDateTime);\n\n  let ms: number;\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n    ms = epochFromDate(date as ZonedDateTime) - date.offset;\n\n    // Validate offset against parsed date.\n    let absolutes = possibleAbsolutes(plainDateTime, date.timeZone);\n    if (!absolutes.includes(ms)) {\n      throw new Error(`Offset ${offsetToString(date.offset)} is invalid for ${dateTimeToString(date)} in ${date.timeZone}`);\n    }\n  } else {\n    // Convert to absolute and back to fix invalid times due to DST.\n    ms = toAbsolute(toCalendarDateTime(plainDateTime), date.timeZone, disambiguation);\n  }\n\n  return fromAbsolute(ms, date.timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the provided time zone.\n */\nexport function parseAbsolute(value: string, timeZone: string): ZonedDateTime {\n  let m = value.match(ABSOLUTE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    timeZone,\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n  }\n\n  return toTimeZone(date as ZonedDateTime, timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the user's local time zone.\n */\nexport function parseAbsoluteToLocal(value: string): ZonedDateTime {\n  return parseAbsolute(value, getLocalTimeZone());\n}\n\nfunction parseNumber(value: string, min: number, max: number) {\n  let val = Number(value);\n  if (val < min || val > max) {\n    throw new RangeError(`Value out of range: ${min} <= ${val} <= ${max}`);\n  }\n\n  return val;\n}\n\nexport function timeToString(time: Time): string {\n  return `${String(time.hour).padStart(2, '0')}:${String(time.minute).padStart(2, '0')}:${String(time.second).padStart(2, '0')}${time.millisecond ? String(time.millisecond / 1000).slice(1) : ''}`;\n}\n\nexport function dateToString(date: CalendarDate): string {\n  let gregorianDate = toCalendar(date, new GregorianCalendar());\n  let year: string;\n  if (gregorianDate.era === 'BC') {\n    year = gregorianDate.year === 1\n      ? '0000'\n      : '-' + String(Math.abs(1 - gregorianDate.year)).padStart(6, '00');\n  } else {\n    year = String(gregorianDate.year).padStart(4, '0');\n  }\n  return `${year}-${String(gregorianDate.month).padStart(2, '0')}-${String(gregorianDate.day).padStart(2, '0')}`;\n}\n\nexport function dateTimeToString(date: AnyDateTime): string {\n  // @ts-ignore\n  return `${dateToString(date)}T${timeToString(date)}`;\n}\n\nfunction offsetToString(offset: number) {\n  let sign = Math.sign(offset) < 0 ? '-' : '+';\n  offset = Math.abs(offset);\n  let offsetHours = Math.floor(offset / (60 * 60 * 1000));\n  let offsetMinutes = (offset % (60 * 60 * 1000)) / (60 * 1000);\n  return `${sign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;\n}\n\nexport function zonedDateTimeToString(date: ZonedDateTime): string {\n  return `${dateTimeToString(date)}${offsetToString(date.offset)}[${date.timeZone}]`;\n}\n\n/**\n * Parses an ISO 8601 duration string (e.g. \"P3Y6M6W4DT12H30M5S\").\n * @param value An ISO 8601 duration string.\n * @returns A DateTimeDuration object.\n */\nexport function parseDuration(value: string): Required<DateTimeDuration> {\n  const match = value.match(DATE_TIME_DURATION_RE);\n\n  if (!match) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const parseDurationGroup = (\n    group: string | undefined,\n    isNegative: boolean\n  ): number => {\n    if (!group) {\n      return 0;\n    }\n    try {\n      const sign = isNegative ? -1 : 1;\n      return sign * Number(group.replace(',', '.'));\n    } catch {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  };\n\n  const isNegative = !!match.groups?.negative;\n\n  const hasRequiredGroups = requiredDurationGroups.some(group => match.groups?.[group]);\n\n  if (!hasRequiredGroups) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const durationStringIncludesTime = match.groups?.time;\n\n  if (durationStringIncludesTime) {\n    const hasRequiredDurationTimeGroups = requiredDurationTimeGroups.some(group => match.groups?.[group]);\n    if (!hasRequiredDurationTimeGroups) {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  }\n\n  const duration: Mutable<DateTimeDuration> = {\n    years: parseDurationGroup(match.groups?.years, isNegative),\n    months: parseDurationGroup(match.groups?.months, isNegative),\n    weeks: parseDurationGroup(match.groups?.weeks, isNegative),\n    days: parseDurationGroup(match.groups?.days, isNegative),\n    hours: parseDurationGroup(match.groups?.hours, isNegative),\n    minutes: parseDurationGroup(match.groups?.minutes, isNegative),\n    seconds: parseDurationGroup(match.groups?.seconds, isNegative)\n  };\n\n  if (duration.hours !== undefined && ((duration.hours % 1) !== 0) && (duration.minutes || duration.seconds)) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  if (duration.minutes !== undefined && ((duration.minutes % 1) !== 0) && duration.seconds) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  return duration as Required<DateTimeDuration>;\n}\n", "import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n", "function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {add, addTime, addZoned, constrain, constrainTime, cycleDate, cycleTime, cycleZoned, set, setTime, setZoned, subtract, subtractTime, subtractZoned} from './manipulation';\nimport {AnyCalendarDate, AnyTime, Calendar, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {compareDate, compareTime} from './queries';\nimport {dateTimeToString, dateToString, timeToString, zonedDateTimeToString} from './string';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {toCalendarDateTime, toDate, toZoned, zonedToDate} from './conversion';\n\nfunction shiftArgs(args: any[]) {\n  let calendar: Calendar = typeof args[0] === 'object'\n    ? args.shift()\n    : new GregorianCalendar();\n\n  let era: string;\n  if (typeof args[0] === 'string') {\n    era = args.shift();\n  } else {\n    let eras = calendar.getEras();\n    era = eras[eras.length - 1];\n  }\n\n  let year = args.shift();\n  let month = args.shift();\n  let day = args.shift();\n\n  return [calendar, era, year, month, day];\n}\n\n/** A CalendarDate represents a date without any time components in a specific calendar system. */\nexport class CalendarDate {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // i.e. a ZonedDateTime should not be be passable to a parameter that expects CalendarDate.\n  // If that behavior is desired, use the AnyCalendarDate interface instead.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n\n  constructor(year: number, month: number, day: number);\n  constructor(era: string, year: number, month: number, day: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDate {\n    if (this.era) {\n      return new CalendarDate(this.calendar, this.era, this.year, this.month, this.day);\n    } else {\n      return new CalendarDate(this.calendar, this.year, this.month, this.day);\n    }\n  }\n\n  /** Returns a new `CalendarDate` with the given duration added to it. */\n  add(duration: DateDuration): CalendarDate {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given duration subtracted from it. */\n  subtract(duration: DateDuration): CalendarDate {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields): CalendarDate {\n    return set(this, fields);\n  }\n\n  /**\n   * Returns a new `CalendarDate` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField, amount: number, options?: CycleOptions): CalendarDate {\n    return cycleDate(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object, with the time set to midnight in the given time zone. */\n  toDate(timeZone: string): Date {\n    return toDate(this, timeZone);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: AnyCalendarDate): number {\n    return compareDate(this, b);\n  }\n}\n\n/** A Time represents a clock time without any date components. */\nexport class Time {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The hour, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(\n    hour: number = 0,\n    minute: number = 0,\n    second: number = 0,\n    millisecond: number = 0\n  ) {\n    this.hour = hour;\n    this.minute = minute;\n    this.second = second;\n    this.millisecond = millisecond;\n    constrainTime(this);\n  }\n\n  /** Returns a copy of this time. */\n  copy(): Time {\n    return new Time(this.hour, this.minute, this.second, this.millisecond);\n  }\n\n  /** Returns a new `Time` with the given duration added to it. */\n  add(duration: TimeDuration): Time {\n    return addTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given duration subtracted from it. */\n  subtract(duration: TimeDuration): Time {\n    return subtractTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: TimeFields): Time {\n    return setTime(this, fields);\n  }\n\n  /**\n   * Returns a new `Time` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: TimeField, amount: number, options?: CycleTimeOptions): Time {\n    return cycleTime(this, field, amount, options);\n  }\n\n  /** Converts the time to an ISO 8601 formatted string. */\n  toString(): string {\n    return timeToString(this);\n  }\n\n  /** Compares this time with another. A negative result indicates that this time is before the given one, and a positive time indicates that it is after. */\n  compare(b: AnyTime): number {\n    return compareTime(this, b);\n  }\n}\n\n/** A CalendarDateTime represents a date and time without a time zone, in a specific calendar system. */\nexport class CalendarDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDateTime {\n    if (this.era) {\n      return new CalendarDateTime(this.calendar, this.era, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new CalendarDateTime(this.calendar, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): CalendarDateTime {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): CalendarDateTime {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields): CalendarDateTime {\n    return set(setTime(this, fields), fields);\n  }\n\n  /**\n   * Returns a new `CalendarDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime {\n    switch (field) {\n      case 'era':\n      case 'year':\n      case 'month':\n      case 'day':\n        return cycleDate(this, field, amount, options);\n      default:\n        return cycleTime(this, field, amount, options);\n    }\n  }\n\n  /** Converts the date to a native JavaScript Date object in the given time zone. */\n  toDate(timeZone: string, disambiguation?: Disambiguation): Date {\n    return toDate(this, timeZone, disambiguation);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateTimeToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    let res = compareDate(this, b);\n    if (res === 0) {\n      return compareTime(this, toCalendarDateTime(b));\n    }\n\n    return res;\n  }\n}\n\n/** A ZonedDateTime represents a date and time in a specific time zone and calendar system. */\nexport class ZonedDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n  /** The IANA time zone identifier that this date and time is represented in. */\n  public readonly timeZone: string;\n  /** The UTC offset for this time, in milliseconds. */\n  public readonly offset: number;\n\n  constructor(year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    let timeZone = args.shift();\n    let offset = args.shift();\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.timeZone = timeZone;\n    this.offset = offset;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): ZonedDateTime {\n    if (this.era) {\n      return new ZonedDateTime(this.calendar, this.era, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new ZonedDateTime(this.calendar, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): ZonedDateTime {\n    return addZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): ZonedDateTime {\n    return subtractZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n    return setZoned(this, fields, disambiguation);\n  }\n\n  /**\n   * Returns a new `ZonedDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n    return cycleZoned(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object. */\n  toDate(): Date {\n    return zonedToDate(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string, including the UTC offset and time zone identifier. */\n  toString(): string {\n    return zonedDateTimeToString(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string in UTC. */\n  toAbsoluteString(): string {\n    return this.toDate().toISOString();\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    // TODO: Is this a bad idea??\n    return this.toDate().getTime() - toZoned(b, this.timeZone).toDate().getTime();\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, Calendar, DateFields, Disambiguation, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {constrain} from './manipulation';\nimport {getExtendedYear, GregorianCalendar} from './calendars/GregorianCalendar';\nimport {getLocalTimeZone, isEqualCalendar} from './queries';\nimport {Mutable} from './utils';\n\nexport function epochFromDate(date: AnyDateTime): number {\n  date = toCalendar(date, new GregorianCalendar());\n  let year = getExtendedYear(date.era, date.year);\n  return epochFromParts(year, date.month, date.day, date.hour, date.minute, date.second, date.millisecond);\n}\n\nfunction epochFromParts(year: number, month: number, day: number, hour: number, minute: number, second: number, millisecond: number): number {\n  // Note: Date.UTC() interprets one and two-digit years as being in the\n  // 20th century, so don't use it\n  let date = new Date();\n  date.setUTCHours(hour, minute, second, millisecond);\n  date.setUTCFullYear(year, month - 1, day);\n  return date.getTime();\n}\n\nexport function getTimeZoneOffset(ms: number, timeZone: string): number {\n  // Fast path for UTC.\n  if (timeZone === 'UTC') {\n    return 0;\n  }\n\n  // Fast path: for local timezone after 1970, use native Date.\n  if (ms > 0 && timeZone === getLocalTimeZone()) {\n    return new Date(ms).getTimezoneOffset() * -60 * 1000;\n  }\n\n  let {year, month, day, hour, minute, second} = getTimeZoneParts(ms, timeZone);\n  let utc = epochFromParts(year, month, day, hour, minute, second, 0);\n  return utc - Math.floor(ms / 1000) * 1000;\n}\n\nconst formattersByTimeZone = new Map<string, Intl.DateTimeFormat>();\n\nfunction getTimeZoneParts(ms: number, timeZone: string) {\n  let formatter = formattersByTimeZone.get(timeZone);\n  if (!formatter) {\n    formatter = new Intl.DateTimeFormat('en-US', {\n      timeZone,\n      hour12: false,\n      era: 'short',\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric',\n      hour: 'numeric',\n      minute: 'numeric',\n      second: 'numeric'\n    });\n\n    formattersByTimeZone.set(timeZone, formatter);\n  }\n\n  let parts = formatter.formatToParts(new Date(ms));\n  let namedParts: {[name: string]: string} = {};\n  for (let part of parts) {\n    if (part.type !== 'literal') {\n      namedParts[part.type] = part.value;\n    }\n  }\n\n\n  return {\n    // Firefox returns B instead of BC... https://bugzilla.mozilla.org/show_bug.cgi?id=1752253\n    year: namedParts.era === 'BC' || namedParts.era === 'B' ? -namedParts.year + 1 : +namedParts.year,\n    month: +namedParts.month,\n    day: +namedParts.day,\n    hour: namedParts.hour === '24' ? 0 : +namedParts.hour, // bugs.chromium.org/p/chromium/issues/detail?id=1045791\n    minute: +namedParts.minute,\n    second: +namedParts.second\n  };\n}\n\nconst DAYMILLIS = 86400000;\n\nexport function possibleAbsolutes(date: CalendarDateTime, timeZone: string): number[] {\n  let ms = epochFromDate(date);\n  let earlier = ms - getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let later = ms - getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  return getValidWallTimes(date, timeZone, earlier, later);\n}\n\nfunction getValidWallTimes(date: CalendarDateTime, timeZone: string, earlier: number, later: number): number[] {\n  let found = earlier === later ? [earlier] : [earlier, later];\n  return found.filter(absolute => isValidWallTime(date, timeZone, absolute));\n}\n\nfunction isValidWallTime(date: CalendarDateTime, timeZone: string, absolute: number) {\n  let parts = getTimeZoneParts(absolute, timeZone);\n  return date.year === parts.year\n    && date.month === parts.month\n    && date.day === parts.day\n    && date.hour === parts.hour\n    && date.minute === parts.minute\n    && date.second === parts.second;\n}\n\nexport function toAbsolute(date: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): number {\n  let dateTime = toCalendarDateTime(date);\n\n  // Fast path: if the time zone is UTC, use native Date.\n  if (timeZone === 'UTC') {\n    return epochFromDate(dateTime);\n  }\n\n  // Fast path: if the time zone is the local timezone and disambiguation is compatible, use native Date.\n  if (timeZone === getLocalTimeZone() && disambiguation === 'compatible') {\n    dateTime = toCalendar(dateTime, new GregorianCalendar());\n\n    // Don't use Date constructor here because two-digit years are interpreted in the 20th century.\n    let date = new Date();\n    let year = getExtendedYear(dateTime.era, dateTime.year);\n    date.setFullYear(year, dateTime.month - 1, dateTime.day);\n    date.setHours(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n    return date.getTime();\n  }\n\n  let ms = epochFromDate(dateTime);\n  let offsetBefore = getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let offsetAfter = getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  let valid = getValidWallTimes(dateTime, timeZone, ms - offsetBefore, ms - offsetAfter);\n\n  if (valid.length === 1) {\n    return valid[0];\n  }\n\n  if (valid.length > 1) {\n    switch (disambiguation) {\n      // 'compatible' means 'earlier' for \"fall back\" transitions\n      case 'compatible':\n      case 'earlier':\n        return valid[0];\n      case 'later':\n        return valid[valid.length - 1];\n      case 'reject':\n        throw new RangeError('Multiple possible absolute times found');\n    }\n  }\n\n  switch (disambiguation) {\n    case 'earlier':\n      return Math.min(ms - offsetBefore, ms - offsetAfter);\n    // 'compatible' means 'later' for \"spring forward\" transitions\n    case 'compatible':\n    case 'later':\n      return Math.max(ms - offsetBefore, ms - offsetAfter);\n    case 'reject':\n      throw new RangeError('No such absolute time found');\n  }\n}\n\nexport function toDate(dateTime: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): Date {\n  return new Date(toAbsolute(dateTime, timeZone, disambiguation));\n}\n\n/**\n * Takes a Unix epoch (milliseconds since 1970) and converts it to the provided time zone.\n */\nexport function fromAbsolute(ms: number, timeZone: string): ZonedDateTime {\n  let offset = getTimeZoneOffset(ms, timeZone);\n  let date = new Date(ms + offset);\n  let year = date.getUTCFullYear();\n  let month = date.getUTCMonth() + 1;\n  let day = date.getUTCDate();\n  let hour = date.getUTCHours();\n  let minute = date.getUTCMinutes();\n  let second = date.getUTCSeconds();\n  let millisecond = date.getUTCMilliseconds();\n\n  return new ZonedDateTime(year < 1 ? 'BC' : 'AD', year < 1 ? -year + 1 : year, month, day, timeZone, offset, hour, minute, second, millisecond);\n}\n\n/**\n * Takes a `Date` object and converts it to the provided time zone.\n */\nexport function fromDate(date: Date, timeZone: string): ZonedDateTime {\n  return fromAbsolute(date.getTime(), timeZone);\n}\n\nexport function fromDateToLocal(date: Date): ZonedDateTime {\n  return fromDate(date, getLocalTimeZone());\n}\n\n/** Converts a value with date components such as a `CalendarDateTime` or `ZonedDateTime` into a `CalendarDate`. */\nexport function toCalendarDate(dateTime: AnyCalendarDate): CalendarDate {\n  return new CalendarDate(dateTime.calendar, dateTime.era, dateTime.year, dateTime.month, dateTime.day);\n}\n\nexport function toDateFields(date: AnyCalendarDate): DateFields {\n  return {\n    era: date.era,\n    year: date.year,\n    month: date.month,\n    day: date.day\n  };\n}\n\nexport function toTimeFields(date: AnyTime): TimeFields {\n  return {\n    hour: date.hour,\n    minute: date.minute,\n    second: date.second,\n    millisecond: date.millisecond\n  };\n}\n\n/**\n * Converts a date value to a `CalendarDateTime`. An optional `Time` value can be passed to set the time\n * of the resulting value, otherwise it will default to midnight.\n */\nexport function toCalendarDateTime(date: CalendarDate | CalendarDateTime | ZonedDateTime, time?: AnyTime): CalendarDateTime {\n  let hour = 0, minute = 0, second = 0, millisecond = 0;\n  if ('timeZone' in date) {\n    ({hour, minute, second, millisecond} = date);\n  } else if ('hour' in date && !time) {\n    return date;\n  }\n\n  if (time) {\n    ({hour, minute, second, millisecond} = time);\n  }\n\n  return new CalendarDateTime(\n    date.calendar,\n    date.era,\n    date.year,\n    date.month,\n    date.day,\n    hour,\n    minute,\n    second,\n    millisecond\n  );\n}\n\n/** Extracts the time components from a value containing a date and time. */\nexport function toTime(dateTime: CalendarDateTime | ZonedDateTime): Time {\n  return new Time(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n}\n\n/** Converts a date from one calendar system to another. */\nexport function toCalendar<T extends AnyCalendarDate>(date: T, calendar: Calendar): T {\n  if (isEqualCalendar(date.calendar, calendar)) {\n    return date;\n  }\n\n  let calendarDate = calendar.fromJulianDay(date.calendar.toJulianDay(date));\n  let copy: Mutable<T> = date.copy();\n  copy.calendar = calendar;\n  copy.era = calendarDate.era;\n  copy.year = calendarDate.year;\n  copy.month = calendarDate.month;\n  copy.day = calendarDate.day;\n  constrain(copy);\n  return copy;\n}\n\n/**\n * Converts a date value to a `ZonedDateTime` in the provided time zone. The `disambiguation` option can be set\n * to control how values that fall on daylight saving time changes are interpreted.\n */\nexport function toZoned(date: CalendarDate | CalendarDateTime | ZonedDateTime, timeZone: string, disambiguation?: Disambiguation): ZonedDateTime {\n  if (date instanceof ZonedDateTime) {\n    if (date.timeZone === timeZone) {\n      return date;\n    }\n\n    return toTimeZone(date, timeZone);\n  }\n\n  let ms = toAbsolute(date, timeZone, disambiguation);\n  return fromAbsolute(ms, timeZone);\n}\n\nexport function zonedToDate(date: ZonedDateTime): Date {\n  let ms = epochFromDate(date) - date.offset;\n  return new Date(ms);\n}\n\n/** Converts a `ZonedDateTime` from one time zone to another. */\nexport function toTimeZone(date: ZonedDateTime, timeZone: string): ZonedDateTime {\n  let ms = epochFromDate(date) - date.offset;\n  return toCalendar(fromAbsolute(ms, timeZone), date.calendar);\n}\n\n/** Converts the given `ZonedDateTime` into the user's local time zone. */\nexport function toLocalTimeZone(date: ZonedDateTime): ZonedDateTime {\n  return toTimeZone(date, getLocalTimeZone());\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Data from https://github.com/unicode-cldr/cldr-core/blob/master/supplemental/weekData.json\n// Locales starting on Sunday have been removed for compression.\nexport const weekStartData = {\n  '001': 1,\n  AD: 1,\n  AE: 6,\n  AF: 6,\n  AI: 1,\n  AL: 1,\n  AM: 1,\n  AN: 1,\n  AR: 1,\n  AT: 1,\n  AU: 1,\n  AX: 1,\n  AZ: 1,\n  BA: 1,\n  BE: 1,\n  BG: 1,\n  BH: 6,\n  BM: 1,\n  BN: 1,\n  BY: 1,\n  CH: 1,\n  CL: 1,\n  CM: 1,\n  CN: 1,\n  CR: 1,\n  CY: 1,\n  CZ: 1,\n  DE: 1,\n  DJ: 6,\n  DK: 1,\n  DZ: 6,\n  EC: 1,\n  EE: 1,\n  EG: 6,\n  ES: 1,\n  FI: 1,\n  FJ: 1,\n  FO: 1,\n  FR: 1,\n  GB: 1,\n  GE: 1,\n  GF: 1,\n  GP: 1,\n  GR: 1,\n  HR: 1,\n  HU: 1,\n  IE: 1,\n  IQ: 6,\n  IR: 6,\n  IS: 1,\n  IT: 1,\n  JO: 6,\n  KG: 1,\n  KW: 6,\n  KZ: 1,\n  LB: 1,\n  LI: 1,\n  LK: 1,\n  LT: 1,\n  LU: 1,\n  LV: 1,\n  LY: 6,\n  MC: 1,\n  MD: 1,\n  ME: 1,\n  MK: 1,\n  MN: 1,\n  MQ: 1,\n  MV: 5,\n  MY: 1,\n  NL: 1,\n  NO: 1,\n  NZ: 1,\n  OM: 6,\n  PL: 1,\n  QA: 6,\n  RE: 1,\n  RO: 1,\n  RS: 1,\n  RU: 1,\n  SD: 6,\n  SE: 1,\n  SI: 1,\n  SK: 1,\n  SM: 1,\n  SY: 6,\n  TJ: 1,\n  TM: 1,\n  TR: 1,\n  UA: 1,\n  UY: 1,\n  UZ: 1,\n  VA: 1,\n  VN: 1,\n  XK: 1\n};\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyTime, Calendar} from './types';\nimport {CalendarDate, CalendarDateTime, ZonedDateTime} from './CalendarDate';\nimport {fromAbsolute, toAbsolute, toCalendar, toCalendarDate} from './conversion';\nimport {weekStartData} from './weekStartData';\n\ntype DateValue = CalendarDate | CalendarDateTime | ZonedDateTime;\n\n/** Returns whether the given dates occur on the same day, regardless of the time or calendar system. */\nexport function isSameDay(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  return a.era === b.era && a.year === b.year && a.month === b.month && a.day === b.day;\n}\n\n/** Returns whether the given dates occur in the same month, using the calendar system of the first date. */\nexport function isSameMonth(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  // In the Japanese calendar, months can span multiple eras/years, so only compare the first of the month.\n  a = startOfMonth(a);\n  b = startOfMonth(b);\n  return a.era === b.era && a.year === b.year && a.month === b.month;\n}\n\n/** Returns whether the given dates occur in the same year, using the calendar system of the first date. */\nexport function isSameYear(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  a = startOfYear(a);\n  b = startOfYear(b);\n  return a.era === b.era && a.year === b.year;\n}\n\n/** Returns whether the given dates occur on the same day, and are of the same calendar system. */\nexport function isEqualDay(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameDay(a, b);\n}\n\n/** Returns whether the given dates occur in the same month, and are of the same calendar system. */\nexport function isEqualMonth(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameMonth(a, b);\n}\n\n/** Returns whether the given dates occur in the same year, and are of the same calendar system. */\nexport function isEqualYear(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameYear(a, b);\n}\n\n/** Returns whether two calendars are the same. */\nexport function isEqualCalendar(a: Calendar, b: Calendar): boolean {\n  return a.isEqual?.(b) ?? b.isEqual?.(a) ?? a.identifier === b.identifier;\n}\n\n/** Returns whether the date is today in the given time zone. */\nexport function isToday(date: DateValue, timeZone: string): boolean {\n  return isSameDay(date, today(timeZone));\n}\n\nconst DAY_MAP = {\n  sun: 0,\n  mon: 1,\n  tue: 2,\n  wed: 3,\n  thu: 4,\n  fri: 5,\n  sat: 6\n};\n\ntype DayOfWeek = 'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat';\n\n/**\n * Returns the day of week for the given date and locale. Days are numbered from zero to six,\n * where zero is the first day of the week in the given locale. For example, in the United States,\n * the first day of the week is Sunday, but in France it is Monday.\n */\nexport function getDayOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let weekStart = firstDayOfWeek ? DAY_MAP[firstDayOfWeek] : getWeekStart(locale);\n  let dayOfWeek = Math.ceil(julian + 1 - weekStart) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  return dayOfWeek;\n}\n\n/** Returns the current time in the given time zone. */\nexport function now(timeZone: string): ZonedDateTime {\n  return fromAbsolute(Date.now(), timeZone);\n}\n\n/** Returns today's date in the given time zone. */\nexport function today(timeZone: string): CalendarDate {\n  return toCalendarDate(now(timeZone));\n}\n\nexport function compareDate(a: AnyCalendarDate, b: AnyCalendarDate): number {\n  return a.calendar.toJulianDay(a) - b.calendar.toJulianDay(b);\n}\n\nexport function compareTime(a: AnyTime, b: AnyTime): number {\n  return timeToMs(a) - timeToMs(b);\n}\n\nfunction timeToMs(a: AnyTime): number {\n  return a.hour * 60 * 60 * 1000 + a.minute * 60 * 1000 + a.second * 1000 + a.millisecond;\n}\n\n/**\n * Returns the number of hours in the given date and time zone.\n * Usually this is 24, but it could be 23 or 25 if the date is on a daylight saving transition.\n */\nexport function getHoursInDay(a: CalendarDate, timeZone: string): number {\n  let ms = toAbsolute(a, timeZone);\n  let tomorrow = a.add({days: 1});\n  let tomorrowMs = toAbsolute(tomorrow, timeZone);\n  return (tomorrowMs - ms) / 3600000;\n}\n\nlet localTimeZone: string | null = null;\n\n/** Returns the time zone identifier for the current user. */\nexport function getLocalTimeZone(): string {\n  // TODO: invalidate this somehow?\n  if (localTimeZone == null) {\n    localTimeZone = new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  return localTimeZone!;\n}\n\n/** Returns the first date of the month for the given date. */\nexport function startOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function startOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function startOfMonth(date: CalendarDate): CalendarDate;\nexport function startOfMonth(date: DateValue): DateValue;\nexport function startOfMonth(date: DateValue): DateValue {\n  // Use `subtract` instead of `set` so we don't get constrained in an era.\n  return date.subtract({days: date.day - 1});\n}\n\n/** Returns the last date of the month for the given date. */\nexport function endOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function endOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function endOfMonth(date: CalendarDate): CalendarDate;\nexport function endOfMonth(date: DateValue): DateValue;\nexport function endOfMonth(date: DateValue): DateValue {\n  return date.add({days: date.calendar.getDaysInMonth(date) - date.day});\n}\n\n/** Returns the first day of the year for the given date. */\nexport function startOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function startOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function startOfYear(date: CalendarDate): CalendarDate;\nexport function startOfYear(date: DateValue): DateValue;\nexport function startOfYear(date: DateValue): DateValue {\n  return startOfMonth(date.subtract({months: date.month - 1}));\n}\n\n/** Returns the last day of the year for the given date. */\nexport function endOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function endOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function endOfYear(date: CalendarDate): CalendarDate;\nexport function endOfYear(date: DateValue): DateValue;\nexport function endOfYear(date: DateValue): DateValue {\n  return endOfMonth(date.add({months: date.calendar.getMonthsInYear(date) - date.month}));\n}\n\nexport function getMinimumMonthInYear(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumMonthInYear) {\n    return date.calendar.getMinimumMonthInYear(date);\n  }\n\n  return 1;\n}\n\nexport function getMinimumDayInMonth(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumDayInMonth) {\n    return date.calendar.getMinimumDayInMonth(date);\n  }\n\n  return 1;\n}\n\n/** Returns the first date of the week for the given date and locale. */\nexport function startOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function startOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function startOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  let dayOfWeek = getDayOfWeek(date, locale, firstDayOfWeek);\n  return date.subtract({days: dayOfWeek});\n}\n\n/** Returns the last date of the week for the given date and locale. */\nexport function endOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function endOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function endOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  return startOfWeek(date, locale, firstDayOfWeek).add({days: 6});\n}\n\nconst cachedRegions = new Map<string, string>();\n\nfunction getRegion(locale: string): string | undefined {\n  // If the Intl.Locale API is available, use it to get the region for the locale.\n  // @ts-ignore\n  if (Intl.Locale) {\n    // Constructing an Intl.Locale is expensive, so cache the result.\n    let region = cachedRegions.get(locale);\n    if (!region) {\n      // @ts-ignore\n      region = new Intl.Locale(locale).maximize().region;\n      if (region) {\n        cachedRegions.set(locale, region);\n      }\n    }\n    return region;\n  }\n\n  // If not, just try splitting the string.\n  // If the second part of the locale string is 'u',\n  // then this is a unicode extension, so ignore it.\n  // Otherwise, it should be the region.\n  let part = locale.split('-')[1];\n  return part === 'u' ? undefined : part;\n}\n\nfunction getWeekStart(locale: string): number {\n  // TODO: use Intl.Locale for this once browsers support the weekInfo property\n  // https://github.com/tc39/proposal-intl-locale-info\n  let region = getRegion(locale);\n  return region ? weekStartData[region] || 0 : 0;\n}\n\n/** Returns the number of weeks in the given month and locale. */\nexport function getWeeksInMonth(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let days = date.calendar.getDaysInMonth(date);\n  return Math.ceil((getDayOfWeek(startOfMonth(date), locale, firstDayOfWeek) + days) / 7);\n}\n\n/** Returns the lesser of the two provider dates. */\nexport function minDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) <= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\n/** Returns the greater of the two provider dates. */\nexport function maxDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) >= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\nconst WEEKEND_DATA = {\n  AF: [4, 5],\n  AE: [5, 6],\n  BH: [5, 6],\n  DZ: [5, 6],\n  EG: [5, 6],\n  IL: [5, 6],\n  IQ: [5, 6],\n  IR: [5, 5],\n  JO: [5, 6],\n  KW: [5, 6],\n  LY: [5, 6],\n  OM: [5, 6],\n  QA: [5, 6],\n  SA: [5, 6],\n  SD: [5, 6],\n  SY: [5, 6],\n  YE: [5, 6]\n};\n\n/** Returns whether the given date is on a weekend in the given locale. */\nexport function isWeekend(date: DateValue, locale: string): boolean {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let dayOfWeek = Math.ceil(julian + 1) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  let region = getRegion(locale);\n  // Use Intl.Locale for this once weekInfo is supported.\n  // https://github.com/tc39/proposal-intl-locale-info\n  let [start, end] = WEEKEND_DATA[region!] || [6, 0];\n  return dayOfWeek === start || dayOfWeek === end;\n}\n\n/** Returns whether the given date is on a weekday in the given locale. */\nexport function isWeekday(date: DateValue, locale: string): boolean {\n  return !isWeekend(date, locale);\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\n\nconst BUDDHIST_ERA_START = -543;\n\n/**\n * The Buddhist calendar is the same as the Gregorian calendar, but counts years\n * starting from the birth of Buddha in 543 BC (Gregorian). It supports only one\n * era, identified as 'BE'.\n */\nexport class BuddhistCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'buddhist';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let gregorianDate = super.fromJulianDay(jd);\n    let year = getExtendedYear(gregorianDate.era, gregorianDate.year);\n    return new CalendarDate(\n      this,\n      year - BUDDHIST_ERA_START,\n      gregorianDate.month,\n      gregorianDate.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['BE'];\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  balanceDate(): void {}\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(date.year + BUDDHIST_ERA_START);\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {Mutable} from '../utils';\n\nconst ETHIOPIC_EPOCH = 1723856;\nconst COPTIC_EPOCH = 1824665;\n\n// The delta between Amete Alem 1 and Amete Mihret 1\n// AA 5501 = AM 1\nconst AMETE_MIHRET_DELTA = 5500;\n\nfunction ceToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return (\n    epoch                   // difference from Julian epoch to 1,1,1\n    + 365 * year            // number of days from years\n    + Math.floor(year / 4)  // extra day of leap year\n    + 30 * (month - 1)      // number of days from months (1 based)\n    + day - 1               // number of days for present month (1 based)\n  );\n}\n\nfunction julianDayToCE(epoch: number, jd: number) {\n  let year = Math.floor((4 * (jd - epoch)) / 1461);\n  let month = 1 + Math.floor((jd - ceToJulianDay(epoch, year, 1, 1)) / 30);\n  let day = jd + 1 - ceToJulianDay(epoch, year, month, 1);\n  return [year, month, day];\n}\n\nfunction getLeapDay(year: number) {\n  return Math.floor((year % 4) / 3);\n}\n\nfunction getDaysInMonth(year: number, month: number) {\n  // The Ethiopian and Coptic calendars have 13 months, 12 of 30 days each and\n  // an intercalary month at the end of the year of 5 or 6 days, depending whether\n  // the year is a leap year or not. The Leap Year follows the same rules as the\n  // Julian Calendar so that the extra month always has six days in the year before\n  // a Julian Leap Year.\n  if (month % 13 !== 0) {\n    // not intercalary month\n    return 30;\n  } else {\n    // intercalary month 5 days + possible leap day\n    return getLeapDay(year) + 5;\n  }\n}\n\n/**\n * The Ethiopic calendar system is the official calendar used in Ethiopia.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'AA' and 'AM'.\n */\nexport class EthiopicCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'ethiopic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    let era = 'AM';\n    if (year <= 0) {\n      era = 'AA';\n      year += AMETE_MIHRET_DELTA;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'AA') {\n      year -= AMETE_MIHRET_DELTA;\n    }\n\n    return ceToJulianDay(ETHIOPIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(): number {\n    return 13;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return 365 + getLeapDay(date.year);\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-31 gregorian is 9992-20-02 ethiopic.\n    // Round down to 9991 for the last full year.\n    // AA 9999-01-01 ethiopic is 4506-09-30 gregorian.\n    return date.era === 'AA' ? 9999 : 9991;\n  }\n\n  getEras(): string[] {\n    return ['AA', 'AM'];\n  }\n}\n\n/**\n * The Ethiopic (Amete Alem) calendar is the same as the modern Ethiopic calendar,\n * except years were measured from a different epoch. Only one era is supported: 'AA'.\n */\nexport class EthiopicAmeteAlemCalendar extends EthiopicCalendar {\n  identifier: CalendarIdentifier = 'ethioaa'; // also known as 'ethiopic-amete-alem' in ICU\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    year += AMETE_MIHRET_DELTA;\n    return new CalendarDate(this, 'AA', year, month, day);\n  }\n\n  getEras(): string[] {\n    return ['AA'];\n  }\n\n  getYearsInEra(): number {\n    // 9999-13-04 ethioaa is the maximum date, which is equivalent to 4506-09-29 gregorian.\n    return 9999;\n  }\n}\n\n/**\n * The Coptic calendar is similar to the Ethiopic calendar.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'BCE' and 'CE'.\n */\nexport class CopticCalendar extends EthiopicCalendar {\n  identifier: CalendarIdentifier = 'coptic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(COPTIC_EPOCH, jd);\n    let era = 'CE';\n    if (year <= 0) {\n      era = 'BCE';\n      year = 1 - year;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return ceToJulianDay(COPTIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return getDaysInMonth(year, date.month);\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BCE';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BCE' ? 'CE' : 'BCE';\n      date.year = 1 - date.year;\n    }\n  }\n\n  getEras(): string[] {\n    return ['BCE', 'CE'];\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-30 gregorian is 9716-02-20 coptic.\n    // Round down to 9715 for the last full year.\n    // BCE 9999-01-01 coptic is BC 9716-06-15 gregorian.\n    return date.era === 'BCE' ? 9999 : 9715;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst HEBREW_EPOCH = 347997;\n\n// Hebrew date calculations are performed in terms of days, hours, and\n// \"parts\" (or halakim), which are 1/1080 of an hour, or 3 1/3 seconds.\nconst HOUR_PARTS = 1080;\nconst DAY_PARTS  = 24 * HOUR_PARTS;\n\n// An approximate value for the length of a lunar month.\n// It is used to calculate the approximate year and month of a given\n// absolute date.\nconst MONTH_DAYS = 29;\nconst MONTH_FRACT = 12 * HOUR_PARTS + 793;\nconst MONTH_PARTS = MONTH_DAYS * DAY_PARTS + MONTH_FRACT;\n\nfunction isLeapYear(year: number) {\n  return mod(year * 7 + 1, 19) < 7;\n}\n\n// Test for delay of start of new year and to avoid\n// Sunday, Wednesday, and Friday as start of the new year.\nfunction hebrewDelay1(year: number) {\n  let months = Math.floor((235 * year - 234) / 19);\n  let parts = 12084 + 13753 * months;\n  let day = months * 29 + Math.floor(parts / 25920);\n\n  if (mod(3 * (day + 1), 7) < 3) {\n    day += 1;\n  }\n\n  return day;\n}\n\n// Check for delay in start of new year due to length of adjacent years\nfunction hebrewDelay2(year: number) {\n  let last = hebrewDelay1(year - 1);\n  let present = hebrewDelay1(year);\n  let next = hebrewDelay1(year + 1);\n\n  if (next - present === 356) {\n    return 2;\n  }\n\n  if (present - last === 382) {\n    return 1;\n  }\n\n  return 0;\n}\n\nfunction startOfYear(year: number) {\n  return hebrewDelay1(year) + hebrewDelay2(year);\n}\n\nfunction getDaysInYear(year: number) {\n  return startOfYear(year + 1) - startOfYear(year);\n}\n\nfunction getYearType(year: number) {\n  let yearLength = getDaysInYear(year);\n\n  if (yearLength > 380) {\n    yearLength -= 30; // Subtract length of leap month.\n  }\n\n  switch (yearLength) {\n    case 353:\n      return 0; // deficient\n    case 354:\n      return 1; // normal\n    case 355:\n      return 2; // complete\n  }\n}\n\nfunction getDaysInMonth(year: number, month: number): number {\n  // Normalize month numbers from 1 - 13, even on non-leap years\n  if (month >= 6 && !isLeapYear(year)) {\n    month++;\n  }\n\n  // First of all, dispose of fixed-length 29 day months\n  if (month === 4 || month === 7 || month === 9 || month === 11 || month === 13) {\n    return 29;\n  }\n\n  let yearType = getYearType(year);\n\n  // If it's Heshvan, days depend on length of year\n  if (month === 2) {\n    return yearType === 2 ? 30 : 29;\n  }\n\n  // Similarly, Kislev varies with the length of year\n  if (month === 3) {\n    return yearType === 0 ? 29 : 30;\n  }\n\n  // Adar I only exists in leap years\n  if (month === 6) {\n    return isLeapYear(year) ? 30 : 0;\n  }\n\n  return 30;\n}\n\n/**\n * The Hebrew calendar is used in Israel and around the world by the Jewish faith.\n * Years include either 12 or 13 months depending on whether it is a leap year.\n * In leap years, an extra month is inserted at month 6.\n */\nexport class HebrewCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'hebrew';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let d = jd - HEBREW_EPOCH;\n    let m = (d * DAY_PARTS) / MONTH_PARTS;           // Months (approx)\n    let year = Math.floor((19 * m + 234) / 235) + 1; // Years (approx)\n    let ys = startOfYear(year);                      // 1st day of year\n    let dayOfYear = Math.floor(d - ys);\n\n    // Because of the postponement rules, it's possible to guess wrong.  Fix it.\n    while (dayOfYear < 1) {\n      year--;\n      ys = startOfYear(year);\n      dayOfYear = Math.floor(d - ys);\n    }\n\n    // Now figure out which month we're in, and the date within that month\n    let month = 1;\n    let monthStart = 0;\n    while (monthStart < dayOfYear) {\n      monthStart += getDaysInMonth(year, month);\n      month++;\n    }\n\n    month--;\n    monthStart -= getDaysInMonth(year, month);\n\n    let day = dayOfYear - monthStart;\n    return new CalendarDate(this, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = startOfYear(date.year);\n    for (let month = 1; month < date.month; month++) {\n      jd += getDaysInMonth(date.year, month);\n    }\n\n    return jd + date.day + HEBREW_EPOCH;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 13 : 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return getDaysInYear(date.year);\n  }\n\n  getYearsInEra(): number {\n    // 6239 gregorian\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['AM'];\n  }\n\n  balanceYearMonth(date: Mutable<AnyCalendarDate>, previousDate: AnyCalendarDate): void {\n    // Keep date in the same month when switching between leap years and non leap years\n    if (previousDate.year !== date.year) {\n      if (isLeapYear(previousDate.year) && !isLeapYear(date.year) && previousDate.month > 6) {\n        date.month--;\n      } else if (!isLeapYear(previousDate.year) && isLeapYear(date.year) && previousDate.month > 6) {\n        date.month++;\n      }\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, GregorianCalendar, gregorianToJulianDay, isLeapYear} from './GregorianCalendar';\n\n// Starts in 78 AD,\nconst INDIAN_ERA_START = 78;\n\n// The Indian year starts 80 days later than the Gregorian year.\nconst INDIAN_YEAR_START = 80;\n\n/**\n * The Indian National Calendar is similar to the Gregorian calendar, but with\n * years numbered since the Saka era in 78 AD (Gregorian). There are 12 months\n * in each year, with either 30 or 31 days. Only one era identifier is supported: 'saka'.\n */\nexport class IndianCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'indian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    // Gregorian date for Julian day\n    let date = super.fromJulianDay(jd);\n\n    // Year in Saka era\n    let indianYear = date.year - INDIAN_ERA_START;\n\n    // Day number in Gregorian year (starting from 0)\n    let yDay = jd - gregorianToJulianDay(date.era, date.year, 1, 1);\n\n    let leapMonth: number;\n    if (yDay < INDIAN_YEAR_START) {\n      //  Day is at the end of the preceding Saka year\n      indianYear--;\n\n      // Days in leapMonth this year, previous Gregorian year\n      leapMonth = isLeapYear(date.year - 1) ? 31 : 30;\n      yDay += leapMonth + (31 * 5) + (30 * 3) + 10;\n    } else {\n      // Days in leapMonth this year\n      leapMonth = isLeapYear(date.year) ? 31 : 30;\n      yDay -= INDIAN_YEAR_START;\n    }\n\n    let indianMonth: number;\n    let indianDay: number;\n    if (yDay < leapMonth) {\n      indianMonth = 1;\n      indianDay = yDay + 1;\n    } else {\n      let mDay = yDay - leapMonth;\n      if (mDay < (31 * 5)) {\n        indianMonth = Math.floor(mDay / 31) + 2;\n        indianDay = (mDay % 31) + 1;\n      } else {\n        mDay -= 31 * 5;\n        indianMonth = Math.floor(mDay / 30) + 7;\n        indianDay = (mDay % 30) + 1;\n      }\n    }\n\n    return new CalendarDate(this, indianYear, indianMonth, indianDay);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let extendedYear = date.year + INDIAN_ERA_START;\n    let [era, year] = fromExtendedYear(extendedYear);\n\n    let leapMonth: number;\n    let jd: number;\n    if (isLeapYear(year)) {\n      leapMonth = 31;\n      jd = gregorianToJulianDay(era, year, 3, 21);\n    } else {\n      leapMonth = 30;\n      jd = gregorianToJulianDay(era, year, 3, 22);\n    }\n\n    if (date.month === 1) {\n      return jd + date.day - 1;\n    }\n\n    jd += leapMonth + Math.min(date.month - 2, 5) * 31;\n\n    if (date.month >= 8) {\n      jd += (date.month - 7) * 30;\n    }\n\n    jd += date.day - 1;\n    return jd;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month === 1 && isLeapYear(date.year + INDIAN_ERA_START)) {\n      return 31;\n    }\n\n    if (date.month >= 2 && date.month <= 6) {\n      return 31;\n    }\n\n    return 30;\n  }\n\n  getYearsInEra(): number {\n    // 9999-12-31 gregorian is 9920-10-10 indian.\n    // Round down to 9919 for the last full year.\n    return 9919;\n  }\n\n  getEras(): string[] {\n    return ['saka'];\n  }\n\n  balanceDate(): void {}\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\n\nconst CIVIL_EPOC = 1948440; // CE 622 July 16 Friday (Julian calendar) / CE 622 July 19 (Gregorian calendar)\nconst ASTRONOMICAL_EPOC = 1948439; // CE 622 July 15 Thursday (Julian calendar)\nconst UMALQURA_YEAR_START = 1300;\nconst UMALQURA_YEAR_END = 1600;\nconst UMALQURA_START_DAYS = 460322;\n\nfunction islamicToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return day +\n    Math.ceil(29.5 * (month - 1)) +\n    (year - 1) * 354 +\n    Math.floor((3 + 11 * year) / 30) +\n    epoch - 1;\n}\n\nfunction julianDayToIslamic(calendar: Calendar, epoch: number, jd: number) {\n  let year = Math.floor((30 * (jd - epoch) + 10646) / 10631);\n  let month = Math.min(12, Math.ceil((jd - (29 + islamicToJulianDay(epoch, year, 1, 1))) / 29.5) + 1);\n  let day = jd - islamicToJulianDay(epoch, year, month, 1) + 1;\n\n  return new CalendarDate(calendar, year, month, day);\n}\n\nfunction isLeapYear(year: number): boolean {\n  return (14 + 11 * year) % 30 < 11;\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The civil variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Friday, July 16 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicCivilCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'islamic-civil';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, CIVIL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(CIVIL_EPOC, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let length = 29 + date.month % 2;\n    if (date.month === 12 && isLeapYear(date.year)) {\n      length++;\n    }\n\n    return length;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 355 : 354;\n  }\n\n  getYearsInEra(): number {\n    // 9999 gregorian\n    return 9665;\n  }\n\n  getEras(): string[] {\n    return ['AH'];\n  }\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The tabular variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Thursday, July 15 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicTabularCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-tbla';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, ASTRONOMICAL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(ASTRONOMICAL_EPOC, date.year, date.month, date.day);\n  }\n}\n\n// Generated by scripts/generate-umalqura.js\nconst UMALQURA_DATA = 'qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=';\nlet UMALQURA_MONTHLENGTH: Uint16Array;\nlet UMALQURA_YEAR_START_TABLE: Uint32Array;\n\nfunction umalquraYearStart(year: number): number {\n  return UMALQURA_START_DAYS + UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\nfunction umalquraMonthLength(year: number, month: number): number {\n  let idx = (year - UMALQURA_YEAR_START);\n  let mask = (0x01 << (11 - (month - 1)));\n  if ((UMALQURA_MONTHLENGTH[idx] & mask) === 0) {\n    return 29;\n  } else {\n    return 30;\n  }\n}\n\nfunction umalquraMonthStart(year: number, month: number): number {\n  let day = umalquraYearStart(year);\n  for (let i = 1; i < month; i++) {\n    day += umalquraMonthLength(year, i);\n  }\n  return day;\n}\n\nfunction umalquraYearLength(year: number): number {\n  return UMALQURA_YEAR_START_TABLE[year + 1 - UMALQURA_YEAR_START] - UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The Umalqura variant is primarily used in Saudi Arabia. It is a lunar calendar, based on astronomical\n * calculations that predict the sighting of a crescent moon. Month and year lengths vary between years\n * depending on these calculations.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicUmalquraCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-umalqura';\n\n  constructor() {\n    super();\n    if (!UMALQURA_MONTHLENGTH) {\n      UMALQURA_MONTHLENGTH = new Uint16Array(Uint8Array.from(atob(UMALQURA_DATA), c => c.charCodeAt(0)).buffer);\n    }\n\n    if (!UMALQURA_YEAR_START_TABLE) {\n      UMALQURA_YEAR_START_TABLE = new Uint32Array(UMALQURA_YEAR_END - UMALQURA_YEAR_START + 1);\n\n      let yearStart = 0;\n      for (let year = UMALQURA_YEAR_START; year <= UMALQURA_YEAR_END; year++) {\n        UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START] = yearStart;\n        for (let i = 1; i <= 12; i++) {\n          yearStart += umalquraMonthLength(year, i);\n        }\n      }\n    }\n  }\n\n  fromJulianDay(jd: number): CalendarDate {\n    let days = jd - CIVIL_EPOC;\n    let startDays = umalquraYearStart(UMALQURA_YEAR_START);\n    let endDays = umalquraYearStart(UMALQURA_YEAR_END);\n    if (days < startDays || days > endDays) {\n      return super.fromJulianDay(jd);\n    } else {\n      let y = UMALQURA_YEAR_START - 1;\n      let m = 1;\n      let d = 1;\n      while (d > 0) {\n        y++;\n        d = days - umalquraYearStart(y) + 1;\n        let yearLength = umalquraYearLength(y);\n        if (d === yearLength) {\n          m = 12;\n          break;\n        } else if (d < yearLength) {\n          let monthLength = umalquraMonthLength(y, m);\n          m = 1;\n          while (d > monthLength) {\n            d -= monthLength;\n            m++;\n            monthLength = umalquraMonthLength(y, m);\n          }\n          break;\n        }\n      }\n\n      return new CalendarDate(this, y, m, (days - umalquraMonthStart(y, m) + 1));\n    }\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.toJulianDay(date);\n    }\n\n    return CIVIL_EPOC + umalquraMonthStart(date.year, date.month) + (date.day - 1);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInMonth(date);\n    }\n\n    return umalquraMonthLength(date.year, date.month);\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInYear(date);\n    }\n\n    return umalquraYearLength(date.year);\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst ERA_START_DATES = [[1868, 9, 8], [1912, 7, 30], [1926, 12, 25], [1989, 1, 8], [2019, 5, 1]];\nconst ERA_END_DATES = [[1912, 7, 29], [1926, 12, 24], [1989, 1, 7], [2019, 4, 30]];\nconst ERA_ADDENDS = [1867, 1911, 1925, 1988, 2018];\nconst ERA_NAMES = ['meiji', 'taisho', 'showa', 'heisei', 'reiwa'];\n\nfunction findEraFromGregorianDate(date: AnyCalendarDate) {\n  const idx = ERA_START_DATES.findIndex(([year, month, day]) => {\n    if (date.year < year) {\n      return true;\n    }\n\n    if (date.year === year && date.month < month) {\n      return true;\n    }\n\n    if (date.year === year && date.month === month && date.day < day) {\n      return true;\n    }\n\n    return false;\n  });\n\n  if (idx === -1) {\n    return ERA_START_DATES.length - 1;\n  }\n\n  if (idx === 0) {\n    return 0;\n  }\n\n  return idx - 1;\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let eraAddend = ERA_ADDENDS[ERA_NAMES.indexOf(date.era)];\n  if (!eraAddend) {\n    throw new Error('Unknown era: ' + date.era);\n  }\n\n  return new CalendarDate(\n    date.year + eraAddend,\n    date.month,\n    date.day\n  );\n}\n\n/**\n * The Japanese calendar is based on the Gregorian calendar, but with eras for the reign of each Japanese emperor.\n * Whenever a new emperor ascends to the throne, a new era begins and the year starts again from 1.\n * Note that eras before 1868 (Gregorian) are not currently supported by this implementation.\n */\nexport class JapaneseCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'japanese';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let era = findEraFromGregorianDate(date);\n\n    return new CalendarDate(\n      this,\n      ERA_NAMES[era],\n      date.year - ERA_ADDENDS[era],\n      date.month,\n      date.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let gregorianDate = toGregorian(date);\n    let era = findEraFromGregorianDate(gregorianDate);\n\n    if (ERA_NAMES[era] !== date.era) {\n      date.era = ERA_NAMES[era];\n      date.year = gregorianDate.year - ERA_ADDENDS[era];\n    }\n\n    // Constrain in case we went before the first supported era.\n    this.constrainDate(date);\n  }\n\n  constrainDate(date: Mutable<AnyCalendarDate>): void {\n    let idx = ERA_NAMES.indexOf(date.era);\n    let end = ERA_END_DATES[idx];\n    if (end != null) {\n      let [endYear, endMonth, endDay] = end;\n\n      // Constrain the year to the maximum possible value in the era.\n      // Then constrain the month and day fields within that.\n      let maxYear = endYear - ERA_ADDENDS[idx];\n      date.year = Math.max(1, Math.min(maxYear, date.year));\n      if (date.year === maxYear) {\n        date.month = Math.min(endMonth, date.month);\n\n        if (date.month === endMonth) {\n          date.day = Math.min(endDay, date.day);\n        }\n      }\n    }\n\n    if (date.year === 1 && idx >= 0) {\n      let [, startMonth, startDay] = ERA_START_DATES[idx];\n      date.month = Math.max(startMonth, date.month);\n\n      if (date.month === startMonth) {\n        date.day = Math.max(startDay, date.day);\n      }\n    }\n  }\n\n  getEras(): string[] {\n    return ERA_NAMES;\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // Get the number of years in the era, taking into account the date's month and day fields.\n    let era = ERA_NAMES.indexOf(date.era);\n    let cur = ERA_START_DATES[era];\n    let next = ERA_START_DATES[era + 1];\n    if (next == null) {\n      // 9999 gregorian is the maximum year allowed.\n      return 9999 - cur[0] + 1;\n    }\n\n    let years = next[0] - cur[0];\n\n    if (date.month < next[1] || (date.month === next[1] && date.day < next[2])) {\n      years++;\n    }\n\n    return years;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getMinimumMonthInYear(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start ? start[1] : 1;\n  }\n\n  getMinimumDayInMonth(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start && date.month === start[1] ? start[2] : 1;\n  }\n}\n\nfunction getMinimums(date: AnyCalendarDate) {\n  if (date.year === 1) {\n    let idx = ERA_NAMES.indexOf(date.era);\n    return ERA_START_DATES[idx];\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod} from '../utils';\n\nconst PERSIAN_EPOCH = 1948320;\n\n// Number of days from the start of the year to the start of each month.\nconst MONTH_START = [\n  0, // Farvardin\n  31, // Ordibehesht\n  62, // Khordad\n  93, // Tir\n  124, // <PERSON><PERSON><PERSON>\n  155, // <PERSON><PERSON><PERSON>\n  186, // Mehr\n  216, // <PERSON><PERSON>\n  246, // Azar\n  276, // Dey\n  306, // Bahman\n  336  // Esfand\n];\n\n/**\n * The Persian calendar is the main calendar used in Iran and Afghanistan. It has 12 months\n * in each year, the first 6 of which have 31 days, and the next 5 have 30 days. The 12th month\n * has either 29 or 30 days depending on whether it is a leap year. The Persian year starts\n * around the March equinox.\n */\nexport class PersianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'persian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let daysSinceEpoch = jd - PERSIAN_EPOCH;\n    let year = 1 + Math.floor((33 * daysSinceEpoch + 3) / 12053);\n    let farvardin1 = 365 * (year - 1) + Math.floor((8 * year + 21) / 33);\n    let dayOfYear = daysSinceEpoch - farvardin1;\n    let month = dayOfYear < 216\n      ? Math.floor(dayOfYear / 31)\n      : Math.floor((dayOfYear - 6) / 30);\n    let day = dayOfYear - MONTH_START[month] + 1;\n    return new CalendarDate(this, year, month + 1, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = PERSIAN_EPOCH - 1 + 365 * (date.year - 1) + Math.floor((8 * date.year + 21) / 33);\n    jd += MONTH_START[date.month - 1];\n    jd += date.day;\n    return jd;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month <= 6) {\n      return 31;\n    }\n\n    if (date.month <= 11) {\n      return 30;\n    }\n\n    let isLeapYear = mod(25 * date.year + 11, 33) < 8;\n    return isLeapYear ? 30 : 29;\n  }\n\n  getEras(): string[] {\n    return ['AP'];\n  }\n\n  getYearsInEra(): number {\n    // 9378-10-10 persian is 9999-12-31 gregorian.\n    // Round down to 9377 to set the maximum full year.\n    return 9377;\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst TAIWAN_ERA_START = 1911;\n\nfunction gregorianYear(date: AnyCalendarDate) {\n  return date.era === 'minguo'\n    ? date.year + TAIWAN_ERA_START\n    : 1 - date.year + TAIWAN_ERA_START;\n}\n\nfunction gregorianToTaiwan(year: number): [string, number] {\n  let y = year - TAIWAN_ERA_START;\n  if (y > 0) {\n    return ['minguo', y];\n  } else {\n    return ['before_minguo', 1 - y];\n  }\n}\n\n/**\n * The Taiwanese calendar is the same as the Gregorian calendar, but years\n * are numbered starting from 1912 (Gregorian). Two eras are supported:\n * 'before_minguo' and 'minguo'.\n */\nexport class TaiwanCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'roc'; // Republic of China\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let extendedYear = getExtendedYear(date.era, date.year);\n    let [era, year] = gregorianToTaiwan(extendedYear);\n    return new CalendarDate(this, era, year, date.month, date.day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['before_minguo', 'minguo'];\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let [era, year] = gregorianToTaiwan(gregorianYear(date));\n    date.era = era;\n    date.year = year;\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'before_minguo';\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    return date.era === 'before_minguo' ? 9999 : 9999 - TAIWAN_ERA_START;\n  }\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(gregorianYear(date));\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BuddhistCalendar} from './calendars/BuddhistCalendar';\nimport {Calendar, CalendarIdentifier} from './types';\nimport {CopticCalendar, EthiopicAmeteAlemCalendar, EthiopicCalendar} from './calendars/EthiopicCalendar';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {HebrewCalendar} from './calendars/HebrewCalendar';\nimport {IndianCalendar} from './calendars/IndianCalendar';\nimport {IslamicCivilCalendar, IslamicTabularCalendar, IslamicUmalquraCalendar} from './calendars/IslamicCalendar';\nimport {JapaneseCalendar} from './calendars/JapaneseCalendar';\nimport {PersianCalendar} from './calendars/PersianCalendar';\nimport {TaiwanCalendar} from './calendars/TaiwanCalendar';\n\n/** Creates a `Calendar` instance from a Unicode calendar identifier string. */\nexport function createCalendar(name: CalendarIdentifier): Calendar {\n  switch (name) {\n    case 'buddhist':\n      return new BuddhistCalendar();\n    case 'ethiopic':\n      return new EthiopicCalendar();\n    case 'ethioaa':\n      return new EthiopicAmeteAlemCalendar();\n    case 'coptic':\n      return new CopticCalendar();\n    case 'hebrew':\n      return new HebrewCalendar();\n    case 'indian':\n      return new IndianCalendar();\n    case 'islamic-civil':\n      return new IslamicCivilCalendar();\n    case 'islamic-tbla':\n      return new IslamicTabularCalendar();\n    case 'islamic-umalqura':\n      return new IslamicUmalquraCalendar();\n    case 'japanese':\n      return new JapaneseCalendar();\n    case 'persian':\n      return new PersianCalendar();\n    case 'roc':\n      return new TaiwanCalendar();\n    case 'gregory':\n    default:\n      return new GregorianCalendar();\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.DateTimeFormat>();\n\ninterface DateRangeFormatPart extends Intl.DateTimeFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/** A wrapper around Intl.DateTimeFormat that fixes various browser bugs, and polyfills new features. */\nexport class DateFormatter implements Intl.DateTimeFormat {\n  private formatter: Intl.DateTimeFormat;\n  private options: Intl.DateTimeFormatOptions;\n  private resolvedHourCycle: Intl.DateTimeFormatOptions['hourCycle'];\n\n  constructor(locale: string, options: Intl.DateTimeFormatOptions = {}) {\n    this.formatter = getCachedDateFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a date as a string according to the locale and format options passed to the constructor. */\n  format(value: Date): string {\n    return this.formatter.format(value);\n  }\n\n  /** Formats a date to an array of parts such as separators, numbers, punctuation, and more. */\n  formatToParts(value: Date): Intl.DateTimeFormatPart[] {\n    return this.formatter.formatToParts(value);\n  }\n\n  /** Formats a date range as a string. */\n  formatRange(start: Date, end: Date): string {\n    // @ts-ignore\n    if (typeof this.formatter.formatRange === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.formatter.format(start)} – ${this.formatter.format(end)}`;\n  }\n\n  /** Formats a date range as an array of parts. */\n  formatRangeToParts(start: Date, end: Date): DateRangeFormatPart[] {\n    // @ts-ignore\n    if (typeof this.formatter.formatRangeToParts === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.formatter.formatToParts(start);\n    let endParts = this.formatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as DateRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as DateRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedDateTimeFormatOptions {\n    let resolvedOptions = this.formatter.resolvedOptions();\n    if (hasBuggyResolvedHourCycle()) {\n      if (!this.resolvedHourCycle) {\n        this.resolvedHourCycle = getResolvedHourCycle(resolvedOptions.locale, this.options);\n      }\n      resolvedOptions.hourCycle = this.resolvedHourCycle;\n      resolvedOptions.hour12 = this.resolvedHourCycle === 'h11' || this.resolvedHourCycle === 'h12';\n    }\n\n    // Safari uses a different name for the Ethiopic (Amete Alem) calendar.\n    // https://bugs.webkit.org/show_bug.cgi?id=241564\n    if (resolvedOptions.calendar === 'ethiopic-amete-alem') {\n      resolvedOptions.calendar = 'ethioaa';\n    }\n\n    return resolvedOptions;\n  }\n}\n\n// There are multiple bugs involving the hour12 and hourCycle options in various browser engines.\n//   - Chrome [1] (and the ECMA 402 spec [2]) resolve hour12: false in English and other locales to h24 (24:00 - 23:59)\n//     rather than h23 (00:00 - 23:59). Same can happen with hour12: true in French, which Chrome resolves to h11 (00:00 - 11:59)\n//     rather than h12 (12:00 - 11:59).\n//   - WebKit returns an incorrect hourCycle resolved option in the French locale due to incorrect parsing of 'h' literal\n//     in the resolved pattern. It also formats incorrectly when specifying the hourCycle option for the same reason. [3]\n// [1] https://bugs.chromium.org/p/chromium/issues/detail?id=1045791\n// [2] https://github.com/tc39/ecma402/issues/402\n// [3] https://bugs.webkit.org/show_bug.cgi?id=229313\n\n// https://github.com/unicode-org/cldr/blob/018b55eff7ceb389c7e3fc44e2f657eae3b10b38/common/supplemental/supplementalData.xml#L4774-L4802\nconst hour12Preferences = {\n  true: {\n    // Only Japanese uses the h11 style for 12 hour time. All others use h12.\n    ja: 'h11'\n  },\n  false: {\n    // All locales use h23 for 24 hour time. None use h24.\n  }\n};\n\nfunction getCachedDateFormatter(locale: string, options: Intl.DateTimeFormatOptions = {}): Intl.DateTimeFormat {\n  // Work around buggy hour12 behavior in Chrome / ECMA 402 spec by using hourCycle instead.\n  // Only apply the workaround if the issue is detected, because the hourCycle option is buggy in Safari.\n  if (typeof options.hour12 === 'boolean' && hasBuggyHour12Behavior()) {\n    options = {...options};\n    let pref = hour12Preferences[String(options.hour12)][locale.split('-')[0]];\n    let defaultHourCycle = options.hour12 ? 'h12' : 'h23';\n    options.hourCycle = pref ?? defaultHourCycle;\n    delete options.hour12;\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.DateTimeFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\nlet _hasBuggyHour12Behavior: boolean | null = null;\nfunction hasBuggyHour12Behavior() {\n  if (_hasBuggyHour12Behavior == null) {\n    _hasBuggyHour12Behavior = new Intl.DateTimeFormat('en-US', {\n      hour: 'numeric',\n      hour12: false\n    }).format(new Date(2020, 2, 3, 0)) === '24';\n  }\n\n  return _hasBuggyHour12Behavior;\n}\n\nlet _hasBuggyResolvedHourCycle: boolean | null = null;\nfunction hasBuggyResolvedHourCycle() {\n  if (_hasBuggyResolvedHourCycle == null) {\n    _hasBuggyResolvedHourCycle = new Intl.DateTimeFormat('fr', {\n      hour: 'numeric',\n      hour12: false\n    }).resolvedOptions().hourCycle === 'h12';\n  }\n\n  return _hasBuggyResolvedHourCycle;\n}\n\nfunction getResolvedHourCycle(locale: string, options: Intl.DateTimeFormatOptions) {\n  if (!options.timeStyle && !options.hour) {\n    return undefined;\n  }\n\n  // Work around buggy results in resolved hourCycle and hour12 options in WebKit.\n  // Format the minimum possible hour and maximum possible hour in a day and parse the results.\n  locale = locale.replace(/(-u-)?-nu-[a-zA-Z0-9]+/, '');\n  locale += (locale.includes('-u-') ? '' : '-u') + '-nu-latn';\n  let formatter = getCachedDateFormatter(locale, {\n    ...options,\n    timeZone: undefined // use local timezone\n  });\n\n  let min = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 0)).find(p => p.type === 'hour')!.value, 10);\n  let max = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 23)).find(p => p.type === 'hour')!.value, 10);\n\n  if (min === 0 && max === 23) {\n    return 'h23';\n  }\n\n  if (min === 24 && max === 23) {\n    return 'h24';\n  }\n\n  if (min === 0 && max === 11) {\n    return 'h11';\n  }\n\n  if (min === 12 && max === 11) {\n    return 'h12';\n  }\n\n  throw new Error('Unexpected hour cycle result');\n}\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => d.overflows[0] > 0 && getSideAxis(d.placement) === initialSideAxis)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { arrow as arrow$1, computePosition } from '@floating-ui/dom';\nexport { autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/dom';\nimport { isNode, getNodeName } from '@floating-ui/utils/dom';\nimport { unref, computed, ref, shallowRef, watch, getCurrentScope, onScopeDispose, shallowReadonly } from 'vue-demi';\n\nfunction isComponentPublicInstance(target) {\n  return target != null && typeof target === 'object' && '$el' in target;\n}\nfunction unwrapElement(target) {\n  if (isComponentPublicInstance(target)) {\n    const element = target.$el;\n    return isNode(element) && getNodeName(element) === '#comment' ? null : element;\n  }\n  return target;\n}\n\nfunction toValue(source) {\n  return typeof source === 'function' ? source() : unref(source);\n}\n\n/**\n * Positions an inner element of the floating element such that it is centered to the reference element.\n * @param options The arrow options.\n * @see https://floating-ui.com/docs/arrow\n */\nfunction arrow(options) {\n  return {\n    name: 'arrow',\n    options,\n    fn(args) {\n      const element = unwrapElement(toValue(options.element));\n      if (element == null) {\n        return {};\n      }\n      return arrow$1({\n        element,\n        padding: options.padding\n      }).fn(args);\n    }\n  };\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element next to a reference element when it is given a certain CSS positioning strategy.\n * @param reference The reference template ref.\n * @param floating The floating template ref.\n * @param options The floating options.\n * @see https://floating-ui.com/docs/vue\n */\nfunction useFloating(reference, floating, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const whileElementsMountedOption = options.whileElementsMounted;\n  const openOption = computed(() => {\n    var _toValue;\n    return (_toValue = toValue(options.open)) != null ? _toValue : true;\n  });\n  const middlewareOption = computed(() => toValue(options.middleware));\n  const placementOption = computed(() => {\n    var _toValue2;\n    return (_toValue2 = toValue(options.placement)) != null ? _toValue2 : 'bottom';\n  });\n  const strategyOption = computed(() => {\n    var _toValue3;\n    return (_toValue3 = toValue(options.strategy)) != null ? _toValue3 : 'absolute';\n  });\n  const transformOption = computed(() => {\n    var _toValue4;\n    return (_toValue4 = toValue(options.transform)) != null ? _toValue4 : true;\n  });\n  const referenceElement = computed(() => unwrapElement(reference.value));\n  const floatingElement = computed(() => unwrapElement(floating.value));\n  const x = ref(0);\n  const y = ref(0);\n  const strategy = ref(strategyOption.value);\n  const placement = ref(placementOption.value);\n  const middlewareData = shallowRef({});\n  const isPositioned = ref(false);\n  const floatingStyles = computed(() => {\n    const initialStyles = {\n      position: strategy.value,\n      left: '0',\n      top: '0'\n    };\n    if (!floatingElement.value) {\n      return initialStyles;\n    }\n    const xVal = roundByDPR(floatingElement.value, x.value);\n    const yVal = roundByDPR(floatingElement.value, y.value);\n    if (transformOption.value) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + xVal + \"px, \" + yVal + \"px)\",\n        ...(getDPR(floatingElement.value) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy.value,\n      left: xVal + \"px\",\n      top: yVal + \"px\"\n    };\n  });\n  let whileElementsMountedCleanup;\n  function update() {\n    if (referenceElement.value == null || floatingElement.value == null) {\n      return;\n    }\n    const open = openOption.value;\n    computePosition(referenceElement.value, floatingElement.value, {\n      middleware: middlewareOption.value,\n      placement: placementOption.value,\n      strategy: strategyOption.value\n    }).then(position => {\n      x.value = position.x;\n      y.value = position.y;\n      strategy.value = position.strategy;\n      placement.value = position.placement;\n      middlewareData.value = position.middlewareData;\n      /**\n       * The floating element's position may be recomputed while it's closed\n       * but still mounted (such as when transitioning out). To ensure\n       * `isPositioned` will be `false` initially on the next open, avoid\n       * setting it to `true` when `open === false` (must be specified).\n       */\n      isPositioned.value = open !== false;\n    });\n  }\n  function cleanup() {\n    if (typeof whileElementsMountedCleanup === 'function') {\n      whileElementsMountedCleanup();\n      whileElementsMountedCleanup = undefined;\n    }\n  }\n  function attach() {\n    cleanup();\n    if (whileElementsMountedOption === undefined) {\n      update();\n      return;\n    }\n    if (referenceElement.value != null && floatingElement.value != null) {\n      whileElementsMountedCleanup = whileElementsMountedOption(referenceElement.value, floatingElement.value, update);\n      return;\n    }\n  }\n  function reset() {\n    if (!openOption.value) {\n      isPositioned.value = false;\n    }\n  }\n  watch([middlewareOption, placementOption, strategyOption, openOption], update, {\n    flush: 'sync'\n  });\n  watch([referenceElement, floatingElement], attach, {\n    flush: 'sync'\n  });\n  watch(openOption, reset, {\n    flush: 'sync'\n  });\n  if (getCurrentScope()) {\n    onScopeDispose(cleanup);\n  }\n  return {\n    x: shallowReadonly(x),\n    y: shallowReadonly(y),\n    strategy: shallowReadonly(strategy),\n    placement: shallowReadonly(placement),\n    middlewareData: shallowReadonly(middlewareData),\n    isPositioned: shallowReadonly(isPositioned),\n    floatingStyles,\n    update\n  };\n}\n\nexport { arrow, useFloating };\n", "import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.NumberFormat>();\n\nlet supportsSignDisplay = false;\ntry {\n  supportsSignDisplay = (new Intl.NumberFormat('de-DE', {signDisplay: 'exceptZero'})).resolvedOptions().signDisplay === 'exceptZero';\n  // eslint-disable-next-line no-empty\n} catch {}\n\nlet supportsUnit = false;\ntry {\n  supportsUnit = (new Intl.NumberFormat('de-DE', {style: 'unit', unit: 'degree'})).resolvedOptions().style === 'unit';\n  // eslint-disable-next-line no-empty\n} catch {}\n\n// Polyfill for units since Safari doesn't support them yet. See https://bugs.webkit.org/show_bug.cgi?id=215438.\n// Currently only polyfilling the unit degree in narrow format for ColorSlider in our supported locales.\n// Values were determined by switching to each locale manually in Chrome.\nconst UNITS = {\n  degree: {\n    narrow: {\n      default: '°',\n      'ja-JP': ' 度',\n      'zh-TW': '度',\n      'sl-SI': ' °'\n      // Arabic?? But Safari already doesn't use Arabic digits so might be ok...\n      // https://bugs.webkit.org/show_bug.cgi?id=218139\n    }\n  }\n};\n\nexport interface NumberFormatOptions extends Intl.NumberFormatOptions {\n  /** Overrides default numbering system for the current locale. */\n  numberingSystem?: string\n}\n\ninterface NumberRangeFormatPart extends Intl.NumberFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/**\n * A wrapper around Intl.NumberFormat providing additional options, polyfills, and caching for performance.\n */\nexport class NumberFormatter implements Intl.NumberFormat {\n  private numberFormatter: Intl.NumberFormat;\n  private options: NumberFormatOptions;\n\n  constructor(locale: string, options: NumberFormatOptions = {}) {\n    this.numberFormatter = getCachedNumberFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a number value as a string, according to the locale and options provided to the constructor. */\n  format(value: number): string {\n    let res = '';\n    if (!supportsSignDisplay && this.options.signDisplay != null) {\n      res = numberFormatSignDisplayPolyfill(this.numberFormatter, this.options.signDisplay, value);\n    } else {\n      res = this.numberFormatter.format(value);\n    }\n\n    if (this.options.style === 'unit' && !supportsUnit) {\n      let {unit, unitDisplay = 'short', locale} = this.resolvedOptions();\n      if (!unit) {\n        return res;\n      }\n      let values = UNITS[unit]?.[unitDisplay];\n      res += values[locale] || values.default;\n    }\n\n    return res;\n  }\n\n  /** Formats a number to an array of parts such as separators, digits, punctuation, and more. */\n  formatToParts(value: number): Intl.NumberFormatPart[] {\n    // TODO: implement signDisplay for formatToParts\n    return this.numberFormatter.formatToParts(value);\n  }\n\n  /** Formats a number range as a string. */\n  formatRange(start: number, end: number): string {\n    if (typeof this.numberFormatter.formatRange === 'function') {\n      return this.numberFormatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.format(start)} – ${this.format(end)}`;\n  }\n\n  /** Formats a number range as an array of parts. */\n  formatRangeToParts(start: number, end: number): NumberRangeFormatPart[] {\n    if (typeof this.numberFormatter.formatRangeToParts === 'function') {\n      return this.numberFormatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.numberFormatter.formatToParts(start);\n    let endParts = this.numberFormatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as NumberRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as NumberRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedNumberFormatOptions {\n    let options = this.numberFormatter.resolvedOptions();\n    if (!supportsSignDisplay && this.options.signDisplay != null) {\n      options = {...options, signDisplay: this.options.signDisplay};\n    }\n\n    if (!supportsUnit && this.options.style === 'unit') {\n      options = {...options, style: 'unit', unit: this.options.unit, unitDisplay: this.options.unitDisplay};\n    }\n\n    return options;\n  }\n}\n\nfunction getCachedNumberFormatter(locale: string, options: NumberFormatOptions = {}): Intl.NumberFormat {\n  let {numberingSystem} = options;\n  if (numberingSystem && locale.includes('-nu-')) {\n    if (!locale.includes('-u-')) {\n      locale += '-u-';\n    }\n    locale += `-nu-${numberingSystem}`;\n  }\n\n  if (options.style === 'unit' && !supportsUnit) {\n    let {unit, unitDisplay = 'short'} = options;\n    if (!unit) {\n      throw new Error('unit option must be provided with style: \"unit\"');\n    }\n    if (!UNITS[unit]?.[unitDisplay]) {\n      throw new Error(`Unsupported unit ${unit} with unitDisplay = ${unitDisplay}`);\n    }\n    options = {...options, style: 'decimal'};\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.NumberFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\n/** @private - exported for tests */\nexport function numberFormatSignDisplayPolyfill(numberFormat: Intl.NumberFormat, signDisplay: string, num: number): string {\n  if (signDisplay === 'auto') {\n    return numberFormat.format(num);\n  } else if (signDisplay === 'never') {\n    return numberFormat.format(Math.abs(num));\n  } else {\n    let needsPositiveSign = false;\n    if (signDisplay === 'always') {\n      needsPositiveSign = num > 0 || Object.is(num, 0);\n    } else if (signDisplay === 'exceptZero') {\n      if (Object.is(num, -0) || Object.is(num, 0)) {\n        num = Math.abs(num);\n      } else {\n        needsPositiveSign = num > 0;\n      }\n    }\n\n    if (needsPositiveSign) {\n      let negative = numberFormat.format(-num);\n      let noSign = numberFormat.format(num);\n      // ignore RTL/LTR marker character\n      let minus = negative.replace(noSign, '').replace(/\\u200e|\\u061C/, '');\n      if ([...minus].length !== 1) {\n        console.warn('@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case');\n      }\n      let positive = negative.replace(noSign, '!!!').replace(minus, '+').replace('!!!', noSign);\n      return positive;\n    } else {\n      return numberFormat.format(num);\n    }\n  }\n}\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {NumberFormatter} from './NumberFormatter';\n\ninterface Symbols {\n  minusSign?: string,\n  plusSign?: string,\n  decimal?: string,\n  group?: string,\n  literals: RegExp,\n  numeral: RegExp,\n  index: (v: string) => string\n}\n\nconst CURRENCY_SIGN_REGEX = new RegExp('^.*\\\\(.*\\\\).*$');\nconst NUMBERING_SYSTEMS = ['latn', 'arab', 'hanidec', 'deva', 'beng'];\n\n/**\n * A NumberParser can be used to perform locale-aware parsing of numbers from Unicode strings,\n * as well as validation of partial user input. It automatically detects the numbering system\n * used in the input, and supports parsing decimals, percentages, currency values, and units\n * according to the locale.\n */\nexport class NumberParser {\n  private locale: string;\n  private options: Intl.NumberFormatOptions;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    this.options = options;\n  }\n\n  /**\n   * Parses the given string to a number. Returns NaN if a valid number could not be parsed.\n   */\n  parse(value: string): number {\n    return getNumberParserImpl(this.locale, this.options, value).parse(value);\n  }\n\n  /**\n   * Returns whether the given string could potentially be a valid number. This should be used to\n   * validate user input as the user types. If a `minValue` or `maxValue` is provided, the validity\n   * of the minus/plus sign characters can be checked.\n   */\n  isValidPartialNumber(value: string, minValue?: number, maxValue?: number): boolean {\n    return getNumberParserImpl(this.locale, this.options, value).isValidPartialNumber(value, minValue, maxValue);\n  }\n\n  /**\n   * Returns a numbering system for which the given string is valid in the current locale.\n   * If no numbering system could be detected, the default numbering system for the current\n   * locale is returned.\n   */\n  getNumberingSystem(value: string): string {\n    return getNumberParserImpl(this.locale, this.options, value).options.numberingSystem;\n  }\n}\n\nconst numberParserCache = new Map<string, NumberParserImpl>();\nfunction getNumberParserImpl(locale: string, options: Intl.NumberFormatOptions, value: string) {\n  // First try the default numbering system for the provided locale\n  let defaultParser = getCachedNumberParser(locale, options);\n\n  // If that doesn't match, and the locale doesn't include a hard coded numbering system,\n  // try each of the other supported numbering systems until we find one that matches.\n  if (!locale.includes('-nu-') && !defaultParser.isValidPartialNumber(value)) {\n    for (let numberingSystem of NUMBERING_SYSTEMS) {\n      if (numberingSystem !== defaultParser.options.numberingSystem) {\n        let parser = getCachedNumberParser(locale + (locale.includes('-u-') ? '-nu-' : '-u-nu-') + numberingSystem, options);\n        if (parser.isValidPartialNumber(value)) {\n          return parser;\n        }\n      }\n    }\n  }\n\n  return defaultParser;\n}\n\nfunction getCachedNumberParser(locale: string, options: Intl.NumberFormatOptions) {\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  let parser = numberParserCache.get(cacheKey);\n  if (!parser) {\n    parser = new NumberParserImpl(locale, options);\n    numberParserCache.set(cacheKey, parser);\n  }\n\n  return parser;\n}\n\n// The actual number parser implementation. Instances of this class are cached\n// based on the locale, options, and detected numbering system.\nclass NumberParserImpl {\n  formatter: Intl.NumberFormat;\n  options: Intl.ResolvedNumberFormatOptions;\n  symbols: Symbols;\n  locale: string;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    // see https://tc39.es/ecma402/#sec-setnfdigitoptions, when using roundingIncrement, the maximumFractionDigits and minimumFractionDigits must be equal\n    // by default, they are 0 and 3 respectively, so we set them to 0 if neither are set\n    if (options.roundingIncrement !== 1 && options.roundingIncrement != null) {\n      if (options.maximumFractionDigits == null && options.minimumFractionDigits == null) {\n        options.maximumFractionDigits = 0;\n        options.minimumFractionDigits = 0;\n      } else if (options.maximumFractionDigits == null) {\n        options.maximumFractionDigits = options.minimumFractionDigits;\n      } else if (options.minimumFractionDigits == null) {\n        options.minimumFractionDigits = options.maximumFractionDigits;\n      }\n      // if both are specified, let the normal Range Error be thrown\n    }\n    this.formatter = new Intl.NumberFormat(locale, options);\n    this.options = this.formatter.resolvedOptions();\n    this.symbols = getSymbols(locale, this.formatter, this.options, options);\n    if (this.options.style === 'percent' && ((this.options.minimumFractionDigits ?? 0) > 18 || (this.options.maximumFractionDigits ?? 0) > 18)) {\n      console.warn('NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.');\n    }\n  }\n\n  parse(value: string) {\n    // to parse the number, we need to remove anything that isn't actually part of the number, for example we want '-10.40' not '-10.40 USD'\n    let fullySanitizedValue = this.sanitize(value);\n\n    if (this.symbols.group) {\n      // Remove group characters, and replace decimal points and numerals with ASCII values.\n      fullySanitizedValue = replaceAll(fullySanitizedValue, this.symbols.group, '');\n    }\n    if (this.symbols.decimal) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.decimal!, '.');\n    }\n    if (this.symbols.minusSign) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.minusSign!, '-');\n    }\n    fullySanitizedValue = fullySanitizedValue.replace(this.symbols.numeral, this.symbols.index);\n\n    if (this.options.style === 'percent') {\n      // javascript is bad at dividing by 100 and maintaining the same significant figures, so perform it on the string before parsing\n      let isNegative = fullySanitizedValue.indexOf('-');\n      fullySanitizedValue = fullySanitizedValue.replace('-', '');\n      fullySanitizedValue = fullySanitizedValue.replace('+', '');\n      let index = fullySanitizedValue.indexOf('.');\n      if (index === -1) {\n        index = fullySanitizedValue.length;\n      }\n      fullySanitizedValue = fullySanitizedValue.replace('.', '');\n      if (index - 2 === 0) {\n        fullySanitizedValue = `0.${fullySanitizedValue}`;\n      } else if (index - 2 === -1) {\n        fullySanitizedValue = `0.0${fullySanitizedValue}`;\n      } else if (index - 2 === -2) {\n        fullySanitizedValue = '0.00';\n      } else {\n        fullySanitizedValue = `${fullySanitizedValue.slice(0, index - 2)}.${fullySanitizedValue.slice(index - 2)}`;\n      }\n      if (isNegative > -1) {\n        fullySanitizedValue = `-${fullySanitizedValue}`;\n      }\n    }\n\n    let newValue = fullySanitizedValue ? +fullySanitizedValue : NaN;\n    if (isNaN(newValue)) {\n      return NaN;\n    }\n\n    if (this.options.style === 'percent') {\n      // extra step for rounding percents to what our formatter would output\n      let options = {\n        ...this.options,\n        style: 'decimal' as const,\n        minimumFractionDigits: Math.min((this.options.minimumFractionDigits ?? 0) + 2, 20),\n        maximumFractionDigits: Math.min((this.options.maximumFractionDigits ?? 0) + 2, 20)\n      };\n      return (new NumberParser(this.locale, options)).parse(new NumberFormatter(this.locale, options).format(newValue));\n    }\n\n    // accounting will always be stripped to a positive number, so if it's accounting and has a () around everything, then we need to make it negative again\n    if (this.options.currencySign === 'accounting' && CURRENCY_SIGN_REGEX.test(value)) {\n      newValue = -1 * newValue;\n    }\n\n    return newValue;\n  }\n\n  sanitize(value: string) {\n    // Remove literals and whitespace, which are allowed anywhere in the string\n    value = value.replace(this.symbols.literals, '');\n\n    // Replace the ASCII minus sign with the minus sign used in the current locale\n    // so that both are allowed in case the user's keyboard doesn't have the locale's minus sign.\n    if (this.symbols.minusSign) {\n      value = value.replace('-', this.symbols.minusSign);\n    }\n\n    // In arab numeral system, their decimal character is 1643, but most keyboards don't type that\n    // instead they use the , (44) character or apparently the (1548) character.\n    if (this.options.numberingSystem === 'arab') {\n      if (this.symbols.decimal) {\n        value = value.replace(',', this.symbols.decimal);\n        value = value.replace(String.fromCharCode(1548), this.symbols.decimal);\n      }\n      if (this.symbols.group) {\n        value = replaceAll(value, '.', this.symbols.group);\n      }\n    }\n\n    // fr-FR group character is narrow non-breaking space, char code 8239 (U+202F), but that's not a key on the french keyboard,\n    // so allow space and non-breaking space as a group char as well\n    if (this.options.locale === 'fr-FR' && this.symbols.group) {\n      value = replaceAll(value, ' ', this.symbols.group);\n      value = replaceAll(value, /\\u00A0/g, this.symbols.group);\n    }\n\n    return value;\n  }\n\n  isValidPartialNumber(value: string, minValue: number = -Infinity, maxValue: number = Infinity): boolean {\n    value = this.sanitize(value);\n\n    // Remove minus or plus sign, which must be at the start of the string.\n    if (this.symbols.minusSign && value.startsWith(this.symbols.minusSign) && minValue < 0) {\n      value = value.slice(this.symbols.minusSign.length);\n    } else if (this.symbols.plusSign && value.startsWith(this.symbols.plusSign) && maxValue > 0) {\n      value = value.slice(this.symbols.plusSign.length);\n    }\n\n    // Numbers cannot start with a group separator\n    if (this.symbols.group && value.startsWith(this.symbols.group)) {\n      return false;\n    }\n\n    // Numbers that can't have any decimal values fail if a decimal character is typed\n    if (this.symbols.decimal && value.indexOf(this.symbols.decimal) > -1 && this.options.maximumFractionDigits === 0) {\n      return false;\n    }\n\n    // Remove numerals, groups, and decimals\n    if (this.symbols.group) {\n      value = replaceAll(value, this.symbols.group, '');\n    }\n    value = value.replace(this.symbols.numeral, '');\n    if (this.symbols.decimal) {\n      value = value.replace(this.symbols.decimal, '');\n    }\n\n    // The number is valid if there are no remaining characters\n    return value.length === 0;\n  }\n}\n\nconst nonLiteralParts = new Set(['decimal', 'fraction', 'integer', 'minusSign', 'plusSign', 'group']);\n\n// This list is derived from https://www.unicode.org/cldr/charts/43/supplemental/language_plural_rules.html#comparison and includes\n// all unique numbers which we need to check in order to determine all the plural forms for a given locale.\n// See: https://github.com/adobe/react-spectrum/pull/5134/files#r1337037855 for used script\nconst pluralNumbers = [\n  0, 4, 2, 1, 11, 20, 3, 7, 100, 21, 0.1, 1.1\n];\n\nfunction getSymbols(locale: string, formatter: Intl.NumberFormat, intlOptions: Intl.ResolvedNumberFormatOptions, originalOptions: Intl.NumberFormatOptions): Symbols {\n  // formatter needs access to all decimal places in order to generate the correct literal strings for the plural set\n  let symbolFormatter = new Intl.NumberFormat(locale, {...intlOptions,\n    // Resets so we get the full range of symbols\n    minimumSignificantDigits: 1,\n    maximumSignificantDigits: 21,\n    roundingIncrement: 1,\n    roundingPriority: 'auto',\n    roundingMode: 'halfExpand'\n  });\n  // Note: some locale's don't add a group symbol until there is a ten thousands place\n  let allParts = symbolFormatter.formatToParts(-10000.111);\n  let posAllParts = symbolFormatter.formatToParts(10000.111);\n  let pluralParts = pluralNumbers.map(n => symbolFormatter.formatToParts(n));\n\n  let minusSign = allParts.find(p => p.type === 'minusSign')?.value ?? '-';\n  let plusSign = posAllParts.find(p => p.type === 'plusSign')?.value;\n\n  // Safari does not support the signDisplay option, but our number parser polyfills it.\n  // If no plus sign was returned, but the original options contained signDisplay, default to the '+' character.\n  if (!plusSign && (originalOptions?.signDisplay === 'exceptZero' || originalOptions?.signDisplay === 'always')) {\n    plusSign = '+';\n  }\n\n  // If maximumSignificantDigits is 1 (the minimum) then we won't get decimal characters out of the above formatters\n  // Percent also defaults to 0 fractionDigits, so we need to make a new one that isn't percent to get an accurate decimal\n  let decimalParts = new Intl.NumberFormat(locale, {...intlOptions, minimumFractionDigits: 2, maximumFractionDigits: 2}).formatToParts(0.001);\n\n  let decimal = decimalParts.find(p => p.type === 'decimal')?.value;\n  let group = allParts.find(p => p.type === 'group')?.value;\n\n  // this set is also for a regex, it's all literals that might be in the string we want to eventually parse that\n  // don't contribute to the numerical value\n  let allPartsLiterals = allParts.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value));\n  let pluralPartsLiterals = pluralParts.flatMap(p => p.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value)));\n  let sortedLiterals = [...new Set([...allPartsLiterals, ...pluralPartsLiterals])].sort((a, b) => b.length - a.length);\n\n  let literals = sortedLiterals.length === 0 ?\n      new RegExp('[\\\\p{White_Space}]', 'gu') :\n      new RegExp(`${sortedLiterals.join('|')}|[\\\\p{White_Space}]`, 'gu');\n\n  // These are for replacing non-latn characters with the latn equivalent\n  let numerals = [...new Intl.NumberFormat(intlOptions.locale, {useGrouping: false}).format(9876543210)].reverse();\n  let indexes = new Map(numerals.map((d, i) => [d, i]));\n  let numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n  let index = d => String(indexes.get(d));\n\n  return {minusSign, plusSign, decimal, group, literals, numeral, index};\n}\n\nfunction replaceAll(str: string, find: string | RegExp, replace: string) {\n  if (str.replaceAll) {\n    return str.replaceAll(find, replace);\n  }\n\n  return str.split(find).join(replace);\n}\n\nfunction escapeRegex(string: string) {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;IAAAA,eAAA;IAAAA,eAAA;AAgBO,SAAS,0CAAI,QAAgB,WAAiB;AACnD,SAAO,SAAS,YAAY,KAAK,MAAM,SAAS,SAAA;AAClD;;;ACCA,IAAM,8BAAQ;AACP,SAAS,0CAAqB,KAAa,MAAc,OAAe,KAAW;AACxF,SAAO,0CAAgB,KAAK,IAAA;AAE5B,MAAI,KAAK,OAAO;AAChB,MAAI,cAAc;AAClB,MAAI,SAAS,EACX,eAAc;WACL,0CAAW,IAAA,EACpB,eAAc;AAGhB,SACE,8BACA,IACA,MAAM,KACN,KAAK,MAAM,KAAK,CAAA,IAChB,KAAK,MAAM,KAAK,GAAA,IAChB,KAAK,MAAM,KAAK,GAAA,IAChB,KAAK,OAAO,MAAM,QAAQ,OAAO,KAAK,cAAc,GAAA;AAExD;AAEO,SAAS,0CAAW,MAAY;AACrC,SAAO,OAAO,MAAM,MAAM,OAAO,QAAQ,KAAK,OAAO,QAAQ;AAC/D;AAEO,SAAS,0CAAgB,KAAa,MAAY;AACvD,SAAO,QAAQ,OAAO,IAAI,OAAO;AACnC;AAEO,SAAS,0CAAiB,MAAY;AAC3C,MAAI,MAAM;AACV,MAAI,QAAQ,GAAG;AACb,UAAM;AACN,WAAO,IAAI;EACb;AAEA,SAAO;IAAC;IAAK;;AACf;AAEA,IAAM,oCAAc;EAClB,UAAU;IAAC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;;EACvD,UAAU;IAAC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;;AACzD;AAMO,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,MAAM;AACV,QAAI,SAAS,MAAM;AACnB,QAAI,aAAa,KAAK,MAAM,SAAS,MAAA;AACrC,QAAI,OAAM,GAAA,2CAAI,QAAQ,MAAA;AACtB,QAAI,OAAO,KAAK,MAAM,MAAM,KAAA;AAC5B,QAAI,SAAQ,GAAA,2CAAI,KAAK,KAAA;AACrB,QAAI,OAAO,KAAK,MAAM,QAAQ,IAAA;AAC9B,QAAI,SAAQ,GAAA,2CAAI,OAAO,IAAA;AACvB,QAAI,SAAS,KAAK,MAAM,QAAQ,GAAA;AAEhC,QAAI,eAAe,aAAa,MAAM,OAAO,MAAM,OAAO,IAAI,UAAU,SAAS,KAAK,WAAW,IAAI,IAAI;AACzG,QAAI,CAAC,KAAK,IAAA,IAAQ,0CAAiB,YAAA;AACnC,QAAI,UAAU,MAAM,0CAAqB,KAAK,MAAM,GAAG,CAAA;AACvD,QAAI,UAAU;AACd,QAAI,MAAM,0CAAqB,KAAK,MAAM,GAAG,CAAA,EAC3C,WAAU;aACD,0CAAW,IAAA,EACpB,WAAU;AAEZ,QAAI,QAAQ,KAAK,QAAQ,UAAU,WAAW,KAAK,OAAO,GAAA;AAC1D,QAAI,MAAM,MAAM,0CAAqB,KAAK,MAAM,OAAO,CAAA,IAAK;AAE5D,WAAO,KAAI,GAAA,2CAAa,KAAK,MAAM,OAAO,GAAA;EAC5C;EAEA,YAAY,MAA+B;AACzC,WAAO,0CAAqB,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EACvE;EAEA,eAAe,MAA+B;AAC5C,WAAO,kCAAY,0CAAW,KAAK,IAAI,IAAI,aAAa,UAAA,EAAY,KAAK,QAAQ,CAAA;EACnF;;EAGA,gBAAgB,MAA+B;AAC7C,WAAO;EACT;EAEA,cAAc,MAA+B;AAC3C,WAAO,0CAAW,KAAK,IAAI,IAAI,MAAM;EACvC;;EAGA,cAAc,MAA+B;AAC3C,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAM;;EAChB;EAEA,aAAa,MAAgC;AAC3C,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,MAAsC;AAChD,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,MAAM,KAAK,QAAQ,OAAO,OAAO;AACtC,WAAK,OAAO,IAAI,KAAK;IACvB;EACF;;SA/DA,aAAiC;;AAgEnC;;;ACpHA,IAAM,iCAAW;AAKV,SAAS,0CAAI,MAAuC,UAA0B;AACnF,MAAI,cAAsD,KAAK,KAAI;AACnE,MAAI,OAAO,UAAU,cAAc,oCAAc,aAAa,QAAA,IAAY;AAE1E,iCAAS,aAAa,SAAS,SAAS,CAAA;AACxC,MAAI,YAAY,SAAS,iBACvB,aAAY,SAAS,iBAAiB,aAAa,IAAA;AAGrD,cAAY,SAAS,SAAS,UAAU;AAExC,yCAAiB,WAAA;AACjB,0CAAkB,WAAA;AAElB,cAAY,QAAQ,SAAS,SAAS,KAAK;AAC3C,cAAY,OAAO,SAAS,QAAQ;AACpC,cAAY,OAAO;AAEnB,mCAAW,WAAA;AAEX,MAAI,YAAY,SAAS,YACvB,aAAY,SAAS,YAAY,WAAA;AAOnC,MAAI,YAAY,OAAO,GAAG;AACxB,gBAAY,OAAO;AACnB,gBAAY,QAAQ;AACpB,gBAAY,MAAM;EACpB;AAEA,MAAI,UAAU,YAAY,SAAS,cAAc,WAAA;AACjD,MAAI,YAAY,OAAO,SAAS;QACX,oCAAA;AAAnB,QAAI,gBAAe,sCAAA,wBAAA,YAAY,UAAS,kBAAY,QAAjC,uCAAA,SAAA,SAAA,mCAAA,KAAA,uBAAoC,WAAA;AACvD,gBAAY,OAAO;AACnB,gBAAY,QAAQ,eAAe,IAAI,YAAY,SAAS,gBAAgB,WAAA;AAC5E,gBAAY,MAAM,eAAe,IAAI,YAAY,SAAS,eAAe,WAAA;EAC3E;AAEA,MAAI,YAAY,QAAQ,GAAG;AACzB,gBAAY,QAAQ;AACpB,gBAAY,MAAM;EACpB;AAEA,MAAI,WAAW,YAAY,SAAS,gBAAgB,WAAA;AACpD,MAAI,YAAY,QAAQ,UAAU;AAChC,gBAAY,QAAQ;AACpB,gBAAY,MAAM,YAAY,SAAS,eAAe,WAAA;EACxD;AAEA,cAAY,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,SAAS,eAAe,WAAA,GAAc,YAAY,GAAG,CAAA;AACxG,SAAO;AACT;AAEA,SAAS,+BAAS,MAAgC,OAAa;MACzD,6BAAA;AAAJ,OAAI,+BAAA,iBAAA,KAAK,UAAS,kBAAY,QAA1B,gCAAA,SAAA,SAAA,4BAAA,KAAA,gBAA6B,IAAA,EAC/B,SAAQ,CAAC;AAGX,OAAK,QAAQ;AACf;AAEA,SAAS,uCAAiB,MAA8B;AACtD,SAAO,KAAK,QAAQ,GAAG;AACrB,mCAAS,MAAM,EAAA;AACf,SAAK,SAAS,KAAK,SAAS,gBAAgB,IAAA;EAC9C;AAEA,MAAI,eAAe;AACnB,SAAO,KAAK,SAAS,eAAe,KAAK,SAAS,gBAAgB,IAAA,IAAQ;AACxE,SAAK,SAAS;AACd,mCAAS,MAAM,CAAA;EACjB;AACF;AAEA,SAAS,iCAAW,MAA8B;AAChD,SAAO,KAAK,MAAM,GAAG;AACnB,SAAK;AACL,2CAAiB,IAAA;AACjB,SAAK,OAAO,KAAK,SAAS,eAAe,IAAA;EAC3C;AAEA,SAAO,KAAK,MAAM,KAAK,SAAS,eAAe,IAAA,GAAO;AACpD,SAAK,OAAO,KAAK,SAAS,eAAe,IAAA;AACzC,SAAK;AACL,2CAAiB,IAAA;EACnB;AACF;AAEA,SAAS,wCAAkB,MAA8B;AACvD,OAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,gBAAgB,IAAA,GAAO,KAAK,KAAK,CAAA;AACjF,OAAK,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,eAAe,IAAA,GAAO,KAAK,GAAG,CAAA;AAC9E;AAEO,SAAS,0CAAU,MAA8B;AACtD,MAAI,KAAK,SAAS,cAChB,MAAK,SAAS,cAAc,IAAA;AAG9B,OAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,SAAS,cAAc,IAAA,GAAO,KAAK,IAAI,CAAA;AAC7E,0CAAkB,IAAA;AACpB;AAEO,SAAS,0CAAe,UAA0B;AACvD,MAAI,kBAAkB,CAAC;AACvB,WAAS,OAAO,SACd,KAAI,OAAO,SAAS,GAAA,MAAS,SAC3B,iBAAgB,GAAA,IAAO,CAAC,SAAS,GAAA;AAIrC,SAAO;AACT;AAIO,SAAS,0CAAS,MAAuC,UAA0B;AACxF,SAAO,0CAAI,MAAM,0CAAe,QAAA,CAAA;AAClC;AAIO,SAAS,0CAAI,MAAuC,QAAkB;AAC3E,MAAI,cAAwC,KAAK,KAAI;AAErD,MAAI,OAAO,OAAO,KAChB,aAAY,MAAM,OAAO;AAG3B,MAAI,OAAO,QAAQ,KACjB,aAAY,OAAO,OAAO;AAG5B,MAAI,OAAO,SAAS,KAClB,aAAY,QAAQ,OAAO;AAG7B,MAAI,OAAO,OAAO,KAChB,aAAY,MAAM,OAAO;AAG3B,4CAAU,WAAA;AACV,SAAO;AACT;AAIO,SAAS,0CAAQ,OAAgC,QAAkB;AACxE,MAAI,eAAiD,MAAM,KAAI;AAE/D,MAAI,OAAO,QAAQ,KACjB,cAAa,OAAO,OAAO;AAG7B,MAAI,OAAO,UAAU,KACnB,cAAa,SAAS,OAAO;AAG/B,MAAI,OAAO,UAAU,KACnB,cAAa,SAAS,OAAO;AAG/B,MAAI,OAAO,eAAe,KACxB,cAAa,cAAc,OAAO;AAGpC,4CAAc,YAAA;AACd,SAAO;AACT;AAEA,SAAS,kCAAY,MAAsB;AACzC,OAAK,UAAU,KAAK,MAAM,KAAK,cAAc,GAAA;AAC7C,OAAK,cAAc,qCAAe,KAAK,aAAa,GAAA;AAEpD,OAAK,UAAU,KAAK,MAAM,KAAK,SAAS,EAAA;AACxC,OAAK,SAAS,qCAAe,KAAK,QAAQ,EAAA;AAE1C,OAAK,QAAQ,KAAK,MAAM,KAAK,SAAS,EAAA;AACtC,OAAK,SAAS,qCAAe,KAAK,QAAQ,EAAA;AAE1C,MAAI,OAAO,KAAK,MAAM,KAAK,OAAO,EAAA;AAClC,OAAK,OAAO,qCAAe,KAAK,MAAM,EAAA;AAEtC,SAAO;AACT;AAEO,SAAS,0CAAc,MAAsB;AAClD,OAAK,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,aAAa,GAAA,CAAA;AAC1D,OAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAA,CAAA;AAChD,OAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAA,CAAA;AAChD,OAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,MAAM,EAAA,CAAA;AAC9C;AAEA,SAAS,qCAAe,GAAW,GAAS;AAC1C,MAAI,SAAS,IAAI;AACjB,MAAI,SAAS,EACX,WAAU;AAEZ,SAAO;AACT;AAEA,SAAS,oCAAc,MAAwB,UAAsB;AACnE,OAAK,QAAQ,SAAS,SAAS;AAC/B,OAAK,UAAU,SAAS,WAAW;AACnC,OAAK,UAAU,SAAS,WAAW;AACnC,OAAK,eAAe,SAAS,gBAAgB;AAC7C,SAAO,kCAAY,IAAA;AACrB;AAEO,SAAS,0CAAQ,MAAY,UAAsB;AACxD,MAAI,MAAM,KAAK,KAAI;AACnB,sCAAc,KAAK,QAAA;AACnB,SAAO;AACT;AAEO,SAAS,0CAAa,MAAY,UAAsB;AAC7D,SAAO,0CAAQ,MAAM,0CAAe,QAAA,CAAA;AACtC;AAIO,SAAS,0CAAU,OAAwC,OAAkB,QAAgB,SAAsB;AACxH,MAAI,UAAoD,MAAM,KAAI;AAElE,UAAQ,OAAA;IACN,KAAK,OAAO;AACV,UAAI,OAAO,MAAM,SAAS,QAAO;AACjC,UAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACrC,UAAI,WAAW,EACb,OAAM,IAAI,MAAM,kBAAkB,MAAM,GAAG;AAE7C,iBAAW,iCAAW,UAAU,QAAQ,GAAG,KAAK,SAAS,GAAG,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AAC1E,cAAQ,MAAM,KAAK,QAAA;AAGnB,gDAAU,OAAA;AACV;IACF;IACA,KAAK;UACC,gCAAA;AAAJ,WAAI,kCAAA,oBAAA,QAAQ,UAAS,kBAAY,QAA7B,mCAAA,SAAA,SAAA,+BAAA,KAAA,mBAAgC,OAAA,EAClC,UAAS,CAAC;AAMZ,cAAQ,OAAO,iCAAW,MAAM,MAAM,QAAQ,WAAW,MAAM,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AAC7E,UAAI,QAAQ,SAAS,UACnB,SAAQ,OAAO;AAGjB,UAAI,QAAQ,SAAS,iBACnB,SAAQ,SAAS,iBAAiB,SAAS,KAAA;AAE7C;IAEF,KAAK;AACH,cAAQ,QAAQ,iCAAW,MAAM,OAAO,QAAQ,GAAG,MAAM,SAAS,gBAAgB,KAAA,GAAQ,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACxG;IACF,KAAK;AACH,cAAQ,MAAM,iCAAW,MAAM,KAAK,QAAQ,GAAG,MAAM,SAAS,eAAe,KAAA,GAAQ,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACnG;IACF;AACE,YAAM,IAAI,MAAM,uBAAuB,KAAA;EAC3C;AAEA,MAAI,MAAM,SAAS,YACjB,OAAM,SAAS,YAAY,OAAA;AAG7B,4CAAU,OAAA;AACV,SAAO;AACT;AAIO,SAAS,0CAAU,OAAgC,OAAkB,QAAgB,SAA0B;AACpH,MAAI,UAA4C,MAAM,KAAI;AAE1D,UAAQ,OAAA;IACN,KAAK,QAAQ;AACX,UAAI,QAAQ,MAAM;AAClB,UAAIC,OAAM;AACV,UAAIC,OAAM;AACV,WAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,eAAc,IAAI;AAC7B,YAAI,OAAO,SAAS;AACpB,QAAAD,OAAM,OAAO,KAAK;AAClB,QAAAC,OAAM,OAAO,KAAK;MACpB;AACA,cAAQ,OAAO,iCAAW,OAAO,QAAQD,MAAKC,MAAK,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACjE;IACF;IACA,KAAK;AACH,cAAQ,SAAS,iCAAW,MAAM,QAAQ,QAAQ,GAAG,IAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACvE;IACF,KAAK;AACH,cAAQ,SAAS,iCAAW,MAAM,QAAQ,QAAQ,GAAG,IAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AACvE;IACF,KAAK;AACH,cAAQ,cAAc,iCAAW,MAAM,aAAa,QAAQ,GAAG,KAAK,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK;AAClF;IACF;AACE,YAAM,IAAI,MAAM,uBAAuB,KAAA;EAC3C;AAEA,SAAO;AACT;AAEA,SAAS,iCAAW,OAAe,QAAgBD,MAAaC,MAAaC,SAAQ,OAAK;AACxF,MAAIA,QAAO;AACT,aAAS,KAAK,KAAK,MAAA;AAEnB,QAAI,QAAQF,KACV,SAAQC;AAGV,QAAI,MAAM,KAAK,IAAI,MAAA;AACnB,QAAI,SAAS,EACX,SAAQ,KAAK,KAAK,QAAQ,GAAA,IAAO;QAEjC,SAAQ,KAAK,MAAM,QAAQ,GAAA,IAAO;AAGpC,QAAI,QAAQA,KACV,SAAQD;EAEZ,OAAO;AACL,aAAS;AACT,QAAI,QAAQA,KACV,SAAQC,QAAOD,OAAM,QAAQ;aACpB,QAAQC,KACjB,SAAQD,QAAO,QAAQC,OAAM;EAEjC;AAEA,SAAO;AACT;AAEO,SAAS,0CAAS,UAAyB,UAA0B;AAC1E,MAAI;AACJ,MAAK,SAAS,SAAS,QAAQ,SAAS,UAAU,KAAO,SAAS,UAAU,QAAQ,SAAS,WAAW,KAAO,SAAS,SAAS,QAAQ,SAAS,UAAU,KAAO,SAAS,QAAQ,QAAQ,SAAS,SAAS,GAAI;AAChN,QAAIE,OAAM,2CAAI,GAAA,2CAAmB,QAAA,GAAW;MAC1C,OAAO,SAAS;MAChB,QAAQ,SAAS;MACjB,OAAO,SAAS;MAChB,MAAM,SAAS;IACjB,CAAA;AAIA,UAAK,GAAA,2CAAWA,MAAK,SAAS,QAAQ;EACxC;AAEE,UAAK,GAAA,0CAAc,QAAA,IAAY,SAAS;AAM1C,QAAM,SAAS,gBAAgB;AAC/B,SAAO,SAAS,WAAW,KAAK;AAChC,SAAO,SAAS,WAAW,KAArB;AACN,SAAO,SAAS,SAAS,KAAnB;AAEN,MAAI,OAAM,GAAA,2CAAa,IAAI,SAAS,QAAQ;AAC5C,UAAO,GAAA,2CAAW,KAAK,SAAS,QAAQ;AAC1C;AAEO,SAAS,0CAAc,UAAyB,UAA0B;AAC/E,SAAO,0CAAS,UAAU,0CAAe,QAAA,CAAA;AAC3C;AAEO,SAAS,0CAAW,UAAyB,OAA8B,QAAgB,SAA0B;AAI1H,UAAQ,OAAA;IACN,KAAK,QAAQ;AACX,UAAIH,OAAM;AACV,UAAIC,OAAM;AACV,WAAI,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,eAAc,IAAI;AAC7B,YAAI,OAAO,SAAS,QAAQ;AAC5B,QAAAD,OAAM,OAAO,KAAK;AAClB,QAAAC,OAAM,OAAO,KAAK;MACpB;AAOA,UAAI,iBAAgB,GAAA,2CAAmB,QAAA;AACvC,UAAI,WAAU,GAAA,2CAAW,0CAAQ,eAAe;QAAC,MAAMD;MAAG,CAAA,GAAI,KAAI,GAAA,2CAAgB,CAAA;AAClF,UAAI,cAAc;SAAC,GAAA,2CAAW,SAAS,SAAS,UAAU,SAAA;SAAY,GAAA,2CAAW,SAAS,SAAS,UAAU,OAAA;QAC1G,OAAO,CAAAI,SAAM,GAAA,2CAAaA,KAAI,SAAS,QAAQ,EAAE,QAAQ,QAAQ,GAAG,EAAE,CAAA;AAEzE,UAAI,WAAU,GAAA,2CAAW,0CAAQ,eAAe;QAAC,MAAMH;MAAG,CAAA,GAAI,KAAI,GAAA,2CAAgB,CAAA;AAClF,UAAI,cAAc;SAAC,GAAA,2CAAW,SAAS,SAAS,UAAU,SAAA;SAAY,GAAA,2CAAW,SAAS,SAAS,UAAU,OAAA;QAC1G,OAAO,CAAAG,SAAM,GAAA,2CAAaA,KAAI,SAAS,QAAQ,EAAE,QAAQ,QAAQ,GAAG,EAAE,IAAG;AAK5E,UAAI,MAAK,GAAA,0CAAc,QAAA,IAAY,SAAS;AAC5C,UAAI,QAAQ,KAAK,MAAM,KAAK,8BAAA;AAC5B,UAAI,YAAY,KAAK;AACrB,WAAK,iCACH,OACA,QACA,KAAK,MAAM,cAAc,8BAAA,GACzB,KAAK,MAAM,cAAc,8BAAA,GACzB,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,KAAK,IACZ,iCAAW;AAGf,cAAO,GAAA,4CAAW,GAAA,2CAAa,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;IAC1E;IACA,KAAK;IACL,KAAK;IACL,KAAK;AAEH,aAAO,0CAAU,UAAU,OAAO,QAAQ,OAAA;IAC5C,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK,OAAO;AACV,UAAI,MAAM,2CAAU,GAAA,2CAAmB,QAAA,GAAW,OAAO,QAAQ,OAAA;AACjE,UAAI,MAAK,GAAA,2CAAW,KAAK,SAAS,QAAQ;AAC1C,cAAO,GAAA,4CAAW,GAAA,2CAAa,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;IAC1E;IACA;AACE,YAAM,IAAI,MAAM,uBAAuB,KAAA;EAC3C;AACF;AAEO,SAAS,0CAAS,UAAyB,QAAiC,gBAA+B;AAGhH,MAAI,iBAAgB,GAAA,2CAAmB,QAAA;AACvC,MAAI,MAAM,0CAAQ,0CAAI,eAAe,MAAA,GAAS,MAAA;AAI9C,MAAI,IAAI,QAAQ,aAAA,MAAmB,EACjC,QAAO;AAGT,MAAI,MAAK,GAAA,2CAAW,KAAK,SAAS,UAAU,cAAA;AAC5C,UAAO,GAAA,4CAAW,GAAA,2CAAa,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAC1E;A;;;;;ACjcA,IAAM,mDAA6B;EAAC;EAAS;EAAW;;AACxD,IAAM,+CAAyB;EAAC;EAAS;EAAU;EAAS;KAAW;;AAiKhE,SAAS,0CAAa,MAAU;AACrC,SAAO,GAAG,OAAO,KAAK,IAAI,EAAE,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,KAAK,MAAM,EAAE,SAAS,GAAG,GAAA,CAAA,GAAO,KAAK,cAAc,OAAO,KAAK,cAAc,GAAA,EAAM,MAAM,CAAA,IAAK,EAAA;AAC/L;AAEO,SAAS,0CAAa,MAAkB;AAC7C,MAAI,iBAAgB,GAAA,2CAAW,MAAM,KAAI,GAAA,2CAAgB,CAAA;AACzD,MAAI;AACJ,MAAI,cAAc,QAAQ,KACxB,QAAO,cAAc,SAAS,IAC1B,SACA,MAAM,OAAO,KAAK,IAAI,IAAI,cAAc,IAAI,CAAA,EAAG,SAAS,GAAG,IAAA;MAE/D,QAAO,OAAO,cAAc,IAAI,EAAE,SAAS,GAAG,GAAA;AAEhD,SAAO,GAAG,IAAA,IAAQ,OAAO,cAAc,KAAK,EAAE,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,cAAc,GAAG,EAAE,SAAS,GAAG,GAAA,CAAA;AAC1G;AAEO,SAAS,0CAAiB,MAAiB;AAEhD,SAAO,GAAG,0CAAa,IAAA,CAAA,IAAS,0CAAa,IAAA,CAAA;AAC/C;AAEA,SAAS,qCAAeC,SAAc;AACpC,MAAI,OAAO,KAAK,KAAKA,OAAA,IAAU,IAAI,MAAM;AACzC,EAAAA,UAAS,KAAK,IAAIA,OAAA;AAClB,MAAI,cAAc,KAAK,MAAMA,UAAU,IAAA;AACvC,MAAI,gBAAiBA,UAAU,OAAoB;AACnD,SAAO,GAAG,IAAA,GAAO,OAAO,WAAA,EAAa,SAAS,GAAG,GAAA,CAAA,IAAQ,OAAO,aAAA,EAAe,SAAS,GAAG,GAAA,CAAA;AAC7F;AAEO,SAAS,0CAAsB,MAAmB;AACvD,SAAO,GAAG,0CAAiB,IAAA,CAAA,GAAQ,qCAAe,KAAK,MAAM,CAAA,IAAK,KAAK,QAAQ;AACjF;;;AC5NA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,SAAS,6BAA6B,KAAK,mBAAmB;AAC1D,MAAI,kBAAkB,IAAI,GAAG,GAAG;AAC5B,UAAM,IAAI,UAAU,gEAAgE;AAAA,EACxF;AACJ;;;ADFA,SAAS,0BAA0B,KAAK,YAAY,OAAO;AACvD,+BAA6B,KAAK,UAAU;AAC5C,aAAW,IAAI,KAAK,KAAK;AAC7B;;;AEcA,SAAS,gCAAU,MAAW;AAC5B,MAAI,WAAqB,OAAO,KAAK,CAAA,MAAO,WACxC,KAAK,MAAK,IACV,KAAI,GAAA,2CAAgB;AAExB,MAAI;AACJ,MAAI,OAAO,KAAK,CAAA,MAAO,SACrB,OAAM,KAAK,MAAK;OACX;AACL,QAAI,OAAO,SAAS,QAAO;AAC3B,UAAM,KAAK,KAAK,SAAS,CAAA;EAC3B;AAEA,MAAI,OAAO,KAAK,MAAK;AACrB,MAAI,QAAQ,KAAK,MAAK;AACtB,MAAI,MAAM,KAAK,MAAK;AAEpB,SAAO;IAAC;IAAU;IAAK;IAAM;IAAO;;AACtC;IAQE,8BAAA,oBAAA,QAAA;AALK,IAAM,4CAAN,MAAM,2CAAA;;EAqCX,OAAqB;AACnB,QAAI,KAAK,IACP,QAAO,IAAI,2CAAa,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;QAEhF,QAAO,IAAI,2CAAa,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EAE1E;;EAGA,IAAI,UAAsC;AACxC,YAAO,GAAA,2CAAI,MAAM,QAAA;EACnB;;EAGA,SAAS,UAAsC;AAC7C,YAAO,GAAA,2CAAS,MAAM,QAAA;EACxB;;EAGA,IAAI,QAAkC;AACpC,YAAO,GAAA,2CAAI,MAAM,MAAA;EACnB;;;;;EAMA,MAAM,OAAkB,QAAgB,SAAsC;AAC5E,YAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;EACxC;;EAGA,OAAO,UAAwB;AAC7B,YAAO,GAAA,2CAAO,MAAM,QAAA;EACtB;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAa,IAAI;EAC1B;;EAGA,QAAQ,GAA4B;AAClC,YAAO,GAAA,2CAAY,MAAM,CAAA;EAC3B;EAxDA,eAAe,MAAa;AApB5B,KAAA,GAAA,2BAAA,MAAA,6BAAA;;aAAA;;AAqBE,QAAI,CAAC,UAAU,KAAK,MAAM,OAAO,GAAA,IAAO,gCAAU,IAAA;AAClD,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,MAAM;AAEX,KAAA,GAAA,2CAAU,IAAI;EAChB;AAgDF;IAME,+BAAA,oBAAA,QAAA;AAHK,IAAM,2CAAN,MAAM,0CAAA;;EA2BX,OAAa;AACX,WAAO,IAAI,0CAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;EACvE;;EAGA,IAAI,UAA8B;AAChC,YAAO,GAAA,2CAAQ,MAAM,QAAA;EACvB;;EAGA,SAAS,UAA8B;AACrC,YAAO,GAAA,2CAAa,MAAM,QAAA;EAC5B;;EAGA,IAAI,QAA0B;AAC5B,YAAO,GAAA,2CAAQ,MAAM,MAAA;EACvB;;;;;EAMA,MAAM,OAAkB,QAAgB,SAAkC;AACxE,YAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;EACxC;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAa,IAAI;EAC1B;;EAGA,QAAQ,GAAoB;AAC1B,YAAO,GAAA,2CAAY,MAAM,CAAA;EAC3B;EAjDA,YACE,OAAe,GACf,SAAiB,GACjB,SAAiB,GACjB,cAAsB,GACtB;AAfF,KAAA,GAAA,2BAAA,MAAA,8BAAA;;aAAA;;AAgBE,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,KAAA,GAAA,2CAAc,IAAI;EACpB;AAuCF;IAME,+BAAA,oBAAA,QAAA;AAHK,IAAM,4CAAN,MAAM,2CAAA;;EA+CX,OAAyB;AACvB,QAAI,KAAK,IACP,QAAO,IAAI,2CAAiB,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;QAE3I,QAAO,IAAI,2CAAiB,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;EAErI;;EAGA,IAAI,UAA8C;AAChD,YAAO,GAAA,2CAAI,MAAM,QAAA;EACnB;;EAGA,SAAS,UAA8C;AACrD,YAAO,GAAA,2CAAS,MAAM,QAAA;EACxB;;EAGA,IAAI,QAAmD;AACrD,YAAO,GAAA,4CAAI,GAAA,2CAAQ,MAAM,MAAA,GAAS,MAAA;EACpC;;;;;EAMA,MAAM,OAA8B,QAAgB,SAA8C;AAChG,YAAQ,OAAA;MACN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,gBAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;MACxC;AACE,gBAAO,GAAA,2CAAU,MAAM,OAAO,QAAQ,OAAA;IAC1C;EACF;;EAGA,OAAO,UAAkB,gBAAuC;AAC9D,YAAO,GAAA,2CAAO,MAAM,UAAU,cAAA;EAChC;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAiB,IAAI;EAC9B;;EAGA,QAAQ,GAA4D;AAClE,QAAI,OAAM,GAAA,2CAAY,MAAM,CAAA;AAC5B,QAAI,QAAQ,EACV,SAAO,GAAA,2CAAY,OAAM,GAAA,2CAAmB,CAAA,CAAA;AAG9C,WAAO;EACT;EAzEA,eAAe,MAAa;AA5B5B,KAAA,GAAA,2BAAA,MAAA,8BAAA;;aAAA;;AA6BE,QAAI,CAAC,UAAU,KAAK,MAAM,OAAO,GAAA,IAAO,gCAAU,IAAA;AAClD,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,OAAO,KAAK,MAAK,KAAM;AAC5B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,cAAc,KAAK,MAAK,KAAM;AAEnC,KAAA,GAAA,2CAAU,IAAI;EAChB;AA6DF;IAME,+BAAA,oBAAA,QAAA;AAHK,IAAM,4CAAN,MAAM,2CAAA;;EAuDX,OAAsB;AACpB,QAAI,KAAK,IACP,QAAO,IAAI,2CAAc,KAAK,UAAU,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;QAEpK,QAAO,IAAI,2CAAc,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,UAAU,KAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;EAE9J;;EAGA,IAAI,UAA2C;AAC7C,YAAO,GAAA,2CAAS,MAAM,QAAA;EACxB;;EAGA,SAAS,UAA2C;AAClD,YAAO,GAAA,2CAAc,MAAM,QAAA;EAC7B;;EAGA,IAAI,QAAiC,gBAAgD;AACnF,YAAO,GAAA,2CAAS,MAAM,QAAQ,cAAA;EAChC;;;;;EAMA,MAAM,OAA8B,QAAgB,SAA2C;AAC7F,YAAO,GAAA,2CAAW,MAAM,OAAO,QAAQ,OAAA;EACzC;;EAGA,SAAe;AACb,YAAO,GAAA,0CAAY,IAAI;EACzB;;EAGA,WAAmB;AACjB,YAAO,GAAA,2CAAsB,IAAI;EACnC;;EAGA,mBAA2B;AACzB,WAAO,KAAK,OAAM,EAAG,YAAW;EAClC;;EAGA,QAAQ,GAA4D;AAElE,WAAO,KAAK,OAAM,EAAG,QAAO,KAAK,GAAA,2CAAQ,GAAG,KAAK,QAAQ,EAAE,OAAM,EAAG,QAAO;EAC7E;EAtEA,eAAe,MAAa;AAhC5B,KAAA,GAAA,2BAAA,MAAA,8BAAA;;aAAA;;AAiCE,QAAI,CAAC,UAAU,KAAK,MAAM,OAAO,GAAA,IAAO,gCAAU,IAAA;AAClD,QAAI,WAAW,KAAK,MAAK;AACzB,QAAIC,UAAS,KAAK,MAAK;AACvB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,SAASA;AACd,SAAK,OAAO,KAAK,MAAK,KAAM;AAC5B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,SAAS,KAAK,MAAK,KAAM;AAC9B,SAAK,cAAc,KAAK,MAAK,KAAM;AAEnC,KAAA,GAAA,2CAAU,IAAI;EAChB;AAsDF;;;AC/XO,SAAS,yCAAc,MAAiB;AAC7C,SAAO,0CAAW,MAAM,KAAI,GAAA,2CAAgB,CAAA;AAC5C,MAAI,QAAO,GAAA,2CAAgB,KAAK,KAAK,KAAK,IAAI;AAC9C,SAAO,qCAAe,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW;AACzG;AAEA,SAAS,qCAAe,MAAc,OAAe,KAAa,MAAc,QAAgB,QAAgB,aAAmB;AAGjI,MAAI,OAAO,oBAAI,KAAA;AACf,OAAK,YAAY,MAAM,QAAQ,QAAQ,WAAA;AACvC,OAAK,eAAe,MAAM,QAAQ,GAAG,GAAA;AACrC,SAAO,KAAK,QAAO;AACrB;AAEO,SAAS,0CAAkB,IAAY,UAAgB;AAE5D,MAAI,aAAa,MACf,QAAO;AAIT,MAAI,KAAK,KAAK,cAAa,GAAA,2CAAe,EACxC,QAAO,IAAI,KAAK,EAAA,EAAI,kBAAiB,IAA9B;AAGT,MAAI,EAAA,MAAK,OAAO,KAAK,MAAM,QAAQ,OAAQ,IAAI,uCAAiB,IAAI,QAAA;AACpE,MAAI,MAAM,qCAAe,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAA;AACjE,SAAO,MAAM,KAAK,MAAM,KAAK,GAAA,IAAQ;AACvC;AAEA,IAAM,6CAAuB,oBAAI,IAAA;AAEjC,SAAS,uCAAiB,IAAY,UAAgB;AACpD,MAAI,YAAY,2CAAqB,IAAI,QAAA;AACzC,MAAI,CAAC,WAAW;AACd,gBAAY,IAAI,KAAK,eAAe,SAAS;;MAE3C,QAAQ;MACR,KAAK;MACL,MAAM;MACN,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;MACR,QAAQ;IACV,CAAA;AAEA,+CAAqB,IAAI,UAAU,SAAA;EACrC;AAEA,MAAI,QAAQ,UAAU,cAAc,IAAI,KAAK,EAAA,CAAA;AAC7C,MAAI,aAAuC,CAAC;AAC5C,WAAS,QAAQ,MACf,KAAI,KAAK,SAAS,UAChB,YAAW,KAAK,IAAI,IAAI,KAAK;AAKjC,SAAO;;IAEL,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW;IAC7F,OAAO,CAAC,WAAW;IACnB,KAAK,CAAC,WAAW;IACjB,MAAM,WAAW,SAAS,OAAO,IAAI,CAAC,WAAW;IACjD,QAAQ,CAAC,WAAW;IACpB,QAAQ,CAAC,WAAW;EACtB;AACF;AAEA,IAAM,kCAAY;AASlB,SAAS,wCAAkB,MAAwB,UAAkB,SAAiB,OAAa;AACjG,MAAI,QAAQ,YAAY,QAAQ;IAAC;MAAW;IAAC;IAAS;;AACtD,SAAO,MAAM,OAAO,CAAA,aAAY,sCAAgB,MAAM,UAAU,QAAA,CAAA;AAClE;AAEA,SAAS,sCAAgB,MAAwB,UAAkB,UAAgB;AACjF,MAAI,QAAQ,uCAAiB,UAAU,QAAA;AACvC,SAAO,KAAK,SAAS,MAAM,QACtB,KAAK,UAAU,MAAM,SACrB,KAAK,QAAQ,MAAM,OACnB,KAAK,SAAS,MAAM,QACpB,KAAK,WAAW,MAAM,UACtB,KAAK,WAAW,MAAM;AAC7B;AAEO,SAAS,0CAAW,MAAuC,UAAkB,iBAAiC,cAAY;AAC/H,MAAI,WAAW,0CAAmB,IAAA;AAGlC,MAAI,aAAa,MACf,QAAO,yCAAc,QAAA;AAIvB,MAAI,cAAa,GAAA,2CAAe,KAAO,mBAAmB,cAAc;AACtE,eAAW,0CAAW,UAAU,KAAI,GAAA,2CAAgB,CAAA;AAGpD,QAAIC,QAAO,oBAAI,KAAA;AACf,QAAI,QAAO,GAAA,2CAAgB,SAAS,KAAK,SAAS,IAAI;AACtD,IAAAA,MAAK,YAAY,MAAM,SAAS,QAAQ,GAAG,SAAS,GAAG;AACvD,IAAAA,MAAK,SAAS,SAAS,MAAM,SAAS,QAAQ,SAAS,QAAQ,SAAS,WAAW;AACnF,WAAOA,MAAK,QAAO;EACrB;AAEA,MAAI,KAAK,yCAAc,QAAA;AACvB,MAAI,eAAe,0CAAkB,KAAK,iCAAW,QAAA;AACrD,MAAI,cAAc,0CAAkB,KAAK,iCAAW,QAAA;AACpD,MAAI,QAAQ,wCAAkB,UAAU,UAAU,KAAK,cAAc,KAAK,WAAA;AAE1E,MAAI,MAAM,WAAW,EACnB,QAAO,MAAM,CAAA;AAGf,MAAI,MAAM,SAAS,EACjB,SAAQ,gBAAA;;IAEN,KAAK;IACL,KAAK;AACH,aAAO,MAAM,CAAA;IACf,KAAK;AACH,aAAO,MAAM,MAAM,SAAS,CAAA;IAC9B,KAAK;AACH,YAAM,IAAI,WAAW,wCAAA;EACzB;AAGF,UAAQ,gBAAA;IACN,KAAK;AACH,aAAO,KAAK,IAAI,KAAK,cAAc,KAAK,WAAA;;IAE1C,KAAK;IACL,KAAK;AACH,aAAO,KAAK,IAAI,KAAK,cAAc,KAAK,WAAA;IAC1C,KAAK;AACH,YAAM,IAAI,WAAW,6BAAA;EACzB;AACF;AAEO,SAAS,0CAAO,UAA2C,UAAkB,iBAAiC,cAAY;AAC/H,SAAO,IAAI,KAAK,0CAAW,UAAU,UAAU,cAAA,CAAA;AACjD;AAKO,SAAS,0CAAa,IAAY,UAAgB;AACvD,MAAIC,UAAS,0CAAkB,IAAI,QAAA;AACnC,MAAI,OAAO,IAAI,KAAK,KAAKA,OAAA;AACzB,MAAI,OAAO,KAAK,eAAc;AAC9B,MAAI,QAAQ,KAAK,YAAW,IAAK;AACjC,MAAI,MAAM,KAAK,WAAU;AACzB,MAAI,OAAO,KAAK,YAAW;AAC3B,MAAI,SAAS,KAAK,cAAa;AAC/B,MAAI,SAAS,KAAK,cAAa;AAC/B,MAAI,cAAc,KAAK,mBAAkB;AAEzC,SAAO,KAAI,GAAA,2CAAc,OAAO,IAAI,OAAO,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,MAAM,OAAO,KAAK,UAAUA,SAAQ,MAAM,QAAQ,QAAQ,WAAA;AACpI;AAcO,SAAS,0CAAe,UAAyB;AACtD,SAAO,KAAI,GAAA,2CAAa,SAAS,UAAU,SAAS,KAAK,SAAS,MAAM,SAAS,OAAO,SAAS,GAAG;AACtG;AAwBO,SAAS,0CAAmB,MAAuD,MAAc;AACtG,MAAI,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc;AACpD,MAAI,cAAc,KACf,EAAA,EAAA,MAAK,QAAQ,QAAQ,YAAa,IAAI;WAC9B,UAAU,QAAQ,CAAC,KAC5B,QAAO;AAGT,MAAI,KACD,EAAA,EAAA,MAAK,QAAQ,QAAQ,YAAa,IAAI;AAGzC,SAAO,KAAI,GAAA,2CACT,KAAK,UACL,KAAK,KACL,KAAK,MACL,KAAK,OACL,KAAK,KACL,MACA,QACA,QACA,WAAA;AAEJ;AAQO,SAAS,0CAAsC,MAAS,UAAkB;AAC/E,OAAI,GAAA,0CAAgB,KAAK,UAAU,QAAA,EACjC,QAAO;AAGT,MAAI,eAAe,SAAS,cAAc,KAAK,SAAS,YAAY,IAAA,CAAA;AACpE,MAAI,OAAmB,KAAK,KAAI;AAChC,OAAK,WAAW;AAChB,OAAK,MAAM,aAAa;AACxB,OAAK,OAAO,aAAa;AACzB,OAAK,QAAQ,aAAa;AAC1B,OAAK,MAAM,aAAa;AACxB,GAAA,GAAA,2CAAU,IAAA;AACV,SAAO;AACT;AAMO,SAAS,0CAAQ,MAAuD,UAAkB,gBAA+B;AAC9H,MAAI,iBAAgB,GAAA,4CAAe;AACjC,QAAI,KAAK,aAAa,SACpB,QAAO;AAGT,WAAO,0CAAW,MAAM,QAAA;EAC1B;AAEA,MAAI,KAAK,0CAAW,MAAM,UAAU,cAAA;AACpC,SAAO,0CAAa,IAAI,QAAA;AAC1B;AAEO,SAAS,yCAAY,MAAmB;AAC7C,MAAI,KAAK,yCAAc,IAAA,IAAQ,KAAK;AACpC,SAAO,IAAI,KAAK,EAAA;AAClB;AAGO,SAAS,0CAAW,MAAqB,UAAgB;AAC9D,MAAI,KAAK,yCAAc,IAAA,IAAQ,KAAK;AACpC,SAAO,0CAAW,0CAAa,IAAI,QAAA,GAAW,KAAK,QAAQ;AAC7D;;;AC/SA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAcO,IAAM,4CAAgB;EAC3B,OAAO;EACP,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;EACJ,IAAI;AACN;;;AC1FO,SAAS,0CAAU,GAAc,GAAY;AAClD,OAAI,GAAA,2CAAW,GAAG,EAAE,QAAQ;AAC5B,SAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE;AACpF;AAGO,SAAS,0CAAY,GAAc,GAAY;AACpD,OAAI,GAAA,2CAAW,GAAG,EAAE,QAAQ;AAE5B,MAAI,0CAAa,CAAA;AACjB,MAAI,0CAAa,CAAA;AACjB,SAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC/D;AAWO,SAAS,0CAAW,GAAc,GAAY;AACnD,SAAO,yCAAgB,EAAE,UAAU,EAAE,QAAQ,KAAK,0CAAU,GAAG,CAAA;AACjE;AAGO,SAAS,0CAAa,GAAc,GAAY;AACrD,SAAO,yCAAgB,EAAE,UAAU,EAAE,QAAQ,KAAK,0CAAY,GAAG,CAAA;AACnE;AAQO,SAAS,yCAAgB,GAAa,GAAW;MAC/C,YAAkB;MAAlB,aAAA;AAAP,UAAO,QAAA,eAAA,aAAA,EAAE,aAAO,QAAT,eAAA,SAAA,SAAA,WAAA,KAAA,GAAY,CAAA,OAAA,QAAZ,gBAAA,SAAA,eAAkB,aAAA,EAAE,aAAO,QAAT,eAAA,SAAA,SAAA,WAAA,KAAA,GAAY,CAAA,OAAA,QAA9B,SAAA,SAAA,OAAoC,EAAE,eAAe,EAAE;AAChE;AAGO,SAAS,0CAAQ,MAAiB,UAAgB;AACvD,SAAO,0CAAU,MAAM,0CAAM,QAAA,CAAA;AAC/B;AAEA,IAAM,gCAAU;EACd,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACP;AASO,SAAS,0CAAa,MAAiB,QAAgB,gBAA0B;AACtF,MAAI,SAAS,KAAK,SAAS,YAAY,IAAA;AAIvC,MAAI,YAAY,iBAAiB,8BAAQ,cAAA,IAAkB,mCAAa,MAAA;AACxE,MAAI,YAAY,KAAK,KAAK,SAAS,IAAI,SAAA,IAAa;AACpD,MAAI,YAAY,EACd,cAAa;AAGf,SAAO;AACT;AAGO,SAAS,yCAAI,UAAgB;AAClC,UAAO,GAAA,2CAAa,KAAK,IAAG,GAAI,QAAA;AAClC;AAGO,SAAS,0CAAM,UAAgB;AACpC,UAAO,GAAA,2CAAe,yCAAI,QAAA,CAAA;AAC5B;AAEO,SAAS,0CAAY,GAAoB,GAAkB;AAChE,SAAO,EAAE,SAAS,YAAY,CAAA,IAAK,EAAE,SAAS,YAAY,CAAA;AAC5D;AAEO,SAAS,0CAAY,GAAY,GAAU;AAChD,SAAO,+BAAS,CAAA,IAAK,+BAAS,CAAA;AAChC;AAEA,SAAS,+BAAS,GAAU;AAC1B,SAAO,EAAE,OAAF,OAA0B,EAAE,SAAF,MAAuB,EAAE,SAAS,MAAO,EAAE;AAC9E;AAaA,IAAI,sCAA+B;AAG5B,SAAS,4CAAA;AAEd,MAAI,uCAAiB,KACnB,uCAAgB,IAAI,KAAK,eAAc,EAAG,gBAAe,EAAG;AAG9D,SAAO;AACT;AAOO,SAAS,0CAAa,MAAe;AAE1C,SAAO,KAAK,SAAS;IAAC,MAAM,KAAK,MAAM;EAAC,CAAA;AAC1C;AAOO,SAAS,0CAAW,MAAe;AACxC,SAAO,KAAK,IAAI;IAAC,MAAM,KAAK,SAAS,eAAe,IAAA,IAAQ,KAAK;EAAG,CAAA;AACtE;AAuDA,IAAM,sCAAgB,oBAAI,IAAA;AAE1B,SAAS,gCAAU,QAAc;AAG/B,MAAI,KAAK,QAAQ;AAEf,QAAI,SAAS,oCAAc,IAAI,MAAA;AAC/B,QAAI,CAAC,QAAQ;AAEX,eAAS,IAAI,KAAK,OAAO,MAAA,EAAQ,SAAQ,EAAG;AAC5C,UAAI,OACF,qCAAc,IAAI,QAAQ,MAAA;IAE9B;AACA,WAAO;EACT;AAMA,MAAI,OAAO,OAAO,MAAM,GAAA,EAAK,CAAA;AAC7B,SAAO,SAAS,MAAM,SAAY;AACpC;AAEA,SAAS,mCAAa,QAAc;AAGlC,MAAI,SAAS,gCAAU,MAAA;AACvB,SAAO,UAAS,GAAA,2CAAc,MAAA,KAAW,IAAI;AAC/C;A;;;;;;;;;;ACnOA,IAAM,2CAAqB;AAOpB,IAAM,4CAAN,eAA+B,GAAA,2CAAgB;EAGpD,cAAc,IAA0B;AACtC,QAAI,gBAAgB,MAAM,cAAc,EAAA;AACxC,QAAI,QAAO,GAAA,2CAAgB,cAAc,KAAK,cAAc,IAAI;AAChE,WAAO,KAAI,GAAA,2CACT,MACA,OAAO,0CACP,cAAc,OACd,cAAc,GAAG;EAErB;EAEA,YAAY,MAA+B;AACzC,WAAO,MAAM,YAAY,kCAAY,IAAA,CAAA;EACvC;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,eAAe,MAA+B;AAC5C,WAAO,MAAM,eAAe,kCAAY,IAAA,CAAA;EAC1C;EAEA,cAAoB;EAAC;;AA1BhB,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AA0BnC;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,CAAC,KAAK,IAAA,KAAQ,GAAA,2CAAiB,KAAK,OAAO,wCAAA;AAC/C,SAAO,KAAI,GAAA,2CACT,KACA,MACA,KAAK,OACL,KAAK,GAAG;AAEZ;A;;;;;AC5CA,IAAM,uCAAiB;AACvB,IAAM,qCAAe;AAIrB,IAAM,2CAAqB;AAE3B,SAAS,oCAAc,OAAe,MAAc,OAAe,KAAW;AAC5E,SACE,QACE,MAAM,OACN,KAAK,MAAM,OAAO,CAAA,IAClB,MAAM,QAAQ,KACd,MAAM;AAEZ;AAEA,SAAS,oCAAc,OAAe,IAAU;AAC9C,MAAI,OAAO,KAAK,MAAO,KAAK,KAAK,SAAU,IAAA;AAC3C,MAAI,QAAQ,IAAI,KAAK,OAAO,KAAK,oCAAc,OAAO,MAAM,GAAG,CAAA,KAAM,EAAA;AACrE,MAAI,MAAM,KAAK,IAAI,oCAAc,OAAO,MAAM,OAAO,CAAA;AACrD,SAAO;IAAC;IAAM;IAAO;;AACvB;AAEA,SAAS,iCAAW,MAAY;AAC9B,SAAO,KAAK,MAAO,OAAO,IAAK,CAAA;AACjC;AAEA,SAAS,qCAAe,MAAc,OAAa;AAMjD,MAAI,QAAQ,OAAO;AAEjB,WAAO;;AAGP,WAAO,iCAAW,IAAA,IAAQ;AAE9B;AAOO,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,CAAC,MAAM,OAAO,GAAA,IAAO,oCAAc,sCAAgB,EAAA;AACvD,QAAI,MAAM;AACV,QAAI,QAAQ,GAAG;AACb,YAAM;AACN,cAAQ;IACV;AAEA,WAAO,KAAI,GAAA,2CAAa,MAAM,KAAK,MAAM,OAAO,GAAA;EAClD;EAEA,YAAY,MAA+B;AACzC,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,QAAQ,KACf,SAAQ;AAGV,WAAO,oCAAc,sCAAgB,MAAM,KAAK,OAAO,KAAK,GAAG;EACjE;EAEA,eAAe,MAA+B;AAC5C,WAAO,qCAAe,KAAK,MAAM,KAAK,KAAK;EAC7C;EAEA,kBAA0B;AACxB,WAAO;EACT;EAEA,cAAc,MAA+B;AAC3C,WAAO,MAAM,iCAAW,KAAK,IAAI;EACnC;EAEA,cAAc,MAA+B;AAI3C,WAAO,KAAK,QAAQ,OAAO,OAAO;EACpC;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAM;;EAChB;;SA3CA,aAAiC;;AA4CnC;AAMO,IAAM,4CAAN,cAAwC,0CAAA;EAG7C,cAAc,IAA0B;AACtC,QAAI,CAAC,MAAM,OAAO,GAAA,IAAO,oCAAc,sCAAgB,EAAA;AACvD,YAAQ;AACR,WAAO,KAAI,GAAA,2CAAa,MAAM,MAAM,MAAM,OAAO,GAAA;EACnD;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,gBAAwB;AAEtB,WAAO;EACT;;AAhBK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAgBnC;AAOO,IAAM,4CAAN,cAA6B,0CAAA;EAGlC,cAAc,IAA0B;AACtC,QAAI,CAAC,MAAM,OAAO,GAAA,IAAO,oCAAc,oCAAc,EAAA;AACrD,QAAI,MAAM;AACV,QAAI,QAAQ,GAAG;AACb,YAAM;AACN,aAAO,IAAI;IACb;AAEA,WAAO,KAAI,GAAA,2CAAa,MAAM,KAAK,MAAM,OAAO,GAAA;EAClD;EAEA,YAAY,MAA+B;AACzC,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,QAAQ,MACf,QAAO,IAAI;AAGb,WAAO,oCAAc,oCAAc,MAAM,KAAK,OAAO,KAAK,GAAG;EAC/D;EAEA,eAAe,MAA+B;AAC5C,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,QAAQ,MACf,QAAO,IAAI;AAGb,WAAO,qCAAe,MAAM,KAAK,KAAK;EACxC;EAEA,aAAa,MAAgC;AAC3C,WAAO,KAAK,QAAQ;EACtB;EAEA,YAAY,MAAsC;AAChD,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,MAAM,KAAK,QAAQ,QAAQ,OAAO;AACvC,WAAK,OAAO,IAAI,KAAK;IACvB;EACF;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAO;;EACjB;EAEA,cAAc,MAA+B;AAI3C,WAAO,KAAK,QAAQ,QAAQ,OAAO;EACrC;;AApDK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAoDnC;A;;;;;AChLA,IAAM,qCAAe;AAIrB,IAAM,mCAAa;AACnB,IAAM,kCAAa,KAAK;AAKxB,IAAM,mCAAa;AACnB,IAAM,oCAAc,KAAK,mCAAa;AACtC,IAAM,oCAAc,mCAAa,kCAAY;AAE7C,SAAS,iCAAW,MAAY;AAC9B,UAAO,GAAA,2CAAI,OAAO,IAAI,GAAG,EAAA,IAAM;AACjC;AAIA,SAAS,mCAAa,MAAY;AAChC,MAAI,SAAS,KAAK,OAAO,MAAM,OAAO,OAAO,EAAA;AAC7C,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,MAAM,SAAS,KAAK,KAAK,MAAM,QAAQ,KAAA;AAE3C,OAAI,GAAA,2CAAI,KAAK,MAAM,IAAI,CAAA,IAAK,EAC1B,QAAO;AAGT,SAAO;AACT;AAGA,SAAS,mCAAa,MAAY;AAChC,MAAI,OAAO,mCAAa,OAAO,CAAA;AAC/B,MAAI,UAAU,mCAAa,IAAA;AAC3B,MAAI,OAAO,mCAAa,OAAO,CAAA;AAE/B,MAAI,OAAO,YAAY,IACrB,QAAO;AAGT,MAAI,UAAU,SAAS,IACrB,QAAO;AAGT,SAAO;AACT;AAEA,SAAS,kCAAY,MAAY;AAC/B,SAAO,mCAAa,IAAA,IAAQ,mCAAa,IAAA;AAC3C;AAEA,SAAS,oCAAc,MAAY;AACjC,SAAO,kCAAY,OAAO,CAAA,IAAK,kCAAY,IAAA;AAC7C;AAEA,SAAS,kCAAY,MAAY;AAC/B,MAAI,aAAa,oCAAc,IAAA;AAE/B,MAAI,aAAa,IACf,eAAc;AAGhB,UAAQ,YAAA;IACN,KAAK;AACH,aAAO;;IACT,KAAK;AACH,aAAO;;IACT,KAAK;AACH,aAAO;EACX;AACF;AAEA,SAAS,qCAAe,MAAc,OAAa;AAEjD,MAAI,SAAS,KAAK,CAAC,iCAAW,IAAA,EAC5B;AAIF,MAAI,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU,GACzE,QAAO;AAGT,MAAI,WAAW,kCAAY,IAAA;AAG3B,MAAI,UAAU,EACZ,QAAO,aAAa,IAAI,KAAK;AAI/B,MAAI,UAAU,EACZ,QAAO,aAAa,IAAI,KAAK;AAI/B,MAAI,UAAU,EACZ,QAAO,iCAAW,IAAA,IAAQ,KAAK;AAGjC,SAAO;AACT;AAOO,IAAM,2CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,IAAI,KAAK;AACb,QAAI,IAAK,IAAI,kCAAa;AAC1B,QAAI,OAAO,KAAK,OAAO,KAAK,IAAI,OAAO,GAAA,IAAO;AAC9C,QAAI,KAAK,kCAAY,IAAA;AACrB,QAAI,YAAY,KAAK,MAAM,IAAI,EAAA;AAG/B,WAAO,YAAY,GAAG;AACpB;AACA,WAAK,kCAAY,IAAA;AACjB,kBAAY,KAAK,MAAM,IAAI,EAAA;IAC7B;AAGA,QAAI,QAAQ;AACZ,QAAI,aAAa;AACjB,WAAO,aAAa,WAAW;AAC7B,oBAAc,qCAAe,MAAM,KAAA;AACnC;IACF;AAEA;AACA,kBAAc,qCAAe,MAAM,KAAA;AAEnC,QAAI,MAAM,YAAY;AACtB,WAAO,KAAI,GAAA,2CAAa,MAAM,MAAM,OAAO,GAAA;EAC7C;EAEA,YAAY,MAA+B;AACzC,QAAI,KAAK,kCAAY,KAAK,IAAI;AAC9B,aAAS,QAAQ,GAAG,QAAQ,KAAK,OAAO,QACtC,OAAM,qCAAe,KAAK,MAAM,KAAA;AAGlC,WAAO,KAAK,KAAK,MAAM;EACzB;EAEA,eAAe,MAA+B;AAC5C,WAAO,qCAAe,KAAK,MAAM,KAAK,KAAK;EAC7C;EAEA,gBAAgB,MAA+B;AAC7C,WAAO,iCAAW,KAAK,IAAI,IAAI,KAAK;EACtC;EAEA,cAAc,MAA+B;AAC3C,WAAO,oCAAc,KAAK,IAAI;EAChC;EAEA,gBAAwB;AAEtB,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,iBAAiB,MAAgC,cAAqC;AAEpF,QAAI,aAAa,SAAS,KAAK,MAAM;AACnC,UAAI,iCAAW,aAAa,IAAI,KAAK,CAAC,iCAAW,KAAK,IAAI,KAAK,aAAa,QAAQ,EAClF,MAAK;eACI,CAAC,iCAAW,aAAa,IAAI,KAAK,iCAAW,KAAK,IAAI,KAAK,aAAa,QAAQ,EACzF,MAAK;IAET;EACF;;SAtEA,aAAiC;;AAuEnC;A;;;;;ACrLA,IAAM,yCAAmB;AAGzB,IAAM,0CAAoB;AAOnB,IAAM,4CAAN,eAA6B,GAAA,2CAAgB;EAGlD,cAAc,IAA0B;AAEtC,QAAI,OAAO,MAAM,cAAc,EAAA;AAG/B,QAAI,aAAa,KAAK,OAAO;AAG7B,QAAI,OAAO,MAAK,GAAA,2CAAqB,KAAK,KAAK,KAAK,MAAM,GAAG,CAAA;AAE7D,QAAI;AACJ,QAAI,OAAO,yCAAmB;AAE5B;AAGA,mBAAY,GAAA,2CAAW,KAAK,OAAO,CAAA,IAAK,KAAK;AAC7C,cAAQ,YAAa,MAAW,KAAU;IAC5C,OAAO;AAEL,mBAAY,GAAA,2CAAW,KAAK,IAAI,IAAI,KAAK;AACzC,cAAQ;IACV;AAEA,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,WAAW;AACpB,oBAAc;AACd,kBAAY,OAAO;IACrB,OAAO;AACL,UAAI,OAAO,OAAO;AAClB,UAAI,OAAQ,KAAS;AACnB,sBAAc,KAAK,MAAM,OAAO,EAAA,IAAM;AACtC,oBAAa,OAAO,KAAM;MAC5B,OAAO;AACL,gBAAQ;AACR,sBAAc,KAAK,MAAM,OAAO,EAAA,IAAM;AACtC,oBAAa,OAAO,KAAM;MAC5B;IACF;AAEA,WAAO,KAAI,GAAA,2CAAa,MAAM,YAAY,aAAa,SAAA;EACzD;EAEA,YAAY,MAA+B;AACzC,QAAI,eAAe,KAAK,OAAO;AAC/B,QAAI,CAAC,KAAK,IAAA,KAAQ,GAAA,2CAAiB,YAAA;AAEnC,QAAI;AACJ,QAAI;AACJ,SAAI,GAAA,2CAAW,IAAA,GAAO;AACpB,kBAAY;AACZ,YAAK,GAAA,2CAAqB,KAAK,MAAM,GAAG,EAAA;IAC1C,OAAO;AACL,kBAAY;AACZ,YAAK,GAAA,2CAAqB,KAAK,MAAM,GAAG,EAAA;IAC1C;AAEA,QAAI,KAAK,UAAU,EACjB,QAAO,KAAK,KAAK,MAAM;AAGzB,UAAM,YAAY,KAAK,IAAI,KAAK,QAAQ,GAAG,CAAA,IAAK;AAEhD,QAAI,KAAK,SAAS,EAChB,QAAO,KAAK,QAAQ,KAAK;AAG3B,UAAM,KAAK,MAAM;AACjB,WAAO;EACT;EAEA,eAAe,MAA+B;AAC5C,QAAI,KAAK,UAAU,MAAK,GAAA,2CAAW,KAAK,OAAO,sCAAA,EAC7C,QAAO;AAGT,QAAI,KAAK,SAAS,KAAK,KAAK,SAAS,EACnC,QAAO;AAGT,WAAO;EACT;EAEA,gBAAwB;AAGtB,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,cAAoB;EAAC;;AAjGhB,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAiGnC;A;;;;;AC9GA,IAAM,mCAAa;AACnB,IAAM,0CAAoB;AAC1B,IAAM,4CAAsB;AAC5B,IAAM,0CAAoB;AAC1B,IAAM,4CAAsB;AAE5B,SAAS,yCAAmB,OAAe,MAAc,OAAe,KAAW;AACjF,SAAO,MACL,KAAK,KAAK,QAAQ,QAAQ,EAAA,KACzB,OAAO,KAAK,MACb,KAAK,OAAO,IAAI,KAAK,QAAQ,EAAA,IAC7B,QAAQ;AACZ;AAEA,SAAS,yCAAmB,UAAoB,OAAe,IAAU;AACvE,MAAI,OAAO,KAAK,OAAO,MAAM,KAAK,SAAS,SAAS,KAAA;AACpD,MAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,KAAK,yCAAmB,OAAO,MAAM,GAAG,CAAA,MAAO,IAAA,IAAQ,CAAA;AACjG,MAAI,MAAM,KAAK,yCAAmB,OAAO,MAAM,OAAO,CAAA,IAAK;AAE3D,SAAO,KAAI,GAAA,2CAAa,UAAU,MAAM,OAAO,GAAA;AACjD;AAEA,SAAS,iCAAW,MAAY;AAC9B,UAAQ,KAAK,KAAK,QAAQ,KAAK;AACjC;AASO,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,WAAO,yCAAmB,MAAM,kCAAY,EAAA;EAC9C;EAEA,YAAY,MAA+B;AACzC,WAAO,yCAAmB,kCAAY,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EACvE;EAEA,eAAe,MAA+B;AAC5C,QAAI,SAAS,KAAK,KAAK,QAAQ;AAC/B,QAAI,KAAK,UAAU,MAAM,iCAAW,KAAK,IAAI,EAC3C;AAGF,WAAO;EACT;EAEA,kBAA0B;AACxB,WAAO;EACT;EAEA,cAAc,MAA+B;AAC3C,WAAO,iCAAW,KAAK,IAAI,IAAI,MAAM;EACvC;EAEA,gBAAwB;AAEtB,WAAO;EACT;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;;SAlCA,aAAiC;;AAmCnC;AASO,IAAM,4CAAN,cAAqC,0CAAA;EAG1C,cAAc,IAA0B;AACtC,WAAO,yCAAmB,MAAM,yCAAmB,EAAA;EACrD;EAEA,YAAY,MAA+B;AACzC,WAAO,yCAAmB,yCAAmB,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EAC9E;;AATK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AASnC;AAGA,IAAM,sCAAgB;AACtB,IAAI;AACJ,IAAI;AAEJ,SAAS,wCAAkB,MAAY;AACrC,SAAO,4CAAsB,gDAA0B,OAAO,yCAAA;AAChE;AAEA,SAAS,0CAAoB,MAAc,OAAa;AACtD,MAAI,MAAO,OAAO;AAClB,MAAI,OAAQ,KAAS,MAAM,QAAQ;AACnC,OAAK,2CAAqB,GAAA,IAAO,UAAU,EACzC,QAAO;MAEP,QAAO;AAEX;AAEA,SAAS,yCAAmB,MAAc,OAAa;AACrD,MAAI,MAAM,wCAAkB,IAAA;AAC5B,WAAS,IAAI,GAAG,IAAI,OAAO,IACzB,QAAO,0CAAoB,MAAM,CAAA;AAEnC,SAAO;AACT;AAEA,SAAS,yCAAmB,MAAY;AACtC,SAAO,gDAA0B,OAAO,IAAI,yCAAA,IAAuB,gDAA0B,OAAO,yCAAA;AACtG;AASO,IAAM,4CAAN,cAAsC,0CAAA;EAsB3C,cAAc,IAA0B;AACtC,QAAI,OAAO,KAAK;AAChB,QAAI,YAAY,wCAAkB,yCAAA;AAClC,QAAI,UAAU,wCAAkB,uCAAA;AAChC,QAAI,OAAO,aAAa,OAAO,QAC7B,QAAO,MAAM,cAAc,EAAA;SACtB;AACL,UAAI,IAAI,4CAAsB;AAC9B,UAAI,IAAI;AACR,UAAI,IAAI;AACR,aAAO,IAAI,GAAG;AACZ;AACA,YAAI,OAAO,wCAAkB,CAAA,IAAK;AAClC,YAAI,aAAa,yCAAmB,CAAA;AACpC,YAAI,MAAM,YAAY;AACpB,cAAI;AACJ;QACF,WAAW,IAAI,YAAY;AACzB,cAAI,cAAc,0CAAoB,GAAG,CAAA;AACzC,cAAI;AACJ,iBAAO,IAAI,aAAa;AACtB,iBAAK;AACL;AACA,0BAAc,0CAAoB,GAAG,CAAA;UACvC;AACA;QACF;MACF;AAEA,aAAO,KAAI,GAAA,2CAAa,MAAM,GAAG,GAAI,OAAO,yCAAmB,GAAG,CAAA,IAAK,CAAA;IACzE;EACF;EAEA,YAAY,MAA+B;AACzC,QAAI,KAAK,OAAO,6CAAuB,KAAK,OAAO,wCACjD,QAAO,MAAM,YAAY,IAAA;AAG3B,WAAO,mCAAa,yCAAmB,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM;EAC9E;EAEA,eAAe,MAA+B;AAC5C,QAAI,KAAK,OAAO,6CAAuB,KAAK,OAAO,wCACjD,QAAO,MAAM,eAAe,IAAA;AAG9B,WAAO,0CAAoB,KAAK,MAAM,KAAK,KAAK;EAClD;EAEA,cAAc,MAA+B;AAC3C,QAAI,KAAK,OAAO,6CAAuB,KAAK,OAAO,wCACjD,QAAO,MAAM,cAAc,IAAA;AAG7B,WAAO,yCAAmB,KAAK,IAAI;EACrC;EA1EA,cAAc;AACZ,UAAK,GAAA,KAHP,aAAiC;AAI/B,QAAI,CAAC,2CACH,8CAAuB,IAAI,YAAY,WAAW,KAAK,KAAK,mCAAA,GAAgB,CAAA,MAAK,EAAE,WAAW,CAAA,CAAA,EAAI,MAAM;AAG1G,QAAI,CAAC,iDAA2B;AAC9B,wDAA4B,IAAI,YAAY,0CAAoB,4CAAsB,CAAA;AAEtF,UAAI,YAAY;AAChB,eAAS,OAAO,2CAAqB,QAAQ,yCAAmB,QAAQ;AACtE,wDAA0B,OAAO,yCAAA,IAAuB;AACxD,iBAAS,IAAI,GAAG,KAAK,IAAI,IACvB,cAAa,0CAAoB,MAAM,CAAA;MAE3C;IACF;EACF;AA0DF;A;;;;;AC5MA,IAAM,wCAAkB;EAAC;IAAC;IAAM;IAAG;;EAAI;IAAC;IAAM;IAAG;;EAAK;IAAC;IAAM;IAAI;;EAAK;IAAC;IAAM;IAAG;;EAAI;IAAC;IAAM;IAAG;;;AAC9F,IAAM,sCAAgB;EAAC;IAAC;IAAM;IAAG;;EAAK;IAAC;IAAM;IAAI;;EAAK;IAAC;IAAM;IAAG;;EAAI;IAAC;IAAM;IAAG;;;AAC9E,IAAM,oCAAc;EAAC;EAAM;EAAM;EAAM;EAAM;;AAC7C,IAAM,kCAAY;EAAC;EAAS;EAAU;EAAS;EAAU;;AAEzD,SAAS,+CAAyB,MAAqB;AACrD,QAAM,MAAM,sCAAgB,UAAU,CAAC,CAAC,MAAM,OAAO,GAAA,MAAI;AACvD,QAAI,KAAK,OAAO,KACd,QAAO;AAGT,QAAI,KAAK,SAAS,QAAQ,KAAK,QAAQ,MACrC,QAAO;AAGT,QAAI,KAAK,SAAS,QAAQ,KAAK,UAAU,SAAS,KAAK,MAAM,IAC3D,QAAO;AAGT,WAAO;EACT,CAAA;AAEA,MAAI,QAAQ,GACV,QAAO,sCAAgB,SAAS;AAGlC,MAAI,QAAQ,EACV,QAAO;AAGT,SAAO,MAAM;AACf;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,YAAY,kCAAY,gCAAU,QAAQ,KAAK,GAAG,CAAA;AACtD,MAAI,CAAC,UACH,OAAM,IAAI,MAAM,kBAAkB,KAAK,GAAG;AAG5C,SAAO,KAAI,GAAA,2CACT,KAAK,OAAO,WACZ,KAAK,OACL,KAAK,GAAG;AAEZ;AAOO,IAAM,4CAAN,eAA+B,GAAA,2CAAgB;EAGpD,cAAc,IAA0B;AACtC,QAAI,OAAO,MAAM,cAAc,EAAA;AAC/B,QAAI,MAAM,+CAAyB,IAAA;AAEnC,WAAO,KAAI,GAAA,2CACT,MACA,gCAAU,GAAA,GACV,KAAK,OAAO,kCAAY,GAAA,GACxB,KAAK,OACL,KAAK,GAAG;EAEZ;EAEA,YAAY,MAA+B;AACzC,WAAO,MAAM,YAAY,kCAAY,IAAA,CAAA;EACvC;EAEA,YAAY,MAAsC;AAChD,QAAI,gBAAgB,kCAAY,IAAA;AAChC,QAAI,MAAM,+CAAyB,aAAA;AAEnC,QAAI,gCAAU,GAAA,MAAS,KAAK,KAAK;AAC/B,WAAK,MAAM,gCAAU,GAAA;AACrB,WAAK,OAAO,cAAc,OAAO,kCAAY,GAAA;IAC/C;AAGA,SAAK,cAAc,IAAA;EACrB;EAEA,cAAc,MAAsC;AAClD,QAAI,MAAM,gCAAU,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,oCAAc,GAAA;AACxB,QAAI,OAAO,MAAM;AACf,UAAI,CAAC,SAAS,UAAU,MAAA,IAAU;AAIlC,UAAI,UAAU,UAAU,kCAAY,GAAA;AACpC,WAAK,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,SAAS,KAAK,IAAI,CAAA;AACnD,UAAI,KAAK,SAAS,SAAS;AACzB,aAAK,QAAQ,KAAK,IAAI,UAAU,KAAK,KAAK;AAE1C,YAAI,KAAK,UAAU,SACjB,MAAK,MAAM,KAAK,IAAI,QAAQ,KAAK,GAAG;MAExC;IACF;AAEA,QAAI,KAAK,SAAS,KAAK,OAAO,GAAG;AAC/B,UAAI,CAAA,EAAG,YAAY,QAAA,IAAY,sCAAgB,GAAA;AAC/C,WAAK,QAAQ,KAAK,IAAI,YAAY,KAAK,KAAK;AAE5C,UAAI,KAAK,UAAU,WACjB,MAAK,MAAM,KAAK,IAAI,UAAU,KAAK,GAAG;IAE1C;EACF;EAEA,UAAoB;AAClB,WAAO;EACT;EAEA,cAAc,MAA+B;AAE3C,QAAI,MAAM,gCAAU,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,sCAAgB,GAAA;AAC1B,QAAI,OAAO,sCAAgB,MAAM,CAAA;AACjC,QAAI,QAAQ;AAEV,aAAO,OAAO,IAAI,CAAA,IAAK;AAGzB,QAAI,QAAQ,KAAK,CAAA,IAAK,IAAI,CAAA;AAE1B,QAAI,KAAK,QAAQ,KAAK,CAAA,KAAO,KAAK,UAAU,KAAK,CAAA,KAAM,KAAK,MAAM,KAAK,CAAA,EACrE;AAGF,WAAO;EACT;EAEA,eAAe,MAA+B;AAC5C,WAAO,MAAM,eAAe,kCAAY,IAAA,CAAA;EAC1C;EAEA,sBAAsB,MAA+B;AACnD,QAAI,QAAQ,kCAAY,IAAA;AACxB,WAAO,QAAQ,MAAM,CAAA,IAAK;EAC5B;EAEA,qBAAqB,MAA+B;AAClD,QAAI,QAAQ,kCAAY,IAAA;AACxB,WAAO,SAAS,KAAK,UAAU,MAAM,CAAA,IAAK,MAAM,CAAA,IAAK;EACvD;;AAjGK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAiGnC;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,MAAM,gCAAU,QAAQ,KAAK,GAAG;AACpC,WAAO,sCAAgB,GAAA;EACzB;AACF;A;;;;;AC7JA,IAAM,sCAAgB;AAGtB,IAAM,oCAAc;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AASK,IAAM,4CAAN,MAAM;EAGX,cAAc,IAA0B;AACtC,QAAI,iBAAiB,KAAK;AAC1B,QAAI,OAAO,IAAI,KAAK,OAAO,KAAK,iBAAiB,KAAK,KAAA;AACtD,QAAI,aAAa,OAAO,OAAO,KAAK,KAAK,OAAO,IAAI,OAAO,MAAM,EAAA;AACjE,QAAI,YAAY,iBAAiB;AACjC,QAAI,QAAQ,YAAY,MACpB,KAAK,MAAM,YAAY,EAAA,IACvB,KAAK,OAAO,YAAY,KAAK,EAAA;AACjC,QAAI,MAAM,YAAY,kCAAY,KAAA,IAAS;AAC3C,WAAO,KAAI,GAAA,2CAAa,MAAM,MAAM,QAAQ,GAAG,GAAA;EACjD;EAEA,YAAY,MAA+B;AACzC,QAAI,KAAK,sCAAgB,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,OAAO,MAAM,EAAA;AACvF,UAAM,kCAAY,KAAK,QAAQ,CAAA;AAC/B,UAAM,KAAK;AACX,WAAO;EACT;EAEA,kBAA0B;AACxB,WAAO;EACT;EAEA,eAAe,MAA+B;AAC5C,QAAI,KAAK,SAAS,EAChB,QAAO;AAGT,QAAI,KAAK,SAAS,GAChB,QAAO;AAGT,QAAI,cAAa,GAAA,2CAAI,KAAK,KAAK,OAAO,IAAI,EAAA,IAAM;AAChD,WAAO,aAAa,KAAK;EAC3B;EAEA,UAAoB;AAClB,WAAO;MAAC;;EACV;EAEA,gBAAwB;AAGtB,WAAO;EACT;;SA9CA,aAAiC;;AA+CnC;A;;;;;ACvEA,IAAM,yCAAmB;AAEzB,SAAS,oCAAc,MAAqB;AAC1C,SAAO,KAAK,QAAQ,WAChB,KAAK,OAAO,yCACZ,IAAI,KAAK,OAAO;AACtB;AAEA,SAAS,wCAAkB,MAAY;AACrC,MAAI,IAAI,OAAO;AACf,MAAI,IAAI,EACN,QAAO;IAAC;IAAU;;MAElB,QAAO;IAAC;IAAiB,IAAI;;AAEjC;AAOO,IAAM,4CAAN,eAA6B,GAAA,2CAAgB;EAGlD,cAAc,IAA0B;AACtC,QAAI,OAAO,MAAM,cAAc,EAAA;AAC/B,QAAI,gBAAe,GAAA,2CAAgB,KAAK,KAAK,KAAK,IAAI;AACtD,QAAI,CAAC,KAAK,IAAA,IAAQ,wCAAkB,YAAA;AACpC,WAAO,KAAI,GAAA,2CAAa,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG;EAC/D;EAEA,YAAY,MAA+B;AACzC,WAAO,MAAM,YAAY,kCAAY,IAAA,CAAA;EACvC;EAEA,UAAoB;AAClB,WAAO;MAAC;MAAiB;;EAC3B;EAEA,YAAY,MAAsC;AAChD,QAAI,CAAC,KAAK,IAAA,IAAQ,wCAAkB,oCAAc,IAAA,CAAA;AAClD,SAAK,MAAM;AACX,SAAK,OAAO;EACd;EAEA,aAAa,MAAgC;AAC3C,WAAO,KAAK,QAAQ;EACtB;EAEA,eAAe,MAA+B;AAC5C,WAAO,MAAM,eAAe,kCAAY,IAAA,CAAA;EAC1C;EAEA,cAAc,MAA+B;AAC3C,WAAO,KAAK,QAAQ,kBAAkB,OAAO,OAAO;EACtD;;AAlCK,UAAA,GAAA,IAAA,GAAA,KACL,aAAiC;;AAkCnC;AAEA,SAAS,kCAAY,MAAqB;AACxC,MAAI,CAAC,KAAK,IAAA,KAAQ,GAAA,2CAAiB,oCAAc,IAAA,CAAA;AACjD,SAAO,KAAI,GAAA,2CACT,KACA,MACA,KAAK,OACL,KAAK,GAAG;AAEZ;;;AC/DO,SAAS,0CAAe,MAAwB;AACrD,UAAQ,MAAA;IACN,KAAK;AACH,aAAO,KAAI,GAAA,2CAAe;IAC5B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAe;IAC5B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAwB;IACrC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAa;IAC1B,KAAK;AACH,aAAO,KAAI,GAAA,0CAAa;IAC1B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAa;IAC1B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAmB;IAChC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAqB;IAClC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAsB;IACnC,KAAK;AACH,aAAO,KAAI,GAAA,2CAAe;IAC5B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAc;IAC3B,KAAK;AACH,aAAO,KAAI,GAAA,2CAAa;IAC1B,KAAK;IACL;AACE,aAAO,KAAI,GAAA,2CAAgB;EAC/B;AACF;;;ACtDA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAYA,IAAI,uCAAiB,oBAAI,IAAA;AAOlB,IAAM,4CAAN,MAAM;;EAWX,OAAO,OAAqB;AAC1B,WAAO,KAAK,UAAU,OAAO,KAAA;EAC/B;;EAGA,cAAc,OAAwC;AACpD,WAAO,KAAK,UAAU,cAAc,KAAA;EACtC;;EAGA,YAAY,OAAa,KAAmB;AAE1C,QAAI,OAAO,KAAK,UAAU,gBAAgB;AAExC,aAAO,KAAK,UAAU,YAAY,OAAO,GAAA;AAG3C,QAAI,MAAM,MACR,OAAM,IAAI,WAAW,gCAAA;AAIvB,WAAO,GAAG,KAAK,UAAU,OAAO,KAAA,CAAA,MAAY,KAAK,UAAU,OAAO,GAAA,CAAA;EACpE;;EAGA,mBAAmB,OAAa,KAAkC;AAEhE,QAAI,OAAO,KAAK,UAAU,uBAAuB;AAE/C,aAAO,KAAK,UAAU,mBAAmB,OAAO,GAAA;AAGlD,QAAI,MAAM,MACR,OAAM,IAAI,WAAW,gCAAA;AAGvB,QAAI,aAAa,KAAK,UAAU,cAAc,KAAA;AAC9C,QAAI,WAAW,KAAK,UAAU,cAAc,GAAA;AAC5C,WAAO;SACF,WAAW,IAAI,CAAA,OAAM;QAAC,GAAG;QAAG,QAAQ;MAAY,EAAA;MACnD;QAAC,MAAM;QAAW,OAAO;QAAO,QAAQ;MAAQ;SAC7C,SAAS,IAAI,CAAA,OAAM;QAAC,GAAG;QAAG,QAAQ;MAAU,EAAA;;EAEnD;;EAGA,kBAAsD;AACpD,QAAI,kBAAkB,KAAK,UAAU,gBAAe;AACpD,QAAI,gDAAA,GAA6B;AAC/B,UAAI,CAAC,KAAK,kBACR,MAAK,oBAAoB,2CAAqB,gBAAgB,QAAQ,KAAK,OAAO;AAEpF,sBAAgB,YAAY,KAAK;AACjC,sBAAgB,SAAS,KAAK,sBAAsB,SAAS,KAAK,sBAAsB;IAC1F;AAIA,QAAI,gBAAgB,aAAa,sBAC/B,iBAAgB,WAAW;AAG7B,WAAO;EACT;EAtEA,YAAY,QAAgB,UAAsC,CAAC,GAAG;AACpE,SAAK,YAAY,6CAAuB,QAAQ,OAAA;AAChD,SAAK,UAAU;EACjB;AAoEF;AAaA,IAAM,0CAAoB;EACxB,MAAM;;IAEJ,IAAI;EACN;EACA,OAAO,CAEP;AACF;AAEA,SAAS,6CAAuB,QAAgB,UAAsC,CAAC,GAAC;AAGtF,MAAI,OAAO,QAAQ,WAAW,aAAa,6CAAA,GAA0B;AACnE,cAAU;MAAC,GAAG;IAAO;AACrB,QAAI,OAAO,wCAAkB,OAAO,QAAQ,MAAM,CAAA,EAAG,OAAO,MAAM,GAAA,EAAK,CAAA,CAAE;AACzE,QAAI,mBAAmB,QAAQ,SAAS,QAAQ;AAChD,YAAQ,YAAY,SAAA,QAAA,SAAA,SAAA,OAAQ;AAC5B,WAAO,QAAQ;EACjB;AAEA,MAAI,WAAW,UAAU,UAAU,OAAO,QAAQ,OAAA,EAAS,KAAK,CAAC,GAAG,MAAM,EAAE,CAAA,IAAK,EAAE,CAAA,IAAK,KAAK,CAAA,EAAG,KAAI,IAAK;AACzG,MAAI,qCAAe,IAAI,QAAA,EACrB,QAAO,qCAAe,IAAI,QAAA;AAG5B,MAAI,kBAAkB,IAAI,KAAK,eAAe,QAAQ,OAAA;AACtD,uCAAe,IAAI,UAAU,eAAA;AAC7B,SAAO;AACT;AAEA,IAAI,gDAA0C;AAC9C,SAAS,+CAAA;AACP,MAAI,iDAA2B,KAC7B,iDAA0B,IAAI,KAAK,eAAe,SAAS;IACzD,MAAM;IACN,QAAQ;EACV,CAAA,EAAG,OAAO,IAAI,KAAK,MAAM,GAAG,GAAG,CAAA,CAAA,MAAQ;AAGzC,SAAO;AACT;AAEA,IAAI,mDAA6C;AACjD,SAAS,kDAAA;AACP,MAAI,oDAA8B,KAChC,oDAA6B,IAAI,KAAK,eAAe,MAAM;IACzD,MAAM;IACN,QAAQ;EACV,CAAA,EAAG,gBAAe,EAAG,cAAc;AAGrC,SAAO;AACT;AAEA,SAAS,2CAAqB,QAAgB,SAAmC;AAC/E,MAAI,CAAC,QAAQ,aAAa,CAAC,QAAQ,KACjC,QAAO;AAKT,WAAS,OAAO,QAAQ,0BAA0B,EAAA;AAClD,aAAW,OAAO,SAAS,KAAA,IAAS,KAAK,QAAQ;AACjD,MAAI,YAAY,6CAAuB,QAAQ;IAC7C,GAAG;IACH,UAAU;;EACZ,CAAA;AAEA,MAAIC,OAAM,SAAS,UAAU,cAAc,IAAI,KAAK,MAAM,GAAG,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,MAAK,EAAE,SAAS,MAAA,EAAS,OAAO,EAAA;AACzG,MAAIC,OAAM,SAAS,UAAU,cAAc,IAAI,KAAK,MAAM,GAAG,GAAG,EAAA,CAAA,EAAK,KAAK,CAAA,MAAK,EAAE,SAAS,MAAA,EAAS,OAAO,EAAA;AAE1G,MAAID,SAAQ,KAAKC,SAAQ,GACvB,QAAO;AAGT,MAAID,SAAQ,MAAMC,SAAQ,GACxB,QAAO;AAGT,MAAID,SAAQ,KAAKC,SAAQ,GACvB,QAAO;AAGT,MAAID,SAAQ,MAAMC,SAAQ,GACxB,QAAO;AAGT,QAAM,IAAI,MAAM,8BAAA;AAClB;A;;;;;;;ACrMA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAKA,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;;;ADpIA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AA+GA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AACjB,gBAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,YAAY,aAAa,IAAI;AAClH,cAAI,CAAC;AAAA;AAAA,UAGL,cAAc,MAAM,OAAK,EAAE,UAAU,CAAC,IAAI,KAAK,YAAY,EAAE,SAAS,MAAM,eAAe,GAAG;AAE5F,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMG,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAID,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,MAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AAMA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,cAAQ,UAAU;AAAA,QAChB,KAAK,mBACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,gBAAgB;AAAA,UAClB,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,wBAAwB;AAAA,cACxB,iBAAiB,sBAAsB,OAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,aAAa;AAAA,UACf,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,gBAAgB;AAAA,cAChB,SAAS,sBAAsB,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,QACF,SACE;AACE,iBAAO,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAwIA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAG,IAAI,WAAW;AAAA,QAClB,GAAG,IAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,UAAU;AAAA,UACR,IAAI,UAAQ;AACV,gBAAI;AAAA,cACF,GAAAC;AAAA,cACA,GAAAC;AAAA,YACF,IAAI;AACJ,mBAAO;AAAA,cACL,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,UAAI,eAAe;AACjB,cAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,cAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,wBAAgB,MAAMD,MAAK,eAAeC,IAAG;AAAA,MAC/C;AACA,UAAI,gBAAgB;AAClB,cAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,cAAMD,OAAM,iBAAiB,SAAS,OAAO;AAC7C,cAAMC,OAAM,iBAAiB,SAAS,OAAO;AAC7C,yBAAiB,MAAMD,MAAK,gBAAgBC,IAAG;AAAA,MACjD;AACA,YAAM,gBAAgB,QAAQ,GAAG;AAAA,QAC/B,GAAG;AAAA,QACH,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,GAAG,cAAc,IAAI;AAAA,UACrB,GAAG,cAAc,IAAI;AAAA,UACrB,SAAS;AAAA,YACP,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,aAAa,SAAU,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAAC,UAAS;AAAA,QACT,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,MAC9B,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,YAAY,SAAS;AACvC,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,YAAM,YAAY,SAASA,SAAQ,KAAK;AACxC,YAAM,iBAAiB,OAAO,cAAc,WAAW;AAAA,QACrD,UAAU;AAAA,QACV,WAAW;AAAA,MACb,IAAI;AAAA,QACF,UAAU;AAAA,QACV,WAAW;AAAA,QACX,GAAG;AAAA,MACL;AACA,UAAI,eAAe;AACjB,cAAM,MAAM,aAAa,MAAM,WAAW;AAC1C,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,eAAe;AAClF,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,eAAe;AACnF,YAAI,gBAAgB,UAAU;AAC5B,0BAAgB;AAAA,QAClB,WAAW,gBAAgB,UAAU;AACnC,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,YAAI,uBAAuB;AAC3B,cAAM,MAAM,aAAa,MAAM,UAAU;AACzC,cAAM,eAAe,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC;AAChE,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,SAAS,GAAG,KAAK,iBAAiB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,SAAS,MAAM,IAAI,MAAM,eAAe,IAAI,eAAe;AACzO,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,UAAU,GAAG,KAAK,eAAe,MAAM,yBAAyB,eAAe,WAAW,OAAO,SAAS,uBAAuB,SAAS,MAAM,MAAM,eAAe,eAAe,YAAY;AACpP,YAAI,iBAAiB,UAAU;AAC7B,2BAAiB;AAAA,QACnB,WAAW,iBAAiB,UAAU;AACpC,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,QACL,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,UAAAL;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAQ,MAAM;AAAA,QAAC;AAAA,QACf,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI,SAAS,SAAS,SAAS,UAAU;AACvC,qBAAa;AACb,oBAAY,eAAgB,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,MACzI,OAAO;AACL,oBAAY;AACZ,qBAAa,cAAc,QAAQ,QAAQ;AAAA,MAC7C;AACA,YAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,YAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,YAAM,0BAA0B,IAAI,SAAS,SAAS,UAAU,GAAG,qBAAqB;AACxF,YAAM,yBAAyB,IAAI,QAAQ,SAAS,SAAS,GAAG,oBAAoB;AACpF,YAAM,UAAU,CAAC,MAAM,eAAe;AACtC,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,WAAK,wBAAwB,MAAM,eAAe,UAAU,QAAQ,sBAAsB,QAAQ,GAAG;AACnG,yBAAiB;AAAA,MACnB;AACA,WAAK,yBAAyB,MAAM,eAAe,UAAU,QAAQ,uBAAuB,QAAQ,GAAG;AACrG,0BAAkB;AAAA,MACpB;AACA,UAAI,WAAW,CAAC,WAAW;AACzB,cAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,cAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,cAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,cAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,YAAI,SAAS;AACX,2BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,QAC1G,OAAO;AACL,4BAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,QAC5G;AAAA,MACF;AACA,YAAM,MAAM;AAAA,QACV,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,MAAMA,UAAS,cAAc,SAAS,QAAQ;AACrE,UAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,eAAO;AAAA,UACL,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;;;AEphCA,IAAAM,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAI,iBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;AHlJA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,iBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAM,iBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAM,IAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAM,IAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAI,iBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiB,iBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgB,iBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAI9B,WAAS,4BAA4B;AACnC,YAAQ,IAAI,oBAAoB,eAAe;AAAA,EACjD;AACA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAC1B,gCAA0B;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,WAAW,CAAC,2BAA2B,iBAAiB;AAC1D,8BAA0B;AAAA,EAC5B;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAM,IAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAM,IAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAO,iBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAK,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAO,iBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,IAAI;AACX,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMC,UAAS;AAef,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAQb,IAAMC,QAAO;AAOb,IAAMC,QAAO;AAOb,IAAMC,SAAQ;AAYd,IAAMC,cAAa;AAMnB,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AIpvBA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ADKA,SAAS,0BAA0B,QAAQ;AACzC,SAAO,UAAU,QAAQ,OAAO,WAAW,YAAY,SAAS;AAClE;AACA,SAASC,eAAc,QAAQ;AAC7B,MAAI,0BAA0B,MAAM,GAAG;AACrC,UAAM,UAAU,OAAO;AACvB,WAAO,OAAO,OAAO,KAAK,YAAY,OAAO,MAAM,aAAa,OAAO;AAAA,EACzE;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ;AACvB,SAAO,OAAO,WAAW,aAAa,OAAO,IAAI,MAAM,MAAM;AAC/D;AAOA,SAASC,OAAM,SAAS;AACtB,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,MAAM;AACP,YAAM,UAAUD,eAAc,QAAQ,QAAQ,OAAO,CAAC;AACtD,UAAI,WAAW,MAAM;AACnB,eAAO,CAAC;AAAA,MACV;AACA,aAAOC,OAAQ;AAAA,QACb;AAAA,QACA,SAAS,QAAQ;AAAA,MACnB,CAAC,EAAE,GAAG,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AASA,SAAS,YAAY,WAAW,UAAU,SAAS;AACjD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM,6BAA6B,QAAQ;AAC3C,QAAM,aAAa,SAAS,MAAM;AAChC,QAAI;AACJ,YAAQ,WAAW,QAAQ,QAAQ,IAAI,MAAM,OAAO,WAAW;AAAA,EACjE,CAAC;AACD,QAAM,mBAAmB,SAAS,MAAM,QAAQ,QAAQ,UAAU,CAAC;AACnE,QAAM,kBAAkB,SAAS,MAAM;AACrC,QAAI;AACJ,YAAQ,YAAY,QAAQ,QAAQ,SAAS,MAAM,OAAO,YAAY;AAAA,EACxE,CAAC;AACD,QAAM,iBAAiB,SAAS,MAAM;AACpC,QAAI;AACJ,YAAQ,YAAY,QAAQ,QAAQ,QAAQ,MAAM,OAAO,YAAY;AAAA,EACvE,CAAC;AACD,QAAM,kBAAkB,SAAS,MAAM;AACrC,QAAI;AACJ,YAAQ,YAAY,QAAQ,QAAQ,SAAS,MAAM,OAAO,YAAY;AAAA,EACxE,CAAC;AACD,QAAM,mBAAmB,SAAS,MAAMD,eAAc,UAAU,KAAK,CAAC;AACtE,QAAM,kBAAkB,SAAS,MAAMA,eAAc,SAAS,KAAK,CAAC;AACpE,QAAM,IAAI,IAAI,CAAC;AACf,QAAM,IAAI,IAAI,CAAC;AACf,QAAM,WAAW,IAAI,eAAe,KAAK;AACzC,QAAM,YAAY,IAAI,gBAAgB,KAAK;AAC3C,QAAM,iBAAiB,WAAW,CAAC,CAAC;AACpC,QAAM,eAAe,IAAI,KAAK;AAC9B,QAAM,iBAAiB,SAAS,MAAM;AACpC,UAAM,gBAAgB;AAAA,MACpB,UAAU,SAAS;AAAA,MACnB,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,gBAAgB,OAAO;AAC1B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,WAAW,gBAAgB,OAAO,EAAE,KAAK;AACtD,UAAM,OAAO,WAAW,gBAAgB,OAAO,EAAE,KAAK;AACtD,QAAI,gBAAgB,OAAO;AACzB,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,OAAO,SAAS,OAAO;AAAA,QACjD,GAAI,OAAO,gBAAgB,KAAK,KAAK,OAAO;AAAA,UAC1C,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU,SAAS;AAAA,MACnB,MAAM,OAAO;AAAA,MACb,KAAK,OAAO;AAAA,IACd;AAAA,EACF,CAAC;AACD,MAAI;AACJ,WAAS,SAAS;AAChB,QAAI,iBAAiB,SAAS,QAAQ,gBAAgB,SAAS,MAAM;AACnE;AAAA,IACF;AACA,UAAM,OAAO,WAAW;AACxB,IAAAE,iBAAgB,iBAAiB,OAAO,gBAAgB,OAAO;AAAA,MAC7D,YAAY,iBAAiB;AAAA,MAC7B,WAAW,gBAAgB;AAAA,MAC3B,UAAU,eAAe;AAAA,IAC3B,CAAC,EAAE,KAAK,cAAY;AAClB,QAAE,QAAQ,SAAS;AACnB,QAAE,QAAQ,SAAS;AACnB,eAAS,QAAQ,SAAS;AAC1B,gBAAU,QAAQ,SAAS;AAC3B,qBAAe,QAAQ,SAAS;AAOhC,mBAAa,QAAQ,SAAS;AAAA,IAChC,CAAC;AAAA,EACH;AACA,WAAS,UAAU;AACjB,QAAI,OAAO,gCAAgC,YAAY;AACrD,kCAA4B;AAC5B,oCAA8B;AAAA,IAChC;AAAA,EACF;AACA,WAAS,SAAS;AAChB,YAAQ;AACR,QAAI,+BAA+B,QAAW;AAC5C,aAAO;AACP;AAAA,IACF;AACA,QAAI,iBAAiB,SAAS,QAAQ,gBAAgB,SAAS,MAAM;AACnE,oCAA8B,2BAA2B,iBAAiB,OAAO,gBAAgB,OAAO,MAAM;AAC9G;AAAA,IACF;AAAA,EACF;AACA,WAAS,QAAQ;AACf,QAAI,CAAC,WAAW,OAAO;AACrB,mBAAa,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,QAAM,CAAC,kBAAkB,iBAAiB,gBAAgB,UAAU,GAAG,QAAQ;AAAA,IAC7E,OAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,kBAAkB,eAAe,GAAG,QAAQ;AAAA,IACjD,OAAO;AAAA,EACT,CAAC;AACD,QAAM,YAAY,OAAO;AAAA,IACvB,OAAO;AAAA,EACT,CAAC;AACD,MAAI,gBAAgB,GAAG;AACrB,mBAAe,OAAO;AAAA,EACxB;AACA,SAAO;AAAA,IACL,GAAG,gBAAgB,CAAC;AAAA,IACpB,GAAG,gBAAgB,CAAC;AAAA,IACpB,UAAU,gBAAgB,QAAQ;AAAA,IAClC,WAAW,gBAAgB,SAAS;AAAA,IACpC,gBAAgB,gBAAgB,cAAc;AAAA,IAC9C,cAAc,gBAAgB,YAAY;AAAA,IAC1C;AAAA,IACA;AAAA,EACF;AACF;;;AE3LA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAYA,IAAI,uCAAiB,oBAAI,IAAA;AAEzB,IAAI,4CAAsB;AAC1B,IAAI;AACF,8CAAuB,IAAI,KAAK,aAAa,SAAS;IAAC,aAAa;EAAY,CAAA,EAAI,gBAAe,EAAG,gBAAgB;AAExH,QAAQ;AAAC;AAET,IAAI,qCAAe;AACnB,IAAI;AACF,uCAAgB,IAAI,KAAK,aAAa,SAAS;IAAC,OAAO;IAAQ,MAAM;EAAQ,CAAA,EAAI,gBAAe,EAAG,UAAU;AAE/G,QAAQ;AAAC;AAKT,IAAM,8BAAQ;EACZ,QAAQ;IACN,QAAQ;MACN,SAAS;MACT,SAAS;MACT,SAAS;MACT,SAAS;IAGX;EACF;AACF;AAcO,IAAM,4CAAN,MAAM;;EAUX,OAAO,OAAuB;AAC5B,QAAI,MAAM;AACV,QAAI,CAAC,6CAAuB,KAAK,QAAQ,eAAe,KACtD,OAAM,0CAAgC,KAAK,iBAAiB,KAAK,QAAQ,aAAa,KAAA;QAEtF,OAAM,KAAK,gBAAgB,OAAO,KAAA;AAGpC,QAAI,KAAK,QAAQ,UAAU,UAAU,CAAC,oCAAc;UAKrC;AAJb,UAAI,EAAA,MAAK,cAAgB,SAAA,OAAe,IAAI,KAAK,gBAAe;AAChE,UAAI,CAAC,KACH,QAAO;AAET,UAAI,UAAS,cAAA,4BAAM,IAAA,OAAK,QAAX,gBAAA,SAAA,SAAA,YAAc,WAAA;AAC3B,aAAO,OAAO,MAAA,KAAW,OAAO;IAClC;AAEA,WAAO;EACT;;EAGA,cAAc,OAAwC;AAEpD,WAAO,KAAK,gBAAgB,cAAc,KAAA;EAC5C;;EAGA,YAAY,OAAe,KAAqB;AAC9C,QAAI,OAAO,KAAK,gBAAgB,gBAAgB,WAC9C,QAAO,KAAK,gBAAgB,YAAY,OAAO,GAAA;AAGjD,QAAI,MAAM,MACR,OAAM,IAAI,WAAW,gCAAA;AAIvB,WAAO,GAAG,KAAK,OAAO,KAAA,CAAA,MAAY,KAAK,OAAO,GAAA,CAAA;EAChD;;EAGA,mBAAmB,OAAe,KAAsC;AACtE,QAAI,OAAO,KAAK,gBAAgB,uBAAuB,WACrD,QAAO,KAAK,gBAAgB,mBAAmB,OAAO,GAAA;AAGxD,QAAI,MAAM,MACR,OAAM,IAAI,WAAW,gCAAA;AAGvB,QAAI,aAAa,KAAK,gBAAgB,cAAc,KAAA;AACpD,QAAI,WAAW,KAAK,gBAAgB,cAAc,GAAA;AAClD,WAAO;SACF,WAAW,IAAI,CAAA,OAAM;QAAC,GAAG;QAAG,QAAQ;MAAY,EAAA;MACnD;QAAC,MAAM;QAAW,OAAO;QAAO,QAAQ;MAAQ;SAC7C,SAAS,IAAI,CAAA,OAAM;QAAC,GAAG;QAAG,QAAQ;MAAU,EAAA;;EAEnD;;EAGA,kBAAoD;AAClD,QAAI,UAAU,KAAK,gBAAgB,gBAAe;AAClD,QAAI,CAAC,6CAAuB,KAAK,QAAQ,eAAe,KACtD,WAAU;MAAC,GAAG;MAAS,aAAa,KAAK,QAAQ;IAAW;AAG9D,QAAI,CAAC,sCAAgB,KAAK,QAAQ,UAAU,OAC1C,WAAU;MAAC,GAAG;MAAS,OAAO;MAAQ,MAAM,KAAK,QAAQ;MAAM,aAAa,KAAK,QAAQ;IAAW;AAGtG,WAAO;EACT;EA7EA,YAAY,QAAgB,UAA+B,CAAC,GAAG;AAC7D,SAAK,kBAAkB,+CAAyB,QAAQ,OAAA;AACxD,SAAK,UAAU;EACjB;AA2EF;AAEA,SAAS,+CAAyB,QAAgB,UAA+B,CAAC,GAAC;AACjF,MAAI,EAAA,gBAAgB,IAAI;AACxB,MAAI,mBAAmB,OAAO,SAAS,MAAA,GAAS;AAC9C,QAAI,CAAC,OAAO,SAAS,KAAA,EACnB,WAAU;AAEZ,cAAU,OAAO,eAAA;EACnB;AAEA,MAAI,QAAQ,UAAU,UAAU,CAAC,oCAAc;QAKxC;AAJL,QAAI,EAAA,MAAK,cAAgB,QAAA,IAAW;AACpC,QAAI,CAAC,KACH,OAAM,IAAI,MAAM,iDAAA;AAElB,QAAI,GAAC,cAAA,4BAAM,IAAA,OAAK,QAAX,gBAAA,SAAA,SAAA,YAAc,WAAA,GACjB,OAAM,IAAI,MAAM,oBAAoB,IAAA,uBAA2B,WAAA,EAAa;AAE9E,cAAU;MAAC,GAAG;MAAS,OAAO;IAAS;EACzC;AAEA,MAAI,WAAW,UAAU,UAAU,OAAO,QAAQ,OAAA,EAAS,KAAK,CAAC,GAAG,MAAM,EAAE,CAAA,IAAK,EAAE,CAAA,IAAK,KAAK,CAAA,EAAG,KAAI,IAAK;AACzG,MAAI,qCAAe,IAAI,QAAA,EACrB,QAAO,qCAAe,IAAI,QAAA;AAG5B,MAAI,kBAAkB,IAAI,KAAK,aAAa,QAAQ,OAAA;AACpD,uCAAe,IAAI,UAAU,eAAA;AAC7B,SAAO;AACT;AAGO,SAAS,0CAAgC,cAAiC,aAAqB,KAAW;AAC/G,MAAI,gBAAgB,OAClB,QAAO,aAAa,OAAO,GAAA;WAClB,gBAAgB,QACzB,QAAO,aAAa,OAAO,KAAK,IAAI,GAAA,CAAA;OAC/B;AACL,QAAI,oBAAoB;AACxB,QAAI,gBAAgB,SAClB,qBAAoB,MAAM,KAAK,OAAO,GAAG,KAAK,CAAA;aACrC,gBAAgB,cAAA;AACzB,UAAI,OAAO,GAAG,KAAK,EAAA,KAAO,OAAO,GAAG,KAAK,CAAA,EACvC,OAAM,KAAK,IAAI,GAAA;UAEf,qBAAoB,MAAM;;AAI9B,QAAI,mBAAmB;AACrB,UAAI,WAAW,aAAa,OAAO,CAAC,GAAA;AACpC,UAAI,SAAS,aAAa,OAAO,GAAA;AAEjC,UAAI,QAAQ,SAAS,QAAQ,QAAQ,EAAA,EAAI,QAAQ,iBAAiB,EAAA;AAClE,UAAI;WAAI;QAAO,WAAW,EACxB,SAAQ,KAAK,0EAAA;AAEf,UAAI,WAAW,SAAS,QAAQ,QAAQ,KAAA,EAAO,QAAQ,OAAO,GAAA,EAAK,QAAQ,OAAO,MAAA;AAClF,aAAO;IACT,MACE,QAAO,aAAa,OAAO,GAAA;EAE/B;AACF;A;;;;;AChLA,IAAM,4CAAsB,IAAI,OAAO,gBAAA;AACvC,IAAM,0CAAoB;EAAC;EAAQ;EAAQ;EAAW;EAAQ;;AAQvD,IAAM,4CAAN,MAAM;;;;EAYX,MAAM,OAAuB;AAC3B,WAAO,0CAAoB,KAAK,QAAQ,KAAK,SAAS,KAAA,EAAO,MAAM,KAAA;EACrE;;;;;;EAOA,qBAAqB,OAAe,UAAmB,UAA4B;AACjF,WAAO,0CAAoB,KAAK,QAAQ,KAAK,SAAS,KAAA,EAAO,qBAAqB,OAAO,UAAU,QAAA;EACrG;;;;;;EAOA,mBAAmB,OAAuB;AACxC,WAAO,0CAAoB,KAAK,QAAQ,KAAK,SAAS,KAAA,EAAO,QAAQ;EACvE;EA5BA,YAAY,QAAgB,UAAoC,CAAC,GAAG;AAClE,SAAK,SAAS;AACd,SAAK,UAAU;EACjB;AA0BF;AAEA,IAAM,0CAAoB,oBAAI,IAAA;AAC9B,SAAS,0CAAoB,QAAgB,SAAmC,OAAa;AAE3F,MAAI,gBAAgB,4CAAsB,QAAQ,OAAA;AAIlD,MAAI,CAAC,OAAO,SAAS,MAAA,KAAW,CAAC,cAAc,qBAAqB,KAAA,GAAQ;AAC1E,aAAS,mBAAmB,wCAC1B,KAAI,oBAAoB,cAAc,QAAQ,iBAAiB;AAC7D,UAAI,SAAS,4CAAsB,UAAU,OAAO,SAAS,KAAA,IAAS,SAAS,YAAY,iBAAiB,OAAA;AAC5G,UAAI,OAAO,qBAAqB,KAAA,EAC9B,QAAO;IAEX;EAEJ;AAEA,SAAO;AACT;AAEA,SAAS,4CAAsB,QAAgB,SAAiC;AAC9E,MAAI,WAAW,UAAU,UAAU,OAAO,QAAQ,OAAA,EAAS,KAAK,CAAC,GAAG,MAAM,EAAE,CAAA,IAAK,EAAE,CAAA,IAAK,KAAK,CAAA,EAAG,KAAI,IAAK;AACzG,MAAI,SAAS,wCAAkB,IAAI,QAAA;AACnC,MAAI,CAAC,QAAQ;AACX,aAAS,IAAI,uCAAiB,QAAQ,OAAA;AACtC,4CAAkB,IAAI,UAAU,MAAA;EAClC;AAEA,SAAO;AACT;AAIA,IAAM,yCAAN,MAAM;EA6BJ,MAAM,OAAe;AAEnB,QAAI,sBAAsB,KAAK,SAAS,KAAA;AAExC,QAAI,KAAK,QAAQ;AAEf,4BAAsB,iCAAW,qBAAqB,KAAK,QAAQ,OAAO,EAAA;AAE5E,QAAI,KAAK,QAAQ,QACf,uBAAsB,oBAAoB,QAAQ,KAAK,QAAQ,SAAU,GAAA;AAE3E,QAAI,KAAK,QAAQ,UACf,uBAAsB,oBAAoB,QAAQ,KAAK,QAAQ,WAAY,GAAA;AAE7E,0BAAsB,oBAAoB,QAAQ,KAAK,QAAQ,SAAS,KAAK,QAAQ,KAAK;AAE1F,QAAI,KAAK,QAAQ,UAAU,WAAW;AAEpC,UAAI,aAAa,oBAAoB,QAAQ,GAAA;AAC7C,4BAAsB,oBAAoB,QAAQ,KAAK,EAAA;AACvD,4BAAsB,oBAAoB,QAAQ,KAAK,EAAA;AACvD,UAAI,QAAQ,oBAAoB,QAAQ,GAAA;AACxC,UAAI,UAAU,GACZ,SAAQ,oBAAoB;AAE9B,4BAAsB,oBAAoB,QAAQ,KAAK,EAAA;AACvD,UAAI,QAAQ,MAAM,EAChB,uBAAsB,KAAK,mBAAA;eAClB,QAAQ,MAAM,GACvB,uBAAsB,MAAM,mBAAA;eACnB,QAAQ,MAAM,GACvB,uBAAsB;UAEtB,uBAAsB,GAAG,oBAAoB,MAAM,GAAG,QAAQ,CAAA,CAAA,IAAM,oBAAoB,MAAM,QAAQ,CAAA,CAAA;AAExG,UAAI,aAAa,GACf,uBAAsB,IAAI,mBAAA;IAE9B;AAEA,QAAI,WAAW,sBAAsB,CAAC,sBAAsB;AAC5D,QAAI,MAAM,QAAA,EACR,QAAO;AAGT,QAAI,KAAK,QAAQ,UAAU,WAAW;UAKD,qCACA;AAJnC,UAAI,UAAU;QACZ,GAAG,KAAK;QACR,OAAO;QACP,uBAAuB,KAAK,MAAK,sCAAA,KAAK,QAAQ,2BAAqB,QAAlC,wCAAA,SAAA,sCAAsC,KAAK,GAAG,EAAA;QAC/E,uBAAuB,KAAK,MAAK,sCAAA,KAAK,QAAQ,2BAAqB,QAAlC,wCAAA,SAAA,sCAAsC,KAAK,GAAG,EAAA;MACjF;AACA,aAAQ,IAAI,0CAAa,KAAK,QAAQ,OAAA,EAAU,MAAM,KAAI,GAAA,2CAAgB,KAAK,QAAQ,OAAA,EAAS,OAAO,QAAA,CAAA;IACzG;AAGA,QAAI,KAAK,QAAQ,iBAAiB,gBAAgB,0CAAoB,KAAK,KAAA,EACzE,YAAW,KAAK;AAGlB,WAAO;EACT;EAEA,SAAS,OAAe;AAEtB,YAAQ,MAAM,QAAQ,KAAK,QAAQ,UAAU,EAAA;AAI7C,QAAI,KAAK,QAAQ,UACf,SAAQ,MAAM,QAAQ,KAAK,KAAK,QAAQ,SAAS;AAKnD,QAAI,KAAK,QAAQ,oBAAoB,QAAQ;AAC3C,UAAI,KAAK,QAAQ,SAAS;AACxB,gBAAQ,MAAM,QAAQ,KAAK,KAAK,QAAQ,OAAO;AAC/C,gBAAQ,MAAM,QAAQ,OAAO,aAAa,IAAA,GAAO,KAAK,QAAQ,OAAO;MACvE;AACA,UAAI,KAAK,QAAQ,MACf,SAAQ,iCAAW,OAAO,KAAK,KAAK,QAAQ,KAAK;IAErD;AAIA,QAAI,KAAK,QAAQ,WAAW,WAAW,KAAK,QAAQ,OAAO;AACzD,cAAQ,iCAAW,OAAO,KAAK,KAAK,QAAQ,KAAK;AACjD,cAAQ,iCAAW,OAAO,WAAW,KAAK,QAAQ,KAAK;IACzD;AAEA,WAAO;EACT;EAEA,qBAAqB,OAAe,WAAmB,WAAW,WAAmB,UAAmB;AACtG,YAAQ,KAAK,SAAS,KAAA;AAGtB,QAAI,KAAK,QAAQ,aAAa,MAAM,WAAW,KAAK,QAAQ,SAAS,KAAK,WAAW,EACnF,SAAQ,MAAM,MAAM,KAAK,QAAQ,UAAU,MAAM;aACxC,KAAK,QAAQ,YAAY,MAAM,WAAW,KAAK,QAAQ,QAAQ,KAAK,WAAW,EACxF,SAAQ,MAAM,MAAM,KAAK,QAAQ,SAAS,MAAM;AAIlD,QAAI,KAAK,QAAQ,SAAS,MAAM,WAAW,KAAK,QAAQ,KAAK,EAC3D,QAAO;AAIT,QAAI,KAAK,QAAQ,WAAW,MAAM,QAAQ,KAAK,QAAQ,OAAO,IAAI,MAAM,KAAK,QAAQ,0BAA0B,EAC7G,QAAO;AAIT,QAAI,KAAK,QAAQ,MACf,SAAQ,iCAAW,OAAO,KAAK,QAAQ,OAAO,EAAA;AAEhD,YAAQ,MAAM,QAAQ,KAAK,QAAQ,SAAS,EAAA;AAC5C,QAAI,KAAK,QAAQ,QACf,SAAQ,MAAM,QAAQ,KAAK,QAAQ,SAAS,EAAA;AAI9C,WAAO,MAAM,WAAW;EAC1B;EAtJA,YAAY,QAAgB,UAAoC,CAAC,GAAG;AAClE,SAAK,SAAS;AAGd,QAAI,QAAQ,sBAAsB,KAAK,QAAQ,qBAAqB,MAAM;AACxE,UAAI,QAAQ,yBAAyB,QAAQ,QAAQ,yBAAyB,MAAM;AAClF,gBAAQ,wBAAwB;AAChC,gBAAQ,wBAAwB;MAClC,WAAW,QAAQ,yBAAyB,KAC1C,SAAQ,wBAAwB,QAAQ;eAC/B,QAAQ,yBAAyB,KAC1C,SAAQ,wBAAwB,QAAQ;IAG5C;AACA,SAAK,YAAY,IAAI,KAAK,aAAa,QAAQ,OAAA;AAC/C,SAAK,UAAU,KAAK,UAAU,gBAAe;AAC7C,SAAK,UAAU,iCAAW,QAAQ,KAAK,WAAW,KAAK,SAAS,OAAA;QACtB,qCAAkD;AAA5F,QAAI,KAAK,QAAQ,UAAU,gBAAe,sCAAA,KAAK,QAAQ,2BAAqB,QAAlC,wCAAA,SAAA,sCAAsC,KAAK,QAAO,sCAAA,KAAK,QAAQ,2BAAqB,QAAlC,wCAAA,SAAA,sCAAsC,KAAK,IACrI,SAAQ,KAAK,uHAAA;EAEjB;AAkIF;AAEA,IAAM,wCAAkB,oBAAI,IAAI;EAAC;EAAW;EAAY;EAAW;EAAa;EAAY;CAAQ;AAKpG,IAAM,sCAAgB;EACpB;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAG;EAAG;EAAK;EAAI;EAAK;;AAG1C,SAAS,iCAAW,QAAgB,WAA8B,aAA+C,iBAAyC;MAexI,gBACD,mBAYD,oBACF;AA3BZ,MAAI,kBAAkB,IAAI,KAAK,aAAa,QAAQ;IAAC,GAAG;;IAEtD,0BAA0B;IAC1B,0BAA0B;IAC1B,mBAAmB;IACnB,kBAAkB;IAClB,cAAc;EAChB,CAAA;AAEA,MAAI,WAAW,gBAAgB,cAAc,UAAA;AAC7C,MAAI,cAAc,gBAAgB,cAAc,SAAA;AAChD,MAAI,cAAc,oCAAc,IAAI,CAAA,MAAK,gBAAgB,cAAc,CAAA,CAAA;MAEvD;AAAhB,MAAI,aAAY,wBAAA,iBAAA,SAAS,KAAK,CAAA,MAAK,EAAE,SAAS,WAAA,OAAA,QAA9B,mBAAA,SAAA,SAAA,eAA4C,WAAK,QAAjD,yBAAA,SAAA,uBAAqD;AACrE,MAAI,YAAW,oBAAA,YAAY,KAAK,CAAA,MAAK,EAAE,SAAS,UAAA,OAAA,QAAjC,sBAAA,SAAA,SAAA,kBAA8C;AAI7D,MAAI,CAAC,cAAa,oBAAA,QAAA,oBAAA,SAAA,SAAA,gBAAiB,iBAAgB,iBAAgB,oBAAA,QAAA,oBAAA,SAAA,SAAA,gBAAiB,iBAAgB,UAClG,YAAW;AAKb,MAAI,eAAe,IAAI,KAAK,aAAa,QAAQ;IAAC,GAAG;IAAa,uBAAuB;IAAG,uBAAuB;EAAC,CAAA,EAAG,cAAc,IAAA;AAErI,MAAI,WAAU,qBAAA,aAAa,KAAK,CAAA,MAAK,EAAE,SAAS,SAAA,OAAA,QAAlC,uBAAA,SAAA,SAAA,mBAA8C;AAC5D,MAAI,SAAQ,kBAAA,SAAS,KAAK,CAAA,MAAK,EAAE,SAAS,OAAA,OAAA,QAA9B,oBAAA,SAAA,SAAA,gBAAwC;AAIpD,MAAI,mBAAmB,SAAS,OAAO,CAAA,MAAK,CAAC,sCAAgB,IAAI,EAAE,IAAI,CAAA,EAAG,IAAI,CAAA,MAAK,kCAAY,EAAE,KAAK,CAAA;AACtG,MAAI,sBAAsB,YAAY,QAAQ,CAAA,MAAK,EAAE,OAAO,CAAAC,OAAK,CAAC,sCAAgB,IAAIA,GAAE,IAAI,CAAA,EAAG,IAAI,CAAAA,OAAK,kCAAYA,GAAE,KAAK,CAAA,CAAA;AAC3H,MAAI,iBAAiB;OAAI,oBAAI,IAAI;SAAI;SAAqB;KAAoB;IAAG,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AAEnH,MAAI,WAAW,eAAe,WAAW,IACrC,IAAI,OAAO,sBAAsB,IAAA,IACjC,IAAI,OAAO,GAAG,eAAe,KAAK,GAAA,CAAA,uBAA2B,IAAA;AAGjE,MAAI,WAAW;OAAI,IAAI,KAAK,aAAa,YAAY,QAAQ;MAAC,aAAa;IAAK,CAAA,EAAG,OAAO,UAAA;IAAa,QAAO;AAC9G,MAAI,UAAU,IAAI,IAAI,SAAS,IAAI,CAAC,GAAG,MAAM;IAAC;IAAG;GAAE,CAAA;AACnD,MAAI,UAAU,IAAI,OAAO,IAAI,SAAS,KAAK,EAAA,CAAA,KAAQ,GAAA;AACnD,MAAI,QAAQ,CAAA,MAAK,OAAO,QAAQ,IAAI,CAAA,CAAA;AAEpC,SAAO;;;;;;;;EAA8D;AACvE;AAEA,SAAS,iCAAW,KAAa,MAAuB,SAAe;AACrE,MAAI,IAAI,WACN,QAAO,IAAI,WAAW,MAAM,OAAA;AAG9B,SAAO,IAAI,MAAM,IAAA,EAAM,KAAK,OAAA;AAC9B;AAEA,SAAS,kCAAY,QAAc;AACjC,SAAO,OAAO,QAAQ,uBAAuB,MAAA;AAC/C;A", "names": ["import_dist", "min", "max", "round", "res", "ms", "offset", "import_dist", "import_dist", "offset", "date", "offset", "import_dist", "import_dist", "min", "max", "import_dist", "import_dist", "import_dist", "platform", "max", "offset", "platform", "placements", "sides", "side", "placement", "overflow", "platform", "x", "y", "min", "max", "offset", "import_dist", "offset", "shift", "flip", "size", "hide", "arrow", "limitShift", "computePosition", "import_dist", "import_dist", "unwrapElement", "arrow", "computePosition", "import_dist", "p"]}