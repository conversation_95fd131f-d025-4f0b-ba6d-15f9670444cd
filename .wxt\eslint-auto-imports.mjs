const globals = {
  "AutoMount": true,
  "AutoMountOptions": true,
  "Browser": true,
  "ContentScriptAnchoredOptions": true,
  "ContentScriptAppendMode": true,
  "ContentScriptContext": true,
  "ContentScriptInlinePositioningOptions": true,
  "ContentScriptModalPositioningOptions": true,
  "ContentScriptOverlayAlignment": true,
  "ContentScriptOverlayPositioningOptions": true,
  "ContentScriptPositioningOptions": true,
  "ContentScriptUi": true,
  "ContentScriptUiOptions": true,
  "IframeContentScriptUi": true,
  "IframeContentScriptUiOptions": true,
  "InjectScriptOptions": true,
  "IntegratedContentScriptUi": true,
  "IntegratedContentScriptUiOptions": true,
  "InvalidMatchPattern": true,
  "MDAlert": true,
  "MDFootnotes": true,
  "MDKatex": true,
  "MDSlider": true,
  "MarkedKatexOptions": true,
  "MatchPattern": true,
  "MigrationError": true,
  "ScriptPublicPath": true,
  "ShadowRootContentScriptUi": true,
  "ShadowRootContentScriptUiOptions": true,
  "StopAutoMount": true,
  "StorageArea": true,
  "StorageAreaChanges": true,
  "StorageItemKey": true,
  "WxtAppConfig": true,
  "WxtStorage": true,
  "WxtStorageItem": true,
  "WxtWindowEventMap": true,
  "addPrefix": true,
  "addSpacingToMarkdown": true,
  "base64decode": true,
  "base64encode": true,
  "browser": true,
  "checkImage": true,
  "copyHtml": true,
  "copyPlain": true,
  "createEmptyNode": true,
  "createIframeUi": true,
  "createIntegratedUi": true,
  "createShadowRootUi": true,
  "createSyntaxPattern": true,
  "createTable": true,
  "css2json": true,
  "customCssWithTemplate": true,
  "customizeTheme": true,
  "defineAppConfig": true,
  "defineBackground": true,
  "defineContentScript": true,
  "defineUnlistedScript": true,
  "defineWxtPlugin": true,
  "downloadMD": true,
  "exportHTML": true,
  "fakeBrowser": true,
  "fetch": true,
  "file": true,
  "formatDoc": true,
  "getStyleString": true,
  "initRenderer": true,
  "injectScript": true,
  "mergeCss": true,
  "modifyHtmlContent": true,
  "modifyHtmlStructure": true,
  "postProcessHtml": true,
  "processClipboardContent": true,
  "removeLeft": true,
  "renderMarkdown": true,
  "resolveVariants": true,
  "safe64": true,
  "sanitizeTitle": true,
  "setupComponents": true,
  "solveWeChatImage": true,
  "storage": true,
  "toBase64": true,
  "toggleFormat": true,
  "ucfirst": true,
  "useAppConfig": true,
  "utf16to8": true,
  "utf8to16": true
}

export default {
  name: "wxt/auto-imports",
  languageOptions: {
    globals,
    /** @type {import('eslint').Linter.SourceType} */
    sourceType: "module",
  },
};
