// Generated by wxt
export {}
declare global {
  const ContentScriptContext: typeof import('wxt/utils/content-script-context')['ContentScriptContext']
  const InvalidMatchPattern: typeof import('wxt/utils/match-patterns')['InvalidMatchPattern']
  const MDAlert: typeof import('D:/projects/markdown编辑器/src/utils/MDAlert')['default']
  const MDFootnotes: typeof import('D:/projects/markdown编辑器/src/utils/MDFootnotes')['default']
  const MDKatex: typeof import('D:/projects/markdown编辑器/src/utils/MDKatex')['MDKatex']
  const MDSlider: typeof import('D:/projects/markdown编辑器/src/utils/MDSlider')['default']
  const MatchPattern: typeof import('wxt/utils/match-patterns')['MatchPattern']
  const addPrefix: typeof import('D:/projects/markdown编辑器/src/utils/index')['addPrefix']
  const addSpacingToMarkdown: typeof import('D:/projects/markdown编辑器/src/utils/autoSpace')['addSpacingToMarkdown']
  const base64decode: typeof import('D:/projects/markdown编辑器/src/utils/tokenTools')['base64decode']
  const base64encode: typeof import('D:/projects/markdown编辑器/src/utils/tokenTools')['base64encode']
  const browser: typeof import('wxt/browser')['browser']
  const checkImage: typeof import('D:/projects/markdown编辑器/src/utils/index')['checkImage']
  const copyHtml: typeof import('D:/projects/markdown编辑器/src/utils/clipboard')['copyHtml']
  const copyPlain: typeof import('D:/projects/markdown编辑器/src/utils/clipboard')['copyPlain']
  const createEmptyNode: typeof import('D:/projects/markdown编辑器/src/utils/index')['createEmptyNode']
  const createIframeUi: typeof import('wxt/utils/content-script-ui/iframe')['createIframeUi']
  const createIntegratedUi: typeof import('wxt/utils/content-script-ui/integrated')['createIntegratedUi']
  const createShadowRootUi: typeof import('wxt/utils/content-script-ui/shadow-root')['createShadowRootUi']
  const createSyntaxPattern: typeof import('D:/projects/markdown编辑器/src/utils/MDAlert')['createSyntaxPattern']
  const createTable: typeof import('D:/projects/markdown编辑器/src/utils/index')['createTable']
  const css2json: typeof import('D:/projects/markdown编辑器/src/utils/index')['css2json']
  const customCssWithTemplate: typeof import('D:/projects/markdown编辑器/src/utils/index')['customCssWithTemplate']
  const customizeTheme: typeof import('D:/projects/markdown编辑器/src/utils/index')['customizeTheme']
  const defineAppConfig: typeof import('wxt/utils/define-app-config')['defineAppConfig']
  const defineBackground: typeof import('wxt/utils/define-background')['defineBackground']
  const defineContentScript: typeof import('wxt/utils/define-content-script')['defineContentScript']
  const defineUnlistedScript: typeof import('wxt/utils/define-unlisted-script')['defineUnlistedScript']
  const defineWxtPlugin: typeof import('wxt/utils/define-wxt-plugin')['defineWxtPlugin']
  const downloadMD: typeof import('D:/projects/markdown编辑器/src/utils/index')['downloadMD']
  const exportHTML: typeof import('D:/projects/markdown编辑器/src/utils/index')['exportHTML']
  const fakeBrowser: typeof import('wxt/testing')['fakeBrowser']
  const fetch: typeof import('D:/projects/markdown编辑器/src/utils/fetch')['default']
  const file: typeof import('D:/projects/markdown编辑器/src/utils/file')['default']
  const formatDoc: typeof import('D:/projects/markdown编辑器/src/utils/index')['formatDoc']
  const getStyleString: typeof import('D:/projects/markdown编辑器/src/utils/index')['getStyleString']
  const initRenderer: typeof import('D:/projects/markdown编辑器/src/utils/renderer')['initRenderer']
  const injectScript: typeof import('wxt/utils/inject-script')['injectScript']
  const mergeCss: typeof import('D:/projects/markdown编辑器/src/utils/index')['mergeCss']
  const modifyHtmlContent: typeof import('D:/projects/markdown编辑器/src/utils/index')['modifyHtmlContent']
  const modifyHtmlStructure: typeof import('D:/projects/markdown编辑器/src/utils/index')['modifyHtmlStructure']
  const postProcessHtml: typeof import('D:/projects/markdown编辑器/src/utils/index')['postProcessHtml']
  const processClipboardContent: typeof import('D:/projects/markdown编辑器/src/utils/index')['processClipboardContent']
  const removeLeft: typeof import('D:/projects/markdown编辑器/src/utils/index')['removeLeft']
  const renderMarkdown: typeof import('D:/projects/markdown编辑器/src/utils/index')['renderMarkdown']
  const resolveVariants: typeof import('D:/projects/markdown编辑器/src/utils/MDAlert')['resolveVariants']
  const safe64: typeof import('D:/projects/markdown编辑器/src/utils/tokenTools')['safe64']
  const sanitizeTitle: typeof import('D:/projects/markdown编辑器/src/utils/index')['sanitizeTitle']
  const setupComponents: typeof import('D:/projects/markdown编辑器/src/utils/setup-components')['setupComponents']
  const solveWeChatImage: typeof import('D:/projects/markdown编辑器/src/utils/index')['solveWeChatImage']
  const storage: typeof import('wxt/utils/storage')['storage']
  const toBase64: typeof import('D:/projects/markdown编辑器/src/utils/index')['toBase64']
  const toggleFormat: typeof import('D:/projects/markdown编辑器/src/utils/editor')['toggleFormat']
  const ucfirst: typeof import('D:/projects/markdown编辑器/src/utils/MDAlert')['ucfirst']
  const useAppConfig: typeof import('wxt/utils/app-config')['useAppConfig']
  const utf16to8: typeof import('D:/projects/markdown编辑器/src/utils/tokenTools')['utf16to8']
  const utf8to16: typeof import('D:/projects/markdown编辑器/src/utils/tokenTools')['utf8to16']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Browser } from 'wxt/browser'
  import('wxt/browser')
  // @ts-ignore
  export type { StorageArea, WxtStorage, WxtStorageItem, StorageItemKey, StorageAreaChanges, MigrationError } from 'wxt/utils/storage'
  import('wxt/utils/storage')
  // @ts-ignore
  export type { WxtWindowEventMap } from 'wxt/utils/content-script-context'
  import('wxt/utils/content-script-context')
  // @ts-ignore
  export type { IframeContentScriptUi, IframeContentScriptUiOptions } from 'wxt/utils/content-script-ui/iframe'
  import('wxt/utils/content-script-ui/iframe')
  // @ts-ignore
  export type { IntegratedContentScriptUi, IntegratedContentScriptUiOptions } from 'wxt/utils/content-script-ui/integrated'
  import('wxt/utils/content-script-ui/integrated')
  // @ts-ignore
  export type { ShadowRootContentScriptUi, ShadowRootContentScriptUiOptions } from 'wxt/utils/content-script-ui/shadow-root'
  import('wxt/utils/content-script-ui/shadow-root')
  // @ts-ignore
  export type { ContentScriptUi, ContentScriptUiOptions, ContentScriptOverlayAlignment, ContentScriptAppendMode, ContentScriptInlinePositioningOptions, ContentScriptOverlayPositioningOptions, ContentScriptModalPositioningOptions, ContentScriptPositioningOptions, ContentScriptAnchoredOptions, AutoMountOptions, StopAutoMount, AutoMount } from 'wxt/utils/content-script-ui/types'
  import('wxt/utils/content-script-ui/types')
  // @ts-ignore
  export type { WxtAppConfig } from 'wxt/utils/define-app-config'
  import('wxt/utils/define-app-config')
  // @ts-ignore
  export type { ScriptPublicPath, InjectScriptOptions } from 'wxt/utils/inject-script'
  import('wxt/utils/inject-script')
  // @ts-ignore
  export type { MarkedKatexOptions } from 'D:/projects/markdown编辑器/src/utils/MDKatex.d'
  import('D:/projects/markdown编辑器/src/utils/MDKatex.d')
}
