{"version": 3, "sources": ["../../buffer-from/index.js"], "sourcesContent": ["/* eslint-disable node/no-deprecated-api */\n\nvar toString = Object.prototype.toString\n\nvar isModern = (\n  typeof Buffer !== 'undefined' &&\n  typeof Buffer.alloc === 'function' &&\n  typeof Buffer.allocUnsafe === 'function' &&\n  typeof Buffer.from === 'function'\n)\n\nfunction isArrayBuffer (input) {\n  return toString.call(input).slice(8, -1) === 'ArrayBuffer'\n}\n\nfunction fromArrayBuffer (obj, byteOffset, length) {\n  byteOffset >>>= 0\n\n  var maxLength = obj.byteLength - byteOffset\n\n  if (maxLength < 0) {\n    throw new RangeError(\"'offset' is out of bounds\")\n  }\n\n  if (length === undefined) {\n    length = maxLength\n  } else {\n    length >>>= 0\n\n    if (length > maxLength) {\n      throw new RangeError(\"'length' is out of bounds\")\n    }\n  }\n\n  return isModern\n    ? Buffer.from(obj.slice(byteOffset, byteOffset + length))\n    : new Buffer(new Uint8Array(obj.slice(byteOffset, byteOffset + length)))\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  return isModern\n    ? Buffer.from(string, encoding)\n    : new Buffer(string, encoding)\n}\n\nfunction bufferFrom (value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (isArrayBuffer(value)) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  return isModern\n    ? Buffer.from(value)\n    : new Buffer(value)\n}\n\nmodule.exports = bufferFrom\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AAEA,QAAI,WAAW,OAAO,UAAU;AAEhC,QAAI,WACF,OAAO,WAAW,eAClB,OAAO,OAAO,UAAU,cACxB,OAAO,OAAO,gBAAgB,cAC9B,OAAO,OAAO,SAAS;AAGzB,aAAS,cAAe,OAAO;AAC7B,aAAO,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,MAAM;AAAA,IAC/C;AAEA,aAAS,gBAAiB,KAAK,YAAY,QAAQ;AACjD,sBAAgB;AAEhB,UAAI,YAAY,IAAI,aAAa;AAEjC,UAAI,YAAY,GAAG;AACjB,cAAM,IAAI,WAAW,2BAA2B;AAAA,MAClD;AAEA,UAAI,WAAW,QAAW;AACxB,iBAAS;AAAA,MACX,OAAO;AACL,oBAAY;AAEZ,YAAI,SAAS,WAAW;AACtB,gBAAM,IAAI,WAAW,2BAA2B;AAAA,QAClD;AAAA,MACF;AAEA,aAAO,WACH,OAAO,KAAK,IAAI,MAAM,YAAY,aAAa,MAAM,CAAC,IACtD,IAAI,OAAO,IAAI,WAAW,IAAI,MAAM,YAAY,aAAa,MAAM,CAAC,CAAC;AAAA,IAC3E;AAEA,aAAS,WAAY,QAAQ,UAAU;AACrC,UAAI,OAAO,aAAa,YAAY,aAAa,IAAI;AACnD,mBAAW;AAAA,MACb;AAEA,UAAI,CAAC,OAAO,WAAW,QAAQ,GAAG;AAChC,cAAM,IAAI,UAAU,4CAA4C;AAAA,MAClE;AAEA,aAAO,WACH,OAAO,KAAK,QAAQ,QAAQ,IAC5B,IAAI,OAAO,QAAQ,QAAQ;AAAA,IACjC;AAEA,aAAS,WAAY,OAAO,kBAAkB,QAAQ;AACpD,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,UAAU,uCAAuC;AAAA,MAC7D;AAEA,UAAI,cAAc,KAAK,GAAG;AACxB,eAAO,gBAAgB,OAAO,kBAAkB,MAAM;AAAA,MACxD;AAEA,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,WAAW,OAAO,gBAAgB;AAAA,MAC3C;AAEA,aAAO,WACH,OAAO,KAAK,KAAK,IACjB,IAAI,OAAO,KAAK;AAAA,IACtB;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["import_dist"]}