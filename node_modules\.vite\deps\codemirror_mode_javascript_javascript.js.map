{"version": 3, "sources": ["../../codemirror/mode/javascript/javascript.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.defineMode(\"javascript\", function(config, parserConfig) {\n  var indentUnit = config.indentUnit;\n  var statementIndent = parserConfig.statementIndent;\n  var jsonldMode = parserConfig.jsonld;\n  var jsonMode = parserConfig.json || jsonldMode;\n  var trackScope = parserConfig.trackScope !== false\n  var isTS = parserConfig.typescript;\n  var wordRE = parserConfig.wordCharacters || /[\\w$\\xa1-\\uffff]/;\n\n  // Tokenizer\n\n  var keywords = function(){\n    function kw(type) {return {type: type, style: \"keyword\"};}\n    var A = kw(\"keyword a\"), B = kw(\"keyword b\"), C = kw(\"keyword c\"), D = kw(\"keyword d\");\n    var operator = kw(\"operator\"), atom = {type: \"atom\", style: \"atom\"};\n\n    return {\n      \"if\": kw(\"if\"), \"while\": A, \"with\": A, \"else\": B, \"do\": B, \"try\": B, \"finally\": B,\n      \"return\": D, \"break\": D, \"continue\": D, \"new\": kw(\"new\"), \"delete\": C, \"void\": C, \"throw\": C,\n      \"debugger\": kw(\"debugger\"), \"var\": kw(\"var\"), \"const\": kw(\"var\"), \"let\": kw(\"var\"),\n      \"function\": kw(\"function\"), \"catch\": kw(\"catch\"),\n      \"for\": kw(\"for\"), \"switch\": kw(\"switch\"), \"case\": kw(\"case\"), \"default\": kw(\"default\"),\n      \"in\": operator, \"typeof\": operator, \"instanceof\": operator,\n      \"true\": atom, \"false\": atom, \"null\": atom, \"undefined\": atom, \"NaN\": atom, \"Infinity\": atom,\n      \"this\": kw(\"this\"), \"class\": kw(\"class\"), \"super\": kw(\"atom\"),\n      \"yield\": C, \"export\": kw(\"export\"), \"import\": kw(\"import\"), \"extends\": C,\n      \"await\": C\n    };\n  }();\n\n  var isOperatorChar = /[+\\-*&%=<>!?|~^@]/;\n  var isJsonldKeyword = /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)\"/;\n\n  function readRegexp(stream) {\n    var escaped = false, next, inSet = false;\n    while ((next = stream.next()) != null) {\n      if (!escaped) {\n        if (next == \"/\" && !inSet) return;\n        if (next == \"[\") inSet = true;\n        else if (inSet && next == \"]\") inSet = false;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n  }\n\n  // Used as scratch variables to communicate multiple values without\n  // consing up tons of objects.\n  var type, content;\n  function ret(tp, style, cont) {\n    type = tp; content = cont;\n    return style;\n  }\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\" && stream.match(/^\\d[\\d_]*(?:[eE][+\\-]?[\\d_]+)?/)) {\n      return ret(\"number\", \"number\");\n    } else if (ch == \".\" && stream.match(\"..\")) {\n      return ret(\"spread\", \"meta\");\n    } else if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n      return ret(ch);\n    } else if (ch == \"=\" && stream.eat(\">\")) {\n      return ret(\"=>\", \"operator\");\n    } else if (ch == \"0\" && stream.match(/^(?:x[\\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/)) {\n      return ret(\"number\", \"number\");\n    } else if (/\\d/.test(ch)) {\n      stream.match(/^[\\d_]*(?:n|(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)?/);\n      return ret(\"number\", \"number\");\n    } else if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      } else if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return ret(\"comment\", \"comment\");\n      } else if (expressionAllowed(stream, state, 1)) {\n        readRegexp(stream);\n        stream.match(/^\\b(([gimyus])(?![gimyus]*\\2))+\\b/);\n        return ret(\"regexp\", \"string-2\");\n      } else {\n        stream.eat(\"=\");\n        return ret(\"operator\", \"operator\", stream.current());\n      }\n    } else if (ch == \"`\") {\n      state.tokenize = tokenQuasi;\n      return tokenQuasi(stream, state);\n    } else if (ch == \"#\" && stream.peek() == \"!\") {\n      stream.skipToEnd();\n      return ret(\"meta\", \"meta\");\n    } else if (ch == \"#\" && stream.eatWhile(wordRE)) {\n      return ret(\"variable\", \"property\")\n    } else if (ch == \"<\" && stream.match(\"!--\") ||\n               (ch == \"-\" && stream.match(\"->\") && !/\\S/.test(stream.string.slice(0, stream.start)))) {\n      stream.skipToEnd()\n      return ret(\"comment\", \"comment\")\n    } else if (isOperatorChar.test(ch)) {\n      if (ch != \">\" || !state.lexical || state.lexical.type != \">\") {\n        if (stream.eat(\"=\")) {\n          if (ch == \"!\" || ch == \"=\") stream.eat(\"=\")\n        } else if (/[<>*+\\-|&?]/.test(ch)) {\n          stream.eat(ch)\n          if (ch == \">\") stream.eat(ch)\n        }\n      }\n      if (ch == \"?\" && stream.eat(\".\")) return ret(\".\")\n      return ret(\"operator\", \"operator\", stream.current());\n    } else if (wordRE.test(ch)) {\n      stream.eatWhile(wordRE);\n      var word = stream.current()\n      if (state.lastType != \".\") {\n        if (keywords.propertyIsEnumerable(word)) {\n          var kw = keywords[word]\n          return ret(kw.type, kw.style, word)\n        }\n        if (word == \"async\" && stream.match(/^(\\s|\\/\\*([^*]|\\*(?!\\/))*?\\*\\/)*[\\[\\(\\w]/, false))\n          return ret(\"async\", \"keyword\", word)\n      }\n      return ret(\"variable\", \"variable\", word)\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next;\n      if (jsonldMode && stream.peek() == \"@\" && stream.match(isJsonldKeyword)){\n        state.tokenize = tokenBase;\n        return ret(\"jsonld-keyword\", \"meta\");\n      }\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) break;\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (!escaped) state.tokenize = tokenBase;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return ret(\"comment\", \"comment\");\n  }\n\n  function tokenQuasi(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (!escaped && (next == \"`\" || next == \"$\" && stream.eat(\"{\"))) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    return ret(\"quasi\", \"string-2\", stream.current());\n  }\n\n  var brackets = \"([{}])\";\n  // This is a crude lookahead trick to try and notice that we're\n  // parsing the argument patterns for a fat-arrow function before we\n  // actually hit the arrow token. It only works if the arrow is on\n  // the same line as the arguments and there's no strange noise\n  // (comments) in between. Fallback is to only notice when we hit the\n  // arrow, and not declare the arguments as locals for the arrow\n  // body.\n  function findFatArrow(stream, state) {\n    if (state.fatArrowAt) state.fatArrowAt = null;\n    var arrow = stream.string.indexOf(\"=>\", stream.start);\n    if (arrow < 0) return;\n\n    if (isTS) { // Try to skip TypeScript return type declarations after the arguments\n      var m = /:\\s*(?:\\w+(?:<[^>]*>|\\[\\])?|\\{[^}]*\\})\\s*$/.exec(stream.string.slice(stream.start, arrow))\n      if (m) arrow = m.index\n    }\n\n    var depth = 0, sawSomething = false;\n    for (var pos = arrow - 1; pos >= 0; --pos) {\n      var ch = stream.string.charAt(pos);\n      var bracket = brackets.indexOf(ch);\n      if (bracket >= 0 && bracket < 3) {\n        if (!depth) { ++pos; break; }\n        if (--depth == 0) { if (ch == \"(\") sawSomething = true; break; }\n      } else if (bracket >= 3 && bracket < 6) {\n        ++depth;\n      } else if (wordRE.test(ch)) {\n        sawSomething = true;\n      } else if (/[\"'\\/`]/.test(ch)) {\n        for (;; --pos) {\n          if (pos == 0) return\n          var next = stream.string.charAt(pos - 1)\n          if (next == ch && stream.string.charAt(pos - 2) != \"\\\\\") { pos--; break }\n        }\n      } else if (sawSomething && !depth) {\n        ++pos;\n        break;\n      }\n    }\n    if (sawSomething && !depth) state.fatArrowAt = pos;\n  }\n\n  // Parser\n\n  var atomicTypes = {\"atom\": true, \"number\": true, \"variable\": true, \"string\": true,\n                     \"regexp\": true, \"this\": true, \"import\": true, \"jsonld-keyword\": true};\n\n  function JSLexical(indented, column, type, align, prev, info) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.prev = prev;\n    this.info = info;\n    if (align != null) this.align = align;\n  }\n\n  function inScope(state, varname) {\n    if (!trackScope) return false\n    for (var v = state.localVars; v; v = v.next)\n      if (v.name == varname) return true;\n    for (var cx = state.context; cx; cx = cx.prev) {\n      for (var v = cx.vars; v; v = v.next)\n        if (v.name == varname) return true;\n    }\n  }\n\n  function parseJS(state, style, type, content, stream) {\n    var cc = state.cc;\n    // Communicate our context to the combinators.\n    // (Less wasteful than consing up a hundred closures on every call.)\n    cx.state = state; cx.stream = stream; cx.marked = null, cx.cc = cc; cx.style = style;\n\n    if (!state.lexical.hasOwnProperty(\"align\"))\n      state.lexical.align = true;\n\n    while(true) {\n      var combinator = cc.length ? cc.pop() : jsonMode ? expression : statement;\n      if (combinator(type, content)) {\n        while(cc.length && cc[cc.length - 1].lex)\n          cc.pop()();\n        if (cx.marked) return cx.marked;\n        if (type == \"variable\" && inScope(state, content)) return \"variable-2\";\n        return style;\n      }\n    }\n  }\n\n  // Combinator utils\n\n  var cx = {state: null, column: null, marked: null, cc: null};\n  function pass() {\n    for (var i = arguments.length - 1; i >= 0; i--) cx.cc.push(arguments[i]);\n  }\n  function cont() {\n    pass.apply(null, arguments);\n    return true;\n  }\n  function inList(name, list) {\n    for (var v = list; v; v = v.next) if (v.name == name) return true\n    return false;\n  }\n  function register(varname) {\n    var state = cx.state;\n    cx.marked = \"def\";\n    if (!trackScope) return\n    if (state.context) {\n      if (state.lexical.info == \"var\" && state.context && state.context.block) {\n        // FIXME function decls are also not block scoped\n        var newContext = registerVarScoped(varname, state.context)\n        if (newContext != null) {\n          state.context = newContext\n          return\n        }\n      } else if (!inList(varname, state.localVars)) {\n        state.localVars = new Var(varname, state.localVars)\n        return\n      }\n    }\n    // Fall through means this is global\n    if (parserConfig.globalVars && !inList(varname, state.globalVars))\n      state.globalVars = new Var(varname, state.globalVars)\n  }\n  function registerVarScoped(varname, context) {\n    if (!context) {\n      return null\n    } else if (context.block) {\n      var inner = registerVarScoped(varname, context.prev)\n      if (!inner) return null\n      if (inner == context.prev) return context\n      return new Context(inner, context.vars, true)\n    } else if (inList(varname, context.vars)) {\n      return context\n    } else {\n      return new Context(context.prev, new Var(varname, context.vars), false)\n    }\n  }\n\n  function isModifier(name) {\n    return name == \"public\" || name == \"private\" || name == \"protected\" || name == \"abstract\" || name == \"readonly\"\n  }\n\n  // Combinators\n\n  function Context(prev, vars, block) { this.prev = prev; this.vars = vars; this.block = block }\n  function Var(name, next) { this.name = name; this.next = next }\n\n  var defaultVars = new Var(\"this\", new Var(\"arguments\", null))\n  function pushcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, false)\n    cx.state.localVars = defaultVars\n  }\n  function pushblockcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, true)\n    cx.state.localVars = null\n  }\n  pushcontext.lex = pushblockcontext.lex = true\n  function popcontext() {\n    cx.state.localVars = cx.state.context.vars\n    cx.state.context = cx.state.context.prev\n  }\n  popcontext.lex = true\n  function pushlex(type, info) {\n    var result = function() {\n      var state = cx.state, indent = state.indented;\n      if (state.lexical.type == \"stat\") indent = state.lexical.indented;\n      else for (var outer = state.lexical; outer && outer.type == \")\" && outer.align; outer = outer.prev)\n        indent = outer.indented;\n      state.lexical = new JSLexical(indent, cx.stream.column(), type, null, state.lexical, info);\n    };\n    result.lex = true;\n    return result;\n  }\n  function poplex() {\n    var state = cx.state;\n    if (state.lexical.prev) {\n      if (state.lexical.type == \")\")\n        state.indented = state.lexical.indented;\n      state.lexical = state.lexical.prev;\n    }\n  }\n  poplex.lex = true;\n\n  function expect(wanted) {\n    function exp(type) {\n      if (type == wanted) return cont();\n      else if (wanted == \";\" || type == \"}\" || type == \")\" || type == \"]\") return pass();\n      else return cont(exp);\n    };\n    return exp;\n  }\n\n  function statement(type, value) {\n    if (type == \"var\") return cont(pushlex(\"vardef\", value), vardef, expect(\";\"), poplex);\n    if (type == \"keyword a\") return cont(pushlex(\"form\"), parenExpr, statement, poplex);\n    if (type == \"keyword b\") return cont(pushlex(\"form\"), statement, poplex);\n    if (type == \"keyword d\") return cx.stream.match(/^\\s*$/, false) ? cont() : cont(pushlex(\"stat\"), maybeexpression, expect(\";\"), poplex);\n    if (type == \"debugger\") return cont(expect(\";\"));\n    if (type == \"{\") return cont(pushlex(\"}\"), pushblockcontext, block, poplex, popcontext);\n    if (type == \";\") return cont();\n    if (type == \"if\") {\n      if (cx.state.lexical.info == \"else\" && cx.state.cc[cx.state.cc.length - 1] == poplex)\n        cx.state.cc.pop()();\n      return cont(pushlex(\"form\"), parenExpr, statement, poplex, maybeelse);\n    }\n    if (type == \"function\") return cont(functiondef);\n    if (type == \"for\") return cont(pushlex(\"form\"), pushblockcontext, forspec, statement, popcontext, poplex);\n    if (type == \"class\" || (isTS && value == \"interface\")) {\n      cx.marked = \"keyword\"\n      return cont(pushlex(\"form\", type == \"class\" ? type : value), className, poplex)\n    }\n    if (type == \"variable\") {\n      if (isTS && value == \"declare\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else if (isTS && (value == \"module\" || value == \"enum\" || value == \"type\") && cx.stream.match(/^\\s*\\w/, false)) {\n        cx.marked = \"keyword\"\n        if (value == \"enum\") return cont(enumdef);\n        else if (value == \"type\") return cont(typename, expect(\"operator\"), typeexpr, expect(\";\"));\n        else return cont(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), block, poplex, poplex)\n      } else if (isTS && value == \"namespace\") {\n        cx.marked = \"keyword\"\n        return cont(pushlex(\"form\"), expression, statement, poplex)\n      } else if (isTS && value == \"abstract\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else {\n        return cont(pushlex(\"stat\"), maybelabel);\n      }\n    }\n    if (type == \"switch\") return cont(pushlex(\"form\"), parenExpr, expect(\"{\"), pushlex(\"}\", \"switch\"), pushblockcontext,\n                                      block, poplex, poplex, popcontext);\n    if (type == \"case\") return cont(expression, expect(\":\"));\n    if (type == \"default\") return cont(expect(\":\"));\n    if (type == \"catch\") return cont(pushlex(\"form\"), pushcontext, maybeCatchBinding, statement, poplex, popcontext);\n    if (type == \"export\") return cont(pushlex(\"stat\"), afterExport, poplex);\n    if (type == \"import\") return cont(pushlex(\"stat\"), afterImport, poplex);\n    if (type == \"async\") return cont(statement)\n    if (value == \"@\") return cont(expression, statement)\n    return pass(pushlex(\"stat\"), expression, expect(\";\"), poplex);\n  }\n  function maybeCatchBinding(type) {\n    if (type == \"(\") return cont(funarg, expect(\")\"))\n  }\n  function expression(type, value) {\n    return expressionInner(type, value, false);\n  }\n  function expressionNoComma(type, value) {\n    return expressionInner(type, value, true);\n  }\n  function parenExpr(type) {\n    if (type != \"(\") return pass()\n    return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex)\n  }\n  function expressionInner(type, value, noComma) {\n    if (cx.state.fatArrowAt == cx.stream.start) {\n      var body = noComma ? arrowBodyNoComma : arrowBody;\n      if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, expect(\"=>\"), body, popcontext);\n      else if (type == \"variable\") return pass(pushcontext, pattern, expect(\"=>\"), body, popcontext);\n    }\n\n    var maybeop = noComma ? maybeoperatorNoComma : maybeoperatorComma;\n    if (atomicTypes.hasOwnProperty(type)) return cont(maybeop);\n    if (type == \"function\") return cont(functiondef, maybeop);\n    if (type == \"class\" || (isTS && value == \"interface\")) { cx.marked = \"keyword\"; return cont(pushlex(\"form\"), classExpression, poplex); }\n    if (type == \"keyword c\" || type == \"async\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"(\") return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex, maybeop);\n    if (type == \"operator\" || type == \"spread\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"[\") return cont(pushlex(\"]\"), arrayLiteral, poplex, maybeop);\n    if (type == \"{\") return contCommasep(objprop, \"}\", null, maybeop);\n    if (type == \"quasi\") return pass(quasi, maybeop);\n    if (type == \"new\") return cont(maybeTarget(noComma));\n    return cont();\n  }\n  function maybeexpression(type) {\n    if (type.match(/[;\\}\\)\\],]/)) return pass();\n    return pass(expression);\n  }\n\n  function maybeoperatorComma(type, value) {\n    if (type == \",\") return cont(maybeexpression);\n    return maybeoperatorNoComma(type, value, false);\n  }\n  function maybeoperatorNoComma(type, value, noComma) {\n    var me = noComma == false ? maybeoperatorComma : maybeoperatorNoComma;\n    var expr = noComma == false ? expression : expressionNoComma;\n    if (type == \"=>\") return cont(pushcontext, noComma ? arrowBodyNoComma : arrowBody, popcontext);\n    if (type == \"operator\") {\n      if (/\\+\\+|--/.test(value) || isTS && value == \"!\") return cont(me);\n      if (isTS && value == \"<\" && cx.stream.match(/^([^<>]|<[^<>]*>)*>\\s*\\(/, false))\n        return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, me);\n      if (value == \"?\") return cont(expression, expect(\":\"), expr);\n      return cont(expr);\n    }\n    if (type == \"quasi\") { return pass(quasi, me); }\n    if (type == \";\") return;\n    if (type == \"(\") return contCommasep(expressionNoComma, \")\", \"call\", me);\n    if (type == \".\") return cont(property, me);\n    if (type == \"[\") return cont(pushlex(\"]\"), maybeexpression, expect(\"]\"), poplex, me);\n    if (isTS && value == \"as\") { cx.marked = \"keyword\"; return cont(typeexpr, me) }\n    if (type == \"regexp\") {\n      cx.state.lastType = cx.marked = \"operator\"\n      cx.stream.backUp(cx.stream.pos - cx.stream.start - 1)\n      return cont(expr)\n    }\n  }\n  function quasi(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasi);\n    return cont(maybeexpression, continueQuasi);\n  }\n  function continueQuasi(type) {\n    if (type == \"}\") {\n      cx.marked = \"string-2\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasi);\n    }\n  }\n  function arrowBody(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expression);\n  }\n  function arrowBodyNoComma(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expressionNoComma);\n  }\n  function maybeTarget(noComma) {\n    return function(type) {\n      if (type == \".\") return cont(noComma ? targetNoComma : target);\n      else if (type == \"variable\" && isTS) return cont(maybeTypeArgs, noComma ? maybeoperatorNoComma : maybeoperatorComma)\n      else return pass(noComma ? expressionNoComma : expression);\n    };\n  }\n  function target(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorComma); }\n  }\n  function targetNoComma(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorNoComma); }\n  }\n  function maybelabel(type) {\n    if (type == \":\") return cont(poplex, statement);\n    return pass(maybeoperatorComma, expect(\";\"), poplex);\n  }\n  function property(type) {\n    if (type == \"variable\") {cx.marked = \"property\"; return cont();}\n  }\n  function objprop(type, value) {\n    if (type == \"async\") {\n      cx.marked = \"property\";\n      return cont(objprop);\n    } else if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      if (value == \"get\" || value == \"set\") return cont(getterSetter);\n      var m // Work around fat-arrow-detection complication for detecting typescript typed arrow params\n      if (isTS && cx.state.fatArrowAt == cx.stream.start && (m = cx.stream.match(/^\\s*:\\s*/, false)))\n        cx.state.fatArrowAt = cx.stream.pos + m[0].length\n      return cont(afterprop);\n    } else if (type == \"number\" || type == \"string\") {\n      cx.marked = jsonldMode ? \"property\" : (cx.style + \" property\");\n      return cont(afterprop);\n    } else if (type == \"jsonld-keyword\") {\n      return cont(afterprop);\n    } else if (isTS && isModifier(value)) {\n      cx.marked = \"keyword\"\n      return cont(objprop)\n    } else if (type == \"[\") {\n      return cont(expression, maybetype, expect(\"]\"), afterprop);\n    } else if (type == \"spread\") {\n      return cont(expressionNoComma, afterprop);\n    } else if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(objprop);\n    } else if (type == \":\") {\n      return pass(afterprop)\n    }\n  }\n  function getterSetter(type) {\n    if (type != \"variable\") return pass(afterprop);\n    cx.marked = \"property\";\n    return cont(functiondef);\n  }\n  function afterprop(type) {\n    if (type == \":\") return cont(expressionNoComma);\n    if (type == \"(\") return pass(functiondef);\n  }\n  function commasep(what, end, sep) {\n    function proceed(type, value) {\n      if (sep ? sep.indexOf(type) > -1 : type == \",\") {\n        var lex = cx.state.lexical;\n        if (lex.info == \"call\") lex.pos = (lex.pos || 0) + 1;\n        return cont(function(type, value) {\n          if (type == end || value == end) return pass()\n          return pass(what)\n        }, proceed);\n      }\n      if (type == end || value == end) return cont();\n      if (sep && sep.indexOf(\";\") > -1) return pass(what)\n      return cont(expect(end));\n    }\n    return function(type, value) {\n      if (type == end || value == end) return cont();\n      return pass(what, proceed);\n    };\n  }\n  function contCommasep(what, end, info) {\n    for (var i = 3; i < arguments.length; i++)\n      cx.cc.push(arguments[i]);\n    return cont(pushlex(end, info), commasep(what, end), poplex);\n  }\n  function block(type) {\n    if (type == \"}\") return cont();\n    return pass(statement, block);\n  }\n  function maybetype(type, value) {\n    if (isTS) {\n      if (type == \":\") return cont(typeexpr);\n      if (value == \"?\") return cont(maybetype);\n    }\n  }\n  function maybetypeOrIn(type, value) {\n    if (isTS && (type == \":\" || value == \"in\")) return cont(typeexpr)\n  }\n  function mayberettype(type) {\n    if (isTS && type == \":\") {\n      if (cx.stream.match(/^\\s*\\w+\\s+is\\b/, false)) return cont(expression, isKW, typeexpr)\n      else return cont(typeexpr)\n    }\n  }\n  function isKW(_, value) {\n    if (value == \"is\") {\n      cx.marked = \"keyword\"\n      return cont()\n    }\n  }\n  function typeexpr(type, value) {\n    if (value == \"keyof\" || value == \"typeof\" || value == \"infer\" || value == \"readonly\") {\n      cx.marked = \"keyword\"\n      return cont(value == \"typeof\" ? expressionNoComma : typeexpr)\n    }\n    if (type == \"variable\" || value == \"void\") {\n      cx.marked = \"type\"\n      return cont(afterType)\n    }\n    if (value == \"|\" || value == \"&\") return cont(typeexpr)\n    if (type == \"string\" || type == \"number\" || type == \"atom\") return cont(afterType);\n    if (type == \"[\") return cont(pushlex(\"]\"), commasep(typeexpr, \"]\", \",\"), poplex, afterType)\n    if (type == \"{\") return cont(pushlex(\"}\"), typeprops, poplex, afterType)\n    if (type == \"(\") return cont(commasep(typearg, \")\"), maybeReturnType, afterType)\n    if (type == \"<\") return cont(commasep(typeexpr, \">\"), typeexpr)\n    if (type == \"quasi\") { return pass(quasiType, afterType); }\n  }\n  function maybeReturnType(type) {\n    if (type == \"=>\") return cont(typeexpr)\n  }\n  function typeprops(type) {\n    if (type.match(/[\\}\\)\\]]/)) return cont()\n    if (type == \",\" || type == \";\") return cont(typeprops)\n    return pass(typeprop, typeprops)\n  }\n  function typeprop(type, value) {\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\"\n      return cont(typeprop)\n    } else if (value == \"?\" || type == \"number\" || type == \"string\") {\n      return cont(typeprop)\n    } else if (type == \":\") {\n      return cont(typeexpr)\n    } else if (type == \"[\") {\n      return cont(expect(\"variable\"), maybetypeOrIn, expect(\"]\"), typeprop)\n    } else if (type == \"(\") {\n      return pass(functiondecl, typeprop)\n    } else if (!type.match(/[;\\}\\)\\],]/)) {\n      return cont()\n    }\n  }\n  function quasiType(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasiType);\n    return cont(typeexpr, continueQuasiType);\n  }\n  function continueQuasiType(type) {\n    if (type == \"}\") {\n      cx.marked = \"string-2\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasiType);\n    }\n  }\n  function typearg(type, value) {\n    if (type == \"variable\" && cx.stream.match(/^\\s*[?:]/, false) || value == \"?\") return cont(typearg)\n    if (type == \":\") return cont(typeexpr)\n    if (type == \"spread\") return cont(typearg)\n    return pass(typeexpr)\n  }\n  function afterType(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n    if (value == \"|\" || type == \".\" || value == \"&\") return cont(typeexpr)\n    if (type == \"[\") return cont(typeexpr, expect(\"]\"), afterType)\n    if (value == \"extends\" || value == \"implements\") { cx.marked = \"keyword\"; return cont(typeexpr) }\n    if (value == \"?\") return cont(typeexpr, expect(\":\"), typeexpr)\n  }\n  function maybeTypeArgs(_, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n  }\n  function typeparam() {\n    return pass(typeexpr, maybeTypeDefault)\n  }\n  function maybeTypeDefault(_, value) {\n    if (value == \"=\") return cont(typeexpr)\n  }\n  function vardef(_, value) {\n    if (value == \"enum\") {cx.marked = \"keyword\"; return cont(enumdef)}\n    return pass(pattern, maybetype, maybeAssign, vardefCont);\n  }\n  function pattern(type, value) {\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(pattern) }\n    if (type == \"variable\") { register(value); return cont(); }\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"[\") return contCommasep(eltpattern, \"]\");\n    if (type == \"{\") return contCommasep(proppattern, \"}\");\n  }\n  function proppattern(type, value) {\n    if (type == \"variable\" && !cx.stream.match(/^\\s*:/, false)) {\n      register(value);\n      return cont(maybeAssign);\n    }\n    if (type == \"variable\") cx.marked = \"property\";\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"}\") return pass();\n    if (type == \"[\") return cont(expression, expect(']'), expect(':'), proppattern);\n    return cont(expect(\":\"), pattern, maybeAssign);\n  }\n  function eltpattern() {\n    return pass(pattern, maybeAssign)\n  }\n  function maybeAssign(_type, value) {\n    if (value == \"=\") return cont(expressionNoComma);\n  }\n  function vardefCont(type) {\n    if (type == \",\") return cont(vardef);\n  }\n  function maybeelse(type, value) {\n    if (type == \"keyword b\" && value == \"else\") return cont(pushlex(\"form\", \"else\"), statement, poplex);\n  }\n  function forspec(type, value) {\n    if (value == \"await\") return cont(forspec);\n    if (type == \"(\") return cont(pushlex(\")\"), forspec1, poplex);\n  }\n  function forspec1(type) {\n    if (type == \"var\") return cont(vardef, forspec2);\n    if (type == \"variable\") return cont(forspec2);\n    return pass(forspec2)\n  }\n  function forspec2(type, value) {\n    if (type == \")\") return cont()\n    if (type == \";\") return cont(forspec2)\n    if (value == \"in\" || value == \"of\") { cx.marked = \"keyword\"; return cont(expression, forspec2) }\n    return pass(expression, forspec2)\n  }\n  function functiondef(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondef);}\n    if (type == \"variable\") {register(value); return cont(functiondef);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, statement, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondef)\n  }\n  function functiondecl(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondecl);}\n    if (type == \"variable\") {register(value); return cont(functiondecl);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondecl)\n  }\n  function typename(type, value) {\n    if (type == \"keyword\" || type == \"variable\") {\n      cx.marked = \"type\"\n      return cont(typename)\n    } else if (value == \"<\") {\n      return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex)\n    }\n  }\n  function funarg(type, value) {\n    if (value == \"@\") cont(expression, funarg)\n    if (type == \"spread\") return cont(funarg);\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(funarg); }\n    if (isTS && type == \"this\") return cont(maybetype, maybeAssign)\n    return pass(pattern, maybetype, maybeAssign);\n  }\n  function classExpression(type, value) {\n    // Class expressions may have an optional name.\n    if (type == \"variable\") return className(type, value);\n    return classNameAfter(type, value);\n  }\n  function className(type, value) {\n    if (type == \"variable\") {register(value); return cont(classNameAfter);}\n  }\n  function classNameAfter(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, classNameAfter)\n    if (value == \"extends\" || value == \"implements\" || (isTS && type == \",\")) {\n      if (value == \"implements\") cx.marked = \"keyword\";\n      return cont(isTS ? typeexpr : expression, classNameAfter);\n    }\n    if (type == \"{\") return cont(pushlex(\"}\"), classBody, poplex);\n  }\n  function classBody(type, value) {\n    if (type == \"async\" ||\n        (type == \"variable\" &&\n         (value == \"static\" || value == \"get\" || value == \"set\" || (isTS && isModifier(value))) &&\n         cx.stream.match(/^\\s+#?[\\w$\\xa1-\\uffff]/, false))) {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      return cont(classfield, classBody);\n    }\n    if (type == \"number\" || type == \"string\") return cont(classfield, classBody);\n    if (type == \"[\")\n      return cont(expression, maybetype, expect(\"]\"), classfield, classBody)\n    if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (isTS && type == \"(\") return pass(functiondecl, classBody)\n    if (type == \";\" || type == \",\") return cont(classBody);\n    if (type == \"}\") return cont();\n    if (value == \"@\") return cont(expression, classBody)\n  }\n  function classfield(type, value) {\n    if (value == \"!\") return cont(classfield)\n    if (value == \"?\") return cont(classfield)\n    if (type == \":\") return cont(typeexpr, maybeAssign)\n    if (value == \"=\") return cont(expressionNoComma)\n    var context = cx.state.lexical.prev, isInterface = context && context.info == \"interface\"\n    return pass(isInterface ? functiondecl : functiondef)\n  }\n  function afterExport(type, value) {\n    if (value == \"*\") { cx.marked = \"keyword\"; return cont(maybeFrom, expect(\";\")); }\n    if (value == \"default\") { cx.marked = \"keyword\"; return cont(expression, expect(\";\")); }\n    if (type == \"{\") return cont(commasep(exportField, \"}\"), maybeFrom, expect(\";\"));\n    return pass(statement);\n  }\n  function exportField(type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(expect(\"variable\")); }\n    if (type == \"variable\") return pass(expressionNoComma, exportField);\n  }\n  function afterImport(type) {\n    if (type == \"string\") return cont();\n    if (type == \"(\") return pass(expression);\n    if (type == \".\") return pass(maybeoperatorComma);\n    return pass(importSpec, maybeMoreImports, maybeFrom);\n  }\n  function importSpec(type, value) {\n    if (type == \"{\") return contCommasep(importSpec, \"}\");\n    if (type == \"variable\") register(value);\n    if (value == \"*\") cx.marked = \"keyword\";\n    return cont(maybeAs);\n  }\n  function maybeMoreImports(type) {\n    if (type == \",\") return cont(importSpec, maybeMoreImports)\n  }\n  function maybeAs(_type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(importSpec); }\n  }\n  function maybeFrom(_type, value) {\n    if (value == \"from\") { cx.marked = \"keyword\"; return cont(expression); }\n  }\n  function arrayLiteral(type) {\n    if (type == \"]\") return cont();\n    return pass(commasep(expressionNoComma, \"]\"));\n  }\n  function enumdef() {\n    return pass(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), commasep(enummember, \"}\"), poplex, poplex)\n  }\n  function enummember() {\n    return pass(pattern, maybeAssign);\n  }\n\n  function isContinuedStatement(state, textAfter) {\n    return state.lastType == \"operator\" || state.lastType == \",\" ||\n      isOperatorChar.test(textAfter.charAt(0)) ||\n      /[,.]/.test(textAfter.charAt(0));\n  }\n\n  function expressionAllowed(stream, state, backUp) {\n    return state.tokenize == tokenBase &&\n      /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\\[{}\\(,;:]|=>)$/.test(state.lastType) ||\n      (state.lastType == \"quasi\" && /\\{\\s*$/.test(stream.string.slice(0, stream.pos - (backUp || 0))))\n  }\n\n  // Interface\n\n  return {\n    startState: function(basecolumn) {\n      var state = {\n        tokenize: tokenBase,\n        lastType: \"sof\",\n        cc: [],\n        lexical: new JSLexical((basecolumn || 0) - indentUnit, 0, \"block\", false),\n        localVars: parserConfig.localVars,\n        context: parserConfig.localVars && new Context(null, null, false),\n        indented: basecolumn || 0\n      };\n      if (parserConfig.globalVars && typeof parserConfig.globalVars == \"object\")\n        state.globalVars = parserConfig.globalVars;\n      return state;\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (!state.lexical.hasOwnProperty(\"align\"))\n          state.lexical.align = false;\n        state.indented = stream.indentation();\n        findFatArrow(stream, state);\n      }\n      if (state.tokenize != tokenComment && stream.eatSpace()) return null;\n      var style = state.tokenize(stream, state);\n      if (type == \"comment\") return style;\n      state.lastType = type == \"operator\" && (content == \"++\" || content == \"--\") ? \"incdec\" : type;\n      return parseJS(state, style, type, content, stream);\n    },\n\n    indent: function(state, textAfter) {\n      if (state.tokenize == tokenComment || state.tokenize == tokenQuasi) return CodeMirror.Pass;\n      if (state.tokenize != tokenBase) return 0;\n      var firstChar = textAfter && textAfter.charAt(0), lexical = state.lexical, top\n      // Kludge to prevent 'maybelse' from blocking lexical scope pops\n      if (!/^\\s*else\\b/.test(textAfter)) for (var i = state.cc.length - 1; i >= 0; --i) {\n        var c = state.cc[i];\n        if (c == poplex) lexical = lexical.prev;\n        else if (c != maybeelse && c != popcontext) break;\n      }\n      while ((lexical.type == \"stat\" || lexical.type == \"form\") &&\n             (firstChar == \"}\" || ((top = state.cc[state.cc.length - 1]) &&\n                                   (top == maybeoperatorComma || top == maybeoperatorNoComma) &&\n                                   !/^[,\\.=+\\-*:?[\\(]/.test(textAfter))))\n        lexical = lexical.prev;\n      if (statementIndent && lexical.type == \")\" && lexical.prev.type == \"stat\")\n        lexical = lexical.prev;\n      var type = lexical.type, closing = firstChar == type;\n\n      if (type == \"vardef\") return lexical.indented + (state.lastType == \"operator\" || state.lastType == \",\" ? lexical.info.length + 1 : 0);\n      else if (type == \"form\" && firstChar == \"{\") return lexical.indented;\n      else if (type == \"form\") return lexical.indented + indentUnit;\n      else if (type == \"stat\")\n        return lexical.indented + (isContinuedStatement(state, textAfter) ? statementIndent || indentUnit : 0);\n      else if (lexical.info == \"switch\" && !closing && parserConfig.doubleIndentSwitch != false)\n        return lexical.indented + (/^(?:case|default)\\b/.test(textAfter) ? indentUnit : 2 * indentUnit);\n      else if (lexical.align) return lexical.column + (closing ? 0 : 1);\n      else return lexical.indented + (closing ? 0 : indentUnit);\n    },\n\n    electricInput: /^\\s*(?:case .*?:|default:|\\{|\\})$/,\n    blockCommentStart: jsonMode ? null : \"/*\",\n    blockCommentEnd: jsonMode ? null : \"*/\",\n    blockCommentContinue: jsonMode ? null : \" * \",\n    lineComment: jsonMode ? null : \"//\",\n    fold: \"brace\",\n    closeBrackets: \"()[]{}''\\\"\\\"``\",\n\n    helperType: jsonMode ? \"json\" : \"javascript\",\n    jsonldMode: jsonldMode,\n    jsonMode: jsonMode,\n\n    expressionAllowed: expressionAllowed,\n\n    skipExpression: function(state) {\n      parseJS(state, \"atom\", \"atom\", \"true\", new CodeMirror.StringStream(\"\", 2, null))\n    }\n  };\n});\n\nCodeMirror.registerHelper(\"wordChars\", \"javascript\", /[\\w$]/);\n\nCodeMirror.defineMIME(\"text/javascript\", \"javascript\");\nCodeMirror.defineMIME(\"text/ecmascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/javascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/x-javascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/ecmascript\", \"javascript\");\nCodeMirror.defineMIME(\"application/json\", { name: \"javascript\", json: true });\nCodeMirror.defineMIME(\"application/x-json\", { name: \"javascript\", json: true });\nCodeMirror.defineMIME(\"application/manifest+json\", { name: \"javascript\", json: true })\nCodeMirror.defineMIME(\"application/ld+json\", { name: \"javascript\", jsonld: true });\nCodeMirror.defineMIME(\"text/typescript\", { name: \"javascript\", typescript: true });\nCodeMirror.defineMIME(\"application/typescript\", { name: \"javascript\", typescript: true });\n\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASC,aAAY;AACxB;AAEA,MAAAA,YAAW,WAAW,cAAc,SAAS,QAAQ,cAAc;AACjE,YAAI,aAAa,OAAO;AACxB,YAAI,kBAAkB,aAAa;AACnC,YAAI,aAAa,aAAa;AAC9B,YAAI,WAAW,aAAa,QAAQ;AACpC,YAAI,aAAa,aAAa,eAAe;AAC7C,YAAI,OAAO,aAAa;AACxB,YAAI,SAAS,aAAa,kBAAkB;AAI5C,YAAI,WAAW,WAAU;AACvB,mBAAS,GAAGC,OAAM;AAAC,mBAAO,EAAC,MAAMA,OAAM,OAAO,UAAS;AAAA,UAAE;AACzD,cAAI,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG,WAAW;AACrF,cAAI,WAAW,GAAG,UAAU,GAAG,OAAO,EAAC,MAAM,QAAQ,OAAO,OAAM;AAElE,iBAAO;AAAA,YACL,MAAM,GAAG,IAAI;AAAA,YAAG,SAAS;AAAA,YAAG,QAAQ;AAAA,YAAG,QAAQ;AAAA,YAAG,MAAM;AAAA,YAAG,OAAO;AAAA,YAAG,WAAW;AAAA,YAChF,UAAU;AAAA,YAAG,SAAS;AAAA,YAAG,YAAY;AAAA,YAAG,OAAO,GAAG,KAAK;AAAA,YAAG,UAAU;AAAA,YAAG,QAAQ;AAAA,YAAG,SAAS;AAAA,YAC3F,YAAY,GAAG,UAAU;AAAA,YAAG,OAAO,GAAG,KAAK;AAAA,YAAG,SAAS,GAAG,KAAK;AAAA,YAAG,OAAO,GAAG,KAAK;AAAA,YACjF,YAAY,GAAG,UAAU;AAAA,YAAG,SAAS,GAAG,OAAO;AAAA,YAC/C,OAAO,GAAG,KAAK;AAAA,YAAG,UAAU,GAAG,QAAQ;AAAA,YAAG,QAAQ,GAAG,MAAM;AAAA,YAAG,WAAW,GAAG,SAAS;AAAA,YACrF,MAAM;AAAA,YAAU,UAAU;AAAA,YAAU,cAAc;AAAA,YAClD,QAAQ;AAAA,YAAM,SAAS;AAAA,YAAM,QAAQ;AAAA,YAAM,aAAa;AAAA,YAAM,OAAO;AAAA,YAAM,YAAY;AAAA,YACvF,QAAQ,GAAG,MAAM;AAAA,YAAG,SAAS,GAAG,OAAO;AAAA,YAAG,SAAS,GAAG,MAAM;AAAA,YAC5D,SAAS;AAAA,YAAG,UAAU,GAAG,QAAQ;AAAA,YAAG,UAAU,GAAG,QAAQ;AAAA,YAAG,WAAW;AAAA,YACvE,SAAS;AAAA,UACX;AAAA,QACF,EAAE;AAEF,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AAEtB,iBAAS,WAAW,QAAQ;AAC1B,cAAI,UAAU,OAAO,MAAM,QAAQ;AACnC,kBAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,gBAAI,CAAC,SAAS;AACZ,kBAAI,QAAQ,OAAO,CAAC,MAAO;AAC3B,kBAAI,QAAQ,IAAK,SAAQ;AAAA,uBAChB,SAAS,QAAQ,IAAK,SAAQ;AAAA,YACzC;AACA,sBAAU,CAAC,WAAW,QAAQ;AAAA,UAChC;AAAA,QACF;AAIA,YAAI,MAAM;AACV,iBAAS,IAAI,IAAI,OAAOC,OAAM;AAC5B,iBAAO;AAAI,oBAAUA;AACrB,iBAAO;AAAA,QACT;AACA,iBAAS,UAAU,QAAQ,OAAO;AAChC,cAAI,KAAK,OAAO,KAAK;AACrB,cAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,kBAAM,WAAW,YAAY,EAAE;AAC/B,mBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,UACrC,WAAW,MAAM,OAAO,OAAO,MAAM,gCAAgC,GAAG;AACtE,mBAAO,IAAI,UAAU,QAAQ;AAAA,UAC/B,WAAW,MAAM,OAAO,OAAO,MAAM,IAAI,GAAG;AAC1C,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,WAAW,qBAAqB,KAAK,EAAE,GAAG;AACxC,mBAAO,IAAI,EAAE;AAAA,UACf,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACvC,mBAAO,IAAI,MAAM,UAAU;AAAA,UAC7B,WAAW,MAAM,OAAO,OAAO,MAAM,uCAAuC,GAAG;AAC7E,mBAAO,IAAI,UAAU,QAAQ;AAAA,UAC/B,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,mBAAO,MAAM,kDAAkD;AAC/D,mBAAO,IAAI,UAAU,QAAQ;AAAA,UAC/B,WAAW,MAAM,KAAK;AACpB,gBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,oBAAM,WAAW;AACjB,qBAAO,aAAa,QAAQ,KAAK;AAAA,YACnC,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,qBAAO,UAAU;AACjB,qBAAO,IAAI,WAAW,SAAS;AAAA,YACjC,WAAW,kBAAkB,QAAQ,OAAO,CAAC,GAAG;AAC9C,yBAAW,MAAM;AACjB,qBAAO,MAAM,mCAAmC;AAChD,qBAAO,IAAI,UAAU,UAAU;AAAA,YACjC,OAAO;AACL,qBAAO,IAAI,GAAG;AACd,qBAAO,IAAI,YAAY,YAAY,OAAO,QAAQ,CAAC;AAAA,YACrD;AAAA,UACF,WAAW,MAAM,KAAK;AACpB,kBAAM,WAAW;AACjB,mBAAO,WAAW,QAAQ,KAAK;AAAA,UACjC,WAAW,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK;AAC5C,mBAAO,UAAU;AACjB,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,WAAW,MAAM,OAAO,OAAO,SAAS,MAAM,GAAG;AAC/C,mBAAO,IAAI,YAAY,UAAU;AAAA,UACnC,WAAW,MAAM,OAAO,OAAO,MAAM,KAAK,KAC9B,MAAM,OAAO,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,OAAO,MAAM,GAAG,OAAO,KAAK,CAAC,GAAI;AAChG,mBAAO,UAAU;AACjB,mBAAO,IAAI,WAAW,SAAS;AAAA,UACjC,WAAW,eAAe,KAAK,EAAE,GAAG;AAClC,gBAAI,MAAM,OAAO,CAAC,MAAM,WAAW,MAAM,QAAQ,QAAQ,KAAK;AAC5D,kBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,oBAAI,MAAM,OAAO,MAAM,IAAK,QAAO,IAAI,GAAG;AAAA,cAC5C,WAAW,cAAc,KAAK,EAAE,GAAG;AACjC,uBAAO,IAAI,EAAE;AACb,oBAAI,MAAM,IAAK,QAAO,IAAI,EAAE;AAAA,cAC9B;AAAA,YACF;AACA,gBAAI,MAAM,OAAO,OAAO,IAAI,GAAG,EAAG,QAAO,IAAI,GAAG;AAChD,mBAAO,IAAI,YAAY,YAAY,OAAO,QAAQ,CAAC;AAAA,UACrD,WAAW,OAAO,KAAK,EAAE,GAAG;AAC1B,mBAAO,SAAS,MAAM;AACtB,gBAAI,OAAO,OAAO,QAAQ;AAC1B,gBAAI,MAAM,YAAY,KAAK;AACzB,kBAAI,SAAS,qBAAqB,IAAI,GAAG;AACvC,oBAAI,KAAK,SAAS,IAAI;AACtB,uBAAO,IAAI,GAAG,MAAM,GAAG,OAAO,IAAI;AAAA,cACpC;AACA,kBAAI,QAAQ,WAAW,OAAO,MAAM,4CAA4C,KAAK;AACnF,uBAAO,IAAI,SAAS,WAAW,IAAI;AAAA,YACvC;AACA,mBAAO,IAAI,YAAY,YAAY,IAAI;AAAA,UACzC;AAAA,QACF;AAEA,iBAAS,YAAY,OAAO;AAC1B,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,UAAU,OAAO;AACrB,gBAAI,cAAc,OAAO,KAAK,KAAK,OAAO,OAAO,MAAM,eAAe,GAAE;AACtE,oBAAM,WAAW;AACjB,qBAAO,IAAI,kBAAkB,MAAM;AAAA,YACrC;AACA,oBAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,kBAAI,QAAQ,SAAS,CAAC,QAAS;AAC/B,wBAAU,CAAC,WAAW,QAAQ;AAAA,YAChC;AACA,gBAAI,CAAC,QAAS,OAAM,WAAW;AAC/B,mBAAO,IAAI,UAAU,QAAQ;AAAA,UAC/B;AAAA,QACF;AAEA,iBAAS,aAAa,QAAQ,OAAO;AACnC,cAAI,WAAW,OAAO;AACtB,iBAAO,KAAK,OAAO,KAAK,GAAG;AACzB,gBAAI,MAAM,OAAO,UAAU;AACzB,oBAAM,WAAW;AACjB;AAAA,YACF;AACA,uBAAY,MAAM;AAAA,UACpB;AACA,iBAAO,IAAI,WAAW,SAAS;AAAA,QACjC;AAEA,iBAAS,WAAW,QAAQ,OAAO;AACjC,cAAI,UAAU,OAAO;AACrB,kBAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,gBAAI,CAAC,YAAY,QAAQ,OAAO,QAAQ,OAAO,OAAO,IAAI,GAAG,IAAI;AAC/D,oBAAM,WAAW;AACjB;AAAA,YACF;AACA,sBAAU,CAAC,WAAW,QAAQ;AAAA,UAChC;AACA,iBAAO,IAAI,SAAS,YAAY,OAAO,QAAQ,CAAC;AAAA,QAClD;AAEA,YAAI,WAAW;AAQf,iBAAS,aAAa,QAAQ,OAAO;AACnC,cAAI,MAAM,WAAY,OAAM,aAAa;AACzC,cAAI,QAAQ,OAAO,OAAO,QAAQ,MAAM,OAAO,KAAK;AACpD,cAAI,QAAQ,EAAG;AAEf,cAAI,MAAM;AACR,gBAAI,IAAI,6CAA6C,KAAK,OAAO,OAAO,MAAM,OAAO,OAAO,KAAK,CAAC;AAClG,gBAAI,EAAG,SAAQ,EAAE;AAAA,UACnB;AAEA,cAAI,QAAQ,GAAG,eAAe;AAC9B,mBAAS,MAAM,QAAQ,GAAG,OAAO,GAAG,EAAE,KAAK;AACzC,gBAAI,KAAK,OAAO,OAAO,OAAO,GAAG;AACjC,gBAAI,UAAU,SAAS,QAAQ,EAAE;AACjC,gBAAI,WAAW,KAAK,UAAU,GAAG;AAC/B,kBAAI,CAAC,OAAO;AAAE,kBAAE;AAAK;AAAA,cAAO;AAC5B,kBAAI,EAAE,SAAS,GAAG;AAAE,oBAAI,MAAM,IAAK,gBAAe;AAAM;AAAA,cAAO;AAAA,YACjE,WAAW,WAAW,KAAK,UAAU,GAAG;AACtC,gBAAE;AAAA,YACJ,WAAW,OAAO,KAAK,EAAE,GAAG;AAC1B,6BAAe;AAAA,YACjB,WAAW,UAAU,KAAK,EAAE,GAAG;AAC7B,uBAAQ,EAAE,KAAK;AACb,oBAAI,OAAO,EAAG;AACd,oBAAI,OAAO,OAAO,OAAO,OAAO,MAAM,CAAC;AACvC,oBAAI,QAAQ,MAAM,OAAO,OAAO,OAAO,MAAM,CAAC,KAAK,MAAM;AAAE;AAAO;AAAA,gBAAM;AAAA,cAC1E;AAAA,YACF,WAAW,gBAAgB,CAAC,OAAO;AACjC,gBAAE;AACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,gBAAgB,CAAC,MAAO,OAAM,aAAa;AAAA,QACjD;AAIA,YAAI,cAAc;AAAA,UAAC,QAAQ;AAAA,UAAM,UAAU;AAAA,UAAM,YAAY;AAAA,UAAM,UAAU;AAAA,UAC1D,UAAU;AAAA,UAAM,QAAQ;AAAA,UAAM,UAAU;AAAA,UAAM,kBAAkB;AAAA,QAAI;AAEvF,iBAAS,UAAU,UAAU,QAAQD,OAAM,OAAO,MAAM,MAAM;AAC5D,eAAK,WAAW;AAChB,eAAK,SAAS;AACd,eAAK,OAAOA;AACZ,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,cAAI,SAAS,KAAM,MAAK,QAAQ;AAAA,QAClC;AAEA,iBAAS,QAAQ,OAAO,SAAS;AAC/B,cAAI,CAAC,WAAY,QAAO;AACxB,mBAAS,IAAI,MAAM,WAAW,GAAG,IAAI,EAAE;AACrC,gBAAI,EAAE,QAAQ,QAAS,QAAO;AAChC,mBAASE,MAAK,MAAM,SAASA,KAAIA,MAAKA,IAAG,MAAM;AAC7C,qBAAS,IAAIA,IAAG,MAAM,GAAG,IAAI,EAAE;AAC7B,kBAAI,EAAE,QAAQ,QAAS,QAAO;AAAA,UAClC;AAAA,QACF;AAEA,iBAAS,QAAQ,OAAO,OAAOF,OAAMG,UAAS,QAAQ;AACpD,cAAI,KAAK,MAAM;AAGf,aAAG,QAAQ;AAAO,aAAG,SAAS;AAAQ,aAAG,SAAS,MAAM,GAAG,KAAK;AAAI,aAAG,QAAQ;AAE/E,cAAI,CAAC,MAAM,QAAQ,eAAe,OAAO;AACvC,kBAAM,QAAQ,QAAQ;AAExB,iBAAM,MAAM;AACV,gBAAI,aAAa,GAAG,SAAS,GAAG,IAAI,IAAI,WAAW,aAAa;AAChE,gBAAI,WAAWH,OAAMG,QAAO,GAAG;AAC7B,qBAAM,GAAG,UAAU,GAAG,GAAG,SAAS,CAAC,EAAE;AACnC,mBAAG,IAAI,EAAE;AACX,kBAAI,GAAG,OAAQ,QAAO,GAAG;AACzB,kBAAIH,SAAQ,cAAc,QAAQ,OAAOG,QAAO,EAAG,QAAO;AAC1D,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAIA,YAAI,KAAK,EAAC,OAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM,IAAI,KAAI;AAC3D,iBAAS,OAAO;AACd,mBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,IAAK,IAAG,GAAG,KAAK,UAAU,CAAC,CAAC;AAAA,QACzE;AACA,iBAAS,OAAO;AACd,eAAK,MAAM,MAAM,SAAS;AAC1B,iBAAO;AAAA,QACT;AACA,iBAAS,OAAO,MAAM,MAAM;AAC1B,mBAAS,IAAI,MAAM,GAAG,IAAI,EAAE,KAAM,KAAI,EAAE,QAAQ,KAAM,QAAO;AAC7D,iBAAO;AAAA,QACT;AACA,iBAAS,SAAS,SAAS;AACzB,cAAI,QAAQ,GAAG;AACf,aAAG,SAAS;AACZ,cAAI,CAAC,WAAY;AACjB,cAAI,MAAM,SAAS;AACjB,gBAAI,MAAM,QAAQ,QAAQ,SAAS,MAAM,WAAW,MAAM,QAAQ,OAAO;AAEvE,kBAAI,aAAa,kBAAkB,SAAS,MAAM,OAAO;AACzD,kBAAI,cAAc,MAAM;AACtB,sBAAM,UAAU;AAChB;AAAA,cACF;AAAA,YACF,WAAW,CAAC,OAAO,SAAS,MAAM,SAAS,GAAG;AAC5C,oBAAM,YAAY,IAAI,IAAI,SAAS,MAAM,SAAS;AAClD;AAAA,YACF;AAAA,UACF;AAEA,cAAI,aAAa,cAAc,CAAC,OAAO,SAAS,MAAM,UAAU;AAC9D,kBAAM,aAAa,IAAI,IAAI,SAAS,MAAM,UAAU;AAAA,QACxD;AACA,iBAAS,kBAAkB,SAAS,SAAS;AAC3C,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,UACT,WAAW,QAAQ,OAAO;AACxB,gBAAI,QAAQ,kBAAkB,SAAS,QAAQ,IAAI;AACnD,gBAAI,CAAC,MAAO,QAAO;AACnB,gBAAI,SAAS,QAAQ,KAAM,QAAO;AAClC,mBAAO,IAAI,QAAQ,OAAO,QAAQ,MAAM,IAAI;AAAA,UAC9C,WAAW,OAAO,SAAS,QAAQ,IAAI,GAAG;AACxC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,IAAI,QAAQ,QAAQ,MAAM,IAAI,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAAA,UACxE;AAAA,QACF;AAEA,iBAAS,WAAW,MAAM;AACxB,iBAAO,QAAQ,YAAY,QAAQ,aAAa,QAAQ,eAAe,QAAQ,cAAc,QAAQ;AAAA,QACvG;AAIA,iBAAS,QAAQ,MAAM,MAAMC,QAAO;AAAE,eAAK,OAAO;AAAM,eAAK,OAAO;AAAM,eAAK,QAAQA;AAAA,QAAM;AAC7F,iBAAS,IAAI,MAAM,MAAM;AAAE,eAAK,OAAO;AAAM,eAAK,OAAO;AAAA,QAAK;AAE9D,YAAI,cAAc,IAAI,IAAI,QAAQ,IAAI,IAAI,aAAa,IAAI,CAAC;AAC5D,iBAAS,cAAc;AACrB,aAAG,MAAM,UAAU,IAAI,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,WAAW,KAAK;AAC1E,aAAG,MAAM,YAAY;AAAA,QACvB;AACA,iBAAS,mBAAmB;AAC1B,aAAG,MAAM,UAAU,IAAI,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,WAAW,IAAI;AACzE,aAAG,MAAM,YAAY;AAAA,QACvB;AACA,oBAAY,MAAM,iBAAiB,MAAM;AACzC,iBAAS,aAAa;AACpB,aAAG,MAAM,YAAY,GAAG,MAAM,QAAQ;AACtC,aAAG,MAAM,UAAU,GAAG,MAAM,QAAQ;AAAA,QACtC;AACA,mBAAW,MAAM;AACjB,iBAAS,QAAQJ,OAAM,MAAM;AAC3B,cAAI,SAAS,WAAW;AACtB,gBAAI,QAAQ,GAAG,OAAO,SAAS,MAAM;AACrC,gBAAI,MAAM,QAAQ,QAAQ,OAAQ,UAAS,MAAM,QAAQ;AAAA,gBACpD,UAAS,QAAQ,MAAM,SAAS,SAAS,MAAM,QAAQ,OAAO,MAAM,OAAO,QAAQ,MAAM;AAC5F,uBAAS,MAAM;AACjB,kBAAM,UAAU,IAAI,UAAU,QAAQ,GAAG,OAAO,OAAO,GAAGA,OAAM,MAAM,MAAM,SAAS,IAAI;AAAA,UAC3F;AACA,iBAAO,MAAM;AACb,iBAAO;AAAA,QACT;AACA,iBAAS,SAAS;AAChB,cAAI,QAAQ,GAAG;AACf,cAAI,MAAM,QAAQ,MAAM;AACtB,gBAAI,MAAM,QAAQ,QAAQ;AACxB,oBAAM,WAAW,MAAM,QAAQ;AACjC,kBAAM,UAAU,MAAM,QAAQ;AAAA,UAChC;AAAA,QACF;AACA,eAAO,MAAM;AAEb,iBAAS,OAAO,QAAQ;AACtB,mBAAS,IAAIA,OAAM;AACjB,gBAAIA,SAAQ,OAAQ,QAAO,KAAK;AAAA,qBACvB,UAAU,OAAOA,SAAQ,OAAOA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,KAAK;AAAA,gBAC5E,QAAO,KAAK,GAAG;AAAA,UACtB;AAAC;AACD,iBAAO;AAAA,QACT;AAEA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAIA,SAAQ,MAAO,QAAO,KAAK,QAAQ,UAAU,KAAK,GAAG,QAAQ,OAAO,GAAG,GAAG,MAAM;AACpF,cAAIA,SAAQ,YAAa,QAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,WAAW,MAAM;AAClF,cAAIA,SAAQ,YAAa,QAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,MAAM;AACvE,cAAIA,SAAQ,YAAa,QAAO,GAAG,OAAO,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,MAAM,GAAG,iBAAiB,OAAO,GAAG,GAAG,MAAM;AACrI,cAAIA,SAAQ,WAAY,QAAO,KAAK,OAAO,GAAG,CAAC;AAC/C,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,kBAAkB,OAAO,QAAQ,UAAU;AACtF,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,cAAIA,SAAQ,MAAM;AAChB,gBAAI,GAAG,MAAM,QAAQ,QAAQ,UAAU,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,SAAS,CAAC,KAAK;AAC5E,iBAAG,MAAM,GAAG,IAAI,EAAE;AACpB,mBAAO,KAAK,QAAQ,MAAM,GAAG,WAAW,WAAW,QAAQ,SAAS;AAAA,UACtE;AACA,cAAIA,SAAQ,WAAY,QAAO,KAAK,WAAW;AAC/C,cAAIA,SAAQ,MAAO,QAAO,KAAK,QAAQ,MAAM,GAAG,kBAAkB,SAAS,WAAW,YAAY,MAAM;AACxG,cAAIA,SAAQ,WAAY,QAAQ,SAAS,aAAc;AACrD,eAAG,SAAS;AACZ,mBAAO,KAAK,QAAQ,QAAQA,SAAQ,UAAUA,QAAO,KAAK,GAAG,WAAW,MAAM;AAAA,UAChF;AACA,cAAIA,SAAQ,YAAY;AACtB,gBAAI,QAAQ,SAAS,WAAW;AAC9B,iBAAG,SAAS;AACZ,qBAAO,KAAK,SAAS;AAAA,YACvB,WAAW,SAAS,SAAS,YAAY,SAAS,UAAU,SAAS,WAAW,GAAG,OAAO,MAAM,UAAU,KAAK,GAAG;AAChH,iBAAG,SAAS;AACZ,kBAAI,SAAS,OAAQ,QAAO,KAAK,OAAO;AAAA,uBAC/B,SAAS,OAAQ,QAAO,KAAK,UAAU,OAAO,UAAU,GAAG,UAAU,OAAO,GAAG,CAAC;AAAA,kBACpF,QAAO,KAAK,QAAQ,MAAM,GAAG,SAAS,OAAO,GAAG,GAAG,QAAQ,GAAG,GAAG,OAAO,QAAQ,MAAM;AAAA,YAC7F,WAAW,QAAQ,SAAS,aAAa;AACvC,iBAAG,SAAS;AACZ,qBAAO,KAAK,QAAQ,MAAM,GAAG,YAAY,WAAW,MAAM;AAAA,YAC5D,WAAW,QAAQ,SAAS,YAAY;AACtC,iBAAG,SAAS;AACZ,qBAAO,KAAK,SAAS;AAAA,YACvB,OAAO;AACL,qBAAO,KAAK,QAAQ,MAAM,GAAG,UAAU;AAAA,YACzC;AAAA,UACF;AACA,cAAIA,SAAQ,SAAU,QAAO;AAAA,YAAK,QAAQ,MAAM;AAAA,YAAG;AAAA,YAAW,OAAO,GAAG;AAAA,YAAG,QAAQ,KAAK,QAAQ;AAAA,YAAG;AAAA,YACjE;AAAA,YAAO;AAAA,YAAQ;AAAA,YAAQ;AAAA,UAAU;AACnE,cAAIA,SAAQ,OAAQ,QAAO,KAAK,YAAY,OAAO,GAAG,CAAC;AACvD,cAAIA,SAAQ,UAAW,QAAO,KAAK,OAAO,GAAG,CAAC;AAC9C,cAAIA,SAAQ,QAAS,QAAO,KAAK,QAAQ,MAAM,GAAG,aAAa,mBAAmB,WAAW,QAAQ,UAAU;AAC/G,cAAIA,SAAQ,SAAU,QAAO,KAAK,QAAQ,MAAM,GAAG,aAAa,MAAM;AACtE,cAAIA,SAAQ,SAAU,QAAO,KAAK,QAAQ,MAAM,GAAG,aAAa,MAAM;AACtE,cAAIA,SAAQ,QAAS,QAAO,KAAK,SAAS;AAC1C,cAAI,SAAS,IAAK,QAAO,KAAK,YAAY,SAAS;AACnD,iBAAO,KAAK,QAAQ,MAAM,GAAG,YAAY,OAAO,GAAG,GAAG,MAAM;AAAA,QAC9D;AACA,iBAAS,kBAAkBA,OAAM;AAC/B,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,OAAO,GAAG,CAAC;AAAA,QAClD;AACA,iBAAS,WAAWA,OAAM,OAAO;AAC/B,iBAAO,gBAAgBA,OAAM,OAAO,KAAK;AAAA,QAC3C;AACA,iBAAS,kBAAkBA,OAAM,OAAO;AACtC,iBAAO,gBAAgBA,OAAM,OAAO,IAAI;AAAA,QAC1C;AACA,iBAAS,UAAUA,OAAM;AACvB,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,iBAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,MAAM;AAAA,QAChE;AACA,iBAAS,gBAAgBA,OAAM,OAAO,SAAS;AAC7C,cAAI,GAAG,MAAM,cAAc,GAAG,OAAO,OAAO;AAC1C,gBAAI,OAAO,UAAU,mBAAmB;AACxC,gBAAIA,SAAQ,IAAK,QAAO,KAAK,aAAa,QAAQ,GAAG,GAAG,SAAS,QAAQ,GAAG,GAAG,QAAQ,OAAO,IAAI,GAAG,MAAM,UAAU;AAAA,qBAC5GA,SAAQ,WAAY,QAAO,KAAK,aAAa,SAAS,OAAO,IAAI,GAAG,MAAM,UAAU;AAAA,UAC/F;AAEA,cAAI,UAAU,UAAU,uBAAuB;AAC/C,cAAI,YAAY,eAAeA,KAAI,EAAG,QAAO,KAAK,OAAO;AACzD,cAAIA,SAAQ,WAAY,QAAO,KAAK,aAAa,OAAO;AACxD,cAAIA,SAAQ,WAAY,QAAQ,SAAS,aAAc;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,QAAQ,MAAM,GAAG,iBAAiB,MAAM;AAAA,UAAG;AACvI,cAAIA,SAAQ,eAAeA,SAAQ,QAAS,QAAO,KAAK,UAAU,oBAAoB,UAAU;AAChG,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,QAAQ,OAAO;AACxF,cAAIA,SAAQ,cAAcA,SAAQ,SAAU,QAAO,KAAK,UAAU,oBAAoB,UAAU;AAChG,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,cAAc,QAAQ,OAAO;AACxE,cAAIA,SAAQ,IAAK,QAAO,aAAa,SAAS,KAAK,MAAM,OAAO;AAChE,cAAIA,SAAQ,QAAS,QAAO,KAAK,OAAO,OAAO;AAC/C,cAAIA,SAAQ,MAAO,QAAO,KAAK,YAAY,OAAO,CAAC;AACnD,iBAAO,KAAK;AAAA,QACd;AACA,iBAAS,gBAAgBA,OAAM;AAC7B,cAAIA,MAAK,MAAM,YAAY,EAAG,QAAO,KAAK;AAC1C,iBAAO,KAAK,UAAU;AAAA,QACxB;AAEA,iBAAS,mBAAmBA,OAAM,OAAO;AACvC,cAAIA,SAAQ,IAAK,QAAO,KAAK,eAAe;AAC5C,iBAAO,qBAAqBA,OAAM,OAAO,KAAK;AAAA,QAChD;AACA,iBAAS,qBAAqBA,OAAM,OAAO,SAAS;AAClD,cAAI,KAAK,WAAW,QAAQ,qBAAqB;AACjD,cAAI,OAAO,WAAW,QAAQ,aAAa;AAC3C,cAAIA,SAAQ,KAAM,QAAO,KAAK,aAAa,UAAU,mBAAmB,WAAW,UAAU;AAC7F,cAAIA,SAAQ,YAAY;AACtB,gBAAI,UAAU,KAAK,KAAK,KAAK,QAAQ,SAAS,IAAK,QAAO,KAAK,EAAE;AACjE,gBAAI,QAAQ,SAAS,OAAO,GAAG,OAAO,MAAM,4BAA4B,KAAK;AAC3E,qBAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,EAAE;AAC/D,gBAAI,SAAS,IAAK,QAAO,KAAK,YAAY,OAAO,GAAG,GAAG,IAAI;AAC3D,mBAAO,KAAK,IAAI;AAAA,UAClB;AACA,cAAIA,SAAQ,SAAS;AAAE,mBAAO,KAAK,OAAO,EAAE;AAAA,UAAG;AAC/C,cAAIA,SAAQ,IAAK;AACjB,cAAIA,SAAQ,IAAK,QAAO,aAAa,mBAAmB,KAAK,QAAQ,EAAE;AACvE,cAAIA,SAAQ,IAAK,QAAO,KAAK,UAAU,EAAE;AACzC,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,iBAAiB,OAAO,GAAG,GAAG,QAAQ,EAAE;AACnF,cAAI,QAAQ,SAAS,MAAM;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,UAAU,EAAE;AAAA,UAAE;AAC9E,cAAIA,SAAQ,UAAU;AACpB,eAAG,MAAM,WAAW,GAAG,SAAS;AAChC,eAAG,OAAO,OAAO,GAAG,OAAO,MAAM,GAAG,OAAO,QAAQ,CAAC;AACpD,mBAAO,KAAK,IAAI;AAAA,UAClB;AAAA,QACF;AACA,iBAAS,MAAMA,OAAM,OAAO;AAC1B,cAAIA,SAAQ,QAAS,QAAO,KAAK;AACjC,cAAI,MAAM,MAAM,MAAM,SAAS,CAAC,KAAK,KAAM,QAAO,KAAK,KAAK;AAC5D,iBAAO,KAAK,iBAAiB,aAAa;AAAA,QAC5C;AACA,iBAAS,cAAcA,OAAM;AAC3B,cAAIA,SAAQ,KAAK;AACf,eAAG,SAAS;AACZ,eAAG,MAAM,WAAW;AACpB,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,iBAAS,UAAUA,OAAM;AACvB,uBAAa,GAAG,QAAQ,GAAG,KAAK;AAChC,iBAAO,KAAKA,SAAQ,MAAM,YAAY,UAAU;AAAA,QAClD;AACA,iBAAS,iBAAiBA,OAAM;AAC9B,uBAAa,GAAG,QAAQ,GAAG,KAAK;AAChC,iBAAO,KAAKA,SAAQ,MAAM,YAAY,iBAAiB;AAAA,QACzD;AACA,iBAAS,YAAY,SAAS;AAC5B,iBAAO,SAASA,OAAM;AACpB,gBAAIA,SAAQ,IAAK,QAAO,KAAK,UAAU,gBAAgB,MAAM;AAAA,qBACpDA,SAAQ,cAAc,KAAM,QAAO,KAAK,eAAe,UAAU,uBAAuB,kBAAkB;AAAA,gBAC9G,QAAO,KAAK,UAAU,oBAAoB,UAAU;AAAA,UAC3D;AAAA,QACF;AACA,iBAAS,OAAO,GAAG,OAAO;AACxB,cAAI,SAAS,UAAU;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,kBAAkB;AAAA,UAAG;AAAA,QACnF;AACA,iBAAS,cAAc,GAAG,OAAO;AAC/B,cAAI,SAAS,UAAU;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,oBAAoB;AAAA,UAAG;AAAA,QACrF;AACA,iBAAS,WAAWA,OAAM;AACxB,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,SAAS;AAC9C,iBAAO,KAAK,oBAAoB,OAAO,GAAG,GAAG,MAAM;AAAA,QACrD;AACA,iBAAS,SAASA,OAAM;AACtB,cAAIA,SAAQ,YAAY;AAAC,eAAG,SAAS;AAAY,mBAAO,KAAK;AAAA,UAAE;AAAA,QACjE;AACA,iBAAS,QAAQA,OAAM,OAAO;AAC5B,cAAIA,SAAQ,SAAS;AACnB,eAAG,SAAS;AACZ,mBAAO,KAAK,OAAO;AAAA,UACrB,WAAWA,SAAQ,cAAc,GAAG,SAAS,WAAW;AACtD,eAAG,SAAS;AACZ,gBAAI,SAAS,SAAS,SAAS,MAAO,QAAO,KAAK,YAAY;AAC9D,gBAAI;AACJ,gBAAI,QAAQ,GAAG,MAAM,cAAc,GAAG,OAAO,UAAU,IAAI,GAAG,OAAO,MAAM,YAAY,KAAK;AAC1F,iBAAG,MAAM,aAAa,GAAG,OAAO,MAAM,EAAE,CAAC,EAAE;AAC7C,mBAAO,KAAK,SAAS;AAAA,UACvB,WAAWA,SAAQ,YAAYA,SAAQ,UAAU;AAC/C,eAAG,SAAS,aAAa,aAAc,GAAG,QAAQ;AAClD,mBAAO,KAAK,SAAS;AAAA,UACvB,WAAWA,SAAQ,kBAAkB;AACnC,mBAAO,KAAK,SAAS;AAAA,UACvB,WAAW,QAAQ,WAAW,KAAK,GAAG;AACpC,eAAG,SAAS;AACZ,mBAAO,KAAK,OAAO;AAAA,UACrB,WAAWA,SAAQ,KAAK;AACtB,mBAAO,KAAK,YAAY,WAAW,OAAO,GAAG,GAAG,SAAS;AAAA,UAC3D,WAAWA,SAAQ,UAAU;AAC3B,mBAAO,KAAK,mBAAmB,SAAS;AAAA,UAC1C,WAAW,SAAS,KAAK;AACvB,eAAG,SAAS;AACZ,mBAAO,KAAK,OAAO;AAAA,UACrB,WAAWA,SAAQ,KAAK;AACtB,mBAAO,KAAK,SAAS;AAAA,UACvB;AAAA,QACF;AACA,iBAAS,aAAaA,OAAM;AAC1B,cAAIA,SAAQ,WAAY,QAAO,KAAK,SAAS;AAC7C,aAAG,SAAS;AACZ,iBAAO,KAAK,WAAW;AAAA,QACzB;AACA,iBAAS,UAAUA,OAAM;AACvB,cAAIA,SAAQ,IAAK,QAAO,KAAK,iBAAiB;AAC9C,cAAIA,SAAQ,IAAK,QAAO,KAAK,WAAW;AAAA,QAC1C;AACA,iBAAS,SAAS,MAAM,KAAK,KAAK;AAChC,mBAAS,QAAQA,OAAM,OAAO;AAC5B,gBAAI,MAAM,IAAI,QAAQA,KAAI,IAAI,KAAKA,SAAQ,KAAK;AAC9C,kBAAI,MAAM,GAAG,MAAM;AACnB,kBAAI,IAAI,QAAQ,OAAQ,KAAI,OAAO,IAAI,OAAO,KAAK;AACnD,qBAAO,KAAK,SAASA,OAAMK,QAAO;AAChC,oBAAIL,SAAQ,OAAOK,UAAS,IAAK,QAAO,KAAK;AAC7C,uBAAO,KAAK,IAAI;AAAA,cAClB,GAAG,OAAO;AAAA,YACZ;AACA,gBAAIL,SAAQ,OAAO,SAAS,IAAK,QAAO,KAAK;AAC7C,gBAAI,OAAO,IAAI,QAAQ,GAAG,IAAI,GAAI,QAAO,KAAK,IAAI;AAClD,mBAAO,KAAK,OAAO,GAAG,CAAC;AAAA,UACzB;AACA,iBAAO,SAASA,OAAM,OAAO;AAC3B,gBAAIA,SAAQ,OAAO,SAAS,IAAK,QAAO,KAAK;AAC7C,mBAAO,KAAK,MAAM,OAAO;AAAA,UAC3B;AAAA,QACF;AACA,iBAAS,aAAa,MAAM,KAAK,MAAM;AACrC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ;AACpC,eAAG,GAAG,KAAK,UAAU,CAAC,CAAC;AACzB,iBAAO,KAAK,QAAQ,KAAK,IAAI,GAAG,SAAS,MAAM,GAAG,GAAG,MAAM;AAAA,QAC7D;AACA,iBAAS,MAAMA,OAAM;AACnB,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,iBAAO,KAAK,WAAW,KAAK;AAAA,QAC9B;AACA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAI,MAAM;AACR,gBAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ;AACrC,gBAAI,SAAS,IAAK,QAAO,KAAK,SAAS;AAAA,UACzC;AAAA,QACF;AACA,iBAAS,cAAcA,OAAM,OAAO;AAClC,cAAI,SAASA,SAAQ,OAAO,SAAS,MAAO,QAAO,KAAK,QAAQ;AAAA,QAClE;AACA,iBAAS,aAAaA,OAAM;AAC1B,cAAI,QAAQA,SAAQ,KAAK;AACvB,gBAAI,GAAG,OAAO,MAAM,kBAAkB,KAAK,EAAG,QAAO,KAAK,YAAY,MAAM,QAAQ;AAAA,gBAC/E,QAAO,KAAK,QAAQ;AAAA,UAC3B;AAAA,QACF;AACA,iBAAS,KAAK,GAAG,OAAO;AACtB,cAAI,SAAS,MAAM;AACjB,eAAG,SAAS;AACZ,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AACA,iBAAS,SAASA,OAAM,OAAO;AAC7B,cAAI,SAAS,WAAW,SAAS,YAAY,SAAS,WAAW,SAAS,YAAY;AACpF,eAAG,SAAS;AACZ,mBAAO,KAAK,SAAS,WAAW,oBAAoB,QAAQ;AAAA,UAC9D;AACA,cAAIA,SAAQ,cAAc,SAAS,QAAQ;AACzC,eAAG,SAAS;AACZ,mBAAO,KAAK,SAAS;AAAA,UACvB;AACA,cAAI,SAAS,OAAO,SAAS,IAAK,QAAO,KAAK,QAAQ;AACtD,cAAIA,SAAQ,YAAYA,SAAQ,YAAYA,SAAQ,OAAQ,QAAO,KAAK,SAAS;AACjF,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,KAAK,GAAG,GAAG,QAAQ,SAAS;AAC1F,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,WAAW,QAAQ,SAAS;AACvE,cAAIA,SAAQ,IAAK,QAAO,KAAK,SAAS,SAAS,GAAG,GAAG,iBAAiB,SAAS;AAC/E,cAAIA,SAAQ,IAAK,QAAO,KAAK,SAAS,UAAU,GAAG,GAAG,QAAQ;AAC9D,cAAIA,SAAQ,SAAS;AAAE,mBAAO,KAAK,WAAW,SAAS;AAAA,UAAG;AAAA,QAC5D;AACA,iBAAS,gBAAgBA,OAAM;AAC7B,cAAIA,SAAQ,KAAM,QAAO,KAAK,QAAQ;AAAA,QACxC;AACA,iBAAS,UAAUA,OAAM;AACvB,cAAIA,MAAK,MAAM,UAAU,EAAG,QAAO,KAAK;AACxC,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,KAAK,SAAS;AACrD,iBAAO,KAAK,UAAU,SAAS;AAAA,QACjC;AACA,iBAAS,SAASA,OAAM,OAAO;AAC7B,cAAIA,SAAQ,cAAc,GAAG,SAAS,WAAW;AAC/C,eAAG,SAAS;AACZ,mBAAO,KAAK,QAAQ;AAAA,UACtB,WAAW,SAAS,OAAOA,SAAQ,YAAYA,SAAQ,UAAU;AAC/D,mBAAO,KAAK,QAAQ;AAAA,UACtB,WAAWA,SAAQ,KAAK;AACtB,mBAAO,KAAK,QAAQ;AAAA,UACtB,WAAWA,SAAQ,KAAK;AACtB,mBAAO,KAAK,OAAO,UAAU,GAAG,eAAe,OAAO,GAAG,GAAG,QAAQ;AAAA,UACtE,WAAWA,SAAQ,KAAK;AACtB,mBAAO,KAAK,cAAc,QAAQ;AAAA,UACpC,WAAW,CAACA,MAAK,MAAM,YAAY,GAAG;AACpC,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AACA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAIA,SAAQ,QAAS,QAAO,KAAK;AACjC,cAAI,MAAM,MAAM,MAAM,SAAS,CAAC,KAAK,KAAM,QAAO,KAAK,SAAS;AAChE,iBAAO,KAAK,UAAU,iBAAiB;AAAA,QACzC;AACA,iBAAS,kBAAkBA,OAAM;AAC/B,cAAIA,SAAQ,KAAK;AACf,eAAG,SAAS;AACZ,eAAG,MAAM,WAAW;AACpB,mBAAO,KAAK,SAAS;AAAA,UACvB;AAAA,QACF;AACA,iBAAS,QAAQA,OAAM,OAAO;AAC5B,cAAIA,SAAQ,cAAc,GAAG,OAAO,MAAM,YAAY,KAAK,KAAK,SAAS,IAAK,QAAO,KAAK,OAAO;AACjG,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ;AACrC,cAAIA,SAAQ,SAAU,QAAO,KAAK,OAAO;AACzC,iBAAO,KAAK,QAAQ;AAAA,QACtB;AACA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAI,SAAS,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,SAAS;AACtF,cAAI,SAAS,OAAOA,SAAQ,OAAO,SAAS,IAAK,QAAO,KAAK,QAAQ;AACrE,cAAIA,SAAQ,IAAK,QAAO,KAAK,UAAU,OAAO,GAAG,GAAG,SAAS;AAC7D,cAAI,SAAS,aAAa,SAAS,cAAc;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,QAAQ;AAAA,UAAE;AAChG,cAAI,SAAS,IAAK,QAAO,KAAK,UAAU,OAAO,GAAG,GAAG,QAAQ;AAAA,QAC/D;AACA,iBAAS,cAAc,GAAG,OAAO;AAC/B,cAAI,SAAS,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,UAAU,GAAG,GAAG,QAAQ,SAAS;AAAA,QACxF;AACA,iBAAS,YAAY;AACnB,iBAAO,KAAK,UAAU,gBAAgB;AAAA,QACxC;AACA,iBAAS,iBAAiB,GAAG,OAAO;AAClC,cAAI,SAAS,IAAK,QAAO,KAAK,QAAQ;AAAA,QACxC;AACA,iBAAS,OAAO,GAAG,OAAO;AACxB,cAAI,SAAS,QAAQ;AAAC,eAAG,SAAS;AAAW,mBAAO,KAAK,OAAO;AAAA,UAAC;AACjE,iBAAO,KAAK,SAAS,WAAW,aAAa,UAAU;AAAA,QACzD;AACA,iBAAS,QAAQA,OAAM,OAAO;AAC5B,cAAI,QAAQ,WAAW,KAAK,GAAG;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,OAAO;AAAA,UAAE;AAC7E,cAAIA,SAAQ,YAAY;AAAE,qBAAS,KAAK;AAAG,mBAAO,KAAK;AAAA,UAAG;AAC1D,cAAIA,SAAQ,SAAU,QAAO,KAAK,OAAO;AACzC,cAAIA,SAAQ,IAAK,QAAO,aAAa,YAAY,GAAG;AACpD,cAAIA,SAAQ,IAAK,QAAO,aAAa,aAAa,GAAG;AAAA,QACvD;AACA,iBAAS,YAAYA,OAAM,OAAO;AAChC,cAAIA,SAAQ,cAAc,CAAC,GAAG,OAAO,MAAM,SAAS,KAAK,GAAG;AAC1D,qBAAS,KAAK;AACd,mBAAO,KAAK,WAAW;AAAA,UACzB;AACA,cAAIA,SAAQ,WAAY,IAAG,SAAS;AACpC,cAAIA,SAAQ,SAAU,QAAO,KAAK,OAAO;AACzC,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,cAAIA,SAAQ,IAAK,QAAO,KAAK,YAAY,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,WAAW;AAC9E,iBAAO,KAAK,OAAO,GAAG,GAAG,SAAS,WAAW;AAAA,QAC/C;AACA,iBAAS,aAAa;AACpB,iBAAO,KAAK,SAAS,WAAW;AAAA,QAClC;AACA,iBAAS,YAAY,OAAO,OAAO;AACjC,cAAI,SAAS,IAAK,QAAO,KAAK,iBAAiB;AAAA,QACjD;AACA,iBAAS,WAAWA,OAAM;AACxB,cAAIA,SAAQ,IAAK,QAAO,KAAK,MAAM;AAAA,QACrC;AACA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAIA,SAAQ,eAAe,SAAS,OAAQ,QAAO,KAAK,QAAQ,QAAQ,MAAM,GAAG,WAAW,MAAM;AAAA,QACpG;AACA,iBAAS,QAAQA,OAAM,OAAO;AAC5B,cAAI,SAAS,QAAS,QAAO,KAAK,OAAO;AACzC,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,UAAU,MAAM;AAAA,QAC7D;AACA,iBAAS,SAASA,OAAM;AACtB,cAAIA,SAAQ,MAAO,QAAO,KAAK,QAAQ,QAAQ;AAC/C,cAAIA,SAAQ,WAAY,QAAO,KAAK,QAAQ;AAC5C,iBAAO,KAAK,QAAQ;AAAA,QACtB;AACA,iBAAS,SAASA,OAAM,OAAO;AAC7B,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ;AACrC,cAAI,SAAS,QAAQ,SAAS,MAAM;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,YAAY,QAAQ;AAAA,UAAE;AAC/F,iBAAO,KAAK,YAAY,QAAQ;AAAA,QAClC;AACA,iBAAS,YAAYA,OAAM,OAAO;AAChC,cAAI,SAAS,KAAK;AAAC,eAAG,SAAS;AAAW,mBAAO,KAAK,WAAW;AAAA,UAAE;AACnE,cAAIA,SAAQ,YAAY;AAAC,qBAAS,KAAK;AAAG,mBAAO,KAAK,WAAW;AAAA,UAAE;AACnE,cAAIA,SAAQ,IAAK,QAAO,KAAK,aAAa,QAAQ,GAAG,GAAG,SAAS,QAAQ,GAAG,GAAG,QAAQ,cAAc,WAAW,UAAU;AAC1H,cAAI,QAAQ,SAAS,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,QAAQ,WAAW;AAAA,QACnG;AACA,iBAAS,aAAaA,OAAM,OAAO;AACjC,cAAI,SAAS,KAAK;AAAC,eAAG,SAAS;AAAW,mBAAO,KAAK,YAAY;AAAA,UAAE;AACpE,cAAIA,SAAQ,YAAY;AAAC,qBAAS,KAAK;AAAG,mBAAO,KAAK,YAAY;AAAA,UAAE;AACpE,cAAIA,SAAQ,IAAK,QAAO,KAAK,aAAa,QAAQ,GAAG,GAAG,SAAS,QAAQ,GAAG,GAAG,QAAQ,cAAc,UAAU;AAC/G,cAAI,QAAQ,SAAS,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,QAAQ,YAAY;AAAA,QACpG;AACA,iBAAS,SAASA,OAAM,OAAO;AAC7B,cAAIA,SAAQ,aAAaA,SAAQ,YAAY;AAC3C,eAAG,SAAS;AACZ,mBAAO,KAAK,QAAQ;AAAA,UACtB,WAAW,SAAS,KAAK;AACvB,mBAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,MAAM;AAAA,UAC5D;AAAA,QACF;AACA,iBAAS,OAAOA,OAAM,OAAO;AAC3B,cAAI,SAAS,IAAK,MAAK,YAAY,MAAM;AACzC,cAAIA,SAAQ,SAAU,QAAO,KAAK,MAAM;AACxC,cAAI,QAAQ,WAAW,KAAK,GAAG;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,MAAM;AAAA,UAAG;AAC7E,cAAI,QAAQA,SAAQ,OAAQ,QAAO,KAAK,WAAW,WAAW;AAC9D,iBAAO,KAAK,SAAS,WAAW,WAAW;AAAA,QAC7C;AACA,iBAAS,gBAAgBA,OAAM,OAAO;AAEpC,cAAIA,SAAQ,WAAY,QAAO,UAAUA,OAAM,KAAK;AACpD,iBAAO,eAAeA,OAAM,KAAK;AAAA,QACnC;AACA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAIA,SAAQ,YAAY;AAAC,qBAAS,KAAK;AAAG,mBAAO,KAAK,cAAc;AAAA,UAAE;AAAA,QACxE;AACA,iBAAS,eAAeA,OAAM,OAAO;AACnC,cAAI,SAAS,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,SAAS,WAAW,GAAG,GAAG,QAAQ,cAAc;AAC5F,cAAI,SAAS,aAAa,SAAS,gBAAiB,QAAQA,SAAQ,KAAM;AACxE,gBAAI,SAAS,aAAc,IAAG,SAAS;AACvC,mBAAO,KAAK,OAAO,WAAW,YAAY,cAAc;AAAA,UAC1D;AACA,cAAIA,SAAQ,IAAK,QAAO,KAAK,QAAQ,GAAG,GAAG,WAAW,MAAM;AAAA,QAC9D;AACA,iBAAS,UAAUA,OAAM,OAAO;AAC9B,cAAIA,SAAQ,WACPA,SAAQ,eACP,SAAS,YAAY,SAAS,SAAS,SAAS,SAAU,QAAQ,WAAW,KAAK,MACnF,GAAG,OAAO,MAAM,0BAA0B,KAAK,GAAI;AACtD,eAAG,SAAS;AACZ,mBAAO,KAAK,SAAS;AAAA,UACvB;AACA,cAAIA,SAAQ,cAAc,GAAG,SAAS,WAAW;AAC/C,eAAG,SAAS;AACZ,mBAAO,KAAK,YAAY,SAAS;AAAA,UACnC;AACA,cAAIA,SAAQ,YAAYA,SAAQ,SAAU,QAAO,KAAK,YAAY,SAAS;AAC3E,cAAIA,SAAQ;AACV,mBAAO,KAAK,YAAY,WAAW,OAAO,GAAG,GAAG,YAAY,SAAS;AACvE,cAAI,SAAS,KAAK;AAChB,eAAG,SAAS;AACZ,mBAAO,KAAK,SAAS;AAAA,UACvB;AACA,cAAI,QAAQA,SAAQ,IAAK,QAAO,KAAK,cAAc,SAAS;AAC5D,cAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,KAAK,SAAS;AACrD,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,cAAI,SAAS,IAAK,QAAO,KAAK,YAAY,SAAS;AAAA,QACrD;AACA,iBAAS,WAAWA,OAAM,OAAO;AAC/B,cAAI,SAAS,IAAK,QAAO,KAAK,UAAU;AACxC,cAAI,SAAS,IAAK,QAAO,KAAK,UAAU;AACxC,cAAIA,SAAQ,IAAK,QAAO,KAAK,UAAU,WAAW;AAClD,cAAI,SAAS,IAAK,QAAO,KAAK,iBAAiB;AAC/C,cAAI,UAAU,GAAG,MAAM,QAAQ,MAAM,cAAc,WAAW,QAAQ,QAAQ;AAC9E,iBAAO,KAAK,cAAc,eAAe,WAAW;AAAA,QACtD;AACA,iBAAS,YAAYA,OAAM,OAAO;AAChC,cAAI,SAAS,KAAK;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,WAAW,OAAO,GAAG,CAAC;AAAA,UAAG;AAChF,cAAI,SAAS,WAAW;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,YAAY,OAAO,GAAG,CAAC;AAAA,UAAG;AACvF,cAAIA,SAAQ,IAAK,QAAO,KAAK,SAAS,aAAa,GAAG,GAAG,WAAW,OAAO,GAAG,CAAC;AAC/E,iBAAO,KAAK,SAAS;AAAA,QACvB;AACA,iBAAS,YAAYA,OAAM,OAAO;AAChC,cAAI,SAAS,MAAM;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,OAAO,UAAU,CAAC;AAAA,UAAG;AAC7E,cAAIA,SAAQ,WAAY,QAAO,KAAK,mBAAmB,WAAW;AAAA,QACpE;AACA,iBAAS,YAAYA,OAAM;AACzB,cAAIA,SAAQ,SAAU,QAAO,KAAK;AAClC,cAAIA,SAAQ,IAAK,QAAO,KAAK,UAAU;AACvC,cAAIA,SAAQ,IAAK,QAAO,KAAK,kBAAkB;AAC/C,iBAAO,KAAK,YAAY,kBAAkB,SAAS;AAAA,QACrD;AACA,iBAAS,WAAWA,OAAM,OAAO;AAC/B,cAAIA,SAAQ,IAAK,QAAO,aAAa,YAAY,GAAG;AACpD,cAAIA,SAAQ,WAAY,UAAS,KAAK;AACtC,cAAI,SAAS,IAAK,IAAG,SAAS;AAC9B,iBAAO,KAAK,OAAO;AAAA,QACrB;AACA,iBAAS,iBAAiBA,OAAM;AAC9B,cAAIA,SAAQ,IAAK,QAAO,KAAK,YAAY,gBAAgB;AAAA,QAC3D;AACA,iBAAS,QAAQ,OAAO,OAAO;AAC7B,cAAI,SAAS,MAAM;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,UAAU;AAAA,UAAG;AAAA,QACvE;AACA,iBAAS,UAAU,OAAO,OAAO;AAC/B,cAAI,SAAS,QAAQ;AAAE,eAAG,SAAS;AAAW,mBAAO,KAAK,UAAU;AAAA,UAAG;AAAA,QACzE;AACA,iBAAS,aAAaA,OAAM;AAC1B,cAAIA,SAAQ,IAAK,QAAO,KAAK;AAC7B,iBAAO,KAAK,SAAS,mBAAmB,GAAG,CAAC;AAAA,QAC9C;AACA,iBAAS,UAAU;AACjB,iBAAO,KAAK,QAAQ,MAAM,GAAG,SAAS,OAAO,GAAG,GAAG,QAAQ,GAAG,GAAG,SAAS,YAAY,GAAG,GAAG,QAAQ,MAAM;AAAA,QAC5G;AACA,iBAAS,aAAa;AACpB,iBAAO,KAAK,SAAS,WAAW;AAAA,QAClC;AAEA,iBAAS,qBAAqB,OAAO,WAAW;AAC9C,iBAAO,MAAM,YAAY,cAAc,MAAM,YAAY,OACvD,eAAe,KAAK,UAAU,OAAO,CAAC,CAAC,KACvC,OAAO,KAAK,UAAU,OAAO,CAAC,CAAC;AAAA,QACnC;AAEA,iBAAS,kBAAkB,QAAQ,OAAO,QAAQ;AAChD,iBAAO,MAAM,YAAY,aACvB,iFAAiF,KAAK,MAAM,QAAQ,KACnG,MAAM,YAAY,WAAW,SAAS,KAAK,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,UAAU,EAAE,CAAC;AAAA,QAClG;AAIA,eAAO;AAAA,UACL,YAAY,SAAS,YAAY;AAC/B,gBAAI,QAAQ;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,IAAI,CAAC;AAAA,cACL,SAAS,IAAI,WAAW,cAAc,KAAK,YAAY,GAAG,SAAS,KAAK;AAAA,cACxE,WAAW,aAAa;AAAA,cACxB,SAAS,aAAa,aAAa,IAAI,QAAQ,MAAM,MAAM,KAAK;AAAA,cAChE,UAAU,cAAc;AAAA,YAC1B;AACA,gBAAI,aAAa,cAAc,OAAO,aAAa,cAAc;AAC/D,oBAAM,aAAa,aAAa;AAClC,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,OAAO,IAAI,GAAG;AAChB,kBAAI,CAAC,MAAM,QAAQ,eAAe,OAAO;AACvC,sBAAM,QAAQ,QAAQ;AACxB,oBAAM,WAAW,OAAO,YAAY;AACpC,2BAAa,QAAQ,KAAK;AAAA,YAC5B;AACA,gBAAI,MAAM,YAAY,gBAAgB,OAAO,SAAS,EAAG,QAAO;AAChE,gBAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,gBAAI,QAAQ,UAAW,QAAO;AAC9B,kBAAM,WAAW,QAAQ,eAAe,WAAW,QAAQ,WAAW,QAAQ,WAAW;AACzF,mBAAO,QAAQ,OAAO,OAAO,MAAM,SAAS,MAAM;AAAA,UACpD;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW;AACjC,gBAAI,MAAM,YAAY,gBAAgB,MAAM,YAAY,WAAY,QAAOD,YAAW;AACtF,gBAAI,MAAM,YAAY,UAAW,QAAO;AACxC,gBAAI,YAAY,aAAa,UAAU,OAAO,CAAC,GAAG,UAAU,MAAM,SAAS;AAE3E,gBAAI,CAAC,aAAa,KAAK,SAAS,EAAG,UAAS,IAAI,MAAM,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAChF,kBAAI,IAAI,MAAM,GAAG,CAAC;AAClB,kBAAI,KAAK,OAAQ,WAAU,QAAQ;AAAA,uBAC1B,KAAK,aAAa,KAAK,WAAY;AAAA,YAC9C;AACA,oBAAQ,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,YAC1C,aAAa,QAAS,MAAM,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC,OAClC,OAAO,sBAAsB,OAAO,yBACrC,CAAC,mBAAmB,KAAK,SAAS;AAC7D,wBAAU,QAAQ;AACpB,gBAAI,mBAAmB,QAAQ,QAAQ,OAAO,QAAQ,KAAK,QAAQ;AACjE,wBAAU,QAAQ;AACpB,gBAAIC,QAAO,QAAQ,MAAM,UAAU,aAAaA;AAEhD,gBAAIA,SAAQ,SAAU,QAAO,QAAQ,YAAY,MAAM,YAAY,cAAc,MAAM,YAAY,MAAM,QAAQ,KAAK,SAAS,IAAI;AAAA,qBAC1HA,SAAQ,UAAU,aAAa,IAAK,QAAO,QAAQ;AAAA,qBACnDA,SAAQ,OAAQ,QAAO,QAAQ,WAAW;AAAA,qBAC1CA,SAAQ;AACf,qBAAO,QAAQ,YAAY,qBAAqB,OAAO,SAAS,IAAI,mBAAmB,aAAa;AAAA,qBAC7F,QAAQ,QAAQ,YAAY,CAAC,WAAW,aAAa,sBAAsB;AAClF,qBAAO,QAAQ,YAAY,sBAAsB,KAAK,SAAS,IAAI,aAAa,IAAI;AAAA,qBAC7E,QAAQ,MAAO,QAAO,QAAQ,UAAU,UAAU,IAAI;AAAA,gBAC1D,QAAO,QAAQ,YAAY,UAAU,IAAI;AAAA,UAChD;AAAA,UAEA,eAAe;AAAA,UACf,mBAAmB,WAAW,OAAO;AAAA,UACrC,iBAAiB,WAAW,OAAO;AAAA,UACnC,sBAAsB,WAAW,OAAO;AAAA,UACxC,aAAa,WAAW,OAAO;AAAA,UAC/B,MAAM;AAAA,UACN,eAAe;AAAA,UAEf,YAAY,WAAW,SAAS;AAAA,UAChC;AAAA,UACA;AAAA,UAEA;AAAA,UAEA,gBAAgB,SAAS,OAAO;AAC9B,oBAAQ,OAAO,QAAQ,QAAQ,QAAQ,IAAID,YAAW,aAAa,IAAI,GAAG,IAAI,CAAC;AAAA,UACjF;AAAA,QACF;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,eAAe,aAAa,cAAc,OAAO;AAE5D,MAAAA,YAAW,WAAW,mBAAmB,YAAY;AACrD,MAAAA,YAAW,WAAW,mBAAmB,YAAY;AACrD,MAAAA,YAAW,WAAW,0BAA0B,YAAY;AAC5D,MAAAA,YAAW,WAAW,4BAA4B,YAAY;AAC9D,MAAAA,YAAW,WAAW,0BAA0B,YAAY;AAC5D,MAAAA,YAAW,WAAW,oBAAoB,EAAE,MAAM,cAAc,MAAM,KAAK,CAAC;AAC5E,MAAAA,YAAW,WAAW,sBAAsB,EAAE,MAAM,cAAc,MAAM,KAAK,CAAC;AAC9E,MAAAA,YAAW,WAAW,6BAA6B,EAAE,MAAM,cAAc,MAAM,KAAK,CAAC;AACrF,MAAAA,YAAW,WAAW,uBAAuB,EAAE,MAAM,cAAc,QAAQ,KAAK,CAAC;AACjF,MAAAA,YAAW,WAAW,mBAAmB,EAAE,MAAM,cAAc,YAAY,KAAK,CAAC;AACjF,MAAAA,YAAW,WAAW,0BAA0B,EAAE,MAAM,cAAc,YAAY,KAAK,CAAC;AAAA,IAExF,CAAC;AAAA;AAAA;", "names": ["import_dist", "CodeMirror", "type", "cont", "cx", "content", "block", "value"]}