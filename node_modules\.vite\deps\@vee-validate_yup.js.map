{"version": 3, "sources": ["../../@vee-validate/yup/dist/vee-validate-yup.mjs"], "sourcesContent": ["/**\n  * vee-validate v4.15.1\n  * (c) 2025 <PERSON><PERSON><PERSON><PERSON>\n  * @license MIT\n  */\nimport { isNotNestedPath, cleanupNonNestedPath } from 'vee-validate';\n\nconst isObject = (obj) => obj !== null && !!obj && typeof obj === 'object' && !Array.isArray(obj);\nfunction isIndex(value) {\n    return Number(value) >= 0;\n}\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n// Reference: https://github.com/lodash/lodash/blob/master/isPlainObject.js\nfunction isPlainObject(value) {\n    if (!isObjectLike(value) || getTag(value) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(value) === null) {\n        return true;\n    }\n    let proto = value;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(value) === proto;\n}\nfunction merge(target, source) {\n    Object.keys(source).forEach(key => {\n        if (isPlainObject(source[key]) && isPlainObject(target[key])) {\n            if (!target[key]) {\n                target[key] = {};\n            }\n            merge(target[key], source[key]);\n            return;\n        }\n        target[key] = source[key];\n    });\n    return target;\n}\n\nfunction toTypedSchema(yupSchema, opts = { abortEarly: false }) {\n    const schema = {\n        __type: 'VVTypedSchema',\n        async parse(values) {\n            var _a;\n            try {\n                // we spread the options because yup mutates the opts object passed\n                const output = await yupSchema.validate(values, Object.assign({}, opts));\n                return {\n                    value: output,\n                    errors: [],\n                };\n            }\n            catch (err) {\n                const error = err;\n                // Yup errors have a name prop one them.\n                // https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n                if (error.name !== 'ValidationError') {\n                    throw err;\n                }\n                if (!((_a = error.inner) === null || _a === void 0 ? void 0 : _a.length) && error.errors.length) {\n                    return { errors: [{ path: error.path, errors: error.errors }] };\n                }\n                const errors = error.inner.reduce((acc, curr) => {\n                    const path = curr.path || '';\n                    if (!acc[path]) {\n                        acc[path] = { errors: [], path };\n                    }\n                    acc[path].errors.push(...curr.errors);\n                    return acc;\n                }, {});\n                // list of aggregated errors\n                return { errors: Object.values(errors) };\n            }\n        },\n        cast(values) {\n            try {\n                return yupSchema.cast(values);\n            }\n            catch (_a) {\n                const defaults = yupSchema.getDefault();\n                if (isObject(defaults) && isObject(values)) {\n                    return merge(defaults, values);\n                }\n                return values;\n            }\n        },\n        describe(path) {\n            try {\n                if (!path) {\n                    return getDescriptionFromYupSpec(yupSchema.spec);\n                }\n                const description = getSpecForPath(path, yupSchema);\n                if (!description) {\n                    return {\n                        required: false,\n                        exists: false,\n                    };\n                }\n                return getDescriptionFromYupSpec(description);\n            }\n            catch (_a) {\n                if ((process.env.NODE_ENV !== 'production')) {\n                    // eslint-disable-next-line no-console\n                    console.warn(`Failed to describe path ${path} on the schema, returning a default description.`);\n                }\n                return {\n                    required: false,\n                    exists: false,\n                };\n            }\n        },\n    };\n    return schema;\n}\nfunction getDescriptionFromYupSpec(spec) {\n    return {\n        required: !spec.optional,\n        exists: true,\n    };\n}\nfunction getSpecForPath(path, schema) {\n    if (!isObjectSchema(schema)) {\n        return null;\n    }\n    if (isNotNestedPath(path)) {\n        const field = schema.fields[cleanupNonNestedPath(path)];\n        return (field === null || field === void 0 ? void 0 : field.spec) || null;\n    }\n    const paths = (path || '').split(/\\.|\\[(\\d+)\\]/).filter(Boolean);\n    let currentSchema = schema;\n    for (let i = 0; i < paths.length; i++) {\n        const p = paths[i];\n        if (isObjectSchema(currentSchema) && p in currentSchema.fields) {\n            currentSchema = currentSchema.fields[p];\n        }\n        else if (isTupleSchema(currentSchema) && isIndex(p)) {\n            currentSchema = currentSchema.spec.types[Number(p)];\n        }\n        else if (isIndex(p) && isArraySchema(currentSchema)) {\n            currentSchema = currentSchema.innerType;\n        }\n        if (i === paths.length - 1) {\n            return currentSchema.spec;\n        }\n    }\n    return null;\n}\nfunction isTupleSchema(schema) {\n    return isObject(schema) && schema.type === 'tuple';\n}\nfunction isObjectSchema(schema) {\n    return isObject(schema) && schema.type === 'object';\n}\nfunction isArraySchema(schema) {\n    return isObject(schema) && schema.type === 'array';\n}\n\nexport { toTypedSchema };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAOA,IAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,CAAC,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG;AAChG,SAAS,QAAQ,OAAO;AACpB,SAAO,OAAO,KAAK,KAAK;AAC5B;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,SAAS,OAAO,OAAO;AACnB,MAAI,SAAS,MAAM;AACf,WAAO,UAAU,SAAY,uBAAuB;AAAA,EACxD;AACA,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK;AAC/C;AAEA,SAAS,cAAc,OAAO;AAC1B,MAAI,CAAC,aAAa,KAAK,KAAK,OAAO,KAAK,MAAM,mBAAmB;AAC7D,WAAO;AAAA,EACX;AACA,MAAI,OAAO,eAAe,KAAK,MAAM,MAAM;AACvC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC1C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACvC;AACA,SAAO,OAAO,eAAe,KAAK,MAAM;AAC5C;AACA,SAAS,MAAM,QAAQ,QAAQ;AAC3B,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAO;AAC/B,QAAI,cAAc,OAAO,GAAG,CAAC,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AAC1D,UAAI,CAAC,OAAO,GAAG,GAAG;AACd,eAAO,GAAG,IAAI,CAAC;AAAA,MACnB;AACA,YAAM,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAC9B;AAAA,IACJ;AACA,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC5B,CAAC;AACD,SAAO;AACX;AAEA,SAAS,cAAc,WAAW,OAAO,EAAE,YAAY,MAAM,GAAG;AAC5D,QAAM,SAAS;AAAA,IACX,QAAQ;AAAA,IACR,MAAM,MAAM,QAAQ;AAChB,UAAI;AACJ,UAAI;AAEA,cAAM,SAAS,MAAM,UAAU,SAAS,QAAQ,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC;AACvE,eAAO;AAAA,UACH,OAAO;AAAA,UACP,QAAQ,CAAC;AAAA,QACb;AAAA,MACJ,SACO,KAAK;AACR,cAAM,QAAQ;AAGd,YAAI,MAAM,SAAS,mBAAmB;AAClC,gBAAM;AAAA,QACV;AACA,YAAI,GAAG,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,MAAM,OAAO,QAAQ;AAC7F,iBAAO,EAAE,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE;AAAA,QAClE;AACA,cAAM,SAAS,MAAM,MAAM,OAAO,CAAC,KAAK,SAAS;AAC7C,gBAAM,OAAO,KAAK,QAAQ;AAC1B,cAAI,CAAC,IAAI,IAAI,GAAG;AACZ,gBAAI,IAAI,IAAI,EAAE,QAAQ,CAAC,GAAG,KAAK;AAAA,UACnC;AACA,cAAI,IAAI,EAAE,OAAO,KAAK,GAAG,KAAK,MAAM;AACpC,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AAEL,eAAO,EAAE,QAAQ,OAAO,OAAO,MAAM,EAAE;AAAA,MAC3C;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,UAAI;AACA,eAAO,UAAU,KAAK,MAAM;AAAA,MAChC,SACO,IAAI;AACP,cAAM,WAAW,UAAU,WAAW;AACtC,YAAI,SAAS,QAAQ,KAAK,SAAS,MAAM,GAAG;AACxC,iBAAO,MAAM,UAAU,MAAM;AAAA,QACjC;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,SAAS,MAAM;AACX,UAAI;AACA,YAAI,CAAC,MAAM;AACP,iBAAO,0BAA0B,UAAU,IAAI;AAAA,QACnD;AACA,cAAM,cAAc,eAAe,MAAM,SAAS;AAClD,YAAI,CAAC,aAAa;AACd,iBAAO;AAAA,YACH,UAAU;AAAA,YACV,QAAQ;AAAA,UACZ;AAAA,QACJ;AACA,eAAO,0BAA0B,WAAW;AAAA,MAChD,SACO,IAAI;AACP,YAAK,QAAQ,IAAI,aAAa,cAAe;AAEzC,kBAAQ,KAAK,2BAA2B,IAAI,kDAAkD;AAAA,QAClG;AACA,eAAO;AAAA,UACH,UAAU;AAAA,UACV,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,0BAA0B,MAAM;AACrC,SAAO;AAAA,IACH,UAAU,CAAC,KAAK;AAAA,IAChB,QAAQ;AAAA,EACZ;AACJ;AACA,SAAS,eAAe,MAAM,QAAQ;AAClC,MAAI,CAAC,eAAe,MAAM,GAAG;AACzB,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB,IAAI,GAAG;AACvB,UAAM,QAAQ,OAAO,OAAO,qBAAqB,IAAI,CAAC;AACtD,YAAQ,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS;AAAA,EACzE;AACA,QAAM,SAAS,QAAQ,IAAI,MAAM,cAAc,EAAE,OAAO,OAAO;AAC/D,MAAI,gBAAgB;AACpB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,IAAI,MAAM,CAAC;AACjB,QAAI,eAAe,aAAa,KAAK,KAAK,cAAc,QAAQ;AAC5D,sBAAgB,cAAc,OAAO,CAAC;AAAA,IAC1C,WACS,cAAc,aAAa,KAAK,QAAQ,CAAC,GAAG;AACjD,sBAAgB,cAAc,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,IACtD,WACS,QAAQ,CAAC,KAAK,cAAc,aAAa,GAAG;AACjD,sBAAgB,cAAc;AAAA,IAClC;AACA,QAAI,MAAM,MAAM,SAAS,GAAG;AACxB,aAAO,cAAc;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ;AAC3B,SAAO,SAAS,MAAM,KAAK,OAAO,SAAS;AAC/C;AACA,SAAS,eAAe,QAAQ;AAC5B,SAAO,SAAS,MAAM,KAAK,OAAO,SAAS;AAC/C;AACA,SAAS,cAAc,QAAQ;AAC3B,SAAO,SAAS,MAAM,KAAK,OAAO,SAAS;AAC/C;", "names": ["import_dist"]}