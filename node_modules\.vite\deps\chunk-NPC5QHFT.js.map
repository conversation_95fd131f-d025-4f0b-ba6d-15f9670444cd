{"version": 3, "sources": ["../../@mermaid-js/parser/dist/chunks/mermaid-parser.core/chunk-EXZZNE6F.mjs"], "sourcesContent": ["import {\n  AbstractMermaidTokenBuilder,\n  CommonValueConverter,\n  InfoGeneratedModule,\n  MermaidGeneratedSharedModule,\n  __name\n} from \"./chunk-7PKI6E2E.mjs\";\n\n// src/language/info/module.ts\nimport {\n  EmptyFileSystem,\n  createDefaultCoreModule,\n  createDefaultSharedCoreModule,\n  inject\n} from \"langium\";\n\n// src/language/info/tokenBuilder.ts\nvar InfoTokenBuilder = class extends AbstractMermaidTokenBuilder {\n  static {\n    __name(this, \"InfoTokenBuilder\");\n  }\n  constructor() {\n    super([\"info\", \"showInfo\"]);\n  }\n};\n\n// src/language/info/module.ts\nvar InfoModule = {\n  parser: {\n    TokenBuilder: /* @__PURE__ */ __name(() => new InfoTokenBuilder(), \"TokenBuilder\"),\n    ValueConverter: /* @__PURE__ */ __name(() => new CommonValueConverter(), \"ValueConverter\")\n  }\n};\nfunction createInfoServices(context = EmptyFileSystem) {\n  const shared = inject(\n    createDefaultSharedCoreModule(context),\n    MermaidGeneratedSharedModule\n  );\n  const Info = inject(\n    createDefaultCoreModule({ shared }),\n    InfoGeneratedModule,\n    InfoModule\n  );\n  shared.ServiceRegistry.register(Info);\n  return { shared, Info };\n}\n__name(createInfoServices, \"createInfoServices\");\n\nexport {\n  InfoModule,\n  createInfoServices\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA;AAiBA,IAAI,oBAAmB,mBAAc,4BAA4B;AAAA,EAI/D,cAAc;AACZ,UAAM,CAAC,QAAQ,UAAU,CAAC;AAAA,EAC5B;AACF,GALI,OAAO,IAAM,kBAAkB,GAFZ;AAUvB,IAAI,aAAa;AAAA,EACf,QAAQ;AAAA,IACN,cAA8B,OAAO,MAAM,IAAI,iBAAiB,GAAG,cAAc;AAAA,IACjF,gBAAgC,OAAO,MAAM,IAAI,qBAAqB,GAAG,gBAAgB;AAAA,EAC3F;AACF;AACA,SAAS,mBAAmB,UAAU,iBAAiB;AACrD,QAAM,SAAS;AAAA,IACb,8BAA8B,OAAO;AAAA,IACrC;AAAA,EACF;AACA,QAAM,OAAO;AAAA,IACX,wBAAwB,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACA,SAAO,gBAAgB,SAAS,IAAI;AACpC,SAAO,EAAE,QAAQ,KAAK;AACxB;AACA,OAAO,oBAAoB,oBAAoB;", "names": ["import_dist"]}