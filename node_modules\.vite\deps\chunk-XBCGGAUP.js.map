{"version": 3, "sources": ["../../@braintree/sanitize-url/dist/constants.js", "../../@braintree/sanitize-url/dist/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BLANK_URL = exports.relativeFirstCharacters = exports.whitespaceEscapeCharsRegex = exports.urlSchemeRegex = exports.ctrlCharactersRegex = exports.htmlCtrlEntityRegex = exports.htmlEntitiesRegex = exports.invalidProtocolRegex = void 0;\nexports.invalidProtocolRegex = /^([^\\w]*)(javascript|data|vbscript)/im;\nexports.htmlEntitiesRegex = /&#(\\w+)(^\\w|;)?/g;\nexports.htmlCtrlEntityRegex = /&(newline|tab);/gi;\nexports.ctrlCharactersRegex = /[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u200D\\uFEFF]/gim;\nexports.urlSchemeRegex = /^.+(:|&colon;)/gim;\nexports.whitespaceEscapeCharsRegex = /(\\\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;\nexports.relativeFirstCharacters = [\".\", \"/\"];\nexports.BLANK_URL = \"about:blank\";\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sanitizeUrl = void 0;\nvar constants_1 = require(\"./constants\");\nfunction isRelativeUrlWithoutProtocol(url) {\n    return constants_1.relativeFirstCharacters.indexOf(url[0]) > -1;\n}\nfunction decodeHtmlCharacters(str) {\n    var removedNullByte = str.replace(constants_1.ctrlCharactersRegex, \"\");\n    return removedNullByte.replace(constants_1.htmlEntitiesRegex, function (match, dec) {\n        return String.fromCharCode(dec);\n    });\n}\nfunction isValidUrl(url) {\n    return URL.canParse(url);\n}\nfunction decodeURI(uri) {\n    try {\n        return decodeURIComponent(uri);\n    }\n    catch (e) {\n        // Ignoring error\n        // It is possible that the URI contains a `%` not associated\n        // with URI/URL-encoding.\n        return uri;\n    }\n}\nfunction sanitizeUrl(url) {\n    if (!url) {\n        return constants_1.BLANK_URL;\n    }\n    var charsToDecode;\n    var decodedUrl = decodeURI(url.trim());\n    do {\n        decodedUrl = decodeHtmlCharacters(decodedUrl)\n            .replace(constants_1.htmlCtrlEntityRegex, \"\")\n            .replace(constants_1.ctrlCharactersRegex, \"\")\n            .replace(constants_1.whitespaceEscapeCharsRegex, \"\")\n            .trim();\n        decodedUrl = decodeURI(decodedUrl);\n        charsToDecode =\n            decodedUrl.match(constants_1.ctrlCharactersRegex) ||\n                decodedUrl.match(constants_1.htmlEntitiesRegex) ||\n                decodedUrl.match(constants_1.htmlCtrlEntityRegex) ||\n                decodedUrl.match(constants_1.whitespaceEscapeCharsRegex);\n    } while (charsToDecode && charsToDecode.length > 0);\n    var sanitizedUrl = decodedUrl;\n    if (!sanitizedUrl) {\n        return constants_1.BLANK_URL;\n    }\n    if (isRelativeUrlWithoutProtocol(sanitizedUrl)) {\n        return sanitizedUrl;\n    }\n    // Remove any leading whitespace before checking the URL scheme\n    var trimmedUrl = sanitizedUrl.trimStart();\n    var urlSchemeParseResults = trimmedUrl.match(constants_1.urlSchemeRegex);\n    if (!urlSchemeParseResults) {\n        return sanitizedUrl;\n    }\n    var urlScheme = urlSchemeParseResults[0].toLowerCase().trim();\n    if (constants_1.invalidProtocolRegex.test(urlScheme)) {\n        return constants_1.BLANK_URL;\n    }\n    var backSanitized = trimmedUrl.replace(/\\\\/g, \"/\");\n    // Handle special cases for mailto: and custom deep-link protocols\n    if (urlScheme === \"mailto:\" || urlScheme.includes(\"://\")) {\n        return backSanitized;\n    }\n    // For http and https URLs, perform additional validation\n    if (urlScheme === \"http:\" || urlScheme === \"https:\") {\n        if (!isValidUrl(backSanitized)) {\n            return constants_1.BLANK_URL;\n        }\n        var url_1 = new URL(backSanitized);\n        url_1.protocol = url_1.protocol.toLowerCase();\n        url_1.hostname = url_1.hostname.toLowerCase();\n        return url_1.toString();\n    }\n    return backSanitized;\n}\nexports.sanitizeUrl = sanitizeUrl;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,YAAY,QAAQ,0BAA0B,QAAQ,6BAA6B,QAAQ,iBAAiB,QAAQ,sBAAsB,QAAQ,sBAAsB,QAAQ,oBAAoB,QAAQ,uBAAuB;AAC3O,YAAQ,uBAAuB;AAC/B,YAAQ,oBAAoB;AAC5B,YAAQ,sBAAsB;AAC9B,YAAQ,sBAAsB;AAC9B,YAAQ,iBAAiB;AACzB,YAAQ,6BAA6B;AACrC,YAAQ,0BAA0B,CAAC,KAAK,GAAG;AAC3C,YAAQ,YAAY;AAAA;AAAA;;;ACVpB,IAAAC,gBAAA;AAAA;AAAA;AAAA;AAAA,QAAAC,eAAA;AAAA,QAAAA,eAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc;AACtB,QAAI,cAAc;AAClB,aAAS,6BAA6B,KAAK;AACvC,aAAO,YAAY,wBAAwB,QAAQ,IAAI,CAAC,CAAC,IAAI;AAAA,IACjE;AACA,aAAS,qBAAqB,KAAK;AAC/B,UAAI,kBAAkB,IAAI,QAAQ,YAAY,qBAAqB,EAAE;AACrE,aAAO,gBAAgB,QAAQ,YAAY,mBAAmB,SAAU,OAAO,KAAK;AAChF,eAAO,OAAO,aAAa,GAAG;AAAA,MAClC,CAAC;AAAA,IACL;AACA,aAAS,WAAW,KAAK;AACrB,aAAO,IAAI,SAAS,GAAG;AAAA,IAC3B;AACA,aAAS,UAAU,KAAK;AACpB,UAAI;AACA,eAAO,mBAAmB,GAAG;AAAA,MACjC,SACO,GAAG;AAIN,eAAO;AAAA,MACX;AAAA,IACJ;AACA,aAAS,YAAY,KAAK;AACtB,UAAI,CAAC,KAAK;AACN,eAAO,YAAY;AAAA,MACvB;AACA,UAAI;AACJ,UAAI,aAAa,UAAU,IAAI,KAAK,CAAC;AACrC,SAAG;AACC,qBAAa,qBAAqB,UAAU,EACvC,QAAQ,YAAY,qBAAqB,EAAE,EAC3C,QAAQ,YAAY,qBAAqB,EAAE,EAC3C,QAAQ,YAAY,4BAA4B,EAAE,EAClD,KAAK;AACV,qBAAa,UAAU,UAAU;AACjC,wBACI,WAAW,MAAM,YAAY,mBAAmB,KAC5C,WAAW,MAAM,YAAY,iBAAiB,KAC9C,WAAW,MAAM,YAAY,mBAAmB,KAChD,WAAW,MAAM,YAAY,0BAA0B;AAAA,MACnE,SAAS,iBAAiB,cAAc,SAAS;AACjD,UAAI,eAAe;AACnB,UAAI,CAAC,cAAc;AACf,eAAO,YAAY;AAAA,MACvB;AACA,UAAI,6BAA6B,YAAY,GAAG;AAC5C,eAAO;AAAA,MACX;AAEA,UAAI,aAAa,aAAa,UAAU;AACxC,UAAI,wBAAwB,WAAW,MAAM,YAAY,cAAc;AACvE,UAAI,CAAC,uBAAuB;AACxB,eAAO;AAAA,MACX;AACA,UAAI,YAAY,sBAAsB,CAAC,EAAE,YAAY,EAAE,KAAK;AAC5D,UAAI,YAAY,qBAAqB,KAAK,SAAS,GAAG;AAClD,eAAO,YAAY;AAAA,MACvB;AACA,UAAI,gBAAgB,WAAW,QAAQ,OAAO,GAAG;AAEjD,UAAI,cAAc,aAAa,UAAU,SAAS,KAAK,GAAG;AACtD,eAAO;AAAA,MACX;AAEA,UAAI,cAAc,WAAW,cAAc,UAAU;AACjD,YAAI,CAAC,WAAW,aAAa,GAAG;AAC5B,iBAAO,YAAY;AAAA,QACvB;AACA,YAAI,QAAQ,IAAI,IAAI,aAAa;AACjC,cAAM,WAAW,MAAM,SAAS,YAAY;AAC5C,cAAM,WAAW,MAAM,SAAS,YAAY;AAC5C,eAAO,MAAM,SAAS;AAAA,MAC1B;AACA,aAAO;AAAA,IACX;AACA,YAAQ,cAAc;AAAA;AAAA;", "names": ["import_dist", "require_dist", "import_dist"]}