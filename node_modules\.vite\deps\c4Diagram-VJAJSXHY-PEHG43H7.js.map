{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/c4Diagram-VJAJSXHY.mjs"], "sourcesContent": ["import {\n  drawRect,\n  getNoteRect\n} from \"./chunk-D6G4REZN.mjs\";\nimport {\n  calculateTextHeight,\n  calculateTextWidth,\n  wrapLabel\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  assignWithDepth_default,\n  common_default,\n  configureSvgSize,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/c4/parser/c4Diagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 29:\n          $$[$0].splice(2, 0, \"SYSTEM\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: {},\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n            break;\n          case 1:\n            return 7;\n            break;\n          case 2:\n            return 8;\n            break;\n          case 3:\n            return 9;\n            break;\n          case 4:\n            return 22;\n            break;\n          case 5:\n            return 23;\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n            break;\n          case 16:\n            break;\n          case 17:\n            return 11;\n            break;\n          case 18:\n            return 15;\n            break;\n          case 19:\n            return 16;\n            break;\n          case 20:\n            return 17;\n            break;\n          case 21:\n            return 18;\n            break;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n            break;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n            break;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n            break;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n            break;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n            break;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n            break;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n            break;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n            break;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n            break;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n            break;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n            break;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n            break;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n            break;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n            break;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n            break;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n            break;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n            break;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n            break;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n            break;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n            break;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n            break;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n            break;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n            break;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n            break;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n            break;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n            break;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n            break;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n            break;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n            break;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n            break;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n            break;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n            break;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n            break;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n            break;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n            break;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n            break;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n            break;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n            break;\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n            break;\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            break;\n          case 71:\n            return 80;\n            break;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n            break;\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n            break;\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n            break;\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n            break;\n          case 81:\n            return \"LBRACE\";\n            break;\n          case 82:\n            return \"RBRACE\";\n            break;\n          case 83:\n            return \"SPACE\";\n            break;\n          case 84:\n            return \"EOL\";\n            break;\n          case 85:\n            return 14;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar c4Diagram_default = parser;\n\n// src/diagrams/c4/c4Db.js\nvar c4ShapeArray = [];\nvar boundaryParseStack = [\"\"];\nvar currentBoundaryParse = \"global\";\nvar parentBoundaryParse = \"\";\nvar boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nvar rels = [];\nvar title = \"\";\nvar wrapEnabled = false;\nvar c4ShapeInRow = 4;\nvar c4BoundaryInRow = 2;\nvar c4Type;\nvar getC4Type = /* @__PURE__ */ __name(function() {\n  return c4Type;\n}, \"getC4Type\");\nvar setC4Type = /* @__PURE__ */ __name(function(c4TypeParam) {\n  let sanitizedText = sanitizeText(c4TypeParam, getConfig());\n  c4Type = sanitizedText;\n}, \"setC4Type\");\nvar addRel = /* @__PURE__ */ __name(function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n}, \"addRel\");\nvar addPersonOrSystem = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n}, \"addPersonOrSystem\");\nvar addContainer = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n}, \"addContainer\");\nvar addComponent = /* @__PURE__ */ __name(function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n}, \"addComponent\");\nvar addPersonOrSystemBoundary = /* @__PURE__ */ __name(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addPersonOrSystemBoundary\");\nvar addContainerBoundary = /* @__PURE__ */ __name(function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addContainerBoundary\");\nvar addDeploymentNode = /* @__PURE__ */ __name(function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"addDeploymentNode\");\nvar popBoundaryParseStack = /* @__PURE__ */ __name(function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n}, \"popBoundaryParseStack\");\nvar updateElStyle = /* @__PURE__ */ __name(function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n}, \"updateElStyle\");\nvar updateRelStyle = /* @__PURE__ */ __name(function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n}, \"updateRelStyle\");\nvar updateLayoutConfig = /* @__PURE__ */ __name(function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow;\n  let c4BoundaryInRowValue = c4BoundaryInRow;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow = c4BoundaryInRowValue;\n  }\n}, \"updateLayoutConfig\");\nvar getC4ShapeInRow = /* @__PURE__ */ __name(function() {\n  return c4ShapeInRow;\n}, \"getC4ShapeInRow\");\nvar getC4BoundaryInRow = /* @__PURE__ */ __name(function() {\n  return c4BoundaryInRow;\n}, \"getC4BoundaryInRow\");\nvar getCurrentBoundaryParse = /* @__PURE__ */ __name(function() {\n  return currentBoundaryParse;\n}, \"getCurrentBoundaryParse\");\nvar getParentBoundaryParse = /* @__PURE__ */ __name(function() {\n  return parentBoundaryParse;\n}, \"getParentBoundaryParse\");\nvar getC4ShapeArray = /* @__PURE__ */ __name(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n}, \"getC4ShapeArray\");\nvar getC4Shape = /* @__PURE__ */ __name(function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n}, \"getC4Shape\");\nvar getC4ShapeKeys = /* @__PURE__ */ __name(function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n}, \"getC4ShapeKeys\");\nvar getBoundaries = /* @__PURE__ */ __name(function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n}, \"getBoundaries\");\nvar getBoundarys = getBoundaries;\nvar getRels = /* @__PURE__ */ __name(function() {\n  return rels;\n}, \"getRels\");\nvar getTitle = /* @__PURE__ */ __name(function() {\n  return title;\n}, \"getTitle\");\nvar setWrap = /* @__PURE__ */ __name(function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n}, \"setWrap\");\nvar autoWrap = /* @__PURE__ */ __name(function() {\n  return wrapEnabled;\n}, \"autoWrap\");\nvar clear = /* @__PURE__ */ __name(function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow = 4;\n  c4BoundaryInRow = 2;\n}, \"clear\");\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar setTitle = /* @__PURE__ */ __name(function(txt) {\n  let sanitizedText = sanitizeText(txt, getConfig());\n  title = sanitizedText;\n}, \"setTitle\");\nvar c4Db_default = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle,\n  getAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().c4, \"getConfig\"),\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\n\n// src/diagrams/c4/c4Renderer.js\nimport { select } from \"d3\";\n\n// src/diagrams/c4/svgDraw.js\nimport { sanitizeUrl } from \"@braintree/sanitize-url\";\nvar drawRect2 = /* @__PURE__ */ __name(function(elem, rectData) {\n  return drawRect(elem, rectData);\n}, \"drawRect\");\nvar drawImage = /* @__PURE__ */ __name(function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : sanitizeUrl(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawRels = /* @__PURE__ */ __name((elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n}, \"drawRels\");\nvar drawBoundary = /* @__PURE__ */ __name(function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect2(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n}, \"drawBoundary\");\nvar drawC4Shape = /* @__PURE__ */ __name(function(elem, c4Shape, conf2) {\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = getNoteRect();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect2(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && c4Shape.techn?.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n}, \"drawC4Shape\");\nvar insertDatabaseIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowEnd = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n}, \"insertArrowEnd\");\nvar insertArrowFilledHead = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertDynamicNumber = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertDynamicNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ __name(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n}, \"insertArrowCrossHead\");\nvar getC4ShapeFont = /* @__PURE__ */ __name((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"getC4ShapeFont\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  __name(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  __name(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  __name(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawBoundary,\n  drawC4Shape,\n  drawRels,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\n\n// src/diagrams/c4/c4Renderer.js\nvar globalBoundaryMaxX = 0;\nvar globalBoundaryMaxY = 0;\nvar c4ShapeInRow2 = 4;\nvar c4BoundaryInRow2 = 2;\nparser.yy = c4Db_default;\nvar conf = {};\nvar Bounds = class {\n  static {\n    __name(this, \"Bounds\");\n  }\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow2) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n};\nvar setConf = /* @__PURE__ */ __name(function(cnf) {\n  assignWithDepth_default(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar c4ShapeFont = /* @__PURE__ */ __name((cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n}, \"c4ShapeFont\");\nvar boundaryFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n}, \"boundaryFont\");\nvar messageFont = /* @__PURE__ */ __name((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = wrapLabel(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(common_default.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = calculateTextHeight(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(common_default.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          calculateTextWidth(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = calculateTextHeight(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\n__name(calcC4ShapeTextWH, \"calcC4ShapeTextWH\");\nvar drawBoundary2 = /* @__PURE__ */ __name(function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = calculateTextWidth(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw_default.drawBoundary(diagram2, boundary, conf);\n}, \"drawBoundary\");\nvar drawC4ShapeArray = /* @__PURE__ */ __name(function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = calculateTextWidth(\n      \"\\xAB\" + c4Shape.typeC4Shape.text + \"\\xBB\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape.label.Y = Y + 8;\n    Y = c4Shape.label.Y + c4Shape.label.height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape.type.Y = Y + 5;\n      Y = c4Shape.type.Y + c4Shape.type.height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape.techn.Y = Y + 5;\n      Y = c4Shape.techn.Y + c4Shape.techn.height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape.descr.Y = Y + 20;\n      Y = c4Shape.descr.Y + c4Shape.descr.height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape.descr.textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw_default.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n}, \"drawC4ShapeArray\");\nvar Point = class {\n  static {\n    __name(this, \"Point\");\n  }\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n};\nvar getIntersectPoint = /* @__PURE__ */ __name(function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n}, \"getIntersectPoint\");\nvar getIntersectPoints = /* @__PURE__ */ __name(function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n}, \"getIntersectPoints\");\nvar drawRels2 = /* @__PURE__ */ __name(function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = calculateTextWidth(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = calculateTextWidth(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw_default.drawRels(diagram2, rels2, conf);\n}, \"drawRels\");\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow2, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary.label.Y = Y + 8;\n    Y = currentBoundary.label.Y + currentBoundary.label.height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.type.Y = Y + 5;\n      Y = currentBoundary.type.Y + currentBoundary.type.height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary.descr.Y = Y + 20;\n      Y = currentBoundary.descr.Y + currentBoundary.descr.height;\n    }\n    if (i == 0 || i % c4BoundaryInRow2 === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary2(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\n__name(drawInsideBoundary, \"drawInsideBoundary\");\nvar draw = /* @__PURE__ */ __name(function(_text, id, _version, diagObj) {\n  conf = getConfig().c4;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  let db = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow2 = db.getC4ShapeInRow();\n  c4BoundaryInRow2 = db.getC4BoundaryInRow();\n  log.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : select(`[id=\"${id}\"]`);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowEnd(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  drawRels2(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  configureSvgSize(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  log.debug(`models:`, box);\n}, \"draw\");\nvar c4Renderer_default = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary: drawBoundary2,\n  setConf,\n  draw\n};\n\n// src/diagrams/c4/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/c4/c4Diagram.ts\nvar diagram = {\n  parser: c4Diagram_default,\n  db: c4Db_default,\n  renderer: c4Renderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name(({ c4, wrap }) => {\n    c4Renderer_default.setConf(c4);\n    c4Db_default.setWrap(wrap);\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAgrDA,0BAA4B;AAxpD5B,IAAI,SAAS,WAAW;AACtB,MAAI,IAAoB,OAAO,SAAS,GAAG,GAAG,IAAI,GAAG;AACnD,SAAK,KAAK,MAAM,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,EAAG;AACrD,WAAO;AAAA,EACT,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC/yC,MAAI,UAAU;AAAA,IACZ,OAAuB,OAAO,SAAS,QAAQ;AAAA,IAC/C,GAAG,OAAO;AAAA,IACV,IAAI,CAAC;AAAA,IACL,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG,cAAc,GAAG,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,eAAe,IAAI,cAAc,IAAI,WAAW,IAAI,cAAc,IAAI,OAAO,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,cAAc,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,qBAAqB,IAAI,kBAAkB,IAAI,SAAS,IAAI,kBAAkB,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,qBAAqB,IAAI,0BAA0B,IAAI,yBAAyB,IAAI,iBAAiB,IAAI,UAAU,IAAI,uBAAuB,IAAI,cAAc,IAAI,mBAAmB,IAAI,YAAY,IAAI,sBAAsB,IAAI,QAAQ,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,oBAAoB,IAAI,UAAU,IAAI,cAAc,IAAI,UAAU,IAAI,aAAa,IAAI,gBAAgB,IAAI,cAAc,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,aAAa,IAAI,gBAAgB,IAAI,mBAAmB,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,uBAAuB,IAAI,aAAa,IAAI,gBAAgB,IAAI,mBAAmB,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,uBAAuB,IAAI,OAAO,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,aAAa,IAAI,mBAAmB,IAAI,oBAAoB,IAAI,wBAAwB,IAAI,aAAa,IAAI,OAAO,IAAI,WAAW,IAAI,aAAa,IAAI,aAAa,IAAI,mBAAmB,IAAI,WAAW,GAAG,QAAQ,EAAE;AAAA,IACzgD,YAAY,EAAE,GAAG,SAAS,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,gBAAgB,IAAI,cAAc,IAAI,WAAW,IAAI,OAAO,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,cAAc,IAAI,iBAAiB,IAAI,SAAS,IAAI,kBAAkB,IAAI,aAAa,IAAI,mBAAmB,IAAI,aAAa,IAAI,mBAAmB,IAAI,6BAA6B,IAAI,UAAU,IAAI,uBAAuB,IAAI,mBAAmB,IAAI,YAAY,IAAI,sBAAsB,IAAI,QAAQ,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,UAAU,IAAI,cAAc,IAAI,UAAU,IAAI,aAAa,IAAI,gBAAgB,IAAI,cAAc,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,aAAa,IAAI,gBAAgB,IAAI,mBAAmB,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,uBAAuB,IAAI,aAAa,IAAI,gBAAgB,IAAI,mBAAmB,IAAI,iBAAiB,IAAI,oBAAoB,IAAI,uBAAuB,IAAI,OAAO,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,aAAa,IAAI,mBAAmB,IAAI,oBAAoB,IAAI,wBAAwB,IAAI,OAAO,IAAI,WAAW,IAAI,aAAa,IAAI,aAAa,IAAI,kBAAkB;AAAA,IACtrC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAAA,IACrrB,eAA+B,OAAO,SAAS,UAAU,QAAQ,QAAQ,UAAU,IAAI,SAAS,IAAI,IAAI;AACtG,UAAI,KAAK,GAAG,SAAS;AACrB,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,aAAG,aAAa,IAAI;AACpB;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB;AAAA,QACF,KAAK;AACH,aAAG,aAAa,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,aAAG,UAAU,GAAG,KAAK,CAAC,CAAC;AACvB;AAAA,QACF,KAAK;AACH,aAAG,SAAS,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;AAC/B,eAAK,IAAI,GAAG,EAAE,EAAE,UAAU,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,GAAG,EAAE,EAAE,UAAU,EAAE,CAAC;AACzC,eAAK,IAAI,GAAG,EAAE,EAAE,UAAU,EAAE;AAC5B;AAAA,QACF,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,SAAS,KAAK,CAAC;AAClB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB,aAAG,kBAAkB,KAAK,CAAC;AAC3B;AAAA,QACF,KAAK;AACH,aAAG,EAAE,EAAE,OAAO,GAAG,GAAG,YAAY;AAChC,aAAG,0BAA0B,GAAG,GAAG,EAAE,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,EAAE,EAAE,OAAO,GAAG,GAAG,QAAQ;AAC5B,aAAG,0BAA0B,GAAG,GAAG,EAAE,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,0BAA0B,GAAG,GAAG,EAAE,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,EAAE,EAAE,OAAO,GAAG,GAAG,WAAW;AAC/B,aAAG,qBAAqB,GAAG,GAAG,EAAE,CAAC;AACjC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,QAAQ,GAAG,GAAG,EAAE,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,SAAS,GAAG,GAAG,EAAE,CAAC;AACvC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,SAAS,GAAG,GAAG,EAAE,CAAC;AACvC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,sBAAsB;AACzB;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,UAAU,GAAG,GAAG,EAAE,CAAC;AACxC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,mBAAmB,GAAG,GAAG,EAAE,CAAC;AACjD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,UAAU,GAAG,GAAG,EAAE,CAAC;AACxC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,aAAa,GAAG,GAAG,EAAE,CAAC;AAC3C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,gBAAgB,GAAG,GAAG,EAAE,CAAC;AAC9C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,mBAAmB,GAAG,GAAG,EAAE,CAAC;AACjD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,sBAAsB,GAAG,GAAG,EAAE,CAAC;AACpD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,kBAAkB,yBAAyB,GAAG,GAAG,EAAE,CAAC;AACvD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,aAAa,GAAG,GAAG,EAAE,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,gBAAgB,GAAG,GAAG,EAAE,CAAC;AACzC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAC5C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,sBAAsB,GAAG,GAAG,EAAE,CAAC;AAC/C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,yBAAyB,GAAG,GAAG,EAAE,CAAC;AAClD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,4BAA4B,GAAG,GAAG,EAAE,CAAC;AACrD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,aAAa,GAAG,GAAG,EAAE,CAAC;AACtC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,gBAAgB,GAAG,GAAG,EAAE,CAAC;AACzC,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAC5C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,sBAAsB,GAAG,GAAG,EAAE,CAAC;AAC/C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,yBAAyB,GAAG,GAAG,EAAE,CAAC;AAClD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,aAAa,4BAA4B,GAAG,GAAG,EAAE,CAAC;AACrD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,OAAO,GAAG,GAAG,EAAE,CAAC;AAC1B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAC5B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAC5B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAC5B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAC5B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAC5B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,OAAO,SAAS,GAAG,GAAG,EAAE,CAAC;AAC5B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,EAAE,EAAE,OAAO,GAAG,CAAC;AAClB,aAAG,OAAO,OAAO,GAAG,GAAG,EAAE,CAAC;AAC1B,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,cAAc,mBAAmB,GAAG,GAAG,EAAE,CAAC;AAC7C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,eAAe,oBAAoB,GAAG,GAAG,EAAE,CAAC;AAC/C,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,aAAG,mBAAmB,wBAAwB,GAAG,GAAG,EAAE,CAAC;AACvD,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AAChB;AAAA,QACF,KAAK;AACH,aAAG,EAAE,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC;AACzB,eAAK,IAAI,GAAG,EAAE;AACd;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,eAAK,IAAI,GAAG,EAAE,EAAE,KAAK;AACrB;AAAA,QACF,KAAK;AACH,cAAI,KAAK,CAAC;AACV,aAAG,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,EAAE,EAAE,KAAK;AACpC,eAAK,IAAI;AACT;AAAA,QACF,KAAK;AACH,eAAK,IAAI;AACT;AAAA,MACJ;AAAA,IACF,GAAG,WAAW;AAAA,IACd,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACz5O,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,EAAE;AAAA,IACjK,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,UAAI,KAAK,aAAa;AACpB,aAAK,MAAM,GAAG;AAAA,MAChB,OAAO;AACL,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF,GAAG,YAAY;AAAA,IACf,OAAuB,OAAO,SAAS,MAAM,OAAO;AAClD,UAAI,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,QAAQ,KAAK,OAAO,SAAS,IAAI,WAAW,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS,GAAG,MAAM;AACtK,UAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,UAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AACrC,UAAI,cAAc,EAAE,IAAI,CAAC,EAAE;AAC3B,eAAS,KAAK,KAAK,IAAI;AACrB,YAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,CAAC,GAAG;AACpD,sBAAY,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC;AAAA,QAC/B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,YAAY,EAAE;AACrC,kBAAY,GAAG,QAAQ;AACvB,kBAAY,GAAG,SAAS;AACxB,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,SAAS,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO;AACnB,aAAO,KAAK,KAAK;AACjB,UAAI,SAAS,OAAO,WAAW,OAAO,QAAQ;AAC9C,UAAI,OAAO,YAAY,GAAG,eAAe,YAAY;AACnD,aAAK,aAAa,YAAY,GAAG;AAAA,MACnC,OAAO;AACL,aAAK,aAAa,OAAO,eAAe,IAAI,EAAE;AAAA,MAChD;AACA,eAAS,SAAS,GAAG;AACnB,cAAM,SAAS,MAAM,SAAS,IAAI;AAClC,eAAO,SAAS,OAAO,SAAS;AAChC,eAAO,SAAS,OAAO,SAAS;AAAA,MAClC;AACA,aAAO,UAAU,UAAU;AAC3B,eAAS,MAAM;AACb,YAAI;AACJ,gBAAQ,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AACxC,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,iBAAiB,OAAO;AAC1B,qBAAS;AACT,oBAAQ,OAAO,IAAI;AAAA,UACrB;AACA,kBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,KAAK;AACjB,UAAI,QAAQ,gBAAgB,OAAO,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,KAAK,UAAU;AAC/E,aAAO,MAAM;AACX,gBAAQ,MAAM,MAAM,SAAS,CAAC;AAC9B,YAAI,KAAK,eAAe,KAAK,GAAG;AAC9B,mBAAS,KAAK,eAAe,KAAK;AAAA,QACpC,OAAO;AACL,cAAI,WAAW,QAAQ,OAAO,UAAU,aAAa;AACnD,qBAAS,IAAI;AAAA,UACf;AACA,mBAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAC9C;AACA,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,GAAG;AACjE,cAAI,SAAS;AACb,qBAAW,CAAC;AACZ,eAAK,KAAK,MAAM,KAAK,GAAG;AACtB,gBAAI,KAAK,WAAW,CAAC,KAAK,IAAI,QAAQ;AACpC,uBAAS,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI,GAAG;AAAA,YAC9C;AAAA,UACF;AACA,cAAI,OAAO,cAAc;AACvB,qBAAS,0BAA0B,WAAW,KAAK,QAAQ,OAAO,aAAa,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,aAAa,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UAC9K,OAAO;AACL,qBAAS,0BAA0B,WAAW,KAAK,mBAAmB,UAAU,MAAM,iBAAiB,OAAO,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,UACrJ;AACA,eAAK,WAAW,QAAQ;AAAA,YACtB,MAAM,OAAO;AAAA,YACb,OAAO,KAAK,WAAW,MAAM,KAAK;AAAA,YAClC,MAAM,OAAO;AAAA,YACb,KAAK;AAAA,YACL;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,GAAG;AACnD,gBAAM,IAAI,MAAM,sDAAsD,QAAQ,cAAc,MAAM;AAAA,QACpG;AACA,gBAAQ,OAAO,CAAC,GAAG;AAAA,UACjB,KAAK;AACH,kBAAM,KAAK,MAAM;AACjB,mBAAO,KAAK,OAAO,MAAM;AACzB,mBAAO,KAAK,OAAO,MAAM;AACzB,kBAAM,KAAK,OAAO,CAAC,CAAC;AACpB,qBAAS;AACT,gBAAI,CAAC,gBAAgB;AACnB,uBAAS,OAAO;AAChB,uBAAS,OAAO;AAChB,yBAAW,OAAO;AAClB,sBAAQ,OAAO;AACf,kBAAI,aAAa,GAAG;AAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,uBAAS;AACT,+BAAiB;AAAA,YACnB;AACA;AAAA,UACF,KAAK;AACH,kBAAM,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC;AACpC,kBAAM,IAAI,OAAO,OAAO,SAAS,GAAG;AACpC,kBAAM,KAAK;AAAA,cACT,YAAY,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cAC/C,WAAW,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,cACrC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE;AAAA,cACjD,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE;AAAA,YACzC;AACA,gBAAI,QAAQ;AACV,oBAAM,GAAG,QAAQ;AAAA,gBACf,OAAO,OAAO,UAAU,OAAO,EAAE,EAAE,MAAM,CAAC;AAAA,gBAC1C,OAAO,OAAO,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,cACnC;AAAA,YACF;AACA,gBAAI,KAAK,cAAc,MAAM,OAAO;AAAA,cAClC;AAAA,cACA;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,OAAO,CAAC;AAAA,cACR;AAAA,cACA;AAAA,YACF,EAAE,OAAO,IAAI,CAAC;AACd,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,YACT;AACA,gBAAI,KAAK;AACP,sBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,CAAC;AACnC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AACjC,uBAAS,OAAO,MAAM,GAAG,KAAK,GAAG;AAAA,YACnC;AACA,kBAAM,KAAK,KAAK,aAAa,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,mBAAO,KAAK,MAAM,CAAC;AACnB,mBAAO,KAAK,MAAM,EAAE;AACpB,uBAAW,MAAM,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,MAAM,SAAS,CAAC,CAAC;AACjE,kBAAM,KAAK,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,mBAAO;AAAA,QACX;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,OAAO;AAAA,EACZ;AACA,MAAI,QAAwB,WAAW;AACrC,QAAI,SAAS;AAAA,MACX,KAAK;AAAA,MACL,YAA4B,OAAO,SAAS,WAAW,KAAK,MAAM;AAChE,YAAI,KAAK,GAAG,QAAQ;AAClB,eAAK,GAAG,OAAO,WAAW,KAAK,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,MAAM,GAAG;AAAA,QACrB;AAAA,MACF,GAAG,YAAY;AAAA;AAAA,MAEf,UAA0B,OAAO,SAAS,OAAO,IAAI;AACnD,aAAK,KAAK,MAAM,KAAK,MAAM,CAAC;AAC5B,aAAK,SAAS;AACd,aAAK,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC3C,aAAK,WAAW,KAAK,SAAS;AAC9B,aAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AAC1C,aAAK,iBAAiB,CAAC,SAAS;AAChC,aAAK,SAAS;AAAA,UACZ,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,GAAG,CAAC;AAAA,QAC3B;AACA,aAAK,SAAS;AACd,eAAO;AAAA,MACT,GAAG,UAAU;AAAA;AAAA,MAEb,OAAuB,OAAO,WAAW;AACvC,YAAI,KAAK,KAAK,OAAO,CAAC;AACtB,aAAK,UAAU;AACf,aAAK;AACL,aAAK;AACL,aAAK,SAAS;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,GAAG,MAAM,iBAAiB;AACtC,YAAI,OAAO;AACT,eAAK;AACL,eAAK,OAAO;AAAA,QACd,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,MAAM,CAAC;AAAA,QACrB;AACA,aAAK,SAAS,KAAK,OAAO,MAAM,CAAC;AACjC,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,OAAuB,OAAO,SAAS,IAAI;AACzC,YAAI,MAAM,GAAG;AACb,YAAI,QAAQ,GAAG,MAAM,eAAe;AACpC,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,SAAS,GAAG;AAC5D,aAAK,UAAU;AACf,YAAI,WAAW,KAAK,MAAM,MAAM,eAAe;AAC/C,aAAK,QAAQ,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,SAAS,CAAC;AACvD,aAAK,UAAU,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC7D,YAAI,MAAM,SAAS,GAAG;AACpB,eAAK,YAAY,MAAM,SAAS;AAAA,QAClC;AACA,YAAI,IAAI,KAAK,OAAO;AACpB,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,SAAS,MAAM,WAAW,SAAS,SAAS,KAAK,OAAO,eAAe,KAAK,SAAS,SAAS,SAAS,MAAM,MAAM,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS,KAAK,OAAO,eAAe;AAAA,QAC1L;AACA,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,SAAS,GAAG;AAAA,QACrD;AACA,aAAK,SAAS,KAAK,OAAO;AAC1B,eAAO;AAAA,MACT,GAAG,OAAO;AAAA;AAAA,MAEV,MAAsB,OAAO,WAAW;AACtC,aAAK,QAAQ;AACb,eAAO;AAAA,MACT,GAAG,MAAM;AAAA;AAAA,MAET,QAAwB,OAAO,WAAW;AACxC,YAAI,KAAK,QAAQ,iBAAiB;AAChC,eAAK,aAAa;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,qIAAqI,KAAK,aAAa,GAAG;AAAA,YAChO,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT,GAAG,QAAQ;AAAA;AAAA,MAEX,MAAsB,OAAO,SAAS,GAAG;AACvC,aAAK,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC;AAAA,MAChC,GAAG,MAAM;AAAA;AAAA,MAET,WAA2B,OAAO,WAAW;AAC3C,YAAI,OAAO,KAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;AACzE,gBAAQ,KAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,MAC7E,GAAG,WAAW;AAAA;AAAA,MAEd,eAA+B,OAAO,WAAW;AAC/C,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,SAAS,IAAI;AACpB,kBAAQ,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,MAAM;AAAA,QAChD;AACA,gBAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,QAAQ,OAAO,EAAE;AAAA,MACjF,GAAG,eAAe;AAAA;AAAA,MAElB,cAA8B,OAAO,WAAW;AAC9C,YAAI,MAAM,KAAK,UAAU;AACzB,YAAI,KAAK,IAAI,MAAM,IAAI,SAAS,CAAC,EAAE,KAAK,GAAG;AAC3C,eAAO,MAAM,KAAK,cAAc,IAAI,OAAO,KAAK;AAAA,MAClD,GAAG,cAAc;AAAA;AAAA,MAEjB,YAA4B,OAAO,SAAS,OAAO,cAAc;AAC/D,YAAI,OAAO,OAAO;AAClB,YAAI,KAAK,QAAQ,iBAAiB;AAChC,mBAAS;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,cACN,YAAY,KAAK,OAAO;AAAA,cACxB,WAAW,KAAK;AAAA,cAChB,cAAc,KAAK,OAAO;AAAA,cAC1B,aAAa,KAAK,OAAO;AAAA,YAC3B;AAAA,YACA,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,IAAI,KAAK;AAAA,YACT,gBAAgB,KAAK,eAAe,MAAM,CAAC;AAAA,YAC3C,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAO,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,gBAAQ,MAAM,CAAC,EAAE,MAAM,iBAAiB;AACxC,YAAI,OAAO;AACT,eAAK,YAAY,MAAM;AAAA,QACzB;AACA,aAAK,SAAS;AAAA,UACZ,YAAY,KAAK,OAAO;AAAA,UACxB,WAAW,KAAK,WAAW;AAAA,UAC3B,cAAc,KAAK,OAAO;AAAA,UAC1B,aAAa,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,MAAM,CAAC,EAAE;AAAA,QAC/I;AACA,aAAK,UAAU,MAAM,CAAC;AACtB,aAAK,SAAS,MAAM,CAAC;AACrB,aAAK,UAAU;AACf,aAAK,SAAS,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,QAAQ;AACvB,eAAK,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM;AAAA,QAC9D;AACA,aAAK,QAAQ;AACb,aAAK,aAAa;AAClB,aAAK,SAAS,KAAK,OAAO,MAAM,MAAM,CAAC,EAAE,MAAM;AAC/C,aAAK,WAAW,MAAM,CAAC;AACvB,gBAAQ,KAAK,cAAc,KAAK,MAAM,KAAK,IAAI,MAAM,cAAc,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC;AACtH,YAAI,KAAK,QAAQ,KAAK,QAAQ;AAC5B,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO;AACT,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY;AAC1B,mBAAS,KAAK,QAAQ;AACpB,iBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,YAAY;AAAA;AAAA,MAEf,MAAsB,OAAO,WAAW;AACtC,YAAI,KAAK,MAAM;AACb,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,OAAO;AAAA,QACd;AACA,YAAI,OAAO,OAAO,WAAW;AAC7B,YAAI,CAAC,KAAK,OAAO;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,QAAQ,KAAK,cAAc;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAY,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAClD,cAAI,cAAc,CAAC,SAAS,UAAU,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,SAAS;AAClE,oBAAQ;AACR,oBAAQ;AACR,gBAAI,KAAK,QAAQ,iBAAiB;AAChC,sBAAQ,KAAK,WAAW,WAAW,MAAM,CAAC,CAAC;AAC3C,kBAAI,UAAU,OAAO;AACnB,uBAAO;AAAA,cACT,WAAW,KAAK,YAAY;AAC1B,wBAAQ;AACR;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,CAAC,KAAK,QAAQ,MAAM;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,OAAO;AACT,kBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK,CAAC;AAC3C,cAAI,UAAU,OAAO;AACnB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,IAAI;AACtB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO,KAAK,WAAW,4BAA4B,KAAK,WAAW,KAAK,2BAA2B,KAAK,aAAa,GAAG;AAAA,YACtH,MAAM;AAAA,YACN,OAAO;AAAA,YACP,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,GAAG,MAAM;AAAA;AAAA,MAET,KAAqB,OAAO,SAAS,MAAM;AACzC,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,GAAG;AACL,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,IAAI;AAAA,QAClB;AAAA,MACF,GAAG,KAAK;AAAA;AAAA,MAER,OAAuB,OAAO,SAAS,MAAM,WAAW;AACtD,aAAK,eAAe,KAAK,SAAS;AAAA,MACpC,GAAG,OAAO;AAAA;AAAA,MAEV,UAA0B,OAAO,SAAS,WAAW;AACnD,YAAI,IAAI,KAAK,eAAe,SAAS;AACrC,YAAI,IAAI,GAAG;AACT,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,eAA+B,OAAO,SAAS,gBAAgB;AAC7D,YAAI,KAAK,eAAe,UAAU,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,GAAG;AACrF,iBAAO,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC,CAAC,EAAE;AAAA,QAC9E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS,EAAE;AAAA,QACpC;AAAA,MACF,GAAG,eAAe;AAAA;AAAA,MAElB,UAA0B,OAAO,SAAS,SAAS,GAAG;AACpD,YAAI,KAAK,eAAe,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC;AACpD,YAAI,KAAK,GAAG;AACV,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,GAAG,UAAU;AAAA;AAAA,MAEb,WAA2B,OAAO,SAAS,UAAU,WAAW;AAC9D,aAAK,MAAM,SAAS;AAAA,MACtB,GAAG,WAAW;AAAA;AAAA,MAEd,gBAAgC,OAAO,SAAS,iBAAiB;AAC/D,eAAO,KAAK,eAAe;AAAA,MAC7B,GAAG,gBAAgB;AAAA,MACnB,SAAS,CAAC;AAAA,MACV,eAA+B,OAAO,SAAS,UAAU,IAAI,KAAK,2BAA2B,UAAU;AACrG,YAAI,UAAU;AACd,gBAAQ,2BAA2B;AAAA,UACjC,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH;AACA;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,YAAY;AACvB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,QAAQ;AACnB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,kBAAkB;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,eAAe;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,YAAY;AACvB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,cAAc;AACzB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,QAAQ;AACnB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,UAAU;AACrB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,iBAAiB;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,kBAAkB;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,eAAe;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,iBAAiB;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,cAAc;AACzB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,oBAAoB;AAC/B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,qBAAqB;AAChC,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,kBAAkB;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,eAAe;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,iBAAiB;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,cAAc;AACzB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,MAAM;AACjB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,QAAQ;AACnB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,QAAQ;AACnB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,KAAK;AAChB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,OAAO;AAClB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,iBAAiB;AAC5B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,kBAAkB;AAC7B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,sBAAsB;AACjC,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,QAAQ;AACnB;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,WAAW;AACtB;AAAA,UACF,KAAK;AACH,iBAAK,MAAM,eAAe;AAC1B,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,MAAM,iBAAiB;AAC5B;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,iBAAK,SAAS;AACd,iBAAK,SAAS;AACd;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,QACJ;AAAA,MACF,GAAG,WAAW;AAAA,MACd,OAAO,CAAC,+BAA+B,+BAA+B,+BAA+B,+BAA+B,wBAAwB,iCAAiC,wBAAwB,wBAAwB,wBAAwB,wBAAwB,yBAAyB,aAAa,eAAe,iCAAiC,yBAAyB,oBAAoB,YAAY,oBAAoB,sBAAsB,sBAAsB,oBAAoB,uBAAuB,qBAAqB,iBAAiB,0BAA0B,uBAAuB,qBAAqB,sBAAsB,mBAAmB,iBAAiB,mBAAmB,8BAA8B,0BAA0B,6BAA6B,0BAA0B,wBAAwB,yBAAyB,sBAAsB,oBAAoB,6BAA6B,6BAA6B,0BAA0B,wBAAwB,yBAAyB,sBAAsB,oBAAoB,0BAA0B,eAAe,iBAAiB,iBAAiB,cAAc,gBAAgB,iBAAiB,gBAAgB,mBAAmB,gBAAgB,mBAAmB,gBAAgB,oBAAoB,gBAAgB,mBAAmB,mBAAmB,6BAA6B,yBAAyB,6BAA6B,UAAU,mBAAmB,YAAY,YAAY,WAAW,UAAU,mBAAmB,gBAAgB,YAAY,cAAc,iBAAiB,cAAc,mBAAmB,cAAc,YAAY,cAAc,WAAW,WAAW,cAAc,gBAAgB,QAAQ;AAAA,MACntD,YAAY,EAAE,uBAAuB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,CAAC,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,CAAC,GAAG,aAAa,MAAM,GAAG,mBAAmB,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,iBAAiB,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,wBAAwB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,oBAAoB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,mBAAmB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,OAAO,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,uBAAuB,EAAE,SAAS,CAAC,GAAG,aAAa,MAAM,GAAG,oBAAoB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,iBAAiB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,mBAAmB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,gBAAgB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,sBAAsB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,uBAAuB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,oBAAoB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,iBAAiB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,mBAAmB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,gBAAgB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,mBAAmB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,uBAAuB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,YAAY,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,oBAAoB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,iBAAiB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,cAAc,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,gBAAgB,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,cAAc,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,MAAM,GAAG,WAAW,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,aAAa,KAAK,EAAE;AAAA,IAC3yG;AACA,WAAO;AAAA,EACT,EAAE;AACF,UAAQ,QAAQ;AAChB,WAAS,SAAS;AAChB,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,QAAQ,QAAQ;AACvB,SAAO,YAAY;AACnB,UAAQ,SAAS;AACjB,SAAO,IAAI,OAAO;AACpB,EAAE;AACF,OAAO,SAAS;AAChB,IAAI,oBAAoB;AAGxB,IAAI,eAAe,CAAC;AACpB,IAAI,qBAAqB,CAAC,EAAE;AAC5B,IAAI,uBAAuB;AAC3B,IAAI,sBAAsB;AAC1B,IAAI,aAAa;AAAA,EACf;AAAA,IACE,OAAO;AAAA,IACP,OAAO,EAAE,MAAM,SAAS;AAAA,IACxB,MAAM,EAAE,MAAM,SAAS;AAAA,IACvB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,gBAAgB;AAAA,EAClB;AACF;AACA,IAAI,OAAO,CAAC;AACZ,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI;AACJ,IAAI,YAA4B,OAAO,WAAW;AAChD,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAA4B,OAAO,SAAS,aAAa;AAC3D,MAAI,gBAAgB,aAAa,aAAa,WAAU,CAAC;AACzD,WAAS;AACX,GAAG,WAAW;AACd,IAAI,SAAyB,OAAO,SAAS,MAAM,MAAM,IAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AACpG,MAAI,SAAS,UAAU,SAAS,QAAQ,SAAS,UAAU,SAAS,QAAQ,OAAO,UAAU,OAAO,QAAQ,UAAU,UAAU,UAAU,MAAM;AAC9I;AAAA,EACF;AACA,MAAI,MAAM,CAAC;AACX,QAAM,MAAM,KAAK,KAAK,CAAC,SAAS,KAAK,SAAS,QAAQ,KAAK,OAAO,EAAE;AACpE,MAAI,KAAK;AACP,UAAM;AAAA,EACR,OAAO;AACL,SAAK,KAAK,GAAG;AAAA,EACf;AACA,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI,QAAQ,EAAE,MAAM,MAAM;AAC1B,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,QAAI,QAAQ,EAAE,MAAM,GAAG;AAAA,EACzB,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAC3B,OAAO;AACL,UAAI,QAAQ,EAAE,MAAM,MAAM;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,QAAI,QAAQ,EAAE,MAAM,GAAG;AAAA,EACzB,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAC3B,OAAO;AACL,UAAI,QAAQ,EAAE,MAAM,MAAM;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,QAAI,GAAG,IAAI;AAAA,EACb,OAAO;AACL,QAAI,SAAS;AAAA,EACf;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,QAAI,GAAG,IAAI;AAAA,EACb,OAAO;AACL,QAAI,OAAO;AAAA,EACb;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,QAAI,GAAG,IAAI;AAAA,EACb,OAAO;AACL,QAAI,OAAO;AAAA,EACb;AACA,MAAI,OAAO,SAAS;AACtB,GAAG,QAAQ;AACX,IAAI,oBAAoC,OAAO,SAAS,aAAa,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC5G,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,iBAAiB,CAAC;AACtB,QAAM,MAAM,aAAa,KAAK,CAAC,oBAAoB,gBAAgB,UAAU,KAAK;AAClF,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,qBAAiB;AAAA,EACnB,OAAO;AACL,mBAAe,QAAQ;AACvB,iBAAa,KAAK,cAAc;AAAA,EAClC;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,mBAAe,QAAQ,EAAE,MAAM,GAAG;AAAA,EACpC,OAAO;AACL,mBAAe,QAAQ,EAAE,MAAM,MAAM;AAAA,EACvC;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,mBAAe,QAAQ,EAAE,MAAM,GAAG;AAAA,EACpC,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,qBAAe,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACtC,OAAO;AACL,qBAAe,QAAQ,EAAE,MAAM,MAAM;AAAA,IACvC;AAAA,EACF;AACA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,mBAAe,GAAG,IAAI;AAAA,EACxB,OAAO;AACL,mBAAe,SAAS;AAAA,EAC1B;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,mBAAe,GAAG,IAAI;AAAA,EACxB,OAAO;AACL,mBAAe,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,mBAAe,GAAG,IAAI;AAAA,EACxB,OAAO;AACL,mBAAe,OAAO;AAAA,EACxB;AACA,iBAAe,cAAc,EAAE,MAAM,YAAY;AACjD,iBAAe,iBAAiB;AAChC,iBAAe,OAAO,SAAS;AACjC,GAAG,mBAAmB;AACtB,IAAI,eAA+B,OAAO,SAAS,aAAa,OAAO,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC9G,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,YAAY,CAAC;AACjB,QAAM,MAAM,aAAa,KAAK,CAAC,eAAe,WAAW,UAAU,KAAK;AACxE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,gBAAY;AAAA,EACd,OAAO;AACL,cAAU,QAAQ;AAClB,iBAAa,KAAK,SAAS;AAAA,EAC7B;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,cAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,EAClC;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AACA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,YAAU,OAAO,SAAS;AAC1B,YAAU,cAAc,EAAE,MAAM,YAAY;AAC5C,YAAU,iBAAiB;AAC7B,GAAG,cAAc;AACjB,IAAI,eAA+B,OAAO,SAAS,aAAa,OAAO,OAAO,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC9G,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,YAAY,CAAC;AACjB,QAAM,MAAM,aAAa,KAAK,CAAC,eAAe,WAAW,UAAU,KAAK;AACxE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,gBAAY;AAAA,EACd,OAAO;AACL,cAAU,QAAQ;AAClB,iBAAa,KAAK,SAAS;AAAA,EAC7B;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,cAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,EAClC;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,cAAU,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,gBAAU,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IACjC,OAAO;AACL,gBAAU,QAAQ,EAAE,MAAM,MAAM;AAAA,IAClC;AAAA,EACF;AACA,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,SAAS;AAAA,EACrB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,cAAU,GAAG,IAAI;AAAA,EACnB,OAAO;AACL,cAAU,OAAO;AAAA,EACnB;AACA,YAAU,OAAO,SAAS;AAC1B,YAAU,cAAc,EAAE,MAAM,YAAY;AAC5C,YAAU,iBAAiB;AAC7B,GAAG,cAAc;AACjB,IAAI,4BAA4C,OAAO,SAAS,OAAO,OAAO,MAAM,MAAM,MAAM;AAC9F,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAChB,QAAM,MAAM,WAAW,KAAK,CAAC,cAAc,UAAU,UAAU,KAAK;AACpE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,eAAW;AAAA,EACb,OAAO;AACL,aAAS,QAAQ;AACjB,eAAW,KAAK,QAAQ;AAAA,EAC1B;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,aAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,EACjC;AACA,MAAI,SAAS,UAAU,SAAS,MAAM;AACpC,aAAS,OAAO,EAAE,MAAM,SAAS;AAAA,EACnC,OAAO;AACL,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,OAAO,EAAE,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,WAAS,iBAAiB;AAC1B,WAAS,OAAO,SAAS;AACzB,wBAAsB;AACtB,yBAAuB;AACvB,qBAAmB,KAAK,mBAAmB;AAC7C,GAAG,2BAA2B;AAC9B,IAAI,uBAAuC,OAAO,SAAS,OAAO,OAAO,MAAM,MAAM,MAAM;AACzF,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAChB,QAAM,MAAM,WAAW,KAAK,CAAC,cAAc,UAAU,UAAU,KAAK;AACpE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,eAAW;AAAA,EACb,OAAO;AACL,aAAS,QAAQ;AACjB,eAAW,KAAK,QAAQ;AAAA,EAC1B;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,aAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,EACjC;AACA,MAAI,SAAS,UAAU,SAAS,MAAM;AACpC,aAAS,OAAO,EAAE,MAAM,YAAY;AAAA,EACtC,OAAO;AACL,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,OAAO,EAAE,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,WAAS,iBAAiB;AAC1B,WAAS,OAAO,SAAS;AACzB,wBAAsB;AACtB,yBAAuB;AACvB,qBAAmB,KAAK,mBAAmB;AAC7C,GAAG,sBAAsB;AACzB,IAAI,oBAAoC,OAAO,SAAS,UAAU,OAAO,OAAO,MAAM,OAAO,QAAQ,MAAM,MAAM;AAC/G,MAAI,UAAU,QAAQ,UAAU,MAAM;AACpC;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAChB,QAAM,MAAM,WAAW,KAAK,CAAC,cAAc,UAAU,UAAU,KAAK;AACpE,MAAI,OAAO,UAAU,IAAI,OAAO;AAC9B,eAAW;AAAA,EACb,OAAO;AACL,aAAS,QAAQ;AACjB,eAAW,KAAK,QAAQ;AAAA,EAC1B;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,aAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,EACjC;AACA,MAAI,SAAS,UAAU,SAAS,MAAM;AACpC,aAAS,OAAO,EAAE,MAAM,OAAO;AAAA,EACjC,OAAO;AACL,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,OAAO,EAAE,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,aAAS,QAAQ,EAAE,MAAM,GAAG;AAAA,EAC9B,OAAO;AACL,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,eAAS,GAAG,IAAI,EAAE,MAAM,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,QAAQ,EAAE,MAAM,MAAM;AAAA,IACjC;AAAA,EACF;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,IAAI,EAAE,CAAC;AACzC,aAAS,GAAG,IAAI;AAAA,EAClB,OAAO;AACL,aAAS,OAAO;AAAA,EAClB;AACA,WAAS,WAAW;AACpB,WAAS,iBAAiB;AAC1B,WAAS,OAAO,SAAS;AACzB,wBAAsB;AACtB,yBAAuB;AACvB,qBAAmB,KAAK,mBAAmB;AAC7C,GAAG,mBAAmB;AACtB,IAAI,wBAAwC,OAAO,WAAW;AAC5D,yBAAuB;AACvB,qBAAmB,IAAI;AACvB,wBAAsB,mBAAmB,IAAI;AAC7C,qBAAmB,KAAK,mBAAmB;AAC7C,GAAG,uBAAuB;AAC1B,IAAI,gBAAgC,OAAO,SAAS,aAAa,aAAa,SAAS,WAAW,aAAa,WAAW,OAAO,QAAQ,OAAO,YAAY,cAAc;AACxK,MAAI,MAAM,aAAa,KAAK,CAAC,YAAY,QAAQ,UAAU,WAAW;AACtE,MAAI,QAAQ,QAAQ;AAClB,UAAM,WAAW,KAAK,CAAC,YAAY,QAAQ,UAAU,WAAW;AAChE,QAAI,QAAQ,QAAQ;AAClB;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,UAAU,YAAY,MAAM;AAC1C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,EAAE,CAAC;AAC5C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,UAAU;AAAA,IAChB;AAAA,EACF;AACA,MAAI,cAAc,UAAU,cAAc,MAAM;AAC9C,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,gBAAgB,UAAU,gBAAgB,MAAM;AAClD,QAAI,OAAO,gBAAgB,UAAU;AACnC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,WAAW,EAAE,CAAC;AAChD,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,cAAc;AAAA,IACpB;AAAA,EACF;AACA,MAAI,cAAc,UAAU,cAAc,MAAM;AAC9C,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AACA,MAAI,WAAW,UAAU,WAAW,MAAM;AACxC,QAAI,OAAO,WAAW,UAAU;AAC9B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AAC3C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,SAAS;AAAA,IACf;AAAA,EACF;AACA,MAAI,UAAU,UAAU,UAAU,MAAM;AACtC,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,EAAE,CAAC;AAC1C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AACA,MAAI,eAAe,UAAU,eAAe,MAAM;AAChD,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,UAAU,EAAE,CAAC;AAC/C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,aAAa;AAAA,IACnB;AAAA,EACF;AACA,MAAI,iBAAiB,UAAU,iBAAiB,MAAM;AACpD,QAAI,OAAO,iBAAiB,UAAU;AACpC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,YAAY,EAAE,CAAC;AACjD,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACF,GAAG,eAAe;AAClB,IAAI,iBAAiC,OAAO,SAAS,aAAa,MAAM,IAAI,WAAW,WAAW,SAAS,SAAS;AAClH,QAAM,MAAM,KAAK,KAAK,CAAC,QAAQ,IAAI,SAAS,QAAQ,IAAI,OAAO,EAAE;AACjE,MAAI,QAAQ,QAAQ;AAClB;AAAA,EACF;AACA,MAAI,cAAc,UAAU,cAAc,MAAM;AAC9C,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,cAAc,UAAU,cAAc,MAAM;AAC9C,QAAI,OAAO,cAAc,UAAU;AACjC,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,SAAS,EAAE,CAAC;AAC9C,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,YAAY,UAAU,YAAY,MAAM;AAC1C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,EAAE,CAAC;AAC5C,UAAI,GAAG,IAAI,SAAS,KAAK;AAAA,IAC3B,OAAO;AACL,UAAI,UAAU,SAAS,OAAO;AAAA,IAChC;AAAA,EACF;AACA,MAAI,YAAY,UAAU,YAAY,MAAM;AAC1C,QAAI,OAAO,YAAY,UAAU;AAC/B,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,EAAE,CAAC;AAC5C,UAAI,GAAG,IAAI,SAAS,KAAK;AAAA,IAC3B,OAAO;AACL,UAAI,UAAU,SAAS,OAAO;AAAA,IAChC;AAAA,EACF;AACF,GAAG,gBAAgB;AACnB,IAAI,qBAAqC,OAAO,SAAS,aAAa,mBAAmB,sBAAsB;AAC7G,MAAI,oBAAoB;AACxB,MAAI,uBAAuB;AAC3B,MAAI,OAAO,sBAAsB,UAAU;AACzC,UAAM,QAAQ,OAAO,OAAO,iBAAiB,EAAE,CAAC;AAChD,wBAAoB,SAAS,KAAK;AAAA,EACpC,OAAO;AACL,wBAAoB,SAAS,iBAAiB;AAAA,EAChD;AACA,MAAI,OAAO,yBAAyB,UAAU;AAC5C,UAAM,QAAQ,OAAO,OAAO,oBAAoB,EAAE,CAAC;AACnD,2BAAuB,SAAS,KAAK;AAAA,EACvC,OAAO;AACL,2BAAuB,SAAS,oBAAoB;AAAA,EACtD;AACA,MAAI,qBAAqB,GAAG;AAC1B,mBAAe;AAAA,EACjB;AACA,MAAI,wBAAwB,GAAG;AAC7B,sBAAkB;AAAA,EACpB;AACF,GAAG,oBAAoB;AACvB,IAAI,kBAAkC,OAAO,WAAW;AACtD,SAAO;AACT,GAAG,iBAAiB;AACpB,IAAI,qBAAqC,OAAO,WAAW;AACzD,SAAO;AACT,GAAG,oBAAoB;AACvB,IAAI,0BAA0C,OAAO,WAAW;AAC9D,SAAO;AACT,GAAG,yBAAyB;AAC5B,IAAI,yBAAyC,OAAO,WAAW;AAC7D,SAAO;AACT,GAAG,wBAAwB;AAC3B,IAAI,kBAAkC,OAAO,SAAS,gBAAgB;AACpE,MAAI,mBAAmB,UAAU,mBAAmB,MAAM;AACxD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,aAAa,OAAO,CAAC,mBAAmB;AAC7C,aAAO,eAAe,mBAAmB;AAAA,IAC3C,CAAC;AAAA,EACH;AACF,GAAG,iBAAiB;AACpB,IAAI,aAA6B,OAAO,SAAS,OAAO;AACtD,SAAO,aAAa,KAAK,CAAC,mBAAmB,eAAe,UAAU,KAAK;AAC7E,GAAG,YAAY;AACf,IAAI,iBAAiC,OAAO,SAAS,gBAAgB;AACnE,SAAO,OAAO,KAAK,gBAAgB,cAAc,CAAC;AACpD,GAAG,gBAAgB;AACnB,IAAI,gBAAgC,OAAO,SAAS,gBAAgB;AAClE,MAAI,mBAAmB,UAAU,mBAAmB,MAAM;AACxD,WAAO;AAAA,EACT,OAAO;AACL,WAAO,WAAW,OAAO,CAAC,aAAa,SAAS,mBAAmB,cAAc;AAAA,EACnF;AACF,GAAG,eAAe;AAClB,IAAI,eAAe;AACnB,IAAI,UAA0B,OAAO,WAAW;AAC9C,SAAO;AACT,GAAG,SAAS;AACZ,IAAI,WAA2B,OAAO,WAAW;AAC/C,SAAO;AACT,GAAG,UAAU;AACb,IAAI,UAA0B,OAAO,SAAS,aAAa;AACzD,gBAAc;AAChB,GAAG,SAAS;AACZ,IAAI,WAA2B,OAAO,WAAW;AAC/C,SAAO;AACT,GAAG,UAAU;AACb,IAAI,QAAwB,OAAO,WAAW;AAC5C,iBAAe,CAAC;AAChB,eAAa;AAAA,IACX;AAAA,MACE,OAAO;AAAA,MACP,OAAO,EAAE,MAAM,SAAS;AAAA,MACxB,MAAM,EAAE,MAAM,SAAS;AAAA,MACvB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,gBAAgB;AAAA,IAClB;AAAA,EACF;AACA,wBAAsB;AACtB,yBAAuB;AACvB,uBAAqB,CAAC,EAAE;AACxB,SAAO,CAAC;AACR,uBAAqB,CAAC,EAAE;AACxB,UAAQ;AACR,gBAAc;AACd,iBAAe;AACf,oBAAkB;AACpB,GAAG,OAAO;AACV,IAAI,WAAW;AAAA,EACb,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,cAAc;AAChB;AACA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,MAAM;AACR;AACA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;AACA,IAAI,WAA2B,OAAO,SAAS,KAAK;AAClD,MAAI,gBAAgB,aAAa,KAAK,WAAU,CAAC;AACjD,UAAQ;AACV,GAAG,UAAU;AACb,IAAI,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAA2B,OAAO,MAAM,WAAU,EAAE,IAAI,WAAW;AAAA,EACnE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAEF;AAOA,IAAI,YAA4B,OAAO,SAAS,MAAM,UAAU;AAC9D,SAAO,SAAS,MAAM,QAAQ;AAChC,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,SAAS,MAAM,OAAO,QAAQ,GAAG,GAAG,MAAM;AAC/E,QAAM,YAAY,KAAK,OAAO,OAAO;AACrC,YAAU,KAAK,SAAS,KAAK;AAC7B,YAAU,KAAK,UAAU,MAAM;AAC/B,YAAU,KAAK,KAAK,CAAC;AACrB,YAAU,KAAK,KAAK,CAAC;AACrB,MAAI,gBAAgB,KAAK,WAAW,uBAAuB,IAAI,WAAO,iCAAY,IAAI;AACtF,YAAU,KAAK,cAAc,aAAa;AAC5C,GAAG,WAAW;AACd,IAAI,WAA2B,OAAO,CAAC,MAAM,OAAO,UAAU;AAC5D,QAAM,WAAW,KAAK,OAAO,GAAG;AAChC,MAAI,IAAI;AACR,WAAS,OAAO,OAAO;AACrB,QAAI,YAAY,IAAI,YAAY,IAAI,YAAY;AAChD,QAAI,cAAc,IAAI,YAAY,IAAI,YAAY;AAClD,QAAI,UAAU,IAAI,UAAU,SAAS,IAAI,OAAO,IAAI;AACpD,QAAI,UAAU,IAAI,UAAU,SAAS,IAAI,OAAO,IAAI;AACpD,QAAI,MAAM;AACV,QAAI,MAAM,GAAG;AACX,UAAI,OAAO,SAAS,OAAO,MAAM;AACjC,WAAK,KAAK,MAAM,IAAI,WAAW,CAAC;AAChC,WAAK,KAAK,MAAM,IAAI,WAAW,CAAC;AAChC,WAAK,KAAK,MAAM,IAAI,SAAS,CAAC;AAC9B,WAAK,KAAK,MAAM,IAAI,SAAS,CAAC;AAC9B,WAAK,KAAK,gBAAgB,GAAG;AAC7B,WAAK,KAAK,UAAU,WAAW;AAC/B,WAAK,MAAM,QAAQ,MAAM;AACzB,UAAI,IAAI,SAAS,SAAS;AACxB,aAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,MACtD;AACA,UAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,aAAK,KAAK,gBAAgB,SAAS,MAAM,YAAY;AAAA,MACvD;AACA,UAAI;AAAA,IACN,OAAO;AACL,UAAI,OAAO,SAAS,OAAO,MAAM;AACjC,WAAK,KAAK,QAAQ,MAAM,EAAE,KAAK,gBAAgB,GAAG,EAAE,KAAK,UAAU,WAAW,EAAE;AAAA,QAC9E;AAAA,QACA,iDAAiD,WAAW,UAAU,IAAI,WAAW,CAAC,EAAE,WAAW,UAAU,IAAI,WAAW,CAAC,EAAE;AAAA,UAC7H;AAAA,UACA,IAAI,WAAW,KAAK,IAAI,SAAS,IAAI,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,IAAI,IAAI,WAAW,KAAK;AAAA,QACrG,EAAE,WAAW,YAAY,IAAI,WAAW,KAAK,IAAI,SAAS,IAAI,IAAI,WAAW,KAAK,CAAC,EAAE,WAAW,SAAS,IAAI,SAAS,CAAC,EAAE,WAAW,SAAS,IAAI,SAAS,CAAC;AAAA,MAC7J;AACA,UAAI,IAAI,SAAS,SAAS;AACxB,aAAK,KAAK,cAAc,SAAS,MAAM,aAAa;AAAA,MACtD;AACA,UAAI,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS;AAChD,aAAK,KAAK,gBAAgB,SAAS,MAAM,YAAY;AAAA,MACvD;AAAA,IACF;AACA,QAAI,cAAc,MAAM,YAAY;AACpC,2BAAuB,KAAK;AAAA,MAC1B,IAAI,MAAM;AAAA,MACV;AAAA,MACA,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI;AAAA,MAC/F,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI;AAAA,MAC/F,IAAI,MAAM;AAAA,MACV,IAAI,MAAM;AAAA,MACV,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AACA,QAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AACtC,oBAAc,MAAM,YAAY;AAChC,6BAAuB,KAAK;AAAA,QAC1B,MAAM,IAAI,MAAM,OAAO;AAAA,QACvB;AAAA,QACA,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI;AAAA,QAC/F,KAAK,IAAI,IAAI,WAAW,GAAG,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,IAAI,IAAI,WAAW,CAAC,IAAI,IAAI,MAAM,kBAAkB,IAAI;AAAA,QAC3H,KAAK,IAAI,IAAI,MAAM,OAAO,IAAI,MAAM,KAAK;AAAA,QACzC,IAAI,MAAM;AAAA,QACV,EAAE,MAAM,WAAW,cAAc,SAAS;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,GAAG,UAAU;AACb,IAAI,eAA+B,OAAO,SAAS,MAAM,UAAU,OAAO;AACxE,QAAM,eAAe,KAAK,OAAO,GAAG;AACpC,MAAI,YAAY,SAAS,UAAU,SAAS,UAAU;AACtD,MAAI,cAAc,SAAS,cAAc,SAAS,cAAc;AAChE,MAAI,YAAY,SAAS,YAAY,SAAS,YAAY;AAC1D,MAAI,aAAa,EAAE,gBAAgB,GAAG,oBAAoB,UAAU;AACpE,MAAI,SAAS,UAAU;AACrB,iBAAa,EAAE,gBAAgB,EAAE;AAAA,EACnC;AACA,MAAI,WAAW;AAAA,IACb,GAAG,SAAS;AAAA,IACZ,GAAG,SAAS;AAAA,IACZ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,IACjB,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,EACT;AACA,YAAU,cAAc,QAAQ;AAChC,MAAI,eAAe,MAAM,aAAa;AACtC,eAAa,aAAa;AAC1B,eAAa,WAAW,aAAa,WAAW;AAChD,eAAa,YAAY;AACzB,yBAAuB,KAAK;AAAA,IAC1B,SAAS,MAAM;AAAA,IACf;AAAA,IACA,SAAS;AAAA,IACT,SAAS,IAAI,SAAS,MAAM;AAAA,IAC5B,SAAS;AAAA,IACT,SAAS;AAAA,IACT,EAAE,MAAM,UAAU;AAAA,IAClB;AAAA,EACF;AACA,MAAI,SAAS,QAAQ,SAAS,KAAK,SAAS,IAAI;AAC9C,mBAAe,MAAM,aAAa;AAClC,iBAAa,YAAY;AACzB,2BAAuB,KAAK;AAAA,MAC1B,SAAS,KAAK;AAAA,MACd;AAAA,MACA,SAAS;AAAA,MACT,SAAS,IAAI,SAAS,KAAK;AAAA,MAC3B,SAAS;AAAA,MACT,SAAS;AAAA,MACT,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,SAAS,SAAS,MAAM,SAAS,IAAI;AAChD,mBAAe,MAAM,aAAa;AAClC,iBAAa,WAAW,aAAa,WAAW;AAChD,iBAAa,YAAY;AACzB,2BAAuB,KAAK;AAAA,MAC1B,SAAS,MAAM;AAAA,MACf;AAAA,MACA,SAAS;AAAA,MACT,SAAS,IAAI,SAAS,MAAM;AAAA,MAC5B,SAAS;AAAA,MACT,SAAS;AAAA,MACT,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF,GAAG,cAAc;AACjB,IAAI,cAA8B,OAAO,SAAS,MAAM,SAAS,OAAO;AAj0DxE,MAAAC;AAk0DE,MAAI,YAAY,QAAQ,UAAU,QAAQ,UAAU,MAAM,QAAQ,YAAY,OAAO,WAAW;AAChG,MAAI,cAAc,QAAQ,cAAc,QAAQ,cAAc,MAAM,QAAQ,YAAY,OAAO,eAAe;AAC9G,MAAI,YAAY,QAAQ,YAAY,QAAQ,YAAY;AACxD,MAAI,YAAY;AAChB,UAAQ,QAAQ,YAAY,MAAM;AAAA,IAChC,KAAK;AACH,kBAAY;AACZ;AAAA,IACF,KAAK;AACH,kBAAY;AACZ;AAAA,EACJ;AACA,QAAM,cAAc,KAAK,OAAO,GAAG;AACnC,cAAY,KAAK,SAAS,YAAY;AACtC,QAAM,OAAO,YAAY;AACzB,UAAQ,QAAQ,YAAY,MAAM;AAAA,IAChC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,WAAK,IAAI,QAAQ;AACjB,WAAK,IAAI,QAAQ;AACjB,WAAK,OAAO;AACZ,WAAK,QAAQ,QAAQ;AACrB,WAAK,SAAS,QAAQ;AACtB,WAAK,SAAS;AACd,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,QAAQ,EAAE,gBAAgB,IAAI;AACnC,gBAAU,aAAa,IAAI;AAC3B;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,OAAO,MAAM,EAAE,KAAK,QAAQ,SAAS,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,UAAU,WAAW,EAAE;AAAA,QACzG;AAAA,QACA,4HAA4H,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,QAAQ,QAAQ,QAAQ,CAAC,EAAE,WAAW,UAAU,QAAQ,MAAM;AAAA,MACvQ;AACA,kBAAY,OAAO,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,UAAU,WAAW,EAAE;AAAA,QACtG;AAAA,QACA,0DAA0D,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,QAAQ,QAAQ,QAAQ,CAAC;AAAA,MAChK;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,kBAAY,OAAO,MAAM,EAAE,KAAK,QAAQ,SAAS,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,UAAU,WAAW,EAAE;AAAA,QACzG;AAAA,QACA,kHAAkH,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,SAAS,QAAQ,KAAK,EAAE,WAAW,QAAQ,QAAQ,SAAS,CAAC;AAAA,MAC5P;AACA,kBAAY,OAAO,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,UAAU,WAAW,EAAE;AAAA,QACtG;AAAA,QACA,2DAA2D,WAAW,UAAU,QAAQ,IAAI,QAAQ,KAAK,EAAE,WAAW,UAAU,QAAQ,CAAC,EAAE,WAAW,QAAQ,QAAQ,SAAS,CAAC;AAAA,MAClL;AACA;AAAA,EACJ;AACA,MAAI,kBAAkB,eAAe,OAAO,QAAQ,YAAY,IAAI;AACpE,cAAY,OAAO,MAAM,EAAE,KAAK,QAAQ,SAAS,EAAE,KAAK,eAAe,gBAAgB,UAAU,EAAE,KAAK,aAAa,gBAAgB,WAAW,CAAC,EAAE,KAAK,cAAc,QAAQ,EAAE,KAAK,gBAAgB,SAAS,EAAE,KAAK,cAAc,QAAQ,YAAY,KAAK,EAAE,KAAK,KAAK,QAAQ,IAAI,QAAQ,QAAQ,IAAI,QAAQ,YAAY,QAAQ,CAAC,EAAE,KAAK,KAAK,QAAQ,IAAI,QAAQ,YAAY,CAAC,EAAE,KAAK,OAAO,QAAQ,YAAY,OAAO,IAAI;AAC/Z,UAAQ,QAAQ,YAAY,MAAM;AAAA,IAChC,KAAK;AAAA,IACL,KAAK;AACH;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,IAAI,QAAQ,QAAQ,IAAI;AAAA,QAChC,QAAQ,IAAI,QAAQ,MAAM;AAAA,QAC1B;AAAA,MACF;AACA;AAAA,EACJ;AACA,MAAI,eAAe,MAAM,QAAQ,YAAY,OAAO,MAAM,EAAE;AAC5D,eAAa,aAAa;AAC1B,eAAa,WAAW,aAAa,WAAW;AAChD,eAAa,YAAY;AACzB,yBAAuB,KAAK;AAAA,IAC1B,QAAQ,MAAM;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,IAAI,QAAQ,MAAM;AAAA,IAC1B,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,EAAE,MAAM,UAAU;AAAA,IAClB;AAAA,EACF;AACA,iBAAe,MAAM,QAAQ,YAAY,OAAO,MAAM,EAAE;AACxD,eAAa,YAAY;AACzB,MAAI,QAAQ,WAASA,MAAA,QAAQ,UAAR,gBAAAA,IAAe,UAAS,IAAI;AAC/C,2BAAuB,KAAK;AAAA,MAC1B,QAAQ,MAAM;AAAA,MACd;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,IAAI,QAAQ,MAAM;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,EAAE,MAAM,WAAW,cAAc,SAAS;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,WAAW,QAAQ,QAAQ,QAAQ,KAAK,SAAS,IAAI;AACnD,2BAAuB,KAAK;AAAA,MAC1B,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,IAAI,QAAQ,KAAK;AAAA,MACzB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,EAAE,MAAM,WAAW,cAAc,SAAS;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,QAAQ,MAAM,SAAS,IAAI;AAC9C,mBAAe,MAAM,WAAW;AAChC,iBAAa,YAAY;AACzB,2BAAuB,KAAK;AAAA,MAC1B,QAAQ,MAAM;AAAA,MACd;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,IAAI,QAAQ,MAAM;AAAA,MAC1B,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,EAAE,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,SAAO,QAAQ;AACjB,GAAG,aAAa;AAChB,IAAI,qBAAqC,OAAO,SAAS,MAAM;AAC7D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,UAAU,EAAE,KAAK,aAAa,SAAS,EAAE,KAAK,aAAa,SAAS,EAAE,OAAO,MAAM,EAAE,KAAK,aAAa,WAAW,EAAE;AAAA,IAClK;AAAA,IACA;AAAA,EACF;AACF,GAAG,oBAAoB;AACvB,IAAI,qBAAqC,OAAO,SAAS,MAAM;AAC7D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,UAAU,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,aAAa,WAAW,EAAE;AAAA,IACjJ;AAAA,IACA;AAAA,EACF;AACF,GAAG,oBAAoB;AACvB,IAAI,kBAAkC,OAAO,SAAS,MAAM;AAC1D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,OAAO,EAAE,KAAK,SAAS,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,aAAa,WAAW,EAAE;AAAA,IAC9I;AAAA,IACA;AAAA,EACF;AACF,GAAG,iBAAiB;AACpB,IAAI,kBAAkC,OAAO,SAAS,MAAM;AAC1D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,WAAW,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,uBAAuB;AAC7P,GAAG,iBAAiB;AACpB,IAAI,iBAAiC,OAAO,SAAS,MAAM;AACzD,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,UAAU,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,gBAAgB,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,wBAAwB;AAC7P,GAAG,gBAAgB;AACnB,IAAI,wBAAwC,OAAO,SAAS,MAAM;AAChE,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,aAAa,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B;AAC9N,GAAG,uBAAuB;AAC1B,IAAI,sBAAsC,OAAO,SAAS,MAAM;AAC9D,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,gBAAgB,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC;AACxO,GAAG,qBAAqB;AACxB,IAAI,uBAAuC,OAAO,SAAS,MAAM;AAC/D,QAAM,OAAO,KAAK,OAAO,MAAM;AAC/B,QAAM,SAAS,KAAK,OAAO,QAAQ,EAAE,KAAK,MAAM,WAAW,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,CAAC,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC;AACnK,SAAO,OAAO,MAAM,EAAE,KAAK,QAAQ,OAAO,EAAE,KAAK,UAAU,SAAS,EAAE,MAAM,oBAAoB,MAAM,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,KAAK,mBAAmB;AACjK,SAAO,OAAO,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,UAAU,SAAS,EAAE,MAAM,oBAAoB,MAAM,EAAE,KAAK,gBAAgB,KAAK,EAAE,KAAK,KAAK,yBAAyB;AACxK,GAAG,sBAAsB;AACzB,IAAI,iBAAiC,OAAO,CAAC,KAAK,gBAAgB;AAChE,SAAO;AAAA,IACL,YAAY,IAAI,cAAc,YAAY;AAAA,IAC1C,UAAU,IAAI,cAAc,UAAU;AAAA,IACtC,YAAY,IAAI,cAAc,YAAY;AAAA,EAC5C;AACF,GAAG,gBAAgB;AACnB,IAAI,yBAAyC,WAAW;AACtD,WAAS,OAAO,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW;AAC1D,UAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,OAAO;AAChI,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,QAAQ,QAAQ;AACvB,WAAS,QAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AAClE,UAAM,EAAE,UAAU,YAAY,WAAW,IAAI;AAC7C,UAAM,QAAQ,QAAQ,MAAM,eAAe,cAAc;AACzD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,KAAK,IAAI,WAAW,YAAY,MAAM,SAAS,KAAK;AAC1D,YAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,qBAAqB,QAAQ,EAAE,MAAM,aAAa,QAAQ,EAAE,MAAM,eAAe,UAAU,EAAE,MAAM,eAAe,UAAU;AACpO,WAAK,OAAO,OAAO,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,sBAAsB,cAAc;AAC5F,oBAAc,MAAM,SAAS;AAAA,IAC/B;AAAA,EACF;AACA,SAAO,SAAS,SAAS;AACzB,WAAS,KAAK,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,OAAO;AAC/D,UAAM,IAAI,EAAE,OAAO,QAAQ;AAC3B,UAAM,IAAI,EAAE,OAAO,eAAe,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM;AACxG,UAAM,OAAO,EAAE,OAAO,WAAW,EAAE,MAAM,WAAW,OAAO,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,SAAS,MAAM;AAC1G,SAAK,OAAO,KAAK,EAAE,MAAM,WAAW,YAAY,EAAE,MAAM,cAAc,QAAQ,EAAE,MAAM,kBAAkB,QAAQ,EAAE,KAAK,OAAO;AAC9H,YAAQ,SAAS,GAAG,GAAG,GAAG,OAAO,QAAQ,WAAW,KAAK;AACzD,kBAAc,MAAM,SAAS;AAAA,EAC/B;AACA,SAAO,MAAM,MAAM;AACnB,WAAS,cAAc,QAAQ,mBAAmB;AAChD,eAAW,OAAO,mBAAmB;AACnC,UAAI,kBAAkB,eAAe,GAAG,GAAG;AACzC,eAAO,KAAK,KAAK,kBAAkB,GAAG,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,eAAe;AACrC,SAAO,SAAS,OAAO;AACrB,WAAO,MAAM,kBAAkB,OAAO,OAAO,MAAM,kBAAkB,QAAQ,SAAS;AAAA,EACxF;AACF,EAAE;AACF,IAAI,kBAAkB;AAAA,EACpB,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,OAAO,KAAK;AACZ,IAAI,OAAO,CAAC;AAjjEZ;AAkjEA,IAAI,UAAS,WAAM;AAAA,EAIjB,YAAY,SAAS;AACnB,SAAK,OAAO;AACZ,SAAK,OAAO,CAAC;AACb,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,QAAQ;AAClB,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,QAAQ;AAClB,SAAK,KAAK,aAAa;AACvB,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,QAAQ;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,QAAQ;AACtB,SAAK,SAAS,MAAM;AACpB,YAAQ,QAAQ,GAAG,UAAU,CAAC;AAAA,EAChC;AAAA,EACA,QAAQ,QAAQ,OAAO,QAAQ,OAAO;AACpC,SAAK,SAAS,SAAS,KAAK,KAAK,SAAS;AAC1C,SAAK,SAAS,QAAQ,KAAK,KAAK,QAAQ;AACxC,SAAK,SAAS,SAAS,KAAK,KAAK,SAAS;AAC1C,SAAK,SAAS,QAAQ,KAAK,KAAK,QAAQ;AAAA,EAC1C;AAAA,EACA,UAAU,KAAK,KAAK,KAAK,KAAK;AAC5B,QAAI,IAAI,GAAG,MAAM,QAAQ;AACvB,UAAI,GAAG,IAAI;AAAA,IACb,OAAO;AACL,UAAI,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,SAAK,SAAS,MAAM,KAAK,SAAS,MAAM;AACxC,QAAI,UAAU,KAAK,SAAS,WAAW,KAAK,SAAS,QAAQ,KAAK,SAAS,QAAQ,QAAQ,SAAS,KAAK,SAAS,QAAQ,QAAQ,SAAS;AAC3I,QAAI,SAAS,UAAU,QAAQ;AAC/B,QAAI,UAAU,KAAK,SAAS,SAAS,QAAQ,SAAS;AACtD,QAAI,SAAS,UAAU,QAAQ;AAC/B,QAAI,WAAW,KAAK,KAAK,cAAc,UAAU,KAAK,KAAK,cAAc,KAAK,SAAS,MAAM,eAAe;AAC1G,gBAAU,KAAK,SAAS,SAAS,QAAQ,SAAS,KAAK;AACvD,gBAAU,KAAK,SAAS,QAAQ,QAAQ,SAAS;AACjD,WAAK,SAAS,QAAQ,SAAS,UAAU,QAAQ;AACjD,WAAK,SAAS,SAAS,KAAK,SAAS;AACrC,WAAK,SAAS,QAAQ,SAAS,UAAU,QAAQ;AACjD,WAAK,SAAS,MAAM;AAAA,IACtB;AACA,YAAQ,IAAI;AACZ,YAAQ,IAAI;AACZ,SAAK,UAAU,KAAK,MAAM,UAAU,SAAS,KAAK,GAAG;AACrD,SAAK,UAAU,KAAK,MAAM,UAAU,SAAS,KAAK,GAAG;AACrD,SAAK,UAAU,KAAK,MAAM,SAAS,QAAQ,KAAK,GAAG;AACnD,SAAK,UAAU,KAAK,MAAM,SAAS,QAAQ,KAAK,GAAG;AACnD,SAAK,UAAU,KAAK,UAAU,UAAU,SAAS,KAAK,GAAG;AACzD,SAAK,UAAU,KAAK,UAAU,UAAU,SAAS,KAAK,GAAG;AACzD,SAAK,UAAU,KAAK,UAAU,SAAS,QAAQ,KAAK,GAAG;AACvD,SAAK,UAAU,KAAK,UAAU,SAAS,QAAQ,KAAK,GAAG;AAAA,EACzD;AAAA,EACA,KAAK,SAAS;AACZ,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AACA,SAAK,WAAW;AAAA,MACd,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AACA,YAAQ,QAAQ,GAAG,UAAU,CAAC;AAAA,EAChC;AAAA,EACA,eAAe,QAAQ;AACrB,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,SAAS;AAAA,EACrB;AACF,GA9EI,OAAO,IAAM,QAAQ,GAFZ;AAiFb,IAAI,UAA0B,OAAO,SAAS,KAAK;AACjD,0BAAwB,MAAM,GAAG;AACjC,MAAI,IAAI,YAAY;AAClB,SAAK,mBAAmB,KAAK,mBAAmB,KAAK,oBAAoB,IAAI;AAAA,EAC/E;AACA,MAAI,IAAI,UAAU;AAChB,SAAK,iBAAiB,KAAK,iBAAiB,KAAK,kBAAkB,IAAI;AAAA,EACzE;AACA,MAAI,IAAI,YAAY;AAClB,SAAK,mBAAmB,KAAK,mBAAmB,KAAK,oBAAoB,IAAI;AAAA,EAC/E;AACF,GAAG,SAAS;AACZ,IAAI,cAA8B,OAAO,CAAC,KAAK,gBAAgB;AAC7D,SAAO;AAAA,IACL,YAAY,IAAI,cAAc,YAAY;AAAA,IAC1C,UAAU,IAAI,cAAc,UAAU;AAAA,IACtC,YAAY,IAAI,cAAc,YAAY;AAAA,EAC5C;AACF,GAAG,aAAa;AAChB,IAAI,eAA+B,OAAO,CAAC,QAAQ;AACjD,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GAAG,cAAc;AACjB,IAAI,cAA8B,OAAO,CAAC,QAAQ;AAChD,SAAO;AAAA,IACL,YAAY,IAAI;AAAA,IAChB,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB;AACF,GAAG,aAAa;AAChB,SAAS,kBAAkB,UAAU,SAAS,iBAAiB,UAAU,gBAAgB;AACvF,MAAI,CAAC,QAAQ,QAAQ,EAAE,OAAO;AAC5B,QAAI,iBAAiB;AACnB,cAAQ,QAAQ,EAAE,OAAO,UAAU,QAAQ,QAAQ,EAAE,MAAM,gBAAgB,QAAQ;AACnF,cAAQ,QAAQ,EAAE,YAAY,QAAQ,QAAQ,EAAE,KAAK,MAAM,eAAe,cAAc,EAAE;AAC1F,cAAQ,QAAQ,EAAE,QAAQ;AAC1B,cAAQ,QAAQ,EAAE,SAAS,oBAAoB,QAAQ,QAAQ,EAAE,MAAM,QAAQ;AAAA,IACjF,OAAO;AACL,UAAI,QAAQ,QAAQ,QAAQ,EAAE,KAAK,MAAM,eAAe,cAAc;AACtE,cAAQ,QAAQ,EAAE,YAAY,MAAM;AACpC,UAAI,aAAa;AACjB,cAAQ,QAAQ,EAAE,SAAS;AAC3B,cAAQ,QAAQ,EAAE,QAAQ;AAC1B,iBAAW,QAAQ,OAAO;AACxB,gBAAQ,QAAQ,EAAE,QAAQ,KAAK;AAAA,UAC7B,mBAAmB,MAAM,QAAQ;AAAA,UACjC,QAAQ,QAAQ,EAAE;AAAA,QACpB;AACA,qBAAa,oBAAoB,MAAM,QAAQ;AAC/C,gBAAQ,QAAQ,EAAE,SAAS,QAAQ,QAAQ,EAAE,SAAS;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AACF;AACA,OAAO,mBAAmB,mBAAmB;AAC7C,IAAI,gBAAgC,OAAO,SAAS,UAAU,UAAU,QAAQ;AAC9E,WAAS,IAAI,OAAO,KAAK;AACzB,WAAS,IAAI,OAAO,KAAK;AACzB,WAAS,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK;AACjD,WAAS,SAAS,OAAO,KAAK,QAAQ,OAAO,KAAK;AAClD,WAAS,MAAM,IAAI,KAAK,gBAAgB;AACxC,MAAI,mBAAmB,SAAS,QAAQ,KAAK;AAC7C,MAAI,oBAAoB,aAAa,IAAI;AACzC,oBAAkB,WAAW,kBAAkB,WAAW;AAC1D,oBAAkB,aAAa;AAC/B,MAAI,iBAAiB,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAC9E,oBAAkB,SAAS,UAAU,kBAAkB,mBAAmB,cAAc;AACxF,kBAAgB,aAAa,UAAU,UAAU,IAAI;AACvD,GAAG,cAAc;AACjB,IAAI,mBAAmC,OAAO,SAAS,eAAe,UAAU,eAAe,aAAa;AAC1G,MAAI,IAAI;AACR,aAAW,cAAc,aAAa;AACpC,QAAI;AACJ,UAAM,UAAU,cAAc,UAAU;AACxC,QAAI,kBAAkB,YAAY,MAAM,QAAQ,YAAY,IAAI;AAChE,oBAAgB,WAAW,gBAAgB,WAAW;AACtD,YAAQ,YAAY,QAAQ;AAAA,MAC1B,MAAS,QAAQ,YAAY,OAAO;AAAA,MACpC;AAAA,IACF;AACA,YAAQ,YAAY,SAAS,gBAAgB,WAAW;AACxD,YAAQ,YAAY,IAAI,KAAK;AAC7B,QAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,SAAS;AACzD,YAAQ,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,EAAE;AAC5C,YAAQ,QAAQ,YAAY,MAAM;AAAA,MAChC,KAAK;AAAA,MACL,KAAK;AACH,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,IAAI;AAClB,YAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AACpC;AAAA,IACJ;AACA,QAAI,QAAQ,QAAQ;AAClB,cAAQ,MAAM,QAAQ;AACtB,cAAQ,MAAM,SAAS;AACvB,cAAQ,MAAM,IAAI;AAClB,UAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,IACtC;AACA,QAAI,kBAAkB,QAAQ,QAAQ,KAAK;AAC3C,QAAI,iBAAiB,KAAK,QAAQ,KAAK,iBAAiB;AACxD,QAAI,mBAAmB,YAAY,MAAM,QAAQ,YAAY,IAAI;AACjE,qBAAiB,WAAW,iBAAiB,WAAW;AACxD,qBAAiB,aAAa;AAC9B,sBAAkB,SAAS,SAAS,iBAAiB,kBAAkB,cAAc;AACrF,YAAQ,MAAM,IAAI,IAAI;AACtB,QAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AACpC,QAAI,QAAQ,QAAQ,QAAQ,KAAK,SAAS,IAAI;AAC5C,cAAQ,KAAK,OAAO,MAAM,QAAQ,KAAK,OAAO;AAC9C,UAAI,mBAAmB,YAAY,MAAM,QAAQ,YAAY,IAAI;AACjE,wBAAkB,QAAQ,SAAS,iBAAiB,kBAAkB,cAAc;AACpF,cAAQ,KAAK,IAAI,IAAI;AACrB,UAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,IACpC,WAAW,QAAQ,SAAS,QAAQ,MAAM,SAAS,IAAI;AACrD,cAAQ,MAAM,OAAO,MAAM,QAAQ,MAAM,OAAO;AAChD,UAAI,mBAAmB,YAAY,MAAM,QAAQ,MAAM,IAAI;AAC3D,wBAAkB,SAAS,SAAS,iBAAiB,kBAAkB,cAAc;AACrF,cAAQ,MAAM,IAAI,IAAI;AACtB,UAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,IACtC;AACA,QAAI,aAAa;AACjB,QAAI,YAAY,QAAQ,MAAM;AAC9B,QAAI,QAAQ,SAAS,QAAQ,MAAM,SAAS,IAAI;AAC9C,UAAI,mBAAmB,YAAY,MAAM,QAAQ,YAAY,IAAI;AACjE,wBAAkB,SAAS,SAAS,iBAAiB,kBAAkB,cAAc;AACrF,cAAQ,MAAM,IAAI,IAAI;AACtB,UAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AACpC,kBAAY,KAAK,IAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,KAAK;AAC7D,mBAAa,IAAI,QAAQ,MAAM,YAAY;AAAA,IAC7C;AACA,gBAAY,YAAY,KAAK;AAC7B,YAAQ,QAAQ,KAAK,IAAI,QAAQ,SAAS,KAAK,OAAO,WAAW,KAAK,KAAK;AAC3E,YAAQ,SAAS,KAAK,IAAI,QAAQ,UAAU,KAAK,QAAQ,YAAY,KAAK,MAAM;AAChF,YAAQ,SAAS,QAAQ,UAAU,KAAK;AACxC,kBAAc,OAAO,OAAO;AAC5B,oBAAgB,YAAY,UAAU,SAAS,IAAI;AAAA,EACrD;AACA,gBAAc,eAAe,KAAK,aAAa;AACjD,GAAG,kBAAkB;AAhxErB,IAAAA;AAixEA,IAAI,SAAQA,MAAA,MAAM;AAAA,EAIhB,YAAY,GAAG,GAAG;AAChB,SAAK,IAAI;AACT,SAAK,IAAI;AAAA,EACX;AACF,GANI,OAAOA,KAAM,OAAO,GAFZA;AASZ,IAAI,oBAAoC,OAAO,SAAS,UAAU,UAAU;AAC1E,MAAI,KAAK,SAAS;AAClB,MAAI,KAAK,SAAS;AAClB,MAAI,KAAK,SAAS;AAClB,MAAI,KAAK,SAAS;AAClB,MAAI,cAAc,KAAK,SAAS,QAAQ;AACxC,MAAI,cAAc,KAAK,SAAS,SAAS;AACzC,MAAI,KAAK,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,KAAK,KAAK,IAAI,KAAK,EAAE;AACzB,MAAI,SAAS,KAAK;AAClB,MAAI,UAAU,SAAS,SAAS,SAAS;AACzC,MAAI,cAAc;AAClB,MAAI,MAAM,MAAM,KAAK,IAAI;AACvB,kBAAc,IAAI,MAAM,KAAK,SAAS,OAAO,WAAW;AAAA,EAC1D,WAAW,MAAM,MAAM,KAAK,IAAI;AAC9B,kBAAc,IAAI,MAAM,IAAI,WAAW;AAAA,EACzC,WAAW,MAAM,MAAM,KAAK,IAAI;AAC9B,kBAAc,IAAI,MAAM,aAAa,KAAK,SAAS,MAAM;AAAA,EAC3D,WAAW,MAAM,MAAM,KAAK,IAAI;AAC9B,kBAAc,IAAI,MAAM,aAAa,EAAE;AAAA,EACzC;AACA,MAAI,KAAK,MAAM,KAAK,IAAI;AACtB,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,IAAI,cAAc,SAAS,SAAS,QAAQ,CAAC;AAAA,IACvE,OAAO;AACL,oBAAc,IAAI;AAAA,QAChB,cAAc,KAAK,KAAK,SAAS,SAAS;AAAA,QAC1C,KAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF,WAAW,KAAK,MAAM,KAAK,IAAI;AAC7B,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,KAAK,SAAS,OAAO,cAAc,SAAS,SAAS,QAAQ,CAAC;AAAA,IACxF,OAAO;AACL,oBAAc,IAAI;AAAA,QAChB,cAAc,KAAK,KAAK,SAAS,SAAS;AAAA,QAC1C,KAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF,WAAW,KAAK,MAAM,KAAK,IAAI;AAC7B,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,KAAK,SAAS,OAAO,cAAc,SAAS,SAAS,QAAQ,CAAC;AAAA,IACxF,OAAO;AACL,oBAAc,IAAI,MAAM,cAAc,SAAS,SAAS,IAAI,KAAK,IAAI,EAAE;AAAA,IACzE;AAAA,EACF,WAAW,KAAK,MAAM,KAAK,IAAI;AAC7B,QAAI,WAAW,QAAQ;AACrB,oBAAc,IAAI,MAAM,IAAI,cAAc,SAAS,QAAQ,IAAI,MAAM;AAAA,IACvE,OAAO;AACL,oBAAc,IAAI,MAAM,cAAc,SAAS,SAAS,IAAI,KAAK,IAAI,EAAE;AAAA,IACzE;AAAA,EACF;AACA,SAAO;AACT,GAAG,mBAAmB;AACtB,IAAI,qBAAqC,OAAO,SAAS,UAAU,SAAS;AAC1E,MAAI,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AACrC,oBAAkB,IAAI,QAAQ,IAAI,QAAQ,QAAQ;AAClD,oBAAkB,IAAI,QAAQ,IAAI,QAAQ,SAAS;AACnD,MAAI,aAAa,kBAAkB,UAAU,iBAAiB;AAC9D,oBAAkB,IAAI,SAAS,IAAI,SAAS,QAAQ;AACpD,oBAAkB,IAAI,SAAS,IAAI,SAAS,SAAS;AACrD,MAAI,WAAW,kBAAkB,SAAS,iBAAiB;AAC3D,SAAO,EAAE,YAAY,SAAS;AAChC,GAAG,oBAAoB;AACvB,IAAI,YAA4B,OAAO,SAAS,UAAU,OAAO,eAAe,SAAS;AACvF,MAAI,IAAI;AACR,WAAS,OAAO,OAAO;AACrB,QAAI,IAAI;AACR,QAAI,cAAc,IAAI,QAAQ,KAAK;AACnC,QAAI,UAAU,YAAY,IAAI;AAC9B,QAAI,cAAc,QAAQ,GAAG,UAAU;AACvC,QAAI,gBAAgB,aAAa;AAC/B,UAAI,MAAM,OAAO,IAAI,OAAO,IAAI,MAAM;AAAA,IACxC;AACA,QAAI,iBAAiB,mBAAmB,IAAI,MAAM,MAAM,OAAO;AAC/D,sBAAkB,SAAS,KAAK,aAAa,SAAS,cAAc;AACpE,QAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AACtC,uBAAiB,mBAAmB,IAAI,MAAM,MAAM,OAAO;AAC3D,wBAAkB,SAAS,KAAK,aAAa,SAAS,cAAc;AAAA,IACtE;AACA,QAAI,IAAI,SAAS,IAAI,MAAM,SAAS,IAAI;AACtC,uBAAiB,mBAAmB,IAAI,MAAM,MAAM,OAAO;AAC3D,wBAAkB,SAAS,KAAK,aAAa,SAAS,cAAc;AAAA,IACtE;AACA,QAAI,WAAW,cAAc,IAAI,IAAI;AACrC,QAAI,UAAU,cAAc,IAAI,EAAE;AAClC,QAAI,SAAS,mBAAmB,UAAU,OAAO;AACjD,QAAI,aAAa,OAAO;AACxB,QAAI,WAAW,OAAO;AAAA,EACxB;AACA,kBAAgB,SAAS,UAAU,OAAO,IAAI;AAChD,GAAG,UAAU;AACb,SAAS,mBAAmB,UAAU,qBAAqB,cAAc,mBAAmB,SAAS;AACnG,MAAI,gBAAgB,IAAI,OAAO,OAAO;AACtC,gBAAc,KAAK,aAAa,aAAa,KAAK,aAAa,KAAK,IAAI,kBAAkB,kBAAkB,MAAM;AAClH,WAAS,CAAC,GAAG,eAAe,KAAK,kBAAkB,QAAQ,GAAG;AAC5D,QAAI,IAAI;AACR,oBAAgB,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,EAAE;AACpD,QAAI,gBAAgB,QAAQ;AAC1B,sBAAgB,MAAM,QAAQ;AAC9B,sBAAgB,MAAM,SAAS;AAC/B,sBAAgB,MAAM,IAAI;AAC1B,UAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAAA,IACtD;AACA,QAAI,0BAA0B,gBAAgB,QAAQ,KAAK;AAC3D,QAAI,2BAA2B,aAAa,IAAI;AAChD,6BAAyB,WAAW,yBAAyB,WAAW;AACxE,6BAAyB,aAAa;AACtC;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,KAAK;AAAA,IACrB;AACA,oBAAgB,MAAM,IAAI,IAAI;AAC9B,QAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AACpD,QAAI,gBAAgB,QAAQ,gBAAgB,KAAK,SAAS,IAAI;AAC5D,sBAAgB,KAAK,OAAO,MAAM,gBAAgB,KAAK,OAAO;AAC9D,UAAI,0BAA0B,aAAa,IAAI;AAC/C;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,KAAK;AAAA,MACrB;AACA,sBAAgB,KAAK,IAAI,IAAI;AAC7B,UAAI,gBAAgB,KAAK,IAAI,gBAAgB,KAAK;AAAA,IACpD;AACA,QAAI,gBAAgB,SAAS,gBAAgB,MAAM,SAAS,IAAI;AAC9D,UAAI,2BAA2B,aAAa,IAAI;AAChD,+BAAyB,WAAW,yBAAyB,WAAW;AACxE;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,KAAK;AAAA,MACrB;AACA,sBAAgB,MAAM,IAAI,IAAI;AAC9B,UAAI,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAAA,IACtD;AACA,QAAI,KAAK,KAAK,IAAI,qBAAqB,GAAG;AACxC,UAAI,KAAK,aAAa,KAAK,SAAS,KAAK;AACzC,UAAI,KAAK,aAAa,KAAK,QAAQ,KAAK,iBAAiB;AACzD,oBAAc,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,IACtC,OAAO;AACL,UAAI,KAAK,cAAc,KAAK,UAAU,cAAc,KAAK,SAAS,cAAc,KAAK,QAAQ,KAAK,iBAAiB,cAAc,KAAK;AACtI,UAAI,KAAK,cAAc,KAAK;AAC5B,oBAAc,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,IACtC;AACA,kBAAc,OAAO,gBAAgB;AACrC,QAAI,6BAA6B,QAAQ,GAAG,gBAAgB,gBAAgB,KAAK;AACjF,QAAI,4BAA4B,QAAQ,GAAG,eAAe,gBAAgB,KAAK;AAC/E,QAAI,0BAA0B,SAAS,GAAG;AACxC;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,0BAAsB,gBAAgB;AACtC,QAAI,wBAAwB,QAAQ,GAAG,aAAa,mBAAmB;AACvE,QAAI,sBAAsB,SAAS,GAAG;AACpC;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,gBAAgB,UAAU,UAAU;AACtC,oBAAc,UAAU,iBAAiB,aAAa;AAAA,IACxD;AACA,iBAAa,KAAK,QAAQ,KAAK;AAAA,MAC7B,cAAc,KAAK,QAAQ,KAAK;AAAA,MAChC,aAAa,KAAK;AAAA,IACpB;AACA,iBAAa,KAAK,QAAQ,KAAK;AAAA,MAC7B,cAAc,KAAK,QAAQ,KAAK;AAAA,MAChC,aAAa,KAAK;AAAA,IACpB;AACA,yBAAqB,KAAK,IAAI,oBAAoB,aAAa,KAAK,KAAK;AACzE,yBAAqB,KAAK,IAAI,oBAAoB,aAAa,KAAK,KAAK;AAAA,EAC3E;AACF;AACA,OAAO,oBAAoB,oBAAoB;AAC/C,IAAI,OAAuB,OAAO,SAAS,OAAO,IAAI,UAAU,SAAS;AACvE,SAAO,WAAU,EAAE;AACnB,QAAM,gBAAgB,WAAU,EAAE;AAClC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,eAAO,OAAO,EAAE;AAAA,EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,eAAO,eAAe,MAAM,EAAE,CAAC,EAAE,gBAAgB,IAAI,IAAI,eAAO,MAAM;AACjH,MAAI,KAAK,QAAQ;AACjB,UAAQ,GAAG,QAAQ,KAAK,IAAI;AAC5B,kBAAgB,GAAG,gBAAgB;AACnC,qBAAmB,GAAG,mBAAmB;AACzC,MAAI,MAAM,KAAK,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC,EAAE;AAC9C,QAAM,WAAW,kBAAkB,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI,IAAI,eAAO,QAAQ,EAAE,IAAI;AAClG,kBAAgB,mBAAmB,QAAQ;AAC3C,kBAAgB,mBAAmB,QAAQ;AAC3C,kBAAgB,gBAAgB,QAAQ;AACxC,MAAI,eAAe,IAAI,OAAO,OAAO;AACrC,eAAa;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AACA,eAAa,KAAK,aAAa,OAAO;AACtC,uBAAqB,KAAK;AAC1B,uBAAqB,KAAK;AAC1B,QAAM,SAAS,QAAQ,GAAG,SAAS;AACnC,MAAI,oBAAoB,QAAQ,GAAG,aAAa,EAAE;AAClD,qBAAmB,UAAU,IAAI,cAAc,mBAAmB,OAAO;AACzE,kBAAgB,gBAAgB,QAAQ;AACxC,kBAAgB,eAAe,QAAQ;AACvC,kBAAgB,qBAAqB,QAAQ;AAC7C,kBAAgB,sBAAsB,QAAQ;AAC9C,YAAU,UAAU,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,YAAY,OAAO;AACxE,eAAa,KAAK,QAAQ;AAC1B,eAAa,KAAK,QAAQ;AAC1B,QAAM,MAAM,aAAa;AACzB,MAAI,YAAY,IAAI,QAAQ,IAAI;AAChC,MAAI,SAAS,YAAY,IAAI,KAAK;AAClC,MAAI,WAAW,IAAI,QAAQ,IAAI;AAC/B,QAAM,QAAQ,WAAW,IAAI,KAAK;AAClC,MAAI,QAAQ;AACV,aAAS,OAAO,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,IAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,KAAK,cAAc,EAAE,KAAK,KAAK,IAAI,SAAS,KAAK,cAAc;AAAA,EACnJ;AACA,mBAAiB,UAAU,QAAQ,OAAO,KAAK,WAAW;AAC1D,QAAM,oBAAoB,SAAS,KAAK;AACxC,WAAS;AAAA,IACP;AAAA,IACA,IAAI,SAAS,KAAK,iBAAiB,QAAQ,KAAK,iBAAiB,qBAAqB,MAAM,QAAQ,OAAO,SAAS;AAAA,EACtH;AACA,MAAI,MAAM,WAAW,GAAG;AAC1B,GAAG,MAAM;AACT,IAAI,qBAAqB;AAAA,EACvB,yBAAyB;AAAA,EACzB,cAAc;AAAA,EACd;AAAA,EACA;AACF;AAGA,IAAI,YAA4B,OAAO,CAAC,YAAY;AAAA,cACtC,QAAQ,YAAY;AAAA,YACtB,QAAQ,SAAS;AAAA;AAAA,GAE1B,WAAW;AACd,IAAI,iBAAiB;AAGrB,IAAI,UAAU;AAAA,EACZ,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAsB,OAAO,CAAC,EAAE,IAAI,KAAK,MAAM;AAC7C,uBAAmB,QAAQ,EAAE;AAC7B,iBAAa,QAAQ,IAAI;AAAA,EAC3B,GAAG,MAAM;AACX;", "names": ["import_dist", "_a"]}