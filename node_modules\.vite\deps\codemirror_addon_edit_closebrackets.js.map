{"version": 3, "sources": ["../../codemirror/addon/edit/closebrackets.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  var defaults = {\n    pairs: \"()[]{}''\\\"\\\"\",\n    closeBefore: \")]}'\\\":;>\",\n    triples: \"\",\n    explode: \"[]{}\"\n  };\n\n  var Pos = CodeMirror.Pos;\n\n  CodeMirror.defineOption(\"autoCloseBrackets\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.removeKeyMap(keyMap);\n      cm.state.closeBrackets = null;\n    }\n    if (val) {\n      ensureBound(getOption(val, \"pairs\"))\n      cm.state.closeBrackets = val;\n      cm.addKeyMap(keyMap);\n    }\n  });\n\n  function getOption(conf, name) {\n    if (name == \"pairs\" && typeof conf == \"string\") return conf;\n    if (typeof conf == \"object\" && conf[name] != null) return conf[name];\n    return defaults[name];\n  }\n\n  var keyMap = {Backspace: handleBackspace, Enter: handleEnter};\n  function ensureBound(chars) {\n    for (var i = 0; i < chars.length; i++) {\n      var ch = chars.charAt(i), key = \"'\" + ch + \"'\"\n      if (!keyMap[key]) keyMap[key] = handler(ch)\n    }\n  }\n  ensureBound(defaults.pairs + \"`\")\n\n  function handler(ch) {\n    return function(cm) { return handleChar(cm, ch); };\n  }\n\n  function getConfig(cm) {\n    var deflt = cm.state.closeBrackets;\n    if (!deflt || deflt.override) return deflt;\n    var mode = cm.getModeAt(cm.getCursor());\n    return mode.closeBrackets || deflt;\n  }\n\n  function handleBackspace(cm) {\n    var conf = getConfig(cm);\n    if (!conf || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var pairs = getOption(conf, \"pairs\");\n    var ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var around = charsAround(cm, ranges[i].head);\n      if (!around || pairs.indexOf(around) % 2 != 0) return CodeMirror.Pass;\n    }\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var cur = ranges[i].head;\n      cm.replaceRange(\"\", Pos(cur.line, cur.ch - 1), Pos(cur.line, cur.ch + 1), \"+delete\");\n    }\n  }\n\n  function handleEnter(cm) {\n    var conf = getConfig(cm);\n    var explode = conf && getOption(conf, \"explode\");\n    if (!explode || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var ranges = cm.listSelections();\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var around = charsAround(cm, ranges[i].head);\n      if (!around || explode.indexOf(around) % 2 != 0) return CodeMirror.Pass;\n    }\n    cm.operation(function() {\n      var linesep = cm.lineSeparator() || \"\\n\";\n      cm.replaceSelection(linesep + linesep, null);\n      moveSel(cm, -1)\n      ranges = cm.listSelections();\n      for (var i = 0; i < ranges.length; i++) {\n        var line = ranges[i].head.line;\n        cm.indentLine(line, null, true);\n        cm.indentLine(line + 1, null, true);\n      }\n    });\n  }\n\n  function moveSel(cm, dir) {\n    var newRanges = [], ranges = cm.listSelections(), primary = 0\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i]\n      if (range.head == cm.getCursor()) primary = i\n      var pos = range.head.ch || dir > 0 ? {line: range.head.line, ch: range.head.ch + dir} : {line: range.head.line - 1}\n      newRanges.push({anchor: pos, head: pos})\n    }\n    cm.setSelections(newRanges, primary)\n  }\n\n  function contractSelection(sel) {\n    var inverted = CodeMirror.cmpPos(sel.anchor, sel.head) > 0;\n    return {anchor: new Pos(sel.anchor.line, sel.anchor.ch + (inverted ? -1 : 1)),\n            head: new Pos(sel.head.line, sel.head.ch + (inverted ? 1 : -1))};\n  }\n\n  function handleChar(cm, ch) {\n    var conf = getConfig(cm);\n    if (!conf || cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n\n    var pairs = getOption(conf, \"pairs\");\n    var pos = pairs.indexOf(ch);\n    if (pos == -1) return CodeMirror.Pass;\n\n    var closeBefore = getOption(conf,\"closeBefore\");\n\n    var triples = getOption(conf, \"triples\");\n\n    var identical = pairs.charAt(pos + 1) == ch;\n    var ranges = cm.listSelections();\n    var opening = pos % 2 == 0;\n\n    var type;\n    for (var i = 0; i < ranges.length; i++) {\n      var range = ranges[i], cur = range.head, curType;\n      var next = cm.getRange(cur, Pos(cur.line, cur.ch + 1));\n      if (opening && !range.empty()) {\n        curType = \"surround\";\n      } else if ((identical || !opening) && next == ch) {\n        if (identical && stringStartsAfter(cm, cur))\n          curType = \"both\";\n        else if (triples.indexOf(ch) >= 0 && cm.getRange(cur, Pos(cur.line, cur.ch + 3)) == ch + ch + ch)\n          curType = \"skipThree\";\n        else\n          curType = \"skip\";\n      } else if (identical && cur.ch > 1 && triples.indexOf(ch) >= 0 &&\n                 cm.getRange(Pos(cur.line, cur.ch - 2), cur) == ch + ch) {\n        if (cur.ch > 2 && /\\bstring/.test(cm.getTokenTypeAt(Pos(cur.line, cur.ch - 2)))) return CodeMirror.Pass;\n        curType = \"addFour\";\n      } else if (identical) {\n        var prev = cur.ch == 0 ? \" \" : cm.getRange(Pos(cur.line, cur.ch - 1), cur)\n        if (!CodeMirror.isWordChar(next) && prev != ch && !CodeMirror.isWordChar(prev)) curType = \"both\";\n        else return CodeMirror.Pass;\n      } else if (opening && (next.length === 0 || /\\s/.test(next) || closeBefore.indexOf(next) > -1)) {\n        curType = \"both\";\n      } else {\n        return CodeMirror.Pass;\n      }\n      if (!type) type = curType;\n      else if (type != curType) return CodeMirror.Pass;\n    }\n\n    var left = pos % 2 ? pairs.charAt(pos - 1) : ch;\n    var right = pos % 2 ? ch : pairs.charAt(pos + 1);\n    cm.operation(function() {\n      if (type == \"skip\") {\n        moveSel(cm, 1)\n      } else if (type == \"skipThree\") {\n        moveSel(cm, 3)\n      } else if (type == \"surround\") {\n        var sels = cm.getSelections();\n        for (var i = 0; i < sels.length; i++)\n          sels[i] = left + sels[i] + right;\n        cm.replaceSelections(sels, \"around\");\n        sels = cm.listSelections().slice();\n        for (var i = 0; i < sels.length; i++)\n          sels[i] = contractSelection(sels[i]);\n        cm.setSelections(sels);\n      } else if (type == \"both\") {\n        cm.replaceSelection(left + right, null);\n        cm.triggerElectric(left + right);\n        moveSel(cm, -1)\n      } else if (type == \"addFour\") {\n        cm.replaceSelection(left + left + left + left, \"before\");\n        moveSel(cm, 1)\n      }\n    });\n  }\n\n  function charsAround(cm, pos) {\n    var str = cm.getRange(Pos(pos.line, pos.ch - 1),\n                          Pos(pos.line, pos.ch + 1));\n    return str.length == 2 ? str : null;\n  }\n\n  function stringStartsAfter(cm, pos) {\n    var token = cm.getTokenAt(Pos(pos.line, pos.ch + 1))\n    return /\\bstring/.test(token.type) && token.start == pos.ch &&\n      (pos.ch == 0 || !/\\bstring/.test(cm.getTokenTypeAt(pos)))\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,QAAAA,eAAA;AAAA,QAAAA,eAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASC,aAAY;AACtB,UAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,aAAa;AAAA,QACb,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAEA,UAAI,MAAMA,YAAW;AAErB,MAAAA,YAAW,aAAa,qBAAqB,OAAO,SAAS,IAAI,KAAK,KAAK;AACzE,YAAI,OAAO,OAAOA,YAAW,MAAM;AACjC,aAAG,aAAa,MAAM;AACtB,aAAG,MAAM,gBAAgB;AAAA,QAC3B;AACA,YAAI,KAAK;AACP,sBAAY,UAAU,KAAK,OAAO,CAAC;AACnC,aAAG,MAAM,gBAAgB;AACzB,aAAG,UAAU,MAAM;AAAA,QACrB;AAAA,MACF,CAAC;AAED,eAAS,UAAU,MAAM,MAAM;AAC7B,YAAI,QAAQ,WAAW,OAAO,QAAQ,SAAU,QAAO;AACvD,YAAI,OAAO,QAAQ,YAAY,KAAK,IAAI,KAAK,KAAM,QAAO,KAAK,IAAI;AACnE,eAAO,SAAS,IAAI;AAAA,MACtB;AAEA,UAAI,SAAS,EAAC,WAAW,iBAAiB,OAAO,YAAW;AAC5D,eAAS,YAAY,OAAO;AAC1B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,KAAK,MAAM,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AAC3C,cAAI,CAAC,OAAO,GAAG,EAAG,QAAO,GAAG,IAAI,QAAQ,EAAE;AAAA,QAC5C;AAAA,MACF;AACA,kBAAY,SAAS,QAAQ,GAAG;AAEhC,eAAS,QAAQ,IAAI;AACnB,eAAO,SAAS,IAAI;AAAE,iBAAO,WAAW,IAAI,EAAE;AAAA,QAAG;AAAA,MACnD;AAEA,eAAS,UAAU,IAAI;AACrB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC,SAAS,MAAM,SAAU,QAAO;AACrC,YAAI,OAAO,GAAG,UAAU,GAAG,UAAU,CAAC;AACtC,eAAO,KAAK,iBAAiB;AAAA,MAC/B;AAEA,eAAS,gBAAgB,IAAI;AAC3B,YAAI,OAAO,UAAU,EAAE;AACvB,YAAI,CAAC,QAAQ,GAAG,UAAU,cAAc,EAAG,QAAOA,YAAW;AAE7D,YAAI,QAAQ,UAAU,MAAM,OAAO;AACnC,YAAI,SAAS,GAAG,eAAe;AAC/B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAG,QAAOA,YAAW;AAC1C,cAAI,SAAS,YAAY,IAAI,OAAO,CAAC,EAAE,IAAI;AAC3C,cAAI,CAAC,UAAU,MAAM,QAAQ,MAAM,IAAI,KAAK,EAAG,QAAOA,YAAW;AAAA,QACnE;AACA,iBAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,cAAI,MAAM,OAAO,CAAC,EAAE;AACpB,aAAG,aAAa,IAAI,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,SAAS;AAAA,QACrF;AAAA,MACF;AAEA,eAAS,YAAY,IAAI;AACvB,YAAI,OAAO,UAAU,EAAE;AACvB,YAAI,UAAU,QAAQ,UAAU,MAAM,SAAS;AAC/C,YAAI,CAAC,WAAW,GAAG,UAAU,cAAc,EAAG,QAAOA,YAAW;AAEhE,YAAI,SAAS,GAAG,eAAe;AAC/B,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAG,QAAOA,YAAW;AAC1C,cAAI,SAAS,YAAY,IAAI,OAAO,CAAC,EAAE,IAAI;AAC3C,cAAI,CAAC,UAAU,QAAQ,QAAQ,MAAM,IAAI,KAAK,EAAG,QAAOA,YAAW;AAAA,QACrE;AACA,WAAG,UAAU,WAAW;AACtB,cAAI,UAAU,GAAG,cAAc,KAAK;AACpC,aAAG,iBAAiB,UAAU,SAAS,IAAI;AAC3C,kBAAQ,IAAI,EAAE;AACd,mBAAS,GAAG,eAAe;AAC3B,mBAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,gBAAI,OAAO,OAAOA,EAAC,EAAE,KAAK;AAC1B,eAAG,WAAW,MAAM,MAAM,IAAI;AAC9B,eAAG,WAAW,OAAO,GAAG,MAAM,IAAI;AAAA,UACpC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,QAAQ,IAAI,KAAK;AACxB,YAAI,YAAY,CAAC,GAAG,SAAS,GAAG,eAAe,GAAG,UAAU;AAC5D,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,MAAM,QAAQ,GAAG,UAAU,EAAG,WAAU;AAC5C,cAAI,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI,EAAC,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,KAAK,IAAG,IAAI,EAAC,MAAM,MAAM,KAAK,OAAO,EAAC;AAClH,oBAAU,KAAK,EAAC,QAAQ,KAAK,MAAM,IAAG,CAAC;AAAA,QACzC;AACA,WAAG,cAAc,WAAW,OAAO;AAAA,MACrC;AAEA,eAAS,kBAAkB,KAAK;AAC9B,YAAI,WAAWD,YAAW,OAAO,IAAI,QAAQ,IAAI,IAAI,IAAI;AACzD,eAAO;AAAA,UAAC,QAAQ,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,KAAK,EAAE;AAAA,UACpE,MAAM,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;AAAA,QAAC;AAAA,MACzE;AAEA,eAAS,WAAW,IAAI,IAAI;AAC1B,YAAI,OAAO,UAAU,EAAE;AACvB,YAAI,CAAC,QAAQ,GAAG,UAAU,cAAc,EAAG,QAAOA,YAAW;AAE7D,YAAI,QAAQ,UAAU,MAAM,OAAO;AACnC,YAAI,MAAM,MAAM,QAAQ,EAAE;AAC1B,YAAI,OAAO,GAAI,QAAOA,YAAW;AAEjC,YAAI,cAAc,UAAU,MAAK,aAAa;AAE9C,YAAI,UAAU,UAAU,MAAM,SAAS;AAEvC,YAAI,YAAY,MAAM,OAAO,MAAM,CAAC,KAAK;AACzC,YAAI,SAAS,GAAG,eAAe;AAC/B,YAAI,UAAU,MAAM,KAAK;AAEzB,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,QAAQ,OAAO,CAAC,GAAG,MAAM,MAAM,MAAM;AACzC,cAAI,OAAO,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;AACrD,cAAI,WAAW,CAAC,MAAM,MAAM,GAAG;AAC7B,sBAAU;AAAA,UACZ,YAAY,aAAa,CAAC,YAAY,QAAQ,IAAI;AAChD,gBAAI,aAAa,kBAAkB,IAAI,GAAG;AACxC,wBAAU;AAAA,qBACH,QAAQ,QAAQ,EAAE,KAAK,KAAK,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK;AAC5F,wBAAU;AAAA;AAEV,wBAAU;AAAA,UACd,WAAW,aAAa,IAAI,KAAK,KAAK,QAAQ,QAAQ,EAAE,KAAK,KAClD,GAAG,SAAS,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,KAAK,IAAI;AACjE,gBAAI,IAAI,KAAK,KAAK,WAAW,KAAK,GAAG,eAAe,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAG,QAAOA,YAAW;AACnG,sBAAU;AAAA,UACZ,WAAW,WAAW;AACpB,gBAAI,OAAO,IAAI,MAAM,IAAI,MAAM,GAAG,SAAS,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG;AACzE,gBAAI,CAACA,YAAW,WAAW,IAAI,KAAK,QAAQ,MAAM,CAACA,YAAW,WAAW,IAAI,EAAG,WAAU;AAAA,gBACrF,QAAOA,YAAW;AAAA,UACzB,WAAW,YAAY,KAAK,WAAW,KAAK,KAAK,KAAK,IAAI,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AAC9F,sBAAU;AAAA,UACZ,OAAO;AACL,mBAAOA,YAAW;AAAA,UACpB;AACA,cAAI,CAAC,KAAM,QAAO;AAAA,mBACT,QAAQ,QAAS,QAAOA,YAAW;AAAA,QAC9C;AAEA,YAAI,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,CAAC,IAAI;AAC7C,YAAI,QAAQ,MAAM,IAAI,KAAK,MAAM,OAAO,MAAM,CAAC;AAC/C,WAAG,UAAU,WAAW;AACtB,cAAI,QAAQ,QAAQ;AAClB,oBAAQ,IAAI,CAAC;AAAA,UACf,WAAW,QAAQ,aAAa;AAC9B,oBAAQ,IAAI,CAAC;AAAA,UACf,WAAW,QAAQ,YAAY;AAC7B,gBAAI,OAAO,GAAG,cAAc;AAC5B,qBAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA;AAC/B,mBAAKA,EAAC,IAAI,OAAO,KAAKA,EAAC,IAAI;AAC7B,eAAG,kBAAkB,MAAM,QAAQ;AACnC,mBAAO,GAAG,eAAe,EAAE,MAAM;AACjC,qBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA;AAC/B,mBAAKA,EAAC,IAAI,kBAAkB,KAAKA,EAAC,CAAC;AACrC,eAAG,cAAc,IAAI;AAAA,UACvB,WAAW,QAAQ,QAAQ;AACzB,eAAG,iBAAiB,OAAO,OAAO,IAAI;AACtC,eAAG,gBAAgB,OAAO,KAAK;AAC/B,oBAAQ,IAAI,EAAE;AAAA,UAChB,WAAW,QAAQ,WAAW;AAC5B,eAAG,iBAAiB,OAAO,OAAO,OAAO,MAAM,QAAQ;AACvD,oBAAQ,IAAI,CAAC;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,YAAY,IAAI,KAAK;AAC5B,YAAI,MAAM,GAAG;AAAA,UAAS,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,UACxB,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,QAAC;AAC/C,eAAO,IAAI,UAAU,IAAI,MAAM;AAAA,MACjC;AAEA,eAAS,kBAAkB,IAAI,KAAK;AAClC,YAAI,QAAQ,GAAG,WAAW,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC;AACnD,eAAO,WAAW,KAAK,MAAM,IAAI,KAAK,MAAM,SAAS,IAAI,OACtD,IAAI,MAAM,KAAK,CAAC,WAAW,KAAK,GAAG,eAAe,GAAG,CAAC;AAAA,MAC3D;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["import_dist", "CodeMirror", "i"]}