{"version": 3, "sources": ["../../vite-plugin-node-polyfills/node_modules/.pnpm/base64-js@1.5.1/node_modules/base64-js/index.js", "../../vite-plugin-node-polyfills/node_modules/.pnpm/ieee754@1.2.1/node_modules/ieee754/index.js", "../../vite-plugin-node-polyfills/node_modules/.pnpm/buffer@6.0.3_patch_hash=zkkuxompt5d553skpnegwi5wuy/node_modules/buffer/index.js", "../../vite-plugin-node-polyfills/shims/buffer/index.ts"], "sourcesContent": ["'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nconst base64 = require('base64-js')\nconst ieee754 = require('ieee754')\nconst customInspectSymbol =\n  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation\n    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n    : null\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\nconst K_MAX_LENGTH = 0x7fffffff\nexports.kMaxLength = K_MAX_LENGTH\nconst { Uint8Array: GlobalUint8Array, ArrayBuffer: GlobalArrayBuffer, SharedArrayBuffer: GlobalSharedArrayBuffer } = globalThis\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&\n    typeof console.error === 'function') {\n  console.error(\n    'This browser lacks typed array (Uint8Array) support which is required by ' +\n    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'\n  )\n}\n\nfunction typedArraySupport () {\n  // Can typed array instances can be augmented?\n  try {\n    const arr = new GlobalUint8Array(1)\n    const proto = { foo: function () { return 42 } }\n    Object.setPrototypeOf(proto, GlobalUint8Array.prototype)\n    Object.setPrototypeOf(arr, proto)\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.buffer\n  }\n})\n\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.byteOffset\n  }\n})\n\nfunction createBuffer (length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"')\n  }\n  // Return an augmented `Uint8Array` instance\n  const buf = new GlobalUint8Array(length)\n  Object.setPrototypeOf(buf, Buffer.prototype)\n  return buf\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError(\n        'The \"string\" argument must be of type string. Received type number'\n      )\n    }\n    return allocUnsafe(arg)\n  }\n  return from(arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\nfunction from (value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  if (GlobalArrayBuffer.isView(value)) {\n    return fromArrayView(value)\n  }\n\n  if (value == null) {\n    throw new TypeError(\n      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n      'or Array-like Object. Received type ' + (typeof value)\n    )\n  }\n\n  if (isInstance(value, GlobalArrayBuffer) ||\n      (value && isInstance(value.buffer, GlobalArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof GlobalSharedArrayBuffer !== 'undefined' &&\n      (isInstance(value, GlobalSharedArrayBuffer) ||\n      (value && isInstance(value.buffer, GlobalSharedArrayBuffer)))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'number') {\n    throw new TypeError(\n      'The \"value\" argument must not be of type number. Received type number'\n    )\n  }\n\n  const valueOf = value.valueOf && value.valueOf()\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length)\n  }\n\n  const b = fromObject(value)\n  if (b) return b\n\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&\n      typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(value[Symbol.toPrimitive]('string'), encodingOrOffset, length)\n  }\n\n  throw new TypeError(\n    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n    'or Array-like Object. Received type ' + (typeof value)\n  )\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length)\n}\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, GlobalUint8Array.prototype)\nObject.setPrototypeOf(Buffer, GlobalUint8Array)\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number')\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n  }\n}\n\nfunction alloc (size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(size).fill(fill, encoding)\n      : createBuffer(size).fill(fill)\n  }\n  return createBuffer(size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  assertSize(size)\n  return createBuffer(size < 0 ? 0 : checked(size) | 0)\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size)\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding)\n  }\n\n  const length = byteLength(string, encoding) | 0\n  let buf = createBuffer(length)\n\n  const actual = buf.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (array) {\n  const length = array.length < 0 ? 0 : checked(array.length) | 0\n  const buf = createBuffer(length)\n  for (let i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayView (arrayView) {\n  if (isInstance(arrayView, GlobalUint8Array)) {\n    const copy = new GlobalUint8Array(arrayView)\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)\n  }\n  return fromArrayLike(arrayView)\n}\n\nfunction fromArrayBuffer (array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds')\n  }\n\n  let buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new GlobalUint8Array(array)\n  } else if (length === undefined) {\n    buf = new GlobalUint8Array(array, byteOffset)\n  } else {\n    buf = new GlobalUint8Array(array, byteOffset, length)\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype)\n\n  return buf\n}\n\nfunction fromObject (obj) {\n  if (Buffer.isBuffer(obj)) {\n    const len = checked(obj.length) | 0\n    const buf = createBuffer(len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0)\n    }\n    return fromArrayLike(obj)\n  }\n\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data)\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return b != null && b._isBuffer === true &&\n    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false\n}\n\nBuffer.compare = function compare (a, b) {\n  if (isInstance(a, GlobalUint8Array)) a = Buffer.from(a, a.offset, a.byteLength)\n  if (isInstance(b, GlobalUint8Array)) b = Buffer.from(b, b.offset, b.byteLength)\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError(\n      'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array'\n    )\n  }\n\n  if (a === b) return 0\n\n  let x = a.length\n  let y = b.length\n\n  for (let i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  let i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  const buffer = Buffer.allocUnsafe(length)\n  let pos = 0\n  for (i = 0; i < list.length; ++i) {\n    let buf = list[i]\n    if (isInstance(buf, GlobalUint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        if (!Buffer.isBuffer(buf)) buf = Buffer.from(buf)\n        buf.copy(buffer, pos)\n      } else {\n        GlobalUint8Array.prototype.set.call(\n          buffer,\n          buf,\n          pos\n        )\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    } else {\n      buf.copy(buffer, pos)\n    }\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (GlobalArrayBuffer.isView(string) || isInstance(string, GlobalArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError(\n      'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' +\n      'Received type ' + typeof string\n    )\n  }\n\n  const len = string.length\n  const mustMatch = (arguments.length > 2 && arguments[2] === true)\n  if (!mustMatch && len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  let loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  const i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  const len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (let i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  const len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (let i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  const len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (let i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  const length = this.length\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.toLocaleString = Buffer.prototype.toString\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  let str = ''\n  const max = exports.INSPECT_MAX_BYTES\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()\n  if (this.length > max) str += ' ... '\n  return '<Buffer ' + str + '>'\n}\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, GlobalUint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength)\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError(\n      'The \"target\" argument must be one of type Buffer or Uint8Array. ' +\n      'Received type ' + (typeof target)\n    )\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  let x = thisEnd - thisStart\n  let y = end - start\n  const len = Math.min(x, y)\n\n  const thisCopy = this.slice(thisStart, thisEnd)\n  const targetCopy = target.slice(start, end)\n\n  for (let i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (typeof GlobalUint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return GlobalUint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return GlobalUint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  let indexSize = 1\n  let arrLength = arr.length\n  let valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  let i\n  if (dir) {\n    let foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      let found = true\n      for (let j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  const remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  const strLen = string.length\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  let i\n  for (i = 0; i < length; ++i) {\n    const parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (numberIsNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0\n    if (isFinite(length)) {\n      length = length >>> 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  const remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  const res = []\n\n  let i = start\n  while (i < end) {\n    const firstByte = buf[i]\n    let codePoint = null\n    let bytesPerSequence = (firstByte > 0xEF)\n      ? 4\n      : (firstByte > 0xDF)\n          ? 3\n          : (firstByte > 0xBF)\n              ? 2\n              : 1\n\n    if (i + bytesPerSequence <= end) {\n      let secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nconst MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  const len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  let res = ''\n  let i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  const len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  let out = ''\n  for (let i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]]\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  const bytes = buf.slice(start, end)\n  let res = ''\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (let i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  const len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  const newBuf = this.subarray(start, end)\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype)\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUintLE =\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUintBE =\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  let val = this[offset + --byteLength]\n  let mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUint8 =\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUint16LE =\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUint16BE =\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUint32LE =\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUint32BE =\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const lo = first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24\n\n  const hi = this[++offset] +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    last * 2 ** 24\n\n  return BigInt(lo) + (BigInt(hi) << BigInt(32))\n})\n\nBuffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const hi = first * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  const lo = this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last\n\n  return (BigInt(hi) << BigInt(32)) + BigInt(lo)\n})\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let i = byteLength\n  let mul = 1\n  let val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = this[offset + 4] +\n    this[offset + 5] * 2 ** 8 +\n    this[offset + 6] * 2 ** 16 +\n    (last << 24) // Overflow\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24)\n})\n\nBuffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = (first << 24) + // Overflow\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last)\n})\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUintLE =\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let mul = 1\n  let i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUintBE =\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUint8 =\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeUint16LE =\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint16BE =\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint32LE =\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset + 3] = (value >>> 24)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 1] = (value >>> 8)\n  this[offset] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeUint32BE =\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nfunction wrtBigUInt64LE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  return offset\n}\n\nfunction wrtBigUInt64BE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset + 7] = lo\n  lo = lo >> 8\n  buf[offset + 6] = lo\n  lo = lo >> 8\n  buf[offset + 5] = lo\n  lo = lo >> 8\n  buf[offset + 4] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset + 3] = hi\n  hi = hi >> 8\n  buf[offset + 2] = hi\n  hi = hi >> 8\n  buf[offset + 1] = hi\n  hi = hi >> 8\n  buf[offset] = hi\n  return offset + 8\n}\n\nBuffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = 0\n  let mul = 1\n  let sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  let sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 3] = (value >>> 24)\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nBuffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  const len = end - start\n\n  if (this === target && typeof GlobalUint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end)\n  } else {\n    GlobalUint8Array.prototype.set.call(\n      target,\n      this.subarray(start, end),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n    if (val.length === 1) {\n      const code = val.charCodeAt(0)\n      if ((encoding === 'utf8' && code < 128) ||\n          encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  } else if (typeof val === 'boolean') {\n    val = Number(val)\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  let i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    const bytes = Buffer.isBuffer(val)\n      ? val\n      : Buffer.from(val, encoding)\n    const len = bytes.length\n    if (len === 0) {\n      throw new TypeError('The value \"' + val +\n        '\" is invalid for argument \"value\"')\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// CUSTOM ERRORS\n// =============\n\n// Simplified versions from Node, changed for Buffer-only usage\nconst errors = {}\nfunction E (sym, getMessage, Base) {\n  errors[sym] = class NodeError extends Base {\n    constructor () {\n      super()\n\n      Object.defineProperty(this, 'message', {\n        value: getMessage.apply(this, arguments),\n        writable: true,\n        configurable: true\n      })\n\n      // Add the error code to the name to include it in the stack trace.\n      this.name = `${this.name} [${sym}]`\n      // Access the stack to generate the error message including the error code\n      // from the name.\n      this.stack // eslint-disable-line no-unused-expressions\n      // Reset the name to the actual name.\n      delete this.name\n    }\n\n    get code () {\n      return sym\n    }\n\n    set code (value) {\n      Object.defineProperty(this, 'code', {\n        configurable: true,\n        enumerable: true,\n        value,\n        writable: true\n      })\n    }\n\n    toString () {\n      return `${this.name} [${sym}]: ${this.message}`\n    }\n  }\n}\n\nE('ERR_BUFFER_OUT_OF_BOUNDS',\n  function (name) {\n    if (name) {\n      return `${name} is outside of buffer bounds`\n    }\n\n    return 'Attempt to access memory outside buffer bounds'\n  }, RangeError)\nE('ERR_INVALID_ARG_TYPE',\n  function (name, actual) {\n    return `The \"${name}\" argument must be of type number. Received type ${typeof actual}`\n  }, TypeError)\nE('ERR_OUT_OF_RANGE',\n  function (str, range, input) {\n    let msg = `The value of \"${str}\" is out of range.`\n    let received = input\n    if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n      received = addNumericalSeparator(String(input))\n    } else if (typeof input === 'bigint') {\n      received = String(input)\n      if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {\n        received = addNumericalSeparator(received)\n      }\n      received += 'n'\n    }\n    msg += ` It must be ${range}. Received ${received}`\n    return msg\n  }, RangeError)\n\nfunction addNumericalSeparator (val) {\n  let res = ''\n  let i = val.length\n  const start = val[0] === '-' ? 1 : 0\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`\n  }\n  return `${val.slice(0, i)}${res}`\n}\n\n// CHECK FUNCTIONS\n// ===============\n\nfunction checkBounds (buf, offset, byteLength) {\n  validateNumber(offset, 'offset')\n  if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {\n    boundsError(offset, buf.length - (byteLength + 1))\n  }\n}\n\nfunction checkIntBI (value, min, max, buf, offset, byteLength) {\n  if (value > max || value < min) {\n    const n = typeof min === 'bigint' ? 'n' : ''\n    let range\n    if (byteLength > 3) {\n      if (min === 0 || min === BigInt(0)) {\n        range = `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}`\n      } else {\n        range = `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` +\n                `${(byteLength + 1) * 8 - 1}${n}`\n      }\n    } else {\n      range = `>= ${min}${n} and <= ${max}${n}`\n    }\n    throw new errors.ERR_OUT_OF_RANGE('value', range, value)\n  }\n  checkBounds(buf, offset, byteLength)\n}\n\nfunction validateNumber (value, name) {\n  if (typeof value !== 'number') {\n    throw new errors.ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n}\n\nfunction boundsError (value, length, type) {\n  if (Math.floor(value) !== value) {\n    validateNumber(value, type)\n    throw new errors.ERR_OUT_OF_RANGE(type || 'offset', 'an integer', value)\n  }\n\n  if (length < 0) {\n    throw new errors.ERR_BUFFER_OUT_OF_BOUNDS()\n  }\n\n  throw new errors.ERR_OUT_OF_RANGE(type || 'offset',\n                                    `>= ${type ? 1 : 0} and <= ${length}`,\n                                    value)\n}\n\n// HELPER FUNCTIONS\n// ================\n\nconst INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0]\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  let codePoint\n  const length = string.length\n  let leadSurrogate = null\n  const bytes = []\n\n  for (let i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  let c, hi, lo\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  let i\n  for (i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance (obj, type) {\n  return obj instanceof type ||\n    (obj != null && obj.constructor != null && obj.constructor.name != null &&\n      obj.constructor.name === type.name)\n}\nfunction numberIsNaN (obj) {\n  // For IE11 support\n  return obj !== obj // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nconst hexSliceLookupTable = (function () {\n  const alphabet = '0123456789abcdef'\n  const table = new Array(256)\n  for (let i = 0; i < 16; ++i) {\n    const i16 = i * 16\n    for (let j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j]\n    }\n  }\n  return table\n})()\n\n// Return not function with Error if BigInt not supported\nfunction defineBigIntMethod (fn) {\n  return typeof BigInt === 'undefined' ? BufferBigIntNotDefined : fn\n}\n\nfunction BufferBigIntNotDefined () {\n  throw new Error('BigInt not supported')\n}\n", "import {\n  Blob,\n  BlobOptions,\n  Buffer,\n  File,\n  FileOptions,\n  INSPECT_MAX_BYTES,\n  // eslint-disable-next-line n/no-deprecated-api\n  SlowBuffer,\n  TranscodeEncoding,\n  atob,\n  btoa,\n  constants,\n  isAscii,\n  isUtf8,\n  kMaxLength,\n  kStringMaxLength,\n  resolveObjectURL,\n  transcode,\n// eslint-disable-next-line unicorn/prefer-node-protocol\n} from 'buffer'\n\nexport {\n  Blob,\n  BlobOptions,\n  Buffer,\n  File,\n  FileOptions,\n  INSPECT_MAX_BYTES,\n  SlowBuffer,\n  TranscodeEncoding,\n  atob,\n  btoa,\n  constants,\n  isAscii,\n  isUtf8,\n  kMaxLength,\n  kStringMaxLength,\n  resolveObjectURL,\n  transcode,\n}\n\nexport default Buffer\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,SAAA,aAAqB;AACrB,SAAA,cAAsB;AACtB,SAAA,gBAAwB;AAExB,IAAI,SAAS,CAAA;AACb,IAAI,YAAY,CAAA;AAChB,IAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAE3D,IAAI,OAAO;AACX,KAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,SAAO,CAAC,IAAI,KAAK,CAAC;AAClB,YAAU,KAAK,WAAW,CAAC,CAAC,IAAI;AAClC;AAHS;AAAO;AAOhB,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAE/B,SAAS,QAAS,KAAK;AACrB,MAAI,MAAM,IAAI;AAEd,MAAI,MAAM,IAAI,GAAG;AACf,UAAM,IAAI,MAAM,gDAAgD;EACpE;AAIE,MAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,MAAI,aAAa,GAAI,YAAW;AAEhC,MAAI,kBAAkB,aAAa,MAC/B,IACA,IAAK,WAAW;AAEpB,SAAO,CAAC,UAAU,eAAe;AACnC;AAGA,SAAS,WAAY,KAAK;AACxB,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,WAAW,KAAK,CAAC;AACrB,MAAI,kBAAkB,KAAK,CAAC;AAC5B,UAAS,WAAW,mBAAmB,IAAI,IAAK;AAClD;AAEA,SAAS,YAAa,KAAK,UAAU,iBAAiB;AACpD,UAAS,WAAW,mBAAmB,IAAI,IAAK;AAClD;AAEA,SAAS,YAAa,KAAK;AACzB,MAAI;AACJ,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,WAAW,KAAK,CAAC;AACrB,MAAI,kBAAkB,KAAK,CAAC;AAE5B,MAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;AAE7D,MAAI,UAAU;AAGd,MAAI,MAAM,kBAAkB,IACxB,WAAW,IACX;AAEJ,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC3B,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,KACpC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IACrC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC;AACjC,QAAI,SAAS,IAAK,OAAO,KAAM;AAC/B,QAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,QAAI,SAAS,IAAI,MAAM;EAC3B;AAEE,MAAI,oBAAoB,GAAG;AACzB,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,IAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACvC,QAAI,SAAS,IAAI,MAAM;EAC3B;AAEE,MAAI,oBAAoB,GAAG;AACzB,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IACpC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACvC,QAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,QAAI,SAAS,IAAI,MAAM;EAC3B;AAEE,SAAO;AACT;AAEA,SAAS,gBAAiB,KAAK;AAC7B,SAAO,OAAO,OAAO,KAAK,EAAI,IAC5B,OAAO,OAAO,KAAK,EAAI,IACvB,OAAO,OAAO,IAAI,EAAI,IACtB,OAAO,MAAM,EAAI;AACrB;AAEA,SAAS,YAAa,OAAO,OAAO,KAAK;AACvC,MAAI;AACJ,MAAI,SAAS,CAAA;AACb,WAAS,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG;AACnC,WACI,MAAM,CAAC,KAAK,KAAM,aAClB,MAAM,IAAI,CAAC,KAAK,IAAK,UACtB,MAAM,IAAI,CAAC,IAAI;AAClB,WAAO,KAAK,gBAAgB,GAAG,CAAC;EACpC;AACE,SAAO,OAAO,KAAK,EAAE;AACvB;AAEA,SAAS,cAAe,OAAO;AAC7B,MAAI;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,aAAa,MAAM;AACvB,MAAI,QAAQ,CAAA;AACZ,MAAI,iBAAiB;AAGrB,WAAS,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,gBAAgB;AACtE,UAAM,KAAK,YAAY,OAAO,GAAI,IAAI,iBAAkB,OAAO,OAAQ,IAAI,cAAe,CAAC;EAC/F;AAGE,MAAI,eAAe,GAAG;AACpB,UAAM,MAAM,MAAM,CAAC;AACnB,UAAM;MACJ,OAAO,OAAO,CAAC,IACf,OAAQ,OAAO,IAAK,EAAI,IACxB;IACN;EACA,WAAa,eAAe,GAAG;AAC3B,WAAO,MAAM,MAAM,CAAC,KAAK,KAAK,MAAM,MAAM,CAAC;AAC3C,UAAM;MACJ,OAAO,OAAO,EAAE,IAChB,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAQ,OAAO,IAAK,EAAI,IACxB;IACN;EACA;AAEE,SAAO,MAAM,KAAK,EAAE;AACtB;;ACpJY,QAAA,OAAG,SAAUA,SAAQ,QAAQ,MAAM,MAAM,QAAQ;AAC3D,MAAI,GAAG;AACP,MAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,MAAI,QAAQ,KAAK,QAAQ;AACzB,MAAI,QAAQ,QAAQ;AACpB,MAAI,QAAQ;AACZ,MAAI,IAAI,OAAQ,SAAS,IAAK;AAC9B,MAAI,IAAI,OAAO,KAAK;AACpB,MAAI,IAAIA,QAAO,SAAS,CAAC;AAEzB,OAAK;AAEL,MAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,QAAO,CAAC;AACR,WAAS;AACT,SAAO,QAAQ,GAAG,IAAK,IAAI,MAAOA,QAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;EAAA;AAE1E,MAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,QAAO,CAAC;AACR,WAAS;AACT,SAAO,QAAQ,GAAG,IAAK,IAAI,MAAOA,QAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;EAAA;AAE1E,MAAI,MAAM,GAAG;AACX,QAAI,IAAI;EACZ,WAAa,MAAM,MAAM;AACrB,WAAO,IAAI,OAAQ,IAAI,KAAK,KAAK;EACrC,OAAS;AACL,QAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,QAAI,IAAI;EACZ;AACE,UAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;AAChD;AAEA,QAAA,QAAgB,SAAUA,SAAQ,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACnE,MAAI,GAAG,GAAG;AACV,MAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,MAAI,QAAQ,KAAK,QAAQ;AACzB,MAAI,QAAQ,QAAQ;AACpB,MAAI,KAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9D,MAAI,IAAI,OAAO,IAAK,SAAS;AAC7B,MAAI,IAAI,OAAO,IAAI;AACnB,MAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;AAE1D,UAAQ,KAAK,IAAI,KAAK;AAEtB,MAAI,MAAM,KAAK,KAAK,UAAU,UAAU;AACtC,QAAI,MAAM,KAAK,IAAI,IAAI;AACvB,QAAI;EACR,OAAS;AACL,QAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG;AACzC,QAAI,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,WAAK;IACX;AACI,QAAI,IAAI,SAAS,GAAG;AAClB,eAAS,KAAK;IACpB,OAAW;AACL,eAAS,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK;IACzC;AACI,QAAI,QAAQ,KAAK,GAAG;AAClB;AACA,WAAK;IACX;AAEI,QAAI,IAAI,SAAS,MAAM;AACrB,UAAI;AACJ,UAAI;IACV,WAAe,IAAI,SAAS,GAAG;AACzB,WAAM,QAAQ,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI;AACxC,UAAI,IAAI;IACd,OAAW;AACL,UAAI,QAAQ,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AACrD,UAAI;IACV;EACA;AAEE,SAAO,QAAQ,GAAGA,QAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;EAAA;AAE9E,MAAK,KAAK,OAAQ;AAClB,UAAQ;AACR,SAAO,OAAO,GAAGA,QAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;EAAA;AAE7E,EAAAA,QAAO,SAAS,IAAI,CAAC,KAAK,IAAI;AAChC;;AC1EA,QAAM,SAASC;AACf,QAAMC,YAAUC;AAChB,QAAM,sBACH,OAAO,WAAW,cAAc,OAAO,OAAO,KAAK,MAAM,aACtD,OAAO,KAAK,EAAE,4BAA4B,IAC1C;AAEN,UAAA,SAAiBC;AACjB,UAAA,aAAqBC;AACrB,UAAA,oBAA4B;AAE5B,QAAM,eAAe;AACrB,UAAA,aAAqB;AACrB,QAAM,EAAE,YAAY,kBAAkB,aAAa,mBAAmB,mBAAmB,wBAAuB,IAAK;AAgBrH,EAAAD,QAAO,sBAAsB,kBAAiB;AAE9C,MAAI,CAACA,QAAO,uBAAuB,OAAO,YAAY,eAClD,OAAO,QAAQ,UAAU,YAAY;AACvC,YAAQ;MACN;;;AAKJ,WAAS,oBAAqB;AAE5B,QAAI;AACF,YAAM,MAAM,IAAI,iBAAiB,CAAC;AAClC,YAAM,QAAQ,EAAE,KAAK,WAAY;AAAE,eAAO;MAAE,EAAE;AAC9C,aAAO,eAAe,OAAO,iBAAiB,SAAS;AACvD,aAAO,eAAe,KAAK,KAAK;AAChC,aAAO,IAAI,IAAG,MAAO;aACd,GAAG;AACV,aAAO;;;AAIX,SAAO,eAAeA,QAAO,WAAW,UAAU;IAChD,YAAY;IACZ,KAAK,WAAY;AACf,UAAI,CAACA,QAAO,SAAS,IAAI,EAAG,QAAO;AACnC,aAAO,KAAK;;EAEhB,CAAC;AAED,SAAO,eAAeA,QAAO,WAAW,UAAU;IAChD,YAAY;IACZ,KAAK,WAAY;AACf,UAAI,CAACA,QAAO,SAAS,IAAI,EAAG,QAAO;AACnC,aAAO,KAAK;;EAEhB,CAAC;AAED,WAAS,aAAc,QAAQ;AAC7B,QAAI,SAAS,cAAc;AACzB,YAAM,IAAI,WAAW,gBAAgB,SAAS,gCAAgC;;AAGhF,UAAM,MAAM,IAAI,iBAAiB,MAAM;AACvC,WAAO,eAAe,KAAKA,QAAO,SAAS;AAC3C,WAAO;;AAaT,WAASA,QAAQ,KAAK,kBAAkB,QAAQ;AAE9C,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,OAAO,qBAAqB,UAAU;AACxC,cAAM,IAAI;UACR;;;AAGJ,aAAO,YAAY,GAAG;;AAExB,WAAO,KAAK,KAAK,kBAAkB,MAAM;;AAG3C,EAAAA,QAAO,WAAW;AAElB,WAAS,KAAM,OAAO,kBAAkB,QAAQ;AAC9C,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,WAAW,OAAO,gBAAgB;;AAG3C,QAAI,kBAAkB,OAAO,KAAK,GAAG;AACnC,aAAO,cAAc,KAAK;;AAG5B,QAAI,SAAS,MAAM;AACjB,YAAM,IAAI;QACR,oHAC0C,OAAO;;;AAIrD,QAAI,WAAW,OAAO,iBAAiB,KAClC,SAAS,WAAW,MAAM,QAAQ,iBAAiB,GAAI;AAC1D,aAAO,gBAAgB,OAAO,kBAAkB,MAAM;;AAGxD,QAAI,OAAO,4BAA4B,gBAClC,WAAW,OAAO,uBAAuB,KACzC,SAAS,WAAW,MAAM,QAAQ,uBAAuB,IAAK;AACjE,aAAO,gBAAgB,OAAO,kBAAkB,MAAM;;AAGxD,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI;QACR;;;AAIJ,UAAM,UAAU,MAAM,WAAW,MAAM,QAAO;AAC9C,QAAI,WAAW,QAAQ,YAAY,OAAO;AACxC,aAAOA,QAAO,KAAK,SAAS,kBAAkB,MAAM;;AAGtD,UAAM,IAAI,WAAW,KAAK;AAC1B,QAAI,EAAG,QAAO;AAEd,QAAI,OAAO,WAAW,eAAe,OAAO,eAAe,QACvD,OAAO,MAAM,OAAO,WAAW,MAAM,YAAY;AACnD,aAAOA,QAAO,KAAK,MAAM,OAAO,WAAW,EAAE,QAAQ,GAAG,kBAAkB,MAAM;;AAGlF,UAAM,IAAI;MACR,oHAC0C,OAAO;;;AAYrD,EAAAA,QAAO,OAAO,SAAU,OAAO,kBAAkB,QAAQ;AACvD,WAAO,KAAK,OAAO,kBAAkB,MAAM;;AAK7C,SAAO,eAAeA,QAAO,WAAW,iBAAiB,SAAS;AAClE,SAAO,eAAeA,SAAQ,gBAAgB;AAE9C,WAAS,WAAY,MAAM;AACzB,QAAI,OAAO,SAAS,UAAU;AAC5B,YAAM,IAAI,UAAU,wCAAwC;IAChE,WAAa,OAAO,GAAG;AACnB,YAAM,IAAI,WAAW,gBAAgB,OAAO,gCAAgC;;;AAIhF,WAAS,MAAO,MAAM,MAAM,UAAU;AACpC,eAAW,IAAI;AACf,QAAI,QAAQ,GAAG;AACb,aAAO,aAAa,IAAI;;AAE1B,QAAI,SAAS,QAAW;AAItB,aAAO,OAAO,aAAa,WACvB,aAAa,IAAI,EAAE,KAAK,MAAM,QAAQ,IACtC,aAAa,IAAI,EAAE,KAAK,IAAI;;AAElC,WAAO,aAAa,IAAI;;AAO1B,EAAAA,QAAO,QAAQ,SAAU,MAAM,MAAM,UAAU;AAC7C,WAAO,MAAM,MAAM,MAAM,QAAQ;;AAGnC,WAAS,YAAa,MAAM;AAC1B,eAAW,IAAI;AACf,WAAO,aAAa,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;;AAMtD,EAAAA,QAAO,cAAc,SAAU,MAAM;AACnC,WAAO,YAAY,IAAI;;AAKzB,EAAAA,QAAO,kBAAkB,SAAU,MAAM;AACvC,WAAO,YAAY,IAAI;;AAGzB,WAAS,WAAY,QAAQ,UAAU;AACrC,QAAI,OAAO,aAAa,YAAY,aAAa,IAAI;AACnD,iBAAW;;AAGb,QAAI,CAACA,QAAO,WAAW,QAAQ,GAAG;AAChC,YAAM,IAAI,UAAU,uBAAuB,QAAQ;;AAGrD,UAAM,SAASE,YAAW,QAAQ,QAAQ,IAAI;AAC9C,QAAI,MAAM,aAAa,MAAM;AAE7B,UAAM,SAAS,IAAI,MAAM,QAAQ,QAAQ;AAEzC,QAAI,WAAW,QAAQ;AAIrB,YAAM,IAAI,MAAM,GAAG,MAAM;;AAG3B,WAAO;;AAGT,WAAS,cAAe,OAAO;AAC7B,UAAM,SAAS,MAAM,SAAS,IAAI,IAAI,QAAQ,MAAM,MAAM,IAAI;AAC9D,UAAM,MAAM,aAAa,MAAM;AAC/B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,UAAI,CAAC,IAAI,MAAM,CAAC,IAAI;;AAEtB,WAAO;;AAGT,WAAS,cAAe,WAAW;AACjC,QAAI,WAAW,WAAW,gBAAgB,GAAG;AAC3C,YAAM,OAAO,IAAI,iBAAiB,SAAS;AAC3C,aAAO,gBAAgB,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;;AAEtE,WAAO,cAAc,SAAS;;AAGhC,WAAS,gBAAiB,OAAO,YAAY,QAAQ;AACnD,QAAI,aAAa,KAAK,MAAM,aAAa,YAAY;AACnD,YAAM,IAAI,WAAW,sCAAsC;;AAG7D,QAAI,MAAM,aAAa,cAAc,UAAU,IAAI;AACjD,YAAM,IAAI,WAAW,sCAAsC;;AAG7D,QAAI;AACJ,QAAI,eAAe,UAAa,WAAW,QAAW;AACpD,YAAM,IAAI,iBAAiB,KAAK;IACpC,WAAa,WAAW,QAAW;AAC/B,YAAM,IAAI,iBAAiB,OAAO,UAAU;IAChD,OAAS;AACL,YAAM,IAAI,iBAAiB,OAAO,YAAY,MAAM;;AAItD,WAAO,eAAe,KAAKF,QAAO,SAAS;AAE3C,WAAO;;AAGT,WAAS,WAAY,KAAK;AACxB,QAAIA,QAAO,SAAS,GAAG,GAAG;AACxB,YAAM,MAAM,QAAQ,IAAI,MAAM,IAAI;AAClC,YAAM,MAAM,aAAa,GAAG;AAE5B,UAAI,IAAI,WAAW,GAAG;AACpB,eAAO;;AAGT,UAAI,KAAK,KAAK,GAAG,GAAG,GAAG;AACvB,aAAO;;AAGT,QAAI,IAAI,WAAW,QAAW;AAC5B,UAAI,OAAO,IAAI,WAAW,YAAY,YAAY,IAAI,MAAM,GAAG;AAC7D,eAAO,aAAa,CAAC;;AAEvB,aAAO,cAAc,GAAG;;AAG1B,QAAI,IAAI,SAAS,YAAY,MAAM,QAAQ,IAAI,IAAI,GAAG;AACpD,aAAO,cAAc,IAAI,IAAI;;;AAIjC,WAAS,QAAS,QAAQ;AAGxB,QAAI,UAAU,cAAc;AAC1B,YAAM,IAAI,WAAW,4DACa,aAAa,SAAS,EAAE,IAAI,QAAQ;;AAExE,WAAO,SAAS;;AAGlB,WAASC,YAAY,QAAQ;AAC3B,QAAI,CAAC,UAAU,QAAQ;AACrB,eAAS;;AAEX,WAAOD,QAAO,MAAM,CAAC,MAAM;;AAG7B,EAAAA,QAAO,WAAW,SAAS,SAAU,GAAG;AACtC,WAAO,KAAK,QAAQ,EAAE,cAAc,QAClC,MAAMA,QAAO;;AAGjB,EAAAA,QAAO,UAAU,SAAS,QAAS,GAAG,GAAG;AACvC,QAAI,WAAW,GAAG,gBAAgB,EAAG,KAAIA,QAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU;AAC9E,QAAI,WAAW,GAAG,gBAAgB,EAAG,KAAIA,QAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU;AAC9E,QAAI,CAACA,QAAO,SAAS,CAAC,KAAK,CAACA,QAAO,SAAS,CAAC,GAAG;AAC9C,YAAM,IAAI;QACR;;;AAIJ,QAAI,MAAM,EAAG,QAAO;AAEpB,QAAI,IAAI,EAAE;AACV,QAAI,IAAI,EAAE;AAEV,aAAS,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,EAAE,GAAG;AAClD,UAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,YAAI,EAAE,CAAC;AACP,YAAI,EAAE,CAAC;AACP;;;AAIJ,QAAI,IAAI,EAAG,QAAO;AAClB,QAAI,IAAI,EAAG,QAAO;AAClB,WAAO;;AAGT,EAAAA,QAAO,aAAa,SAAS,WAAY,UAAU;AACjD,YAAQ,OAAO,QAAQ,EAAE,YAAW,GAAE;MACpC,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO;MACT;AACE,eAAO;;;AAIb,EAAAA,QAAO,SAAS,SAAS,OAAQ,MAAM,QAAQ;AAC7C,QAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,YAAM,IAAI,UAAU,6CAA6C;;AAGnE,QAAI,KAAK,WAAW,GAAG;AACrB,aAAOA,QAAO,MAAM,CAAC;;AAGvB,QAAI;AACJ,QAAI,WAAW,QAAW;AACxB,eAAS;AACT,WAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,kBAAU,KAAK,CAAC,EAAE;;;AAItB,UAAMJ,UAASI,QAAO,YAAY,MAAM;AACxC,QAAI,MAAM;AACV,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,UAAI,MAAM,KAAK,CAAC;AAChB,UAAI,WAAW,KAAK,gBAAgB,GAAG;AACrC,YAAI,MAAM,IAAI,SAASJ,QAAO,QAAQ;AACpC,cAAI,CAACI,QAAO,SAAS,GAAG,EAAG,OAAMA,QAAO,KAAK,GAAG;AAChD,cAAI,KAAKJ,SAAQ,GAAG;QAC5B,OAAa;AACL,2BAAiB,UAAU,IAAI;YAC7BA;YACA;YACA;;;iBAGK,CAACI,QAAO,SAAS,GAAG,GAAG;AAChC,cAAM,IAAI,UAAU,6CAA6C;MACvE,OAAW;AACL,YAAI,KAAKJ,SAAQ,GAAG;;AAEtB,aAAO,IAAI;;AAEb,WAAOA;;AAGT,WAASM,YAAY,QAAQ,UAAU;AACrC,QAAIF,QAAO,SAAS,MAAM,GAAG;AAC3B,aAAO,OAAO;;AAEhB,QAAI,kBAAkB,OAAO,MAAM,KAAK,WAAW,QAAQ,iBAAiB,GAAG;AAC7E,aAAO,OAAO;;AAEhB,QAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,IAAI;QACR,6FACmB,OAAO;;;AAI9B,UAAM,MAAM,OAAO;AACnB,UAAM,YAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM;AAC5D,QAAI,CAAC,aAAa,QAAQ,EAAG,QAAO;AAGpC,QAAI,cAAc;AAClB,eAAS;AACP,cAAQ,UAAQ;QACd,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO;QACT,KAAK;QACL,KAAK;AACH,iBAAO,YAAY,MAAM,EAAE;QAC7B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO,MAAM;QACf,KAAK;AACH,iBAAO,QAAQ;QACjB,KAAK;AACH,iBAAO,cAAc,MAAM,EAAE;QAC/B;AACE,cAAI,aAAa;AACf,mBAAO,YAAY,KAAK,YAAY,MAAM,EAAE;;AAE9C,sBAAY,KAAK,UAAU,YAAW;AACtC,wBAAc;;;;AAItB,EAAAA,QAAO,aAAaE;AAEpB,WAAS,aAAc,UAAU,OAAO,KAAK;AAC3C,QAAI,cAAc;AASlB,QAAI,UAAU,UAAa,QAAQ,GAAG;AACpC,cAAQ;;AAIV,QAAI,QAAQ,KAAK,QAAQ;AACvB,aAAO;;AAGT,QAAI,QAAQ,UAAa,MAAM,KAAK,QAAQ;AAC1C,YAAM,KAAK;;AAGb,QAAI,OAAO,GAAG;AACZ,aAAO;;AAIT,aAAS;AACT,eAAW;AAEX,QAAI,OAAO,OAAO;AAChB,aAAO;;AAGT,QAAI,CAAC,SAAU,YAAW;AAE1B,WAAO,MAAM;AACX,cAAQ,UAAQ;QACd,KAAK;AACH,iBAAO,SAAS,MAAM,OAAO,GAAG;QAElC,KAAK;QACL,KAAK;AACH,iBAAO,UAAU,MAAM,OAAO,GAAG;QAEnC,KAAK;AACH,iBAAO,WAAW,MAAM,OAAO,GAAG;QAEpC,KAAK;QACL,KAAK;AACH,iBAAO,YAAY,MAAM,OAAO,GAAG;QAErC,KAAK;AACH,iBAAO,YAAY,MAAM,OAAO,GAAG;QAErC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO,aAAa,MAAM,OAAO,GAAG;QAEtC;AACE,cAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,sBAAY,WAAW,IAAI,YAAW;AACtC,wBAAc;;;;AAWtB,EAAAF,QAAO,UAAU,YAAY;AAE7B,WAAS,KAAM,GAAG,GAAG,GAAG;AACtB,UAAM,IAAI,EAAE,CAAC;AACb,MAAE,CAAC,IAAI,EAAE,CAAC;AACV,MAAE,CAAC,IAAI;;AAGT,EAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,UAAM,MAAM,KAAK;AACjB,QAAI,MAAM,MAAM,GAAG;AACjB,YAAM,IAAI,WAAW,2CAA2C;;AAElE,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,WAAK,MAAM,GAAG,IAAI,CAAC;;AAErB,WAAO;;AAGT,EAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,UAAM,MAAM,KAAK;AACjB,QAAI,MAAM,MAAM,GAAG;AACjB,YAAM,IAAI,WAAW,2CAA2C;;AAElE,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,WAAK,MAAM,GAAG,IAAI,CAAC;AACnB,WAAK,MAAM,IAAI,GAAG,IAAI,CAAC;;AAEzB,WAAO;;AAGT,EAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,UAAM,MAAM,KAAK;AACjB,QAAI,MAAM,MAAM,GAAG;AACjB,YAAM,IAAI,WAAW,2CAA2C;;AAElE,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,WAAK,MAAM,GAAG,IAAI,CAAC;AACnB,WAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AACvB,WAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AACvB,WAAK,MAAM,IAAI,GAAG,IAAI,CAAC;;AAEzB,WAAO;;AAGT,EAAAA,QAAO,UAAU,WAAW,SAAS,WAAY;AAC/C,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,EAAG,QAAO;AACzB,QAAI,UAAU,WAAW,EAAG,QAAO,UAAU,MAAM,GAAG,MAAM;AAC5D,WAAO,aAAa,MAAM,MAAM,SAAS;;AAG3C,EAAAA,QAAO,UAAU,iBAAiBA,QAAO,UAAU;AAEnD,EAAAA,QAAO,UAAU,SAAS,SAAS,OAAQ,GAAG;AAC5C,QAAI,CAACA,QAAO,SAAS,CAAC,EAAG,OAAM,IAAI,UAAU,2BAA2B;AACxE,QAAI,SAAS,EAAG,QAAO;AACvB,WAAOA,QAAO,QAAQ,MAAM,CAAC,MAAM;;AAGrC,EAAAA,QAAO,UAAU,UAAU,SAAS,UAAW;AAC7C,QAAI,MAAM;AACV,UAAM,MAAM,QAAQ;AACpB,UAAM,KAAK,SAAS,OAAO,GAAG,GAAG,EAAE,QAAQ,WAAW,KAAK,EAAE,KAAI;AACjE,QAAI,KAAK,SAAS,IAAK,QAAO;AAC9B,WAAO,aAAa,MAAM;;AAE5B,MAAI,qBAAqB;AACvB,IAAAA,QAAO,UAAU,mBAAmB,IAAIA,QAAO,UAAU;;AAG3D,EAAAA,QAAO,UAAU,UAAU,SAAS,QAAS,QAAQ,OAAO,KAAK,WAAW,SAAS;AACnF,QAAI,WAAW,QAAQ,gBAAgB,GAAG;AACxC,eAASA,QAAO,KAAK,QAAQ,OAAO,QAAQ,OAAO,UAAU;;AAE/D,QAAI,CAACA,QAAO,SAAS,MAAM,GAAG;AAC5B,YAAM,IAAI;QACR,mFACoB,OAAO;;;AAI/B,QAAI,UAAU,QAAW;AACvB,cAAQ;;AAEV,QAAI,QAAQ,QAAW;AACrB,YAAM,SAAS,OAAO,SAAS;;AAEjC,QAAI,cAAc,QAAW;AAC3B,kBAAY;;AAEd,QAAI,YAAY,QAAW;AACzB,gBAAU,KAAK;;AAGjB,QAAI,QAAQ,KAAK,MAAM,OAAO,UAAU,YAAY,KAAK,UAAU,KAAK,QAAQ;AAC9E,YAAM,IAAI,WAAW,oBAAoB;;AAG3C,QAAI,aAAa,WAAW,SAAS,KAAK;AACxC,aAAO;;AAET,QAAI,aAAa,SAAS;AACxB,aAAO;;AAET,QAAI,SAAS,KAAK;AAChB,aAAO;;AAGT,eAAW;AACX,aAAS;AACT,mBAAe;AACf,iBAAa;AAEb,QAAI,SAAS,OAAQ,QAAO;AAE5B,QAAI,IAAI,UAAU;AAClB,QAAI,IAAI,MAAM;AACd,UAAM,MAAM,KAAK,IAAI,GAAG,CAAC;AAEzB,UAAM,WAAW,KAAK,MAAM,WAAW,OAAO;AAC9C,UAAM,aAAa,OAAO,MAAM,OAAO,GAAG;AAE1C,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,UAAI,SAAS,CAAC,MAAM,WAAW,CAAC,GAAG;AACjC,YAAI,SAAS,CAAC;AACd,YAAI,WAAW,CAAC;AAChB;;;AAIJ,QAAI,IAAI,EAAG,QAAO;AAClB,QAAI,IAAI,EAAG,QAAO;AAClB,WAAO;;AAYT,WAAS,qBAAsBJ,SAAQ,KAAK,YAAY,UAAU,KAAK;AAErE,QAAIA,QAAO,WAAW,EAAG,QAAO;AAGhC,QAAI,OAAO,eAAe,UAAU;AAClC,iBAAW;AACX,mBAAa;IACjB,WAAa,aAAa,YAAY;AAClC,mBAAa;IACjB,WAAa,aAAa,aAAa;AACnC,mBAAa;;AAEf,iBAAa,CAAC;AACd,QAAI,YAAY,UAAU,GAAG;AAE3B,mBAAa,MAAM,IAAKA,QAAO,SAAS;;AAI1C,QAAI,aAAa,EAAG,cAAaA,QAAO,SAAS;AACjD,QAAI,cAAcA,QAAO,QAAQ;AAC/B,UAAI,IAAK,QAAO;UACX,cAAaA,QAAO,SAAS;IACtC,WAAa,aAAa,GAAG;AACzB,UAAI,IAAK,cAAa;UACjB,QAAO;;AAId,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAMI,QAAO,KAAK,KAAK,QAAQ;;AAIjC,QAAIA,QAAO,SAAS,GAAG,GAAG;AAExB,UAAI,IAAI,WAAW,GAAG;AACpB,eAAO;;AAET,aAAO,aAAaJ,SAAQ,KAAK,YAAY,UAAU,GAAG;IAC9D,WAAa,OAAO,QAAQ,UAAU;AAClC,YAAM,MAAM;AACZ,UAAI,OAAO,iBAAiB,UAAU,YAAY,YAAY;AAC5D,YAAI,KAAK;AACP,iBAAO,iBAAiB,UAAU,QAAQ,KAAKA,SAAQ,KAAK,UAAU;QAC9E,OAAa;AACL,iBAAO,iBAAiB,UAAU,YAAY,KAAKA,SAAQ,KAAK,UAAU;;;AAG9E,aAAO,aAAaA,SAAQ,CAAC,GAAG,GAAG,YAAY,UAAU,GAAG;;AAG9D,UAAM,IAAI,UAAU,sCAAsC;;AAG5D,WAAS,aAAc,KAAK,KAAK,YAAY,UAAU,KAAK;AAC1D,QAAI,YAAY;AAChB,QAAI,YAAY,IAAI;AACpB,QAAI,YAAY,IAAI;AAEpB,QAAI,aAAa,QAAW;AAC1B,iBAAW,OAAO,QAAQ,EAAE,YAAW;AACvC,UAAI,aAAa,UAAU,aAAa,WACpC,aAAa,aAAa,aAAa,YAAY;AACrD,YAAI,IAAI,SAAS,KAAK,IAAI,SAAS,GAAG;AACpC,iBAAO;;AAET,oBAAY;AACZ,qBAAa;AACb,qBAAa;AACb,sBAAc;;;AAIlB,aAAS,KAAM,KAAKO,IAAG;AACrB,UAAI,cAAc,GAAG;AACnB,eAAO,IAAIA,EAAC;MAClB,OAAW;AACL,eAAO,IAAI,aAAaA,KAAI,SAAS;;;AAIzC,QAAI;AACJ,QAAI,KAAK;AACP,UAAI,aAAa;AACjB,WAAK,IAAI,YAAY,IAAI,WAAW,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,MAAM,KAAK,KAAK,eAAe,KAAK,IAAI,IAAI,UAAU,GAAG;AACtE,cAAI,eAAe,GAAI,cAAa;AACpC,cAAI,IAAI,aAAa,MAAM,UAAW,QAAO,aAAa;QAClE,OAAa;AACL,cAAI,eAAe,GAAI,MAAK,IAAI;AAChC,uBAAa;;;IAGrB,OAAS;AACL,UAAI,aAAa,YAAY,UAAW,cAAa,YAAY;AACjE,WAAK,IAAI,YAAY,KAAK,GAAG,KAAK;AAChC,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,KAAK,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG;AACrC,oBAAQ;AACR;;;AAGJ,YAAI,MAAO,QAAO;;;AAItB,WAAO;;AAGT,EAAAH,QAAO,UAAU,WAAW,SAAS,SAAU,KAAK,YAAY,UAAU;AACxE,WAAO,KAAK,QAAQ,KAAK,YAAY,QAAQ,MAAM;;AAGrD,EAAAA,QAAO,UAAU,UAAU,SAAS,QAAS,KAAK,YAAY,UAAU;AACtE,WAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,IAAI;;AAGnE,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,KAAK,YAAY,UAAU;AAC9E,WAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,KAAK;;AAGpE,WAAS,SAAU,KAAK,QAAQ,QAAQ,QAAQ;AAC9C,aAAS,OAAO,MAAM,KAAK;AAC3B,UAAM,YAAY,IAAI,SAAS;AAC/B,QAAI,CAAC,QAAQ;AACX,eAAS;IACb,OAAS;AACL,eAAS,OAAO,MAAM;AACtB,UAAI,SAAS,WAAW;AACtB,iBAAS;;;AAIb,UAAM,SAAS,OAAO;AAEtB,QAAI,SAAS,SAAS,GAAG;AACvB,eAAS,SAAS;;AAEpB,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,YAAM,SAAS,SAAS,OAAO,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE;AACnD,UAAI,YAAY,MAAM,EAAG,QAAO;AAChC,UAAI,SAAS,CAAC,IAAI;;AAEpB,WAAO;;AAGT,WAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,WAAO,WAAW,YAAY,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAGjF,WAAS,WAAY,KAAK,QAAQ,QAAQ,QAAQ;AAChD,WAAO,WAAW,aAAa,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAG7D,WAAS,YAAa,KAAK,QAAQ,QAAQ,QAAQ;AACjD,WAAO,WAAW,cAAc,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAG9D,WAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,WAAO,WAAW,eAAe,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAGpF,EAAAA,QAAO,UAAU,QAAQ,SAAS,MAAO,QAAQ,QAAQ,QAAQ,UAAU;AAEzE,QAAI,WAAW,QAAW;AACxB,iBAAW;AACX,eAAS,KAAK;AACd,eAAS;eAEA,WAAW,UAAa,OAAO,WAAW,UAAU;AAC7D,iBAAW;AACX,eAAS,KAAK;AACd,eAAS;IAEb,WAAa,SAAS,MAAM,GAAG;AAC3B,eAAS,WAAW;AACpB,UAAI,SAAS,MAAM,GAAG;AACpB,iBAAS,WAAW;AACpB,YAAI,aAAa,OAAW,YAAW;MAC7C,OAAW;AACL,mBAAW;AACX,iBAAS;;IAEf,OAAS;AACL,YAAM,IAAI;QACR;;;AAIJ,UAAM,YAAY,KAAK,SAAS;AAChC,QAAI,WAAW,UAAa,SAAS,UAAW,UAAS;AAEzD,QAAK,OAAO,SAAS,MAAM,SAAS,KAAK,SAAS,MAAO,SAAS,KAAK,QAAQ;AAC7E,YAAM,IAAI,WAAW,wCAAwC;;AAG/D,QAAI,CAAC,SAAU,YAAW;AAE1B,QAAI,cAAc;AAClB,eAAS;AACP,cAAQ,UAAQ;QACd,KAAK;AACH,iBAAO,SAAS,MAAM,QAAQ,QAAQ,MAAM;QAE9C,KAAK;QACL,KAAK;AACH,iBAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;QAE/C,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO,WAAW,MAAM,QAAQ,QAAQ,MAAM;QAEhD,KAAK;AAEH,iBAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM;QAEjD,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;QAE/C;AACE,cAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,sBAAY,KAAK,UAAU,YAAW;AACtC,wBAAc;;;;AAKtB,EAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,WAAO;MACL,MAAM;MACN,MAAM,MAAM,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,CAAC;;;AAIzD,WAAS,YAAa,KAAK,OAAO,KAAK;AACrC,QAAI,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACrC,aAAO,OAAO,cAAc,GAAG;IACnC,OAAS;AACL,aAAO,OAAO,cAAc,IAAI,MAAM,OAAO,GAAG,CAAC;;;AAIrD,WAAS,UAAW,KAAK,OAAO,KAAK;AACnC,UAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAC9B,UAAM,MAAM,CAAA;AAEZ,QAAI,IAAI;AACR,WAAO,IAAI,KAAK;AACd,YAAM,YAAY,IAAI,CAAC;AACvB,UAAI,YAAY;AAChB,UAAI,mBAAoB,YAAY,MAChC,IACC,YAAY,MACT,IACC,YAAY,MACT,IACA;AAEZ,UAAI,IAAI,oBAAoB,KAAK;AAC/B,YAAI,YAAY,WAAW,YAAY;AAEvC,gBAAQ,kBAAgB;UACtB,KAAK;AACH,gBAAI,YAAY,KAAM;AACpB,0BAAY;;AAEd;UACF,KAAK;AACH,yBAAa,IAAI,IAAI,CAAC;AACtB,iBAAK,aAAa,SAAU,KAAM;AAChC,+BAAiB,YAAY,OAAS,IAAO,aAAa;AAC1D,kBAAI,gBAAgB,KAAM;AACxB,4BAAY;;;AAGhB;UACF,KAAK;AACH,yBAAa,IAAI,IAAI,CAAC;AACtB,wBAAY,IAAI,IAAI,CAAC;AACrB,iBAAK,aAAa,SAAU,QAAS,YAAY,SAAU,KAAM;AAC/D,+BAAiB,YAAY,OAAQ,MAAO,aAAa,OAAS,IAAO,YAAY;AACrF,kBAAI,gBAAgB,SAAU,gBAAgB,SAAU,gBAAgB,QAAS;AAC/E,4BAAY;;;AAGhB;UACF,KAAK;AACH,yBAAa,IAAI,IAAI,CAAC;AACtB,wBAAY,IAAI,IAAI,CAAC;AACrB,yBAAa,IAAI,IAAI,CAAC;AACtB,iBAAK,aAAa,SAAU,QAAS,YAAY,SAAU,QAAS,aAAa,SAAU,KAAM;AAC/F,+BAAiB,YAAY,OAAQ,MAAQ,aAAa,OAAS,MAAO,YAAY,OAAS,IAAO,aAAa;AACnH,kBAAI,gBAAgB,SAAU,gBAAgB,SAAU;AACtD,4BAAY;;;;;AAMtB,UAAI,cAAc,MAAM;AAGtB,oBAAY;AACZ,2BAAmB;MACzB,WAAe,YAAY,OAAQ;AAE7B,qBAAa;AACb,YAAI,KAAK,cAAc,KAAK,OAAQ,KAAM;AAC1C,oBAAY,QAAS,YAAY;;AAGnC,UAAI,KAAK,SAAS;AAClB,WAAK;;AAGP,WAAO,sBAAsB,GAAG;;AAMlC,QAAM,uBAAuB;AAE7B,WAAS,sBAAuB,YAAY;AAC1C,UAAM,MAAM,WAAW;AACvB,QAAI,OAAO,sBAAsB;AAC/B,aAAO,OAAO,aAAa,MAAM,QAAQ,UAAU;;AAIrD,QAAI,MAAM;AACV,QAAI,IAAI;AACR,WAAO,IAAI,KAAK;AACd,aAAO,OAAO,aAAa;QACzB;QACA,WAAW,MAAM,GAAG,KAAK,oBAAoB;;;AAGjD,WAAO;;AAGT,WAAS,WAAY,KAAK,OAAO,KAAK;AACpC,QAAI,MAAM;AACV,UAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,aAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,aAAO,OAAO,aAAa,IAAI,CAAC,IAAI,GAAI;;AAE1C,WAAO;;AAGT,WAAS,YAAa,KAAK,OAAO,KAAK;AACrC,QAAI,MAAM;AACV,UAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,aAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,aAAO,OAAO,aAAa,IAAI,CAAC,CAAC;;AAEnC,WAAO;;AAGT,WAAS,SAAU,KAAK,OAAO,KAAK;AAClC,UAAM,MAAM,IAAI;AAEhB,QAAI,CAAC,SAAS,QAAQ,EAAG,SAAQ;AACjC,QAAI,CAAC,OAAO,MAAM,KAAK,MAAM,IAAK,OAAM;AAExC,QAAI,MAAM;AACV,aAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,aAAO,oBAAoB,IAAI,CAAC,CAAC;;AAEnC,WAAO;;AAGT,WAAS,aAAc,KAAK,OAAO,KAAK;AACtC,UAAM,QAAQ,IAAI,MAAM,OAAO,GAAG;AAClC,QAAI,MAAM;AAEV,aAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AAC5C,aAAO,OAAO,aAAa,MAAM,CAAC,IAAK,MAAM,IAAI,CAAC,IAAI,GAAI;;AAE5D,WAAO;;AAGT,EAAAA,QAAO,UAAU,QAAQ,SAAS,MAAO,OAAO,KAAK;AACnD,UAAM,MAAM,KAAK;AACjB,YAAQ,CAAC,CAAC;AACV,UAAM,QAAQ,SAAY,MAAM,CAAC,CAAC;AAElC,QAAI,QAAQ,GAAG;AACb,eAAS;AACT,UAAI,QAAQ,EAAG,SAAQ;IAC3B,WAAa,QAAQ,KAAK;AACtB,cAAQ;;AAGV,QAAI,MAAM,GAAG;AACX,aAAO;AACP,UAAI,MAAM,EAAG,OAAM;IACvB,WAAa,MAAM,KAAK;AACpB,YAAM;;AAGR,QAAI,MAAM,MAAO,OAAM;AAEvB,UAAM,SAAS,KAAK,SAAS,OAAO,GAAG;AAEvC,WAAO,eAAe,QAAQA,QAAO,SAAS;AAE9C,WAAO;;AAMT,WAAS,YAAa,QAAQ,KAAK,QAAQ;AACzC,QAAK,SAAS,MAAO,KAAK,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAC/E,QAAI,SAAS,MAAM,OAAQ,OAAM,IAAI,WAAW,uCAAuC;;AAGzF,EAAAA,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,QAAQE,aAAY,UAAU;AAC/E,aAAS,WAAW;AACpB,IAAAA,cAAaA,gBAAe;AAC5B,QAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,QAAI,MAAM,KAAK,MAAM;AACrB,QAAI,MAAM;AACV,QAAI,IAAI;AACR,WAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,aAAO,KAAK,SAAS,CAAC,IAAI;;AAG5B,WAAO;;AAGT,EAAAF,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,QAAQE,aAAY,UAAU;AAC/E,aAAS,WAAW;AACpB,IAAAA,cAAaA,gBAAe;AAC5B,QAAI,CAAC,UAAU;AACb,kBAAY,QAAQA,aAAY,KAAK,MAAM;;AAG7C,QAAI,MAAM,KAAK,SAAS,EAAEA,WAAU;AACpC,QAAI,MAAM;AACV,WAAOA,cAAa,MAAM,OAAO,MAAQ;AACvC,aAAO,KAAK,SAAS,EAAEA,WAAU,IAAI;;AAGvC,WAAO;;AAGT,EAAAF,QAAO,UAAU,YACjBA,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQ,UAAU;AACjE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAO,KAAK,MAAM;;AAGpB,EAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAO,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;;AAG7C,EAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAQ,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;;AAG9C,EAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,YAAS,KAAK,MAAM,IACf,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,MACpB,KAAK,SAAS,CAAC,IAAI;;AAG1B,EAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,WAAQ,KAAK,MAAM,IAAI,YACnB,KAAK,SAAS,CAAC,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,IACrB,KAAK,SAAS,CAAC;;AAGnB,EAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,QAAQ;AACtF,aAAS,WAAW;AACpB,mBAAe,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,QAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,kBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,UAAM,KAAK,QACT,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK;AAExB,UAAM,KAAK,KAAK,EAAE,MAAM,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,OAAO,KAAK;AAEd,WAAO,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE;EAC9C,CAAC;AAED,EAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,QAAQ;AACtF,aAAS,WAAW;AACpB,mBAAe,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,QAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,kBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,UAAM,KAAK,QAAQ,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM;AAEf,UAAM,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAC/B,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB;AAEF,YAAQ,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE;EAC/C,CAAC;AAED,EAAAA,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQE,aAAY,UAAU;AAC7E,aAAS,WAAW;AACpB,IAAAA,cAAaA,gBAAe;AAC5B,QAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,QAAI,MAAM,KAAK,MAAM;AACrB,QAAI,MAAM;AACV,QAAI,IAAI;AACR,WAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,aAAO,KAAK,SAAS,CAAC,IAAI;;AAE5B,WAAO;AAEP,QAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIA,WAAU;AAEjD,WAAO;;AAGT,EAAAF,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQE,aAAY,UAAU;AAC7E,aAAS,WAAW;AACpB,IAAAA,cAAaA,gBAAe;AAC5B,QAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,QAAI,IAAIA;AACR,QAAI,MAAM;AACV,QAAI,MAAM,KAAK,SAAS,EAAE,CAAC;AAC3B,WAAO,IAAI,MAAM,OAAO,MAAQ;AAC9B,aAAO,KAAK,SAAS,EAAE,CAAC,IAAI;;AAE9B,WAAO;AAEP,QAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIA,WAAU;AAEjD,WAAO;;AAGT,EAAAF,QAAO,UAAU,WAAW,SAAS,SAAU,QAAQ,UAAU;AAC/D,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,QAAI,EAAE,KAAK,MAAM,IAAI,KAAO,QAAQ,KAAK,MAAM;AAC/C,YAAS,MAAO,KAAK,MAAM,IAAI,KAAK;;AAGtC,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,UAAM,MAAM,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAChD,WAAQ,MAAM,QAAU,MAAM,aAAa;;AAG7C,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,UAAM,MAAM,KAAK,SAAS,CAAC,IAAK,KAAK,MAAM,KAAK;AAChD,WAAQ,MAAM,QAAU,MAAM,aAAa;;AAG7C,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,WAAQ,KAAK,MAAM,IAChB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK;;AAGzB,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,WAAQ,KAAK,MAAM,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC;;AAGpB,EAAAA,QAAO,UAAU,iBAAiB,mBAAmB,SAAS,eAAgB,QAAQ;AACpF,aAAS,WAAW;AACpB,mBAAe,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,QAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,kBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,UAAM,MAAM,KAAK,SAAS,CAAC,IACzB,KAAK,SAAS,CAAC,IAAI,KAAK,IACxB,KAAK,SAAS,CAAC,IAAI,KAAK,MACvB,QAAQ;AAEX,YAAQ,OAAO,GAAG,KAAK,OAAO,EAAE,KAC9B,OAAO,QACP,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,EAAE;EAC5B,CAAC;AAED,EAAAA,QAAO,UAAU,iBAAiB,mBAAmB,SAAS,eAAgB,QAAQ;AACpF,aAAS,WAAW;AACpB,mBAAe,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,QAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,kBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,UAAM,OAAO,SAAS;IACpB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM;AAEf,YAAQ,OAAO,GAAG,KAAK,OAAO,EAAE,KAC9B,OAAO,KAAK,EAAE,MAAM,IAAI,KAAK,KAC7B,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,IAAI;EACR,CAAC;AAED,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAOF,UAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;;AAG/C,EAAAE,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAOF,UAAQ,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;;AAGhD,EAAAE,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAOF,UAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;;AAG/C,EAAAE,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,WAAOF,UAAQ,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;;AAGhD,WAAS,SAAU,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACpD,QAAI,CAACE,QAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,6CAA6C;AAC5F,QAAI,QAAQ,OAAO,QAAQ,IAAK,OAAM,IAAI,WAAW,mCAAmC;AACxF,QAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;;AAG1E,EAAAA,QAAO,UAAU,cACjBA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQE,aAAY,UAAU;AACxF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,IAAAA,cAAaA,gBAAe;AAC5B,QAAI,CAAC,UAAU;AACb,YAAM,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC/C,eAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;;AAGvD,QAAI,MAAM;AACV,QAAI,IAAI;AACR,SAAK,MAAM,IAAI,QAAQ;AACvB,WAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,WAAK,SAAS,CAAC,IAAK,QAAQ,MAAO;;AAGrC,WAAO,SAASA;;AAGlB,EAAAF,QAAO,UAAU,cACjBA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQE,aAAY,UAAU;AACxF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,IAAAA,cAAaA,gBAAe;AAC5B,QAAI,CAAC,UAAU;AACb,YAAM,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC/C,eAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;;AAGvD,QAAI,IAAIA,cAAa;AACrB,QAAI,MAAM;AACV,SAAK,SAAS,CAAC,IAAI,QAAQ;AAC3B,WAAO,EAAE,KAAK,MAAM,OAAO,MAAQ;AACjC,WAAK,SAAS,CAAC,IAAK,QAAQ,MAAO;;AAGrC,WAAO,SAASA;;AAGlB,EAAAF,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQ,UAAU;AAC1E,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,CAAC;AACvD,SAAK,MAAM,IAAK,QAAQ;AACxB,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,SAAK,MAAM,IAAK,QAAQ;AACxB,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,MAAM,IAAK,QAAQ;AACxB,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,WAAO,SAAS;;AAGlB,WAAS,eAAgB,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrD,eAAW,OAAO,KAAK,KAAK,KAAK,QAAQ,CAAC;AAE1C,QAAI,KAAK,OAAO,QAAQ,OAAO,UAAU,CAAC;AAC1C,QAAI,QAAQ,IAAI;AAChB,SAAK,MAAM;AACX,QAAI,QAAQ,IAAI;AAChB,SAAK,MAAM;AACX,QAAI,QAAQ,IAAI;AAChB,SAAK,MAAM;AACX,QAAI,QAAQ,IAAI;AAChB,QAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,OAAO,UAAU,CAAC;AACxD,QAAI,QAAQ,IAAI;AAChB,SAAK,MAAM;AACX,QAAI,QAAQ,IAAI;AAChB,SAAK,MAAM;AACX,QAAI,QAAQ,IAAI;AAChB,SAAK,MAAM;AACX,QAAI,QAAQ,IAAI;AAChB,WAAO;;AAGT,WAAS,eAAgB,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrD,eAAW,OAAO,KAAK,KAAK,KAAK,QAAQ,CAAC;AAE1C,QAAI,KAAK,OAAO,QAAQ,OAAO,UAAU,CAAC;AAC1C,QAAI,SAAS,CAAC,IAAI;AAClB,SAAK,MAAM;AACX,QAAI,SAAS,CAAC,IAAI;AAClB,SAAK,MAAM;AACX,QAAI,SAAS,CAAC,IAAI;AAClB,SAAK,MAAM;AACX,QAAI,SAAS,CAAC,IAAI;AAClB,QAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,OAAO,UAAU,CAAC;AACxD,QAAI,SAAS,CAAC,IAAI;AAClB,SAAK,MAAM;AACX,QAAI,SAAS,CAAC,IAAI;AAClB,SAAK,MAAM;AACX,QAAI,SAAS,CAAC,IAAI;AAClB,SAAK,MAAM;AACX,QAAI,MAAM,IAAI;AACd,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,mBAAmB,mBAAmB,SAAS,iBAAkB,OAAO,SAAS,GAAG;AACnG,WAAO,eAAe,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;EACpF,CAAC;AAED,EAAAA,QAAO,UAAU,mBAAmB,mBAAmB,SAAS,iBAAkB,OAAO,SAAS,GAAG;AACnG,WAAO,eAAe,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;EACpF,CAAC;AAED,EAAAA,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQE,aAAY,UAAU;AACtF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,UAAU;AACb,YAAM,QAAQ,KAAK,IAAI,GAAI,IAAIA,cAAc,CAAC;AAE9C,eAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;;AAG7D,QAAI,IAAI;AACR,QAAI,MAAM;AACV,QAAI,MAAM;AACV,SAAK,MAAM,IAAI,QAAQ;AACvB,WAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,UAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG;AACxD,cAAM;;AAER,WAAK,SAAS,CAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;;AAGlD,WAAO,SAASA;;AAGlB,EAAAF,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQE,aAAY,UAAU;AACtF,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,UAAU;AACb,YAAM,QAAQ,KAAK,IAAI,GAAI,IAAIA,cAAc,CAAC;AAE9C,eAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;;AAG7D,QAAI,IAAIA,cAAa;AACrB,QAAI,MAAM;AACV,QAAI,MAAM;AACV,SAAK,SAAS,CAAC,IAAI,QAAQ;AAC3B,WAAO,EAAE,KAAK,MAAM,OAAO,MAAQ;AACjC,UAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG;AACxD,cAAM;;AAER,WAAK,SAAS,CAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;;AAGlD,WAAO,SAASA;;AAGlB,EAAAF,QAAO,UAAU,YAAY,SAAS,UAAW,OAAO,QAAQ,UAAU;AACxE,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,IAAK;AAC3D,QAAI,QAAQ,EAAG,SAAQ,MAAO,QAAQ;AACtC,SAAK,MAAM,IAAK,QAAQ;AACxB,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,SAAK,MAAM,IAAK,QAAQ;AACxB,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,SAAK,MAAM,IAAK,QAAQ;AACxB,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,QAAI,QAAQ,EAAG,SAAQ,aAAa,QAAQ;AAC5C,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,WAAO,SAAS;;AAGlB,EAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,OAAO,SAAS,GAAG;AACjG,WAAO,eAAe,MAAM,OAAO,QAAQ,CAAC,OAAO,oBAAoB,GAAG,OAAO,oBAAoB,CAAC;EACxG,CAAC;AAED,EAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,OAAO,SAAS,GAAG;AACjG,WAAO,eAAe,MAAM,OAAO,QAAQ,CAAC,OAAO,oBAAoB,GAAG,OAAO,oBAAoB,CAAC;EACxG,CAAC;AAED,WAAS,aAAc,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACxD,QAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AACxE,QAAI,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;;AAG3D,WAAS,WAAY,KAAK,OAAO,QAAQ,cAAc,UAAU;AAC/D,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,UAAU;AACb,mBAAa,KAAK,OAAO,QAAQ,CAAkD;;AAErFF,cAAQ,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AACrD,WAAO,SAAS;;AAGlB,EAAAE,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,WAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,QAAQ;;AAGvD,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,WAAO,WAAW,MAAM,OAAO,QAAQ,OAAO,QAAQ;;AAGxD,WAAS,YAAa,KAAK,OAAO,QAAQ,cAAc,UAAU;AAChE,YAAQ,CAAC;AACT,aAAS,WAAW;AACpB,QAAI,CAAC,UAAU;AACb,mBAAa,KAAK,OAAO,QAAQ,CAAoD;;AAEvFF,cAAQ,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AACrD,WAAO,SAAS;;AAGlB,EAAAE,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,WAAO,YAAY,MAAM,OAAO,QAAQ,MAAM,QAAQ;;AAGxD,EAAAA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,WAAO,YAAY,MAAM,OAAO,QAAQ,OAAO,QAAQ;;AAIzD,EAAAA,QAAO,UAAU,OAAO,SAAS,KAAM,QAAQ,aAAa,OAAO,KAAK;AACtE,QAAI,CAACA,QAAO,SAAS,MAAM,EAAG,OAAM,IAAI,UAAU,6BAA6B;AAC/E,QAAI,CAAC,MAAO,SAAQ;AACpB,QAAI,CAAC,OAAO,QAAQ,EAAG,OAAM,KAAK;AAClC,QAAI,eAAe,OAAO,OAAQ,eAAc,OAAO;AACvD,QAAI,CAAC,YAAa,eAAc;AAChC,QAAI,MAAM,KAAK,MAAM,MAAO,OAAM;AAGlC,QAAI,QAAQ,MAAO,QAAO;AAC1B,QAAI,OAAO,WAAW,KAAK,KAAK,WAAW,EAAG,QAAO;AAGrD,QAAI,cAAc,GAAG;AACnB,YAAM,IAAI,WAAW,2BAA2B;;AAElD,QAAI,QAAQ,KAAK,SAAS,KAAK,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AAChF,QAAI,MAAM,EAAG,OAAM,IAAI,WAAW,yBAAyB;AAG3D,QAAI,MAAM,KAAK,OAAQ,OAAM,KAAK;AAClC,QAAI,OAAO,SAAS,cAAc,MAAM,OAAO;AAC7C,YAAM,OAAO,SAAS,cAAc;;AAGtC,UAAM,MAAM,MAAM;AAElB,QAAI,SAAS,UAAU,OAAO,iBAAiB,UAAU,eAAe,YAAY;AAElF,WAAK,WAAW,aAAa,OAAO,GAAG;IAC3C,OAAS;AACL,uBAAiB,UAAU,IAAI;QAC7B;QACA,KAAK,SAAS,OAAO,GAAG;QACxB;;;AAIJ,WAAO;;AAOT,EAAAA,QAAO,UAAU,OAAO,SAAS,KAAM,KAAK,OAAO,KAAK,UAAU;AAEhE,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,OAAO,UAAU,UAAU;AAC7B,mBAAW;AACX,gBAAQ;AACR,cAAM,KAAK;MACjB,WAAe,OAAO,QAAQ,UAAU;AAClC,mBAAW;AACX,cAAM,KAAK;;AAEb,UAAI,aAAa,UAAa,OAAO,aAAa,UAAU;AAC1D,cAAM,IAAI,UAAU,2BAA2B;;AAEjD,UAAI,OAAO,aAAa,YAAY,CAACA,QAAO,WAAW,QAAQ,GAAG;AAChE,cAAM,IAAI,UAAU,uBAAuB,QAAQ;;AAErD,UAAI,IAAI,WAAW,GAAG;AACpB,cAAMI,QAAO,IAAI,WAAW,CAAC;AAC7B,YAAK,aAAa,UAAUA,QAAO,OAC/B,aAAa,UAAU;AAEzB,gBAAMA;;;IAGd,WAAa,OAAO,QAAQ,UAAU;AAClC,YAAM,MAAM;IAChB,WAAa,OAAO,QAAQ,WAAW;AACnC,YAAM,OAAO,GAAG;;AAIlB,QAAI,QAAQ,KAAK,KAAK,SAAS,SAAS,KAAK,SAAS,KAAK;AACzD,YAAM,IAAI,WAAW,oBAAoB;;AAG3C,QAAI,OAAO,OAAO;AAChB,aAAO;;AAGT,YAAQ,UAAU;AAClB,UAAM,QAAQ,SAAY,KAAK,SAAS,QAAQ;AAEhD,QAAI,CAAC,IAAK,OAAM;AAEhB,QAAI;AACJ,QAAI,OAAO,QAAQ,UAAU;AAC3B,WAAK,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAC5B,aAAK,CAAC,IAAI;;IAEhB,OAAS;AACL,YAAM,QAAQJ,QAAO,SAAS,GAAG,IAC7B,MACAA,QAAO,KAAK,KAAK,QAAQ;AAC7B,YAAM,MAAM,MAAM;AAClB,UAAI,QAAQ,GAAG;AACb,cAAM,IAAI,UAAU,gBAAgB,MAClC,mCAAmC;;AAEvC,WAAK,IAAI,GAAG,IAAI,MAAM,OAAO,EAAE,GAAG;AAChC,aAAK,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG;;;AAInC,WAAO;;AAOT,QAAM,SAAS,CAAA;AACf,WAAS,EAAG,KAAK,YAAY,MAAM;AACjC,WAAO,GAAG,IAAI,MAAM,kBAAkB,KAAK;MACzC,cAAe;AACb,cAAK;AAEL,eAAO,eAAe,MAAM,WAAW;UACrC,OAAO,WAAW,MAAM,MAAM,SAAS;UACvC,UAAU;UACV,cAAc;QACtB,CAAO;AAGD,aAAK,OAAO,GAAG,KAAK,IAAI,KAAK,GAAG;AAGhC,aAAK;AAEL,eAAO,KAAK;;MAGd,IAAI,OAAQ;AACV,eAAO;;MAGT,IAAI,KAAM,OAAO;AACf,eAAO,eAAe,MAAM,QAAQ;UAClC,cAAc;UACd,YAAY;UACZ;UACA,UAAU;QAClB,CAAO;;MAGH,WAAY;AACV,eAAO,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,OAAO;;;;AAKnD;IAAE;IACA,SAAU,MAAM;AACd,UAAI,MAAM;AACR,eAAO,GAAG,IAAI;;AAGhB,aAAO;;IACN;EAAU;AACf;IAAE;IACA,SAAU,MAAM,QAAQ;AACtB,aAAO,QAAQ,IAAI,oDAAoD,OAAO,MAAM;;IACnF;EAAS;AACd;IAAE;IACA,SAAU,KAAK,OAAO,OAAO;AAC3B,UAAI,MAAM,iBAAiB,GAAG;AAC9B,UAAI,WAAW;AACf,UAAI,OAAO,UAAU,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACxD,mBAAW,sBAAsB,OAAO,KAAK,CAAC;MACpD,WAAe,OAAO,UAAU,UAAU;AACpC,mBAAW,OAAO,KAAK;AACvB,YAAI,QAAQ,OAAO,CAAC,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,OAAO,CAAC,KAAK,OAAO,EAAE,IAAI;AACzE,qBAAW,sBAAsB,QAAQ;;AAE3C,oBAAY;;AAEd,aAAO,eAAe,KAAK,cAAc,QAAQ;AACjD,aAAO;;IACN;EAAU;AAEf,WAAS,sBAAuB,KAAK;AACnC,QAAI,MAAM;AACV,QAAI,IAAI,IAAI;AACZ,UAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,IAAI;AACnC,WAAO,KAAK,QAAQ,GAAG,KAAK,GAAG;AAC7B,YAAM,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG;;AAErC,WAAO,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;;AAMjC,WAAS,YAAa,KAAK,QAAQE,aAAY;AAC7C,mBAAe,QAAQ,QAAQ;AAC/B,QAAI,IAAI,MAAM,MAAM,UAAa,IAAI,SAASA,WAAU,MAAM,QAAW;AACvE,kBAAY,QAAQ,IAAI,UAAUA,cAAa,EAAE;;;AAIrD,WAAS,WAAY,OAAO,KAAK,KAAK,KAAK,QAAQA,aAAY;AAC7D,QAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,YAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;AAC1C,UAAI;AACJ,UAAIA,cAAa,GAAG;AAClB,YAAI,QAAQ,KAAK,QAAQ,OAAO,CAAC,GAAG;AAClC,kBAAQ,OAAO,CAAC,WAAW,CAAC,QAAQA,cAAa,KAAK,CAAC,GAAG,CAAC;QACnE,OAAa;AACL,kBAAQ,SAAS,CAAC,QAAQA,cAAa,KAAK,IAAI,CAAC,GAAG,CAAC,iBACzCA,cAAa,KAAK,IAAI,CAAC,GAAG,CAAC;;MAE/C,OAAW;AACL,gBAAQ,MAAM,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;;AAEzC,YAAM,IAAI,OAAO,iBAAiB,SAAS,OAAO,KAAK;;AAEzD,gBAAY,KAAK,QAAQA,WAAU;;AAGrC,WAAS,eAAgB,OAAO,MAAM;AACpC,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,OAAO,qBAAqB,MAAM,UAAU,KAAK;;;AAI/D,WAAS,YAAa,OAAO,QAAQ,MAAM;AACzC,QAAI,KAAK,MAAM,KAAK,MAAM,OAAO;AAC/B,qBAAe,OAAO,IAAI;AAC1B,YAAM,IAAI,OAAO,iBAAiB,QAAQ,UAAU,cAAc,KAAK;;AAGzE,QAAI,SAAS,GAAG;AACd,YAAM,IAAI,OAAO,yBAAwB;;AAG3C,UAAM,IAAI,OAAO;MAAiB,QAAQ;MACR,MAAM,OAAO,IAAI,CAAC,WAAW,MAAM;MACnC;IAAK;;AAMzC,QAAM,oBAAoB;AAE1B,WAAS,YAAa,KAAK;AAEzB,UAAM,IAAI,MAAM,GAAG,EAAE,CAAC;AAEtB,UAAM,IAAI,KAAI,EAAG,QAAQ,mBAAmB,EAAE;AAE9C,QAAI,IAAI,SAAS,EAAG,QAAO;AAE3B,WAAO,IAAI,SAAS,MAAM,GAAG;AAC3B,YAAM,MAAM;;AAEd,WAAO;;AAGT,WAAS,YAAa,QAAQ,OAAO;AACnC,YAAQ,SAAS;AACjB,QAAI;AACJ,UAAM,SAAS,OAAO;AACtB,QAAI,gBAAgB;AACpB,UAAM,QAAQ,CAAA;AAEd,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,kBAAY,OAAO,WAAW,CAAC;AAG/B,UAAI,YAAY,SAAU,YAAY,OAAQ;AAE5C,YAAI,CAAC,eAAe;AAElB,cAAI,YAAY,OAAQ;AAEtB,iBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;UACV,WAAmB,IAAI,MAAM,QAAQ;AAE3B,iBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;;AAIF,0BAAgB;AAEhB;;AAIF,YAAI,YAAY,OAAQ;AACtB,eAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD,0BAAgB;AAChB;;AAIF,qBAAa,gBAAgB,SAAU,KAAK,YAAY,SAAU;iBACzD,eAAe;AAExB,aAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;;AAGpD,sBAAgB;AAGhB,UAAI,YAAY,KAAM;AACpB,aAAK,SAAS,KAAK,EAAG;AACtB,cAAM,KAAK,SAAS;MAC1B,WAAe,YAAY,MAAO;AAC5B,aAAK,SAAS,KAAK,EAAG;AACtB,cAAM;UACJ,aAAa,IAAM;UACnB,YAAY,KAAO;;MAE3B,WAAe,YAAY,OAAS;AAC9B,aAAK,SAAS,KAAK,EAAG;AACtB,cAAM;UACJ,aAAa,KAAM;UACnB,aAAa,IAAM,KAAO;UAC1B,YAAY,KAAO;;MAE3B,WAAe,YAAY,SAAU;AAC/B,aAAK,SAAS,KAAK,EAAG;AACtB,cAAM;UACJ,aAAa,KAAO;UACpB,aAAa,KAAM,KAAO;UAC1B,aAAa,IAAM,KAAO;UAC1B,YAAY,KAAO;;MAE3B,OAAW;AACL,cAAM,IAAI,MAAM,oBAAoB;;;AAIxC,WAAO;;AAGT,WAAS,aAAc,KAAK;AAC1B,UAAM,YAAY,CAAA;AAClB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AAEnC,gBAAU,KAAK,IAAI,WAAW,CAAC,IAAI,GAAI;;AAEzC,WAAO;;AAGT,WAAS,eAAgB,KAAK,OAAO;AACnC,QAAI,GAAG,IAAI;AACX,UAAM,YAAY,CAAA;AAClB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,WAAK,SAAS,KAAK,EAAG;AAEtB,UAAI,IAAI,WAAW,CAAC;AACpB,WAAK,KAAK;AACV,WAAK,IAAI;AACT,gBAAU,KAAK,EAAE;AACjB,gBAAU,KAAK,EAAE;;AAGnB,WAAO;;AAGT,WAAS,cAAe,KAAK;AAC3B,WAAO,OAAO,YAAY,YAAY,GAAG,CAAC;;AAG5C,WAAS,WAAY,KAAK,KAAK,QAAQ,QAAQ;AAC7C,QAAI;AACJ,SAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,UAAK,IAAI,UAAU,IAAI,UAAY,KAAK,IAAI,OAAS;AACrD,UAAI,IAAI,MAAM,IAAI,IAAI,CAAC;;AAEzB,WAAO;;AAMT,WAAS,WAAY,KAAK,MAAM;AAC9B,WAAO,eAAe,QACnB,OAAO,QAAQ,IAAI,eAAe,QAAQ,IAAI,YAAY,QAAQ,QACjE,IAAI,YAAY,SAAS,KAAK;;AAEpC,WAAS,YAAa,KAAK;AAEzB,WAAO,QAAQ;;AAKjB,QAAM,sBAAuB,WAAY;AACvC,UAAM,WAAW;AACjB,UAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAM,MAAM,IAAI;AAChB,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,cAAM,MAAM,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC;;;AAG7C,WAAO;EACT,EAAC;AAGD,WAAS,mBAAoB,IAAI;AAC/B,WAAO,OAAO,WAAW,cAAc,yBAAyB;;AAGlE,WAAS,yBAA0B;AACjC,UAAM,IAAI,MAAM,sBAAsB;EACxC;;AChhEA,IAAA,SAAeF,OAAAA;;;;;;;;;;;;;;;;;;", "names": ["buffer", "require$$0", "ieee754", "require$$1", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "byteLength", "i", "code"]}