import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  ClassDB,
  classDiagram_default,
  classRenderer_v3_unified_default,
  styles_default
} from "./chunk-PFM33CF3.js";
import "./chunk-GLLMIW3A.js";
import "./chunk-MR6MWR7J.js";
import "./chunk-RSPTPNXB.js";
import "./chunk-AFEHBXBB.js";
import "./chunk-NQYH5CHG.js";
import "./chunk-GGOXHACG.js";
import "./chunk-P7MYJ3MY.js";
import "./chunk-2L5SUMRB.js";
import "./chunk-LDWVYXYB.js";
import "./chunk-XBCGGAUP.js";
import {
  __name
} from "./chunk-C5K7Y3YN.js";
import "./chunk-XNDO3ZN6.js";
import "./chunk-6SRBQE6V.js";
import "./chunk-XRJB3B2O.js";
import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-3TBAVN4U.js";

// node_modules/mermaid/dist/chunks/mermaid.core/classDiagram-v2-COTLJTTW.mjs
var import_dist = __toESM(require_dist(), 1);
var import_dist2 = __toESM(require_dist2(), 1);
var import_dist3 = __toESM(require_dist3(), 1);
var diagram = {
  parser: classDiagram_default,
  get db() {
    return new ClassDB();
  },
  renderer: classRenderer_v3_unified_default,
  styles: styles_default,
  init: __name((cnf) => {
    if (!cnf.class) {
      cnf.class = {};
    }
    cnf.class.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
  }, "init")
};
export {
  diagram
};
//# sourceMappingURL=classDiagram-v2-COTLJTTW-DQCVSLIU.js.map
